<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

// Include the configuration file
require_once '../../config.php';

// Get template ID from request
$template_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$template_id) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Template ID is required']);
    exit();
}

try {
    // Get template details
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
    $stmt->execute([$template_id]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => 'Template not found']);
        exit();
    }
    
    // Sample data for preview
    $sample_data = [
        'recipient_full_name' => '<PERSON>',
        'recipient_first_name' => 'John',
        'recipient_email' => '<EMAIL>',
        'recipient_phone' => '+1234567890',
        'sender_name' => $_SESSION['admin_name'] ?? 'Admin User',
        'sender_email' => $_SESSION['admin_email'] ?? '<EMAIL>',
        'church_name' => 'Freedom Assembly Church',
        'total_recipients' => '100',
        'current_date' => date('F j, Y'),
        'current_time' => date('h:i A')
    ];
    
    // Replace placeholders in subject and content
    $subject = $template['subject'];
    $content = $template['content'];
    
    foreach ($sample_data as $key => $value) {
        $subject = str_replace('{' . $key . '}', $value, $subject);
        $content = str_replace('{' . $key . '}', $value, $content);
    }
    
    // Return preview HTML
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'preview' => [
            'subject' => $subject,
            'content' => $content
        ]
    ]);
    
} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?> 