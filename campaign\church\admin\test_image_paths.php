<?php
/**
 * Test Image Path Resolution
 * 
 * This script tests the image path resolution logic to ensure
 * user-uploaded images display correctly instead of fallback images
 */

// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Test Image Path Resolution</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-12'>
            <div class='card'>
                <div class='card-header bg-primary text-white'>
                    <h5 class='mb-0'><i class='bi bi-image'></i> Image Path Resolution Test</h5>
                </div>
                <div class='card-body'>
";

try {
    // Get members with image paths - same query as members.php
    $stmt = $conn->prepare("SELECT id, full_name, image_path FROM members WHERE image_path IS NOT NULL AND image_path != '' ORDER BY id DESC LIMIT 10");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($members) > 0) {
        echo "<h6>Testing Image Path Resolution (Same Logic as members.php):</h6>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>ID</th><th>Name</th><th>DB Path</th><th>Resolved Path</th><th>File Exists</th><th>Preview</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($members as $member) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($member['id']) . "</td>";
            echo "<td>" . htmlspecialchars($member['full_name']) . "</td>";
            echo "<td><code>" . htmlspecialchars($member['image_path']) . "</code></td>";
            
            // Apply the EXACT same logic as in members.php
            $imagePath = '../assets/img/default-profile.jpg'; // Default fallback
            $resolvedPath = 'DEFAULT';
            
            if (!empty($member['image_path'])) {
                $dbImagePath = $member['image_path'];
                
                // Handle different possible formats in database
                if (strpos($dbImagePath, 'uploads/') === 0) {
                    // Path is 'uploads/filename.jpg' - add '../'
                    $testPath = '../' . $dbImagePath;
                } elseif (strpos($dbImagePath, '/uploads/') !== false) {
                    // Path contains '/uploads/' - use as is with '..'
                    $testPath = '..' . $dbImagePath;
                } else {
                    // Assume it's just the filename - add full path
                    $testPath = '../uploads/' . basename($dbImagePath);
                }
                
                // Verify file exists before using it
                $fullPath = __DIR__ . '/' . $testPath;
                if (file_exists($fullPath)) {
                    $imagePath = $testPath;
                    $resolvedPath = $testPath;
                }
            }
            
            echo "<td><code>" . htmlspecialchars($resolvedPath) . "</code></td>";
            
            $fileExists = ($resolvedPath !== 'DEFAULT');
            echo "<td><span class='badge bg-" . ($fileExists ? 'success' : 'warning') . "'>" . ($fileExists ? 'USER IMAGE' : 'DEFAULT') . "</span></td>";
            
            // Show preview
            echo "<td>";
            echo "<img src='" . htmlspecialchars($imagePath) . "' ";
            echo "alt='" . htmlspecialchars($member['full_name']) . "' ";
            echo "class='rounded-circle' ";
            echo "style='width: 50px; height: 50px; object-fit: cover;' ";
            echo "onerror='this.src=\"../assets/img/default-profile.jpg\"; this.title=\"Fallback Image\";' ";
            echo "title='" . htmlspecialchars($member['full_name']) . "'>";
            echo "</td>";
            
            echo "</tr>";
        }
        
        echo "</tbody></table>";
        echo "</div>";
        
        // Summary
        $userImageCount = 0;
        $defaultImageCount = 0;
        
        foreach ($members as $member) {
            if (!empty($member['image_path'])) {
                $dbImagePath = $member['image_path'];
                
                if (strpos($dbImagePath, 'uploads/') === 0) {
                    $testPath = '../' . $dbImagePath;
                } elseif (strpos($dbImagePath, '/uploads/') !== false) {
                    $testPath = '..' . $dbImagePath;
                } else {
                    $testPath = '../uploads/' . basename($dbImagePath);
                }
                
                $fullPath = __DIR__ . '/' . $testPath;
                if (file_exists($fullPath)) {
                    $userImageCount++;
                } else {
                    $defaultImageCount++;
                }
            } else {
                $defaultImageCount++;
            }
        }
        
        echo "<div class='row mt-4'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-success text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>$userImageCount</h3>";
        echo "<p class='mb-0'>User Images Found</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-warning text-dark'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>$defaultImageCount</h3>";
        echo "<p class='mb-0'>Default Images</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        if ($userImageCount > 0) {
            echo "<div class='alert alert-success mt-4'>";
            echo "<h6>✅ Success!</h6>";
            echo "<p class='mb-0'>Found $userImageCount user-uploaded images that should now display correctly on the members page instead of fallback images.</p>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-warning mt-4'>";
            echo "<h6>⚠️ No User Images Found</h6>";
            echo "<p class='mb-0'>All members are using default images. This could mean:</p>";
            echo "<ul class='mb-0'>";
            echo "<li>Image files were deleted from the uploads directory</li>";
            echo "<li>Database paths are incorrect</li>";
            echo "<li>Members haven't uploaded profile images</li>";
            echo "</ul>";
            echo "</div>";
        }
        
    } else {
        echo "<div class='alert alert-info'>No members with image paths found in database</div>";
    }
    
    // Test upload directory
    echo "<hr><h6>Upload Directory Status:</h6>";
    $uploadDir = __DIR__ . '/../uploads/';
    if (is_dir($uploadDir)) {
        $files = scandir($uploadDir);
        $imageFiles = array_filter($files, function($file) {
            return !in_array($file, ['.', '..']) && preg_match('/\.(jpg|jpeg|png|gif)$/i', $file);
        });
        
        echo "<div class='alert alert-info'>";
        echo "<strong>Upload Directory:</strong> " . htmlspecialchars($uploadDir) . "<br>";
        echo "<strong>Total Image Files:</strong> " . count($imageFiles) . "<br>";
        echo "<strong>Directory Writable:</strong> " . (is_writable($uploadDir) ? 'YES' : 'NO') . "<br>";
        echo "</div>";
        
        if (count($imageFiles) > 5) {
            echo "<div class='alert alert-success'>✅ Upload directory contains " . count($imageFiles) . " image files</div>";
        }
    } else {
        echo "<div class='alert alert-danger'>❌ Upload directory does not exist</div>";
    }
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Database Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "
                    <div class='d-grid gap-2 mt-4'>
                        <a href='members.php' class='btn btn-primary'>Test on Members Page</a>
                        <a href='debug_images.php' class='btn btn-outline-info'>Detailed Debug Info</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
