<?php
/**
 * Email Analytics Test
 * 
 * Tests email analytics functionality including tracking, logging, and statistics
 */

class EmailAnalyticsTest
{
    private $pdo;
    
    public function __construct()
    {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    /**
     * Test email logging functionality
     */
    public function testEmailLogging()
    {
        // Create a test member
        $memberId = createTestMember([
            'first_name' => 'Analytics',
            'last_name' => 'Test',
            'email' => '<EMAIL>'
        ]);
        
        // Create test email template
        $templateId = createTestTemplate([
            'template_name' => 'Analytics Test Template',
            'subject' => 'Analytics Test',
            'content' => 'This is a test for analytics'
        ]);
        
        // Log an email
        $sql = "INSERT INTO email_logs 
                (member_id, template_id, email_type, subject, sent_at, status) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        $now = date('Y-m-d H:i:s');
        $success = $stmt->execute([
            $memberId,
            $templateId,
            'test',
            'Analytics Test Subject',
            $now,
            'sent'
        ]);
        
        $logId = $this->pdo->lastInsertId();
        
        // Verify the log entry exists
        $sql = "SELECT * FROM email_logs WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$logId]);
        $log = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $logExists = ($log !== false);
        $dataCorrect = (
            $log && 
            $log['member_id'] == $memberId &&
            $log['template_id'] == $templateId &&
            $log['email_type'] == 'test' &&
            $log['status'] == 'sent'
        );
        
        return [
            'name' => 'Email Logging',
            'success' => $success && $logExists && $dataCorrect,
            'message' => $success && $logExists && $dataCorrect ? 
                "Successfully logged email with ID: $logId" : 
                "Failed to log email or data mismatch"
        ];
    }
    
    /**
     * Test email open tracking
     */
    public function testEmailOpenTracking()
    {
        // Create test member
        $memberId = createTestMember([
            'first_name' => 'Tracking',
            'last_name' => 'Test',
            'email' => '<EMAIL>'
        ]);
        
        // Create a test email log first
        $sql = "INSERT INTO email_logs 
                (member_id, template_id, email_type, subject, sent_at, status) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        $now = date('Y-m-d H:i:s');
        $stmt->execute([
            $memberId,
            null,
            'tracking_test',
            'Open Tracking Test',
            $now,
            'sent'
        ]);
        
        $logId = $this->pdo->lastInsertId();
        
        // Create tracking table if it doesn't exist
        $createTable = "CREATE TABLE IF NOT EXISTS email_tracking (
            id INT(11) NOT NULL AUTO_INCREMENT,
            email_log_id INT(11) NOT NULL,
            member_id INT(11) NOT NULL,
            sent_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            opened_at TIMESTAMP NULL DEFAULT NULL,
            opened_count INT(11) DEFAULT 0,
            ip_address VARCHAR(45) DEFAULT NULL,
            user_agent TEXT,
            PRIMARY KEY (id),
            KEY email_log_id (email_log_id),
            KEY member_id (member_id),
            CONSTRAINT email_tracking_ibfk_1 FOREIGN KEY (email_log_id) REFERENCES email_logs (id) ON DELETE CASCADE,
            CONSTRAINT email_tracking_ibfk_2 FOREIGN KEY (member_id) REFERENCES members (id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $this->pdo->exec($createTable);
        } catch (PDOException $e) {
            // Table might already exist, continue
        }
        
        // Record email open
        $sql = "INSERT INTO email_tracking 
                (email_log_id, member_id, sent_at, opened_at, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        $openSuccess = $stmt->execute([
            $logId,
            $memberId,
            $now,
            date('Y-m-d H:i:s'),
            '127.0.0.1',
            'PHPUnit Test Runner'
        ]);
        
        $trackingId = $this->pdo->lastInsertId();
        
        // Verify tracking record exists
        $sql = "SELECT * FROM email_tracking WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$trackingId]);
        $tracking = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $trackingExists = ($tracking !== false);
        $dataCorrect = (
            $tracking && 
            $tracking['email_log_id'] == $logId &&
            $tracking['member_id'] == $memberId &&
            $tracking['ip_address'] == '127.0.0.1'
        );
        
        return [
            'name' => 'Email Open Tracking',
            'success' => $openSuccess && $trackingExists && $dataCorrect,
            'message' => $openSuccess && $trackingExists && $dataCorrect ? 
                "Successfully tracked email open event" : 
                "Failed to track email open or data mismatch"
        ];
    }
    
    /**
     * Test email analytics summary
     */
    public function testEmailAnalyticsSummary()
    {
        // Create multiple test email logs with different statuses
        
        // First, create a test member
        $memberId = createTestMember([
            'first_name' => 'Summary',
            'last_name' => 'Test',
            'email' => '<EMAIL>'
        ]);
        
        // Clear existing test data
        $this->pdo->exec("DELETE FROM email_logs WHERE email_type = 'summary_test'");
        
        // Insert test data: 5 sent, 2 failed, 3 opened
        $now = date('Y-m-d H:i:s');
        
        // Insert sent emails
        for ($i = 0; $i < 5; $i++) {
            $sql = "INSERT INTO email_logs 
                    (member_id, email_type, subject, sent_at, status) 
                    VALUES (?, ?, ?, ?, ?)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                $memberId,
                'summary_test',
                'Summary Test ' . $i,
                $now,
                'sent'
            ]);
            
            // Make 3 of them opened
            if ($i < 3) {
                $logId = $this->pdo->lastInsertId();
                
                $sql = "INSERT INTO email_tracking (email_log_id, opened_at) 
                        VALUES (?, ?)";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute([$logId, $now]);
            }
        }
        
        // Insert failed emails
        for ($i = 0; $i < 2; $i++) {
            $sql = "INSERT INTO email_logs 
                    (member_id, email_type, subject, sent_at, status, error_message) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                $memberId,
                'summary_test',
                'Failed Test ' . $i,
                $now,
                'failed',
                'Test error message'
            ]);
        }
        
        // Calculate summary statistics
        $sql = "SELECT 
                COUNT(*) as total_emails,
                SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as successful_emails,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_emails
                FROM email_logs 
                WHERE email_type = 'summary_test'";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute();
        $summary = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Calculate open rate
        $sql = "SELECT COUNT(DISTINCT et.email_log_id) as opened_count
                FROM email_tracking et
                JOIN email_logs el ON et.email_log_id = el.id
                WHERE el.email_type = 'summary_test'";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute();
        $openData = $stmt->fetch(PDO::FETCH_ASSOC);
        $openRate = ($summary['successful_emails'] > 0) ? 
            ($openData['opened_count'] / $summary['successful_emails']) * 100 : 0;
        
        // Verify calculated statistics
        $statsCorrect = (
            $summary['total_emails'] == 7 &&
            $summary['successful_emails'] == 5 &&
            $summary['failed_emails'] == 2 &&
            $openData['opened_count'] == 3 &&
            $openRate == 60.0 // 3/5 = 60%
        );
        
        return [
            'name' => 'Email Analytics Summary',
            'success' => $statsCorrect,
            'message' => $statsCorrect ? 
                "Successfully calculated analytics summary: Total: 7, Sent: 5, Failed: 2, Open Rate: 60%" : 
                "Failed to calculate correct analytics summary"
        ];
    }
    
    /**
     * Run all tests
     */
    public function runAllTests()
    {
        $results = [];
        $results[] = $this->testEmailLogging();
        $results[] = $this->testEmailOpenTracking();
        $results[] = $this->testEmailAnalyticsSummary();
        
        return $results;
    }
} 