<?php
/**
 * Simple Test Runner
 * This script runs tests and displays results in a web browser
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include configuration and test files
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/bootstrap.php';

// Include test files
$testFiles = [
    __DIR__ . '/Unit/BirthdayReminderTest.php',
    __DIR__ . '/Unit/EmailTemplateTest.php',
    __DIR__ . '/Unit/SiteSettingsTest.php'
];

require_once __DIR__ . '/Unit/MemberManagementTest.php';
require_once __DIR__ . '/Unit/EmailAnalyticsTest.php';
require_once __DIR__ . '/Unit/FileUploadTest.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Church Email System - Test Results</title>
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #eee;
        }
        .test-suite {
            margin-bottom: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .test-case {
            margin: 0.5rem 0;
            padding: 0.5rem;
            border-left: 4px solid #ddd;
        }
        .success {
            border-left-color: #28a745;
            background: #f0fff0;
        }
        .failure {
            border-left-color: #dc3545;
            background: #fff0f0;
        }
        .error {
            border-left-color: #ffc107;
            background: #fffff0;
        }
        .summary {
            margin-top: 2rem;
            padding: 1rem;
            background: #e9ecef;
            border-radius: 4px;
            text-align: center;
        }
        pre {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            overflow-x: auto;
        }
        .test-output {
            font-family: monospace;
            white-space: pre-wrap;
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Church Email System - Test Results</h1>
        
        <div class="test-suite">
            <h2>Manual Test Runner</h2>
            
            <?php
            // Initialize counters
            $totalTests = 0;
            $passedTests = 0;
            $failedTests = 0;
            $errors = 0;

            // Test the database connection
            try {
                echo "<div class='test-case'>";
                echo "<strong>Database Connection Test</strong>: ";
                $pdo->query("SELECT 1");
                echo "<span class='success'>success</span>";
                echo "</div>";
                $totalTests++;
                $passedTests++;
            } catch (PDOException $e) {
                echo "<div class='test-case failure'>";
                echo "<strong>Database Connection Test</strong>: FAILED<br>";
                echo "<pre>" . htmlspecialchars($e->getMessage()) . "</pre>";
                echo "</div>";
                $totalTests++;
                $failedTests++;
            }

            // Test email template functionality
            try {
                echo "<div class='test-case'>";
                echo "<strong>Email Template Test</strong>: ";
                
                $memberData = [
                    'first_name' => 'Test',
                    'last_name' => 'User',
                    'email' => '<EMAIL>'
                ];
                
                $template = "Hello {first_name} {last_name}!";
                $result = replaceTemplatePlaceholders($template, $memberData);
                
                if ($result === "Hello Test User!") {
                    echo "<span class='success'>success</span>";
                    $passedTests++;
                } else {
                    echo "<span class='failure'>failed</span>";
                    echo "<div class='test-output'>Expected: Hello Test User!<br>Got: " . htmlspecialchars($result) . "</div>";
                    $failedTests++;
                }
                
                $totalTests++;
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='test-case error'>";
                echo "<strong>Email Template Test</strong>: ERROR<br>";
                echo "<pre>" . htmlspecialchars($e->getMessage()) . "</pre>";
                echo "</div>";
                $totalTests++;
                $errors++;
            }

            // Test site settings
            try {
                echo "<div class='test-case'>";
                echo "<strong>Site Settings Test</strong>: ";
                
                // Test setting and getting a value
                $testValue = "Test Value " . time();
                update_site_setting('test_setting', $testValue);
                $retrievedValue = get_site_setting('test_setting');
                
                if ($retrievedValue === $testValue) {
                    echo "<span class='success'>success</span>";
                    $passedTests++;
                } else {
                    echo "<span class='failure'>failed</span>";
                    echo "<div class='test-output'>Expected: $testValue<br>Got: " . htmlspecialchars($retrievedValue) . "</div>";
                    $failedTests++;
                }
                
                $totalTests++;
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='test-case error'>";
                echo "<strong>Site Settings Test</strong>: ERROR<br>";
                echo "<pre>" . htmlspecialchars($e->getMessage()) . "</pre>";
                echo "</div>";
                $totalTests++;
                $errors++;
            }

            // Test birthday reminder functionality
            try {
                echo "<div class='test-case'>";
                echo "<strong>Birthday Reminder Test</strong>: ";
                
                // Create a test member with today's birthday
                $memberId = createTestMember([
                    'first_name' => 'Birthday',
                    'birth_date' => date('Y-m-d')
                ]);
                
                // Initialize BirthdayReminder
                $reminder = new BirthdayReminder($pdo);
                $birthdays = $reminder->getUpcomingBirthdays(0);
                
                if (count($birthdays) > 0 && $birthdays[0]['first_name'] === 'Birthday') {
                    echo "<span class='success'>success</span>";
                    $passedTests++;
                } else {
                    echo "<span class='failure'>failed</span>";
                    echo "<div class='test-output'>Expected: Birthday member<br>Got: " . count($birthdays) . " birthdays</div>";
                    $failedTests++;
                }
                
                $totalTests++;
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='test-case error'>";
                echo "<strong>Birthday Reminder Test</strong>: ERROR<br>";
                echo "<pre>" . htmlspecialchars($e->getMessage()) . "</pre>";
                echo "</div>";
                $totalTests++;
                $errors++;
            }

            // Test member management functionality
            echo "<div class='test-suite'>";
            echo "<h2>Member Management Tests</h2>";
            
            // Initialize the test class
            $memberTests = new MemberManagementTest();
            $memberTestResults = $memberTests->runAllTests();
            
            // Display results
            foreach ($memberTestResults as $result) {
                echo "<div class='test-case " . ($result['success'] ? 'success' : 'failure') . "'>";
                echo "<strong>{$result['name']}</strong>: ";
                echo "<span class='" . ($result['success'] ? 'success' : 'failure') . "'>";
                echo $result['success'] ? 'success' : 'failed';
                echo "</span>";
                echo "<div class='test-output'>{$result['message']}</div>";
                echo "</div>";
                
                $totalTests++;
                if ($result['success']) {
                    $passedTests++;
                } else {
                    $failedTests++;
                }
            }
            
            echo "</div>";

            // Test email analytics functionality
            echo "<div class='test-suite'>";
            echo "<h2>Email Analytics Tests</h2>";
            
            // Initialize the test class
            $analyticsTests = new EmailAnalyticsTest();
            $analyticsTestResults = $analyticsTests->runAllTests();
            
            // Display results
            foreach ($analyticsTestResults as $result) {
                echo "<div class='test-case " . ($result['success'] ? 'success' : 'failure') . "'>";
                echo "<strong>{$result['name']}</strong>: ";
                echo "<span class='" . ($result['success'] ? 'success' : 'failure') . "'>";
                echo $result['success'] ? 'success' : 'failed';
                echo "</span>";
                echo "<div class='test-output'>{$result['message']}</div>";
                echo "</div>";
                
                $totalTests++;
                if ($result['success']) {
                    $passedTests++;
                } else {
                    $failedTests++;
                }
            }
            
            echo "</div>";

            // Test file upload functionality
            echo "<div class='test-suite'>";
            echo "<h2>File Upload Tests</h2>";
            
            // Initialize the test class
            $fileUploadTests = new FileUploadTest();
            $fileUploadTestResults = $fileUploadTests->runAllTests();
            
            // Display results
            foreach ($fileUploadTestResults as $result) {
                echo "<div class='test-case " . ($result['success'] ? 'success' : 'failure') . "'>";
                echo "<strong>{$result['name']}</strong>: ";
                echo "<span class='" . ($result['success'] ? 'success' : 'failure') . "'>";
                echo $result['success'] ? 'success' : 'failed';
                echo "</span>";
                echo "<div class='test-output'>{$result['message']}</div>";
                echo "</div>";
                
                $totalTests++;
                if ($result['success']) {
                    $passedTests++;
                } else {
                    $failedTests++;
                }
            }
            
            echo "</div>";

            // Print summary
            echo "<div class='summary'>";
            echo "<h3>Test Summary</h3>";
            echo "Total Tests: $totalTests<br>";
            echo "Passed: $passedTests<br>";
            echo "Failed: $failedTests<br>";
            echo "Errors: $errors<br>";
            echo "</div>";
            ?>
        </div>
        
        <div class="test-suite">
            <h2>System Information</h2>
            <?php
            echo "<div class='test-case'>";
            echo "<strong>PHP Version</strong>: " . phpversion() . "<br>";
            echo "<strong>MySQL Version</strong>: " . $pdo->query('select version()')->fetchColumn() . "<br>";
            echo "<strong>Test Database</strong>: " . $testDb . "<br>";
            echo "</div>";
            ?>
        </div>
    </div>
</body>
</html> 