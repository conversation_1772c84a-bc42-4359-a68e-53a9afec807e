#!/bin/bash
# Cron job script for processing scheduled emails
# This should be set up to run every 5 minutes

# Set the path to the PHP executable
PHP_BIN=/usr/bin/php

# Set the path to the script
SCRIPT_PATH=/home/<USER>/public_html/church/cron/process_scheduled_emails.php

# Set the cron key
CRON_KEY=fac_2024_secure_cron_8x9q2p5m

# Run the script with the cron key
$PHP_BIN $SCRIPT_PATH cron_key=$CRON_KEY

# For web hosting environments where direct PHP execution is not possible,
# use wget or curl instead:
# wget -q -O /dev/null "https://freedomassemblydb.online/cron/process_scheduled_emails.php?cron_key=$CRON_KEY"
# curl -s "https://freedomassemblydb.online/cron/process_scheduled_emails.php?cron_key=$CRON_KEY" > /dev/null
