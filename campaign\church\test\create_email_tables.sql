-- SQL Script to create tables for email scheduling functionality

-- Email templates table (ALTER to match existing structure)
ALTER TABLE email_templates 
  ADD COLUMN IF NOT EXISTS is_html tinyint(1) NOT NULL DEFAULT 1 AFTER content;

-- Email schedules table
CREATE TABLE IF NOT EXISTS email_schedules (
  id int(11) NOT NULL AUTO_INCREMENT,
  
ame varchar(255) NOT NULL,
  schedule_type enum('one_time','recurring','continuous') NOT NULL,
  start_datetime datetime NOT NULL,
  end_datetime datetime DEFAULT NULL,
  emails_per_hour int(11) NOT NULL DEFAULT 100,
  min_interval_seconds int(11) NOT NULL DEFAULT 5,
  status enum('active','paused','completed','failed') NOT NULL DEFAULT 'active',
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_status (status),
  KEY idx_schedule_type (schedule_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
