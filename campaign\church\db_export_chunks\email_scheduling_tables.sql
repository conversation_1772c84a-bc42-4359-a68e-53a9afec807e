-- Email Scheduling System Tables
-- This file contains the SQL to create all the necessary tables for the email scheduling system

-- Main email schedules table
CREATE TABLE IF NOT EXISTS `email_schedules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `status` enum('pending','active','paused','completed','cancelled') NOT NULL DEFAULT 'pending',
  `schedule_type` enum('immediate','scheduled','recurring') NOT NULL DEFAULT 'immediate',
  `scheduled_date` datetime DEFAULT NULL,
  `recurring_type` enum('daily','weekly','monthly','custom') DEFAULT NULL,
  `recurring_value` varchar(50) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `last_run_at` datetime DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON> `idx_status` (`status`),
  <PERSON>EY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Email schedule settings table
CREATE TABLE IF NOT EXISTS `email_schedule_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `schedule_id` int(11) NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `custom_subject` varchar(255) DEFAULT NULL,
  `custom_content` text DEFAULT NULL,
  `track_opens` tinyint(1) NOT NULL DEFAULT 1,
  `track_clicks` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `schedule_id` (`schedule_id`),
  KEY `idx_template_id` (`template_id`),
  CONSTRAINT `fk_schedule_settings_schedule` FOREIGN KEY (`schedule_id`) REFERENCES `email_schedules` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_schedule_settings_template` FOREIGN KEY (`template_id`) REFERENCES `email_templates` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Email schedule recipients table
CREATE TABLE IF NOT EXISTS `email_schedule_recipients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `schedule_id` int(11) NOT NULL,
  `recipient_type` enum('member','contact','group','custom') NOT NULL,
  `recipient_id` int(11) DEFAULT NULL,
  `custom_email` varchar(100) DEFAULT NULL,
  `custom_name` varchar(100) DEFAULT NULL,
  `status` enum('pending','sent','failed','skipped') NOT NULL DEFAULT 'pending',
  `sent_at` datetime DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_schedule_id` (`schedule_id`),
  KEY `idx_status` (`status`),
  KEY `idx_recipient` (`recipient_type`,`recipient_id`),
  CONSTRAINT `fk_schedule_recipients_schedule` FOREIGN KEY (`schedule_id`) REFERENCES `email_schedules` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Email schedule logs table
CREATE TABLE IF NOT EXISTS `email_schedule_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `schedule_id` int(11) NOT NULL,
  `log_type` enum('info','warning','error','success') NOT NULL DEFAULT 'info',
  `message` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_schedule_id` (`schedule_id`),
  KEY `idx_log_type` (`log_type`),
  CONSTRAINT `fk_schedule_logs_schedule` FOREIGN KEY (`schedule_id`) REFERENCES `email_schedules` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
