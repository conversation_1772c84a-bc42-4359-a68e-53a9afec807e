-- Email tracking table definitions 
 
-- Create the email_tracking table if it doesn't exist 
CREATE TABLE IF NOT EXISTS `email_tracking` ( 
  `id` int(11) NOT NULL AUTO_INCREMENT, 
  `email_type` varchar(100) NOT NULL, 
  `recipient_email` varchar(255) NOT NULL, 
  `sent_at` datetime NOT NULL, 
  `opened_at` datetime DEFAULT NULL, 
  `opened_count` int(11) DEFAULT 0, 
  `tracking_id` varchar(64) NOT NULL, 
  PRIMARY KEY (`id`), 
  KEY `tracking_id` (`tracking_id`) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4; 
 
-- Create the email_templates table if it doesn't exist 
CREATE TABLE IF NOT EXISTS `email_templates` ( 
  `id` int(11) NOT NULL AUTO_INCREMENT, 
  `template_name` varchar(255) NOT NULL, 
  `subject` varchar(255) NOT NULL, 
  `content` text NOT NULL, 
  `is_birthday_template` tinyint(1) DEFAULT 0, 
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP, 
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, 
  PRIMARY KEY (`id`) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4; 
