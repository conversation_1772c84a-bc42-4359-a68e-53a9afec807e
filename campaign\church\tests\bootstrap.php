<?php
/**
 * Test Bootstrap File
 * 
 * This file is loaded before running tests and sets up the testing environment.
 */

// Include the main configuration file
require_once __DIR__ . '/../config.php';

// Include required class files
require_once __DIR__ . '/../classes/BirthdayReminder.php';

// Set testing environment
putenv('APP_ENV=testing');

// Create test database if it doesn't exist
try {
    $testDb = 'churchdb_test';
    
    // Create a temporary connection without database
    $tempPdo = new PDO(
        "mysql:host=$host",
        $username,
        $password,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // Create test database if it doesn't exist
    $tempPdo->exec("DROP DATABASE IF EXISTS $testDb");
    $tempPdo->exec("CREATE DATABASE $testDb");
    
    // Switch to test database
    $tempPdo->exec("USE $testDb");
    
    // Import schema from main database
    $mainDb = $dbname;

    // First, get all tables (excluding views)
    $tables = $tempPdo->query("
        SELECT TABLE_NAME 
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = '$mainDb' 
        AND TABLE_TYPE = 'BASE TABLE'
    ")->fetchAll(PDO::FETCH_COLUMN);
    
    // Copy table structures
    foreach ($tables as $table) {
        $tempPdo->exec("CREATE TABLE $testDb.$table LIKE $mainDb.$table");
    }

    // Now get and create views
    $views = $tempPdo->query("
        SELECT TABLE_NAME, VIEW_DEFINITION 
        FROM information_schema.VIEWS 
        WHERE TABLE_SCHEMA = '$mainDb'
    ")->fetchAll(PDO::FETCH_ASSOC);

    foreach ($views as $view) {
        $viewName = $view['TABLE_NAME'];
        $viewDef = $view['VIEW_DEFINITION'];
        // Replace references to the main database with test database
        $viewDef = str_replace("`$mainDb`.", "`$testDb`.", $viewDef);
        $viewDef = str_replace("$mainDb.", "$testDb.", $viewDef);
        $tempPdo->exec("CREATE VIEW $testDb.$viewName AS $viewDef");
    }
    
    // Close temporary connection
    $tempPdo = null;
    
    // Update the global PDO connection to use test database
    $pdo = new PDO(
        "mysql:host=$host;dbname=$testDb;charset=utf8mb4",
        $username,
        $password,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
} catch (PDOException $e) {
    die("Error setting up test database: " . $e->getMessage());
}

// Function to reset the test database
function resetTestDatabase() {
    global $pdo;
    try {
        // Get all tables (excluding views)
        $tables = $pdo->query("
            SELECT TABLE_NAME 
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_TYPE = 'BASE TABLE'
        ")->fetchAll(PDO::FETCH_COLUMN);
        
        // Disable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
        
        // Truncate all tables
        foreach ($tables as $table) {
            $pdo->exec("TRUNCATE TABLE `$table`");
        }
        
        // Re-enable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
        
    } catch (PDOException $e) {
        die("Error resetting test database: " . $e->getMessage());
    }
}

// Create test data helper function
function createTestMember($data = []) {
    global $pdo;
    
    $defaultData = [
        'first_name' => 'Test',
        'last_name' => 'User',
        'full_name' => 'Test User',
        'email' => '<EMAIL>',
        'phone_number' => '1234567890',
        'birth_date' => date('Y-m-d'),
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    // If first_name or last_name is provided but not full_name, generate it
    if (isset($data['first_name']) || isset($data['last_name'])) {
        $firstName = $data['first_name'] ?? $defaultData['first_name'];
        $lastName = $data['last_name'] ?? $defaultData['last_name'];
        $data['full_name'] = trim($firstName . ' ' . $lastName);
    }
    
    $data = array_merge($defaultData, $data);
    
    try {
        $sql = "INSERT INTO members (first_name, last_name, full_name, email, phone_number, birth_date, created_at) 
                VALUES (:first_name, :last_name, :full_name, :email, :phone_number, :birth_date, :created_at)";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($data);
        
        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        throw new Exception("Error creating test member: " . $e->getMessage());
    }
}

// Create test email template helper function
function createTestTemplate($data = []) {
    global $pdo;
    
    $defaultData = [
        'template_name' => 'Test Template',
        'subject' => 'Test Subject',
        'content' => 'Test Content',
        'is_birthday_template' => 0,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $data = array_merge($defaultData, $data);
    
    try {
        $sql = "INSERT INTO email_templates (template_name, subject, content, is_birthday_template, created_at) 
                VALUES (:template_name, :subject, :content, :is_birthday_template, :created_at)";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($data);
        
        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        throw new Exception("Error creating test template: " . $e->getMessage());
    }
} 