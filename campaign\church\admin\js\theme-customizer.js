/**
 * Advanced Theme Customizer
 * Real-time theme preview and customization
 */

class ThemeCustomizer {
    constructor() {
        this.previewMode = false;
        this.originalStyles = {};
        this.presets = {};
        this.currentPreset = 'custom';
        
        this.init();
    }
    
    init() {
        this.loadPresets();
        this.setupEventListeners();
        this.setupColorPickers();
        this.setupRangeSliders();
        this.setupPresetSelector();
        this.setupPreviewToggle();
        this.setupResetButton();
        this.setupExportImport();
        
        console.log('Theme Customizer initialized');
    }
    
    loadPresets() {
        this.presets = {
            'default': {
                name: 'Default Blue',
                colors: {
                    primary_color: '#007bff',
                    secondary_color: '#6c757d',
                    success_color: '#28a745',
                    danger_color: '#dc3545',
                    warning_color: '#ffc107',
                    info_color: '#17a2b8',
                    background_color: '#ffffff',
                    text_color: '#212529'
                },
                typography: {
                    primary_font: 'Inter',
                    font_size_base: '16',
                    line_height_base: '1.5'
                },
                layout: {
                    border_radius: '0.375',
                    sidebar_width: '250',
                    header_height: '60'
                }
            },
            'modern': {
                name: 'Modern Purple',
                colors: {
                    primary_color: '#6f42c1',
                    secondary_color: '#6c757d',
                    success_color: '#20c997',
                    danger_color: '#e74c3c',
                    warning_color: '#f39c12',
                    info_color: '#3498db',
                    background_color: '#f8f9fa',
                    text_color: '#2c3e50'
                },
                typography: {
                    primary_font: 'Poppins',
                    font_size_base: '15',
                    line_height_base: '1.6'
                },
                layout: {
                    border_radius: '0.5',
                    sidebar_width: '280',
                    header_height: '70'
                }
            },
            'minimal': {
                name: 'Minimal Gray',
                colors: {
                    primary_color: '#495057',
                    secondary_color: '#adb5bd',
                    success_color: '#51cf66',
                    danger_color: '#ff6b6b',
                    warning_color: '#ffd43b',
                    info_color: '#74c0fc',
                    background_color: '#ffffff',
                    text_color: '#343a40'
                },
                typography: {
                    primary_font: 'Source Sans Pro',
                    font_size_base: '16',
                    line_height_base: '1.5'
                },
                layout: {
                    border_radius: '0.25',
                    sidebar_width: '240',
                    header_height: '55'
                }
            },
            'vibrant': {
                name: 'Vibrant Orange',
                colors: {
                    primary_color: '#fd7e14',
                    secondary_color: '#6c757d',
                    success_color: '#20c997',
                    danger_color: '#dc3545',
                    warning_color: '#ffc107',
                    info_color: '#0dcaf0',
                    background_color: '#fff8f0',
                    text_color: '#212529'
                },
                typography: {
                    primary_font: 'Nunito',
                    font_size_base: '16',
                    line_height_base: '1.6'
                },
                layout: {
                    border_radius: '0.75',
                    sidebar_width: '260',
                    header_height: '65'
                }
            },
            'corporate': {
                name: 'Corporate Blue',
                colors: {
                    primary_color: '#0d6efd',
                    secondary_color: '#6c757d',
                    success_color: '#20c997',
                    danger_color: '#dc3545',
                    warning_color: '#ffc107',
                    info_color: '#0dcaf0',
                    background_color: '#fff8f0',
                    text_color: '#212529'
                },
                typography: {
                    primary_font: 'Nunito',
                    font_size_base: '16',
                    line_height_base: '1.6'
                },
                layout: {
                    border_radius: '0.75',
                    sidebar_width: '260',
                    header_height: '65'
                }
            },
            'corporate': {
                name: 'Corporate Blue',
                colors: {
                    primary_color: '#0d6efd',
                    secondary_color: '#6c757d',
                    success_color: '#198754',
                    danger_color: '#dc3545',
                    warning_color: '#ffc107',
                    info_color: '#0dcaf0',
                    background_color: '#f8f9fa',
                    text_color: '#212529'
                },
                typography: {
                    primary_font: 'Roboto',
                    font_size_base: '15',
                    line_height_base: '1.5'
                },
                layout: {
                    border_radius: '0.25',
                    sidebar_width: '270',
                    header_height: '60'
                }
            }
        };
    }
    
    setupEventListeners() {
        // Color input changes
        document.addEventListener('input', (e) => {
            if (e.target.type === 'color' && e.target.name) {
                this.updateColorPreview(e.target.name, e.target.value);
            }
        });
        
        // Range input changes
        document.addEventListener('input', (e) => {
            if (e.target.type === 'range' && e.target.name) {
                this.updateRangePreview(e.target.name, e.target.value);
            }
        });
        
        // Select changes
        document.addEventListener('change', (e) => {
            if (e.target.tagName === 'SELECT' && e.target.name) {
                this.updateSelectPreview(e.target.name, e.target.value);
            }
        });
        
        // Font changes
        document.addEventListener('change', (e) => {
            if (e.target.name && e.target.name.includes('font')) {
                this.updateFontPreview(e.target.name, e.target.value);
            }
        });
    }
    
    setupColorPickers() {
        // Add color picker enhancements
        document.querySelectorAll('input[type="color"]').forEach(input => {
            const container = input.parentElement;
            
            // Add hex input
            const hexInput = document.createElement('input');
            hexInput.type = 'text';
            hexInput.className = 'form-control hex-input';
            hexInput.placeholder = '#000000';
            hexInput.value = input.value;
            hexInput.maxLength = 7;
            
            // Sync color picker and hex input
            input.addEventListener('input', () => {
                hexInput.value = input.value;
            });
            
            hexInput.addEventListener('input', () => {
                if (/^#[0-9A-F]{6}$/i.test(hexInput.value)) {
                    input.value = hexInput.value;
                    input.dispatchEvent(new Event('input'));
                }
            });
            
            container.appendChild(hexInput);
            
            // Add color palette
            this.addColorPalette(container, input);
        });
    }
    
    addColorPalette(container, colorInput) {
        const palette = document.createElement('div');
        palette.className = 'color-palette';
        
        const colors = [
            '#007bff', '#6f42c1', '#e83e8c', '#dc3545', '#fd7e14',
            '#ffc107', '#28a745', '#20c997', '#17a2b8', '#6c757d',
            '#343a40', '#000000', '#ffffff', '#f8f9fa', '#e9ecef'
        ];
        
        colors.forEach(color => {
            const swatch = document.createElement('button');
            swatch.type = 'button';
            swatch.className = 'color-swatch';
            swatch.style.backgroundColor = color;
            swatch.title = color;
            swatch.setAttribute('aria-label', `Select color ${color}`);
            
            swatch.addEventListener('click', () => {
                colorInput.value = color;
                colorInput.dispatchEvent(new Event('input'));
            });
            
            palette.appendChild(swatch);
        });
        
        container.appendChild(palette);
    }
    
    setupRangeSliders() {
        document.querySelectorAll('input[type="range"]').forEach(slider => {
            const container = slider.parentElement;
            
            // Add value display
            const valueDisplay = document.createElement('span');
            valueDisplay.className = 'range-value';
            valueDisplay.textContent = slider.value + (slider.dataset.unit || '');
            
            slider.addEventListener('input', () => {
                valueDisplay.textContent = slider.value + (slider.dataset.unit || '');
            });
            
            container.appendChild(valueDisplay);
        });
    }
    
    setupPresetSelector() {
        const presetSelect = document.getElementById('themePreset');
        if (!presetSelect) return;
        
        // Populate presets
        Object.entries(this.presets).forEach(([key, preset]) => {
            const option = document.createElement('option');
            option.value = key;
            option.textContent = preset.name;
            presetSelect.appendChild(option);
        });
        
        // Handle preset selection
        presetSelect.addEventListener('change', (e) => {
            this.applyPreset(e.target.value);
        });
    }
    
    setupPreviewToggle() {
        const previewToggle = document.getElementById('previewToggle');
        if (!previewToggle) return;
        
        previewToggle.addEventListener('change', (e) => {
            this.togglePreview(e.target.checked);
        });
    }
    
    setupResetButton() {
        const resetButton = document.getElementById('resetTheme');
        if (!resetButton) return;
        
        resetButton.addEventListener('click', () => {
            this.resetToDefault();
        });
    }
    
    setupExportImport() {
        const exportButton = document.getElementById('exportTheme');
        const importButton = document.getElementById('importTheme');
        const importFile = document.getElementById('importFile');
        
        if (exportButton) {
            exportButton.addEventListener('click', () => {
                this.exportTheme();
            });
        }
        
        if (importButton && importFile) {
            importButton.addEventListener('click', () => {
                importFile.click();
            });
            
            importFile.addEventListener('change', (e) => {
                this.importTheme(e.target.files[0]);
            });
        }
    }
    
    updateColorPreview(property, value) {
        if (!this.previewMode) return;
        
        const cssProperty = this.getCSSProperty(property);
        if (cssProperty) {
            document.documentElement.style.setProperty(cssProperty, value);
        }
    }
    
    updateRangePreview(property, value) {
        if (!this.previewMode) return;
        
        const cssProperty = this.getCSSProperty(property);
        const unit = this.getCSSUnit(property);
        
        if (cssProperty) {
            document.documentElement.style.setProperty(cssProperty, value + unit);
        }
    }
    
    updateSelectPreview(property, value) {
        if (!this.previewMode) return;
        
        // Handle special select properties
        if (property === 'sidebar_style') {
            document.body.className = document.body.className.replace(/sidebar-\w+/g, '');
            if (value !== 'default') {
                document.body.classList.add(`sidebar-${value}`);
            }
        }
    }
    
    updateFontPreview(property, value) {
        if (!this.previewMode) return;
        
        const cssProperty = this.getCSSProperty(property);
        if (cssProperty) {
            document.documentElement.style.setProperty(cssProperty, `"${value}", sans-serif`);
        }
    }
    
    getCSSProperty(property) {
        const mapping = {
            'primary_color': '--bs-primary',
            'secondary_color': '--bs-secondary',
            'success_color': '--bs-success',
            'danger_color': '--bs-danger',
            'warning_color': '--bs-warning',
            'info_color': '--bs-info',
            'background_color': '--bs-body-bg',
            'text_color': '--bs-body-color',
            'primary_font': '--bs-font-sans-serif',
            'font_size_base': '--bs-font-size-base',
            'line_height_base': '--bs-line-height-base',
            'border_radius': '--bs-border-radius',
            'sidebar_width': '--sidebar-width',
            'header_height': '--header-height'
        };
        
        return mapping[property] || null;
    }
    
    getCSSUnit(property) {
        const units = {
            'font_size_base': 'px',
            'border_radius': 'rem',
            'sidebar_width': 'px',
            'header_height': 'px'
        };
        
        return units[property] || '';
    }
    
    applyPreset(presetKey) {
        const preset = this.presets[presetKey];
        if (!preset) return;
        
        this.currentPreset = presetKey;
        
        // Apply colors
        Object.entries(preset.colors).forEach(([key, value]) => {
            const input = document.querySelector(`input[name="${key}"]`);
            if (input) {
                input.value = value;
                this.updateColorPreview(key, value);
            }
        });
        
        // Apply typography
        Object.entries(preset.typography).forEach(([key, value]) => {
            const input = document.querySelector(`input[name="${key}"], select[name="${key}"]`);
            if (input) {
                input.value = value;
                if (input.type === 'range') {
                    this.updateRangePreview(key, value);
                } else {
                    this.updateFontPreview(key, value);
                }
            }
        });
        
        // Apply layout
        Object.entries(preset.layout).forEach(([key, value]) => {
            const input = document.querySelector(`input[name="${key}"], select[name="${key}"]`);
            if (input) {
                input.value = value;
                this.updateRangePreview(key, value);
            }
        });
    }
    
    togglePreview(enabled) {
        this.previewMode = enabled;
        
        if (enabled) {
            // Store original styles
            this.storeOriginalStyles();
            // Apply current form values
            this.applyCurrentValues();
        } else {
            // Restore original styles
            this.restoreOriginalStyles();
        }
    }
    
    storeOriginalStyles() {
        const properties = [
            '--bs-primary', '--bs-secondary', '--bs-success', '--bs-danger',
            '--bs-warning', '--bs-info', '--bs-body-bg', '--bs-body-color',
            '--bs-font-sans-serif', '--bs-font-size-base', '--bs-line-height-base',
            '--bs-border-radius', '--sidebar-width', '--header-height'
        ];
        
        properties.forEach(prop => {
            const value = getComputedStyle(document.documentElement).getPropertyValue(prop);
            if (value) {
                this.originalStyles[prop] = value;
            }
        });
    }
    
    restoreOriginalStyles() {
        Object.entries(this.originalStyles).forEach(([prop, value]) => {
            document.documentElement.style.setProperty(prop, value);
        });
    }
    
    applyCurrentValues() {
        // Apply all current form values
        document.querySelectorAll('input[type="color"]').forEach(input => {
            this.updateColorPreview(input.name, input.value);
        });
        
        document.querySelectorAll('input[type="range"]').forEach(input => {
            this.updateRangePreview(input.name, input.value);
        });
        
        document.querySelectorAll('select').forEach(select => {
            this.updateSelectPreview(select.name, select.value);
        });
    }
    
    resetToDefault() {
        this.applyPreset('default');
    }
    
    exportTheme() {
        const theme = this.getCurrentTheme();
        const blob = new Blob([JSON.stringify(theme, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `theme-${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
    }
    
    async importTheme(file) {
        if (!file) return;
        
        try {
            const text = await file.text();
            const theme = JSON.parse(text);
            
            this.applyThemeData(theme);
            
            // Show success message
            if (window.accessibilityManager) {
                window.accessibilityManager.announce('Theme imported successfully', 'polite');
            }
        } catch (error) {
            console.error('Theme import error:', error);
            
            // Show error message
            if (window.accessibilityManager) {
                window.accessibilityManager.announce('Failed to import theme', 'assertive');
            }
        }
    }
    
    getCurrentTheme() {
        const theme = {
            name: 'Custom Theme',
            timestamp: Date.now(),
            colors: {},
            typography: {},
            layout: {}
        };
        
        // Collect current values
        document.querySelectorAll('input, select').forEach(input => {
            if (input.name && input.value) {
                if (input.type === 'color') {
                    theme.colors[input.name] = input.value;
                } else if (input.name.includes('font') || input.name.includes('line_height')) {
                    theme.typography[input.name] = input.value;
                } else {
                    theme.layout[input.name] = input.value;
                }
            }
        });
        
        return theme;
    }
    
    applyThemeData(theme) {
        // Apply colors
        if (theme.colors) {
            Object.entries(theme.colors).forEach(([key, value]) => {
                const input = document.querySelector(`input[name="${key}"]`);
                if (input) {
                    input.value = value;
                    this.updateColorPreview(key, value);
                }
            });
        }
        
        // Apply typography
        if (theme.typography) {
            Object.entries(theme.typography).forEach(([key, value]) => {
                const input = document.querySelector(`input[name="${key}"], select[name="${key}"]`);
                if (input) {
                    input.value = value;
                    if (input.type === 'range') {
                        this.updateRangePreview(key, value);
                    } else {
                        this.updateFontPreview(key, value);
                    }
                }
            });
        }
        
        // Apply layout
        if (theme.layout) {
            Object.entries(theme.layout).forEach(([key, value]) => {
                const input = document.querySelector(`input[name="${key}"], select[name="${key}"]`);
                if (input) {
                    input.value = value;
                    this.updateRangePreview(key, value);
                }
            });
        }
    }
}

// Initialize theme customizer when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.appearance-settings')) {
        window.themeCustomizer = new ThemeCustomizer();
    }
});

// Export for use in other scripts
window.ThemeCustomizer = ThemeCustomizer;
