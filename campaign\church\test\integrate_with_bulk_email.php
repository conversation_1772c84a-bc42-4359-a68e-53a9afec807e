<?php
// <PERSON>ript to integrate lottery number generation with bulk email system
require_once __DIR__ . '/../config.php';

try {
    echo "<h2>Integrating Unique Lottery Numbers with Bulk Email System</h2>";
    
    // Path to the bulk email file
    $bulk_email_file = __DIR__ . '/../admin/bulk_email.php';
    
    // Check if the file exists
    if (!file_exists($bulk_email_file)) {
        echo "<p>❌ Error: Could not find bulk_email.php file at: " . htmlspecialchars($bulk_email_file) . "</p>";
        exit;
    }
    
    // Read the current content
    $current_content = file_get_contents($bulk_email_file);
    
    // Check if our integration code is already present
    if (strpos($current_content, 'process_unique_lottery_numbers') !== false) {
        echo "<p>✅ Integration code is already present in the bulk email system.</p>";
    } else {
        // Prepare our integration code
        $integration_code = <<<'PHP'

// Function to generate unique lottery numbers for each recipient
function process_unique_lottery_numbers($content, $recipient) {
    // Check if this is a lottery template (contains lottery ball placeholders)
    if (strpos($content, '{BALL1}') !== false || 
        strpos($content, '{BALL2}') !== false ||
        strpos($content, '{BALL3}') !== false) {
        
        // Generate unique lottery numbers for this recipient
        $ball1 = rand(1, 20);
        $ball2 = rand(21, 40);
        $ball3 = rand(41, 60);
        $ball4 = rand(61, 80);
        $ball5 = rand(81, 99);
        $powerball = rand(1, 15);
        
        // Replace placeholders with actual numbers
        $content = str_replace(
            ['{BALL1}', '{BALL2}', '{BALL3}', '{BALL4}', '{BALL5}', '{POWERBALL}'],
            [$ball1, $ball2, $ball3, $ball4, $ball5, $powerball],
            $content
        );
        
        error_log("Unique lottery numbers generated for recipient {$recipient['email']}: $ball1, $ball2, $ball3, $ball4, $ball5, $powerball");
    }
    
    return $content;
}

PHP;

        // Find a good place to insert our code - before the send_email function is called in the process_recipient function
        $insertion_point = strpos($current_content, 'function process_recipient');
        
        if ($insertion_point !== false) {
            // Insert our code before the process_recipient function
            $new_content = substr($current_content, 0, $insertion_point) . $integration_code . substr($current_content, $insertion_point);
            
            // Create a backup of the original file
            $backup_file = $bulk_email_file . '.bak';
            if (file_put_contents($backup_file, $current_content)) {
                echo "<p>✅ Created backup of original file at: " . htmlspecialchars($backup_file) . "</p>";
            } else {
                echo "<p>❌ Warning: Could not create backup of original file.</p>";
            }
            
            // Update the file with our integration code
            if (file_put_contents($bulk_email_file, $new_content)) {
                echo "<p>✅ Successfully integrated unique lottery number generation into the bulk email system!</p>";
            } else {
                echo "<p>❌ Error: Could not update the bulk email file.</p>";
                exit;
            }
        } else {
            echo "<p>❌ Error: Could not find appropriate insertion point in the bulk_email.php file.</p>";
            exit;
        }
    }
    
    // Modify the process_recipient function to call our function
    if (strpos($current_content, '$content = process_unique_lottery_numbers($content, $recipient);') === false) {
        // Find the line where placeholders are replaced in process_recipient function
        $pattern = '/\$content\s*=\s*replaceTemplatePlaceholders\(\$template\[\'content\'\],\s*\$recipient\)/';
        
        if (preg_match($pattern, $current_content, $matches, PREG_OFFSET_CAPTURE)) {
            $match_position = $matches[0][1];
            $match_text = $matches[0][0];
            
            // Prepare the modified code
            $modified_line = $match_text . ";\n        // Process unique lottery numbers\n        \$content = process_unique_lottery_numbers(\$content, \$recipient)";
            
            // Replace the original line with our modified version
            $new_content = substr($current_content, 0, $match_position) . $modified_line . substr($current_content, $match_position + strlen($match_text));
            
            // Update the file with our modified code
            if (file_put_contents($bulk_email_file, $new_content)) {
                echo "<p>✅ Successfully added lottery number processing to the email sending process!</p>";
            } else {
                echo "<p>❌ Error: Could not update the email processing code.</p>";
                exit;
            }
        } else {
            echo "<p>❌ Error: Could not find the placeholder replacement code in process_recipient function.</p>";
        }
    } else {
        echo "<p>✅ Lottery number processing is already integrated in the email sending process.</p>";
    }
    
    echo "<h3>Success!</h3>";
    echo "<p>The bulk email system has been updated to generate unique lottery numbers for each recipient.</p>";
    echo "<p>When you send emails using the template with {BALL1}, {BALL2}, etc. placeholders, each recipient will receive different random numbers.</p>";
    
    echo "<h3>How to Use:</h3>";
    echo "<ol>";
    echo "<li>Go to Admin → Email Templates and select the 'The Big Raffle Winner (Unique Numbers)' template</li>";
    echo "<li>Send emails using the Bulk Email feature</li>";
    echo "<li>Each recipient will receive their own unique set of lottery numbers</li>";
    echo "</ol>";
    
    echo "<p><a href='../admin/bulk_email.php' style='display: inline-block; padding: 10px 20px; background-color: #8A2BE2; color: white; text-decoration: none; border-radius: 4px;'>Go to Bulk Email</a></p>";
    
} catch (Exception $e) {
    echo "<h2>Error:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
} 