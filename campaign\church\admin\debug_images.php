<?php
/**
 * Debug Profile Images
 * 
 * This script checks the actual image paths in the database and verifies file existence
 */

// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Debug Profile Images</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-12'>
            <div class='card'>
                <div class='card-header bg-info text-white'>
                    <h5 class='mb-0'><i class='bi bi-bug'></i> Profile Images Debug Information</h5>
                </div>
                <div class='card-body'>
";

try {
    // Get members with image paths
    $stmt = $conn->prepare("SELECT id, full_name, image_path FROM members WHERE image_path IS NOT NULL AND image_path != '' ORDER BY id DESC LIMIT 10");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($members) > 0) {
        echo "<h6>Members with Profile Images:</h6>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>ID</th><th>Name</th><th>Database Path</th><th>File Status</th><th>Preview</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($members as $member) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($member['id']) . "</td>";
            echo "<td>" . htmlspecialchars($member['full_name']) . "</td>";
            echo "<td><code>" . htmlspecialchars($member['image_path']) . "</code></td>";
            
            // Check different possible paths
            $possiblePaths = [
                __DIR__ . '/../' . $member['image_path'],  // From admin folder
                __DIR__ . '/../uploads/' . basename($member['image_path']), // Direct uploads
                __DIR__ . '/../../' . $member['image_path'], // From campaign root
            ];
            
            $fileExists = false;
            $workingPath = '';
            
            foreach ($possiblePaths as $path) {
                if (file_exists($path)) {
                    $fileExists = true;
                    $workingPath = $path;
                    break;
                }
            }
            
            if ($fileExists) {
                echo "<td><span class='badge bg-success'>EXISTS</span><br><small>" . htmlspecialchars($workingPath) . "</small></td>";
                
                // Create web-accessible path for preview
                $webPath = '';
                if (strpos($member['image_path'], 'uploads/') === 0) {
                    $webPath = '../' . $member['image_path'];
                } else {
                    $webPath = '../uploads/' . basename($member['image_path']);
                }
                
                echo "<td><img src='" . htmlspecialchars($webPath) . "' alt='Profile' style='width: 50px; height: 50px; object-fit: cover; border-radius: 50%;' onerror='this.src=\"../assets/img/default-profile.jpg\"'></td>";
            } else {
                echo "<td><span class='badge bg-danger'>MISSING</span></td>";
                echo "<td><img src='../assets/img/default-profile.jpg' alt='Default' style='width: 50px; height: 50px; object-fit: cover; border-radius: 50%;'></td>";
            }
            
            echo "</tr>";
        }
        
        echo "</tbody></table>";
        echo "</div>";
        
        // Show directory contents
        echo "<hr><h6>Upload Directory Contents:</h6>";
        $uploadDir = __DIR__ . '/../uploads/';
        if (is_dir($uploadDir)) {
            $files = scandir($uploadDir);
            $imageFiles = array_filter($files, function($file) {
                return !in_array($file, ['.', '..']) && preg_match('/\.(jpg|jpeg|png|gif)$/i', $file);
            });
            
            if (count($imageFiles) > 0) {
                echo "<div class='alert alert-info'>Found " . count($imageFiles) . " image files in uploads directory:</div>";
                echo "<div class='row'>";
                foreach ($imageFiles as $file) {
                    echo "<div class='col-md-2 mb-3'>";
                    echo "<div class='card'>";
                    echo "<img src='../uploads/" . htmlspecialchars($file) . "' class='card-img-top' style='height: 100px; object-fit: cover;' alt='" . htmlspecialchars($file) . "'>";
                    echo "<div class='card-body p-2'>";
                    echo "<small>" . htmlspecialchars($file) . "</small>";
                    echo "</div>";
                    echo "</div>";
                    echo "</div>";
                }
                echo "</div>";
            } else {
                echo "<div class='alert alert-warning'>No image files found in uploads directory</div>";
            }
        } else {
            echo "<div class='alert alert-danger'>Uploads directory does not exist: " . htmlspecialchars($uploadDir) . "</div>";
        }
        
    } else {
        echo "<div class='alert alert-warning'>No members with profile images found in database</div>";
    }
    
    // Show path resolution logic
    echo "<hr><h6>Path Resolution Logic Test:</h6>";
    echo "<div class='alert alert-info'>";
    echo "<strong>Current working directory:</strong> " . htmlspecialchars(__DIR__) . "<br>";
    echo "<strong>Upload directory:</strong> " . htmlspecialchars(__DIR__ . '/../uploads/') . "<br>";
    echo "<strong>Default image:</strong> " . htmlspecialchars(__DIR__ . '/../assets/img/default-profile.jpg') . "<br>";
    echo "<strong>Default image exists:</strong> " . (file_exists(__DIR__ . '/../assets/img/default-profile.jpg') ? 'YES' : 'NO') . "<br>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Database Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "
                    <div class='d-grid gap-2 mt-4'>
                        <a href='members.php' class='btn btn-primary'>Back to Members</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
