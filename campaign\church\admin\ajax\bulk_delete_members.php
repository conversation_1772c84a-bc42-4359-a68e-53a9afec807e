<?php
/**
 * Bulk Delete Members AJAX Handler
 * 
 * This script handles bulk deletion of members with comprehensive safety measures:
 * - Transaction-based deletion for data integrity
 * - Foreign key constraint handling (email_logs, email_tracking)
 * - Profile image cleanup
 * - Detailed logging and error reporting
 * - Confirmation token validation for security
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Include configuration
require_once '../../config.php';

// Set JSON response header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Validate input data
if (!$data || !isset($data['member_ids']) || !is_array($data['member_ids'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid input data']);
    exit();
}

// Validate confirmation token for security
if (!isset($data['confirmation_token']) || $data['confirmation_token'] !== 'BULK_DELETE_CONFIRMED') {
    echo json_encode(['success' => false, 'message' => 'Confirmation token required']);
    exit();
}

// Sanitize and validate member IDs
$member_ids = array_filter(array_map('intval', $data['member_ids']));

if (empty($member_ids)) {
    echo json_encode(['success' => false, 'message' => 'No valid member IDs provided']);
    exit();
}

// Limit bulk operations to prevent system overload
if (count($member_ids) > 500) {
    echo json_encode(['success' => false, 'message' => 'Bulk delete limited to 500 members at once']);
    exit();
}

try {
    $pdo->beginTransaction();
    
    // First, get member details for logging and file cleanup
    $placeholders = str_repeat('?,', count($member_ids) - 1) . '?';
    $stmt = $pdo->prepare("SELECT id, full_name, email, image_path FROM members WHERE id IN ($placeholders)");
    $stmt->execute($member_ids);
    $members_to_delete = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($members_to_delete)) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'No members found with provided IDs']);
        exit();
    }
    
    $deletion_results = [
        'total_requested' => count($member_ids),
        'members_found' => count($members_to_delete),
        'successfully_deleted' => 0,
        'failed_deletions' => [],
        'deleted_members' => [],
        'files_cleaned' => 0
    ];
    
    // Delete related records and members
    foreach ($members_to_delete as $member) {
        $member_id = $member['id'];
        
        try {
            // Delete from email_logs (if exists)
            $stmt = $pdo->prepare("DELETE FROM email_logs WHERE member_id = ?");
            $stmt->execute([$member_id]);
            
            // Delete from email_tracking (if exists)
            $stmt = $pdo->prepare("DELETE FROM email_tracking WHERE member_id = ?");
            $stmt->execute([$member_id]);
            
            // Delete the member
            $stmt = $pdo->prepare("DELETE FROM members WHERE id = ?");
            $result = $stmt->execute([$member_id]);
            
            if ($result && $stmt->rowCount() > 0) {
                $deletion_results['successfully_deleted']++;
                $deletion_results['deleted_members'][] = [
                    'id' => $member_id,
                    'name' => $member['full_name'],
                    'email' => $member['email']
                ];
                
                // Clean up profile image if exists
                if (!empty($member['image_path'])) {
                    $image_full_path = '../../' . $member['image_path'];
                    if (file_exists($image_full_path)) {
                        if (unlink($image_full_path)) {
                            $deletion_results['files_cleaned']++;
                        }
                    }
                }
                
            } else {
                $deletion_results['failed_deletions'][] = [
                    'id' => $member_id,
                    'name' => $member['full_name'],
                    'email' => $member['email'],
                    'reason' => 'Member not found or already deleted'
                ];
            }
            
        } catch (PDOException $e) {
            $deletion_results['failed_deletions'][] = [
                'id' => $member_id,
                'name' => $member['full_name'],
                'email' => $member['email'],
                'reason' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    // Log the bulk deletion activity
    $log_message = sprintf(
        "Bulk member deletion: %d/%d members successfully deleted by admin ID %d",
        $deletion_results['successfully_deleted'],
        $deletion_results['total_requested'],
        $_SESSION['admin_id']
    );
    
    // Insert activity log
    try {
        $stmt = $pdo->prepare("INSERT INTO admin_activity_logs (admin_id, action, details, ip_address, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute([
            $_SESSION['admin_id'],
            'bulk_delete_members',
            json_encode($deletion_results),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
    } catch (PDOException $e) {
        // Log insertion failed, but don't fail the main operation
        error_log("Failed to log bulk member deletion activity: " . $e->getMessage());
    }
    
    $pdo->commit();
    
    // Prepare response
    $response = [
        'success' => true,
        'message' => sprintf(
            'Bulk deletion completed: %d out of %d members successfully deleted',
            $deletion_results['successfully_deleted'],
            $deletion_results['total_requested']
        ),
        'results' => $deletion_results
    ];
    
    // Add file cleanup info
    if ($deletion_results['files_cleaned'] > 0) {
        $response['file_cleanup'] = sprintf('%d profile images cleaned up', $deletion_results['files_cleaned']);
    }
    
    // Add warnings if there were failures
    if (!empty($deletion_results['failed_deletions'])) {
        $response['warnings'] = sprintf(
            '%d members could not be deleted',
            count($deletion_results['failed_deletions'])
        );
    }
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Bulk member deletion error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Database error occurred during bulk deletion',
        'error_details' => $e->getMessage()
    ]);
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Bulk member deletion error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'An unexpected error occurred',
        'error_details' => $e->getMessage()
    ]);
}
?>
