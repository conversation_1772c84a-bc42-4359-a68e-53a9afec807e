<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;
$conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_template'])) {
        try {
            $template_name = trim($_POST['template_name']);
            $message_content = trim($_POST['message_content']);
            $is_birthday = isset($_POST['is_birthday']) ? 1 : 0;
            
            if (empty($template_name) || empty($message_content)) {
                throw new Exception("Template name and content are required");
            }

            $stmt = $conn->prepare("INSERT INTO whatsapp_templates (template_name, message_content, is_birthday) VALUES (?, ?, ?)");
            $stmt->execute([$template_name, $message_content, $is_birthday]);
            
            $_SESSION['message'] = "Template added successfully";
            
        } catch (PDOException $e) {
            $_SESSION['error'] = "Database error: " . $e->getMessage();
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }
        header("Location: whatsapp_templates.php");
        exit();
    } elseif (isset($_POST['update_template'])) {
        try {
            $template_id = $_POST['template_id'];
            $template_name = trim($_POST['template_name']);
            $message_content = trim($_POST['message_content']);
            $is_birthday = isset($_POST['is_birthday']) ? 1 : 0;
            
            if (empty($template_id) || empty($template_name) || empty($message_content)) {
                throw new Exception("All fields are required");
            }

            $stmt = $conn->prepare("UPDATE whatsapp_templates 
                                   SET template_name = ?, message_content = ?, is_birthday = ?
                                   WHERE id = ?");
            $stmt->execute([$template_name, $message_content, $is_birthday, $template_id]);
            
            if ($stmt->rowCount() > 0) {
                $_SESSION['message'] = "Template updated successfully";
            } else {
                throw new Exception("No changes made or template not found");
            }
            
        } catch (PDOException $e) {
            $_SESSION['error'] = "Database error: " . $e->getMessage();
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }
        header("Location: whatsapp_templates.php");
        exit();
    } elseif (isset($_POST['delete_template'])) {
        try {
            $template_id = $_POST['template_id'];
            
            if (empty($template_id)) {
                throw new Exception("Template ID is required");
            }

            $stmt = $conn->prepare("DELETE FROM whatsapp_templates WHERE id = ?");
            $stmt->execute([$template_id]);
            
            if ($stmt->rowCount() > 0) {
                $_SESSION['message'] = "Template deleted successfully";
            } else {
                throw new Exception("Template not found or already deleted");
            }
            
        } catch (PDOException $e) {
            $_SESSION['error'] = "Database error: " . $e->getMessage();
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
        }
        header("Location: whatsapp_templates.php");
        exit();
    } elseif (isset($_POST['update_whatsapp_settings'])) {
        // Update WhatsApp sender settings
        $sender_number = trim($_POST['sender_number']);
        $sender_name = trim($_POST['sender_name']);
        
        // First check if a setting already exists
        $stmt = $conn->prepare("SELECT id FROM whatsapp_settings WHERE id = 1");
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            // Update existing settings
            $stmt = $conn->prepare("UPDATE whatsapp_settings SET sender_number = ?, sender_name = ? WHERE id = 1");
        } else {
            // Insert new settings
            $stmt = $conn->prepare("INSERT INTO whatsapp_settings (id, sender_number, sender_name) VALUES (1, ?, ?)");
        }
        
        $result = $stmt->execute([$sender_number, $sender_name]);
        
        if ($result) {
            $_SESSION['message'] = "WhatsApp settings updated successfully.";
        } else {
            $_SESSION['error'] = "Failed to update WhatsApp settings.";
        }
        
        header("Location: whatsapp_templates.php");
        exit();
    }
}

// Get all WhatsApp templates
$templates = [];
try {
    $stmt = $conn->prepare("SELECT * FROM whatsapp_templates ORDER BY template_name");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Check if the table doesn't exist
    if (strpos($e->getMessage(), "Table 'churchdb.whatsapp_templates' doesn't exist") !== false) {
        // Create the whatsapp_templates table
        $sql = "CREATE TABLE IF NOT EXISTS whatsapp_templates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            template_name VARCHAR(100) NOT NULL,
            message_content TEXT NOT NULL,
            is_birthday TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $conn->exec($sql);
        
        // Add sample birthday template
        $sample_template = "Hello {full_name},\n\nHappy Birthday! 🎂🎉\n\nWishing you a day filled with joy and blessings on your special day.\n\nBest regards,\nFreedom Assembly Church";
        $stmt = $conn->prepare("INSERT INTO whatsapp_templates (template_name, message_content, is_birthday) VALUES ('Birthday Wishes', ?, 1)");
        $stmt->execute([$sample_template]);
        
        // Try again to get templates
        $stmt = $conn->prepare("SELECT * FROM whatsapp_templates ORDER BY template_name");
        $stmt->execute();
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        // Some other error occurred
        $_SESSION['error'] = "Database error: " . $e->getMessage();
    }
}

// Get WhatsApp settings
$whatsapp_settings = [
    'sender_number' => '',
    'sender_name' => 'Freedom Assembly Church'
];

try {
    // Check if the table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'whatsapp_settings'");
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        // Create the table
        $sql = "CREATE TABLE IF NOT EXISTS whatsapp_settings (
            id INT PRIMARY KEY DEFAULT 1,
            sender_number VARCHAR(20) NOT NULL,
            sender_name VARCHAR(100) NOT NULL
        )";
        
        $conn->exec($sql);
    } else {
        // Get existing settings
        $stmt = $conn->prepare("SELECT * FROM whatsapp_settings WHERE id = 1");
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $settings = $stmt->fetch();
            $whatsapp_settings['sender_number'] = $settings['sender_number'];
            $whatsapp_settings['sender_name'] = $settings['sender_name'];
        }
    }
} catch (PDOException $e) {
    $_SESSION['error'] = "Error getting WhatsApp settings: " . $e->getMessage();
}

// Close connection
$conn = null;

// Set page variables
$page_title = 'WhatsApp Templates';
$page_header = 'WhatsApp Templates';
$page_description = 'Manage WhatsApp message templates for sending birthday wishes.';

// Include header
include 'includes/header.php';

// Display success message if it exists in the session
if (isset($_SESSION['message']) && !empty($_SESSION['message'])) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($_SESSION['message']) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
    // Clear the message after displaying it
    unset($_SESSION['message']);
}

// Display error message if it exists in the session
if (isset($_SESSION['error']) && !empty($_SESSION['error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($_SESSION['error']) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
    // Clear the error after displaying it
    unset($_SESSION['error']);
}
?>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">WhatsApp Settings</h5>
            </div>
            <div class="card-body">
                <form action="whatsapp_templates.php" method="post">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="sender_number" class="form-label">Sender WhatsApp Number</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-phone"></i></span>
                                <input type="text" class="form-control" id="sender_number" name="sender_number" value="<?php echo htmlspecialchars($whatsapp_settings['sender_number']); ?>" required placeholder="Enter in international format (e.g., 1XXXXXXXXXX)">
                            </div>
                            <small class="text-muted">Enter in international format without + or spaces (e.g., 1XXXXXXXXXX)</small>
                        </div>
                        <div class="col-md-6">
                            <label for="sender_name" class="form-label">Sender Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-person"></i></span>
                                <input type="text" class="form-control" id="sender_name" name="sender_name" value="<?php echo htmlspecialchars($whatsapp_settings['sender_name']); ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="submit" name="update_whatsapp_settings" class="btn btn-primary">
                            <i class="bi bi-save"></i> Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">WhatsApp Templates</h5>
                <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addTemplateModal">
                    <i class="bi bi-plus-circle"></i> Add New Template
                </button>
            </div>
            <div class="card-body">
                <?php if (count($templates) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Template Name</th>
                                <th>Message Preview</th>
                                <th>Type</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($templates as $template): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($template['template_name']); ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-info preview-btn" data-content="<?php echo htmlspecialchars($template['message_content']); ?>">
                                        <i class="bi bi-eye"></i> Preview
                                    </button>
                                </td>
                                <td>
                                    <?php if ($template['is_birthday']): ?>
                                    <span class="badge bg-warning text-dark">Birthday</span>
                                    <?php else: ?>
                                    <span class="badge bg-secondary">General</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-primary btn-sm edit-btn" 
                                            data-id="<?php echo $template['id']; ?>"
                                            data-name="<?php echo htmlspecialchars($template['template_name']); ?>"
                                            data-content="<?php echo htmlspecialchars($template['message_content']); ?>"
                                            data-birthday="<?php echo $template['is_birthday']; ?>"
                                            data-bs-toggle="modal" data-bs-target="#editTemplateModal">
                                        <i class="bi bi-pencil"></i> Edit
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm delete-btn"
                                            data-id="<?php echo $template['id']; ?>"
                                            data-name="<?php echo htmlspecialchars($template['template_name']); ?>"
                                            data-bs-toggle="modal" data-bs-target="#deleteTemplateModal">
                                        <i class="bi bi-trash"></i> Delete
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <p class="text-muted">No WhatsApp templates found. Click "Add New Template" to create one.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">WhatsApp Integration Guide</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>How to Use WhatsApp Click to Chat</h6>
                <p>WhatsApp's click to chat feature allows you to begin a chat with someone without having their phone number saved in your phone's address book.</p>
                <p>The format is: <code>https://wa.me/&lt;number&gt;</code> where the number is in international format without + or spaces.</p>
                <ul>
                    <li><strong>Correct format:</strong> https://wa.me/1XXXXXXXXXX</li>
                    <li><strong>Incorrect format:</strong> https://wa.me/+001-(XXX)XXXXXXX</li>
                </ul>
                <p>For pre-filled messages, use: <code>https://wa.me/whatsappphonenumber?text=urlencodedtext</code></p>
            </div>
            <div class="col-md-6">
                <h6>Available Placeholders</h6>
                <p>You can use these placeholders in your WhatsApp templates:</p>
                <ul>
                    <li><code>{full_name}</code> - Member's full name</li>
                    <li><code>{first_name}</code> - Member's first name</li>
                    <li><code>{birth_date}</code> - Member's birth date</li>
                    <li><code>{church_name}</code> - Church name</li>
                    <li><code>{sender_name}</code> - Sender's name from settings</li>
                </ul>
                <h6>Steps to Send Messages</h6>
                <ol>
                    <li>Set up your sender WhatsApp number in Settings</li>
                    <li>Create templates with appropriate placeholders</li>
                    <li>Go to Birthday page and click "Send WhatsApp" for a member</li>
                    <li>Select a template and customize if needed</li>
                    <li>Click Send to open WhatsApp Web with the pre-filled message</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Add Template Modal -->
<div class="modal fade" id="addTemplateModal" tabindex="-1" aria-labelledby="addTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addTemplateModalLabel">Add WhatsApp Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="whatsapp_templates.php" method="post">
                    <div class="mb-3">
                        <label for="template_name" class="form-label">Template Name</label>
                        <input type="text" class="form-control" id="template_name" name="template_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="message_content" class="form-label">Message Content</label>
                        <textarea class="form-control" id="message_content" name="message_content" rows="10" required></textarea>
                        <small class="text-muted">You can use placeholders like {full_name}, {first_name}, etc.</small>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_birthday" name="is_birthday" value="1">
                        <label class="form-check-label" for="is_birthday">Is Birthday Template</label>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="add_template" class="btn btn-success">Add Template</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Edit Template Modal -->
<div class="modal fade" id="editTemplateModal" tabindex="-1" aria-labelledby="editTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editTemplateModalLabel">Edit WhatsApp Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="whatsapp_templates.php" method="post">
                    <input type="hidden" id="edit_template_id" name="template_id">
                    
                    <div class="mb-3">
                        <label for="edit_template_name" class="form-label">Template Name</label>
                        <input type="text" class="form-control" id="edit_template_name" name="template_name" required>
                        <div class="invalid-feedback">Please provide a template name</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_message_content" class="form-label">Message Content</label>
                        <textarea class="form-control" id="edit_message_content" name="message_content" rows="10" required></textarea>
                        <div class="invalid-feedback">Please provide message content</div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_is_birthday" name="is_birthday" value="1">
                        <label class="form-check-label" for="edit_is_birthday">Is Birthday Template</label>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="update_template" class="btn btn-primary">Update Template</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Template Modal -->
<div class="modal fade" id="deleteTemplateModal" tabindex="-1" aria-labelledby="deleteTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteTemplateModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the template "<span id="delete_template_name"></span>"?</p>
                <p class="text-danger">This action cannot be undone.</p>
                <form action="whatsapp_templates.php" method="post">
                    <input type="hidden" id="delete_template_id" name="template_id">
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="delete_template" class="btn btn-danger">Delete Template</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">Message Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="whatsapp-preview">
                    <div class="preview-content" id="preview_content"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Recent WhatsApp Messages</h5>
    </div>
    <div class="card-body">
        <?php
        // Temporary re-open connection for logs section
        require_once '../config.php';
        $conn = $pdo;
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        try {
            $stmt = $conn->prepare("SHOW TABLES LIKE 'whatsapp_logs'");
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $logs_exist = true;
                
                // Get recent logs (join with members and templates)
                $query = "SELECT l.*, m.full_name, t.template_name 
                          FROM whatsapp_logs l
                          LEFT JOIN members m ON l.member_id = m.id
                          LEFT JOIN whatsapp_templates t ON l.template_id = t.id
                          ORDER BY l.sent_at DESC
                          LIMIT 20";
                
                $stmt = $conn->prepare($query);
                $stmt->execute();
                $recent_logs = $stmt->fetchAll();
            }
        } catch (PDOException $e) {
            echo '<div class="alert alert-warning">Error loading WhatsApp logs: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        
        if ($logs_exist && count($recent_logs) > 0):
        ?>
        <div class="table-responsive">
            <table class="table table-sm table-hover">
                <thead>
                    <tr>
                        <th>Date/Time</th>
                        <th>Member</th>
                        <th>Template</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_logs as $log): ?>
                    <tr>
                        <td><?php echo date('M d, Y g:i A', strtotime($log['sent_at'])); ?></td>
                        <td><?php echo htmlspecialchars($log['full_name'] ?? 'Unknown Member'); ?></td>
                        <td><?php echo htmlspecialchars($log['template_name'] ?? 'Unknown Template'); ?></td>
                        <td>
                            <span class="badge bg-<?php echo $log['status'] === 'initiated' ? 'info' : 'success'; ?>">
                                <?php echo ucfirst($log['status']); ?>
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php elseif ($logs_exist): ?>
        <p class="text-muted">No WhatsApp messages have been sent yet.</p>
        <?php else: ?>
        <p class="text-muted">WhatsApp logs will appear here after sending messages.</p>
        <?php endif; ?>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle preview button clicks
        const previewButtons = document.querySelectorAll('.preview-btn');
        previewButtons.forEach(button => {
            button.addEventListener('click', function() {
                const content = this.dataset.content;
                document.getElementById('preview_content').innerText = content;
                new bootstrap.Modal(document.getElementById('previewModal')).show();
            });
        });

        // Handle edit button clicks - fixed data binding
        const editButtons = document.querySelectorAll('.edit-btn');
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.dataset.id;
                const name = this.dataset.name;
                const content = this.dataset.content;
                const isBirthday = this.dataset.birthday === '1';

                document.getElementById('edit_template_id').value = id;
                document.getElementById('edit_template_name').value = name;
                document.getElementById('edit_message_content').value = content;
                document.getElementById('edit_is_birthday').checked = isBirthday;
            });
        });

        // Handle delete button clicks - fixed data binding
        const deleteButtons = document.querySelectorAll('.delete-btn');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                document.getElementById('delete_template_id').value = this.dataset.id;
                document.getElementById('delete_template_name').textContent = this.dataset.name;
            });
        });

        // Handle form submissions - add client-side validation
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const inputs = this.querySelectorAll('input, textarea');
                let isValid = true;

                inputs.forEach(input => {
                    if (input.required && !input.value.trim()) {
                        isValid = false;
                        input.classList.add('is-invalid');
                    } else {
                        input.classList.remove('is-invalid');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    alert('Please fill in all required fields');
                }
            });
        });
    });
</script>

<style>
    .whatsapp-preview {
        background-color: #e5ddd5;
        border-radius: 8px;
        padding: 20px;
        max-height: 400px;
        overflow-y: auto;
    }
    
    .preview-content {
        background-color: #fff;
        border-radius: 7.5px;
        padding: 10px 15px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        position: relative;
        max-width: 80%;
        margin-left: auto;
    }
    
    .preview-content:after {
        content: "";
        position: absolute;
        top: 0;
        right: -10px;
        width: 0;
        height: 0;
        border-top: 10px solid #fff;
        border-right: 10px solid transparent;
    }

    .is-invalid {
        border-color: #dc3545;
        padding-right: calc(1.5em + 0.75rem);
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    .is-invalid:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.25rem rgba(220,53,69,.25);
    }

    .invalid-feedback {
        display: none;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875em;
        color: #dc3545;
    }

    .is-invalid ~ .invalid-feedback {
        display: block;
    }
</style>

<?php
// Include footer
include 'includes/footer.php';

// Now close connection AFTER footer
$conn = null;
?> 