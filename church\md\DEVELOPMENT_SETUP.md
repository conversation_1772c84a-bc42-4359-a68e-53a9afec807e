# Development Environment Setup Guide

This guide provides step-by-step instructions for setting up a development environment for the Church Email Management System.

## Prerequisites

- PHP 7.4 or later
- MySQL 5.7 or later
- Web server (Apache, Nginx, etc.)
- Composer
- Git

## Installation Steps

### 1. <PERSON>lone the Repository

```bash
git clone <repository-url> church
cd church
```

### 2. Install Dependencies

Install PHP dependencies using Composer:

```bash
composer install
```

### 3. Configure the Environment

1. Copy the sample environment file:
   ```bash
   cp environment.sample.php environment.php
   ```

2. Edit the environment.php file to match your local setup:
   ```php
   // Development environment (default)
   define('SITE_URL', 'http://localhost/church');
   
   // Other constants will be set based on SITE_URL
   ```

### 4. Set Up the Database

1. Create a MySQL database:
   ```sql
   CREATE DATABASE churchdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. Import the database schema:
   ```bash
   mysql -u root -p churchdb < sql/churchdb.sql
   ```

3. Configure database credentials in environment.php:
   ```php
   // Database Configuration
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'churchdb');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```

### 5. Set Up File Permissions

Ensure that the web server has write permissions to the following directories:
```bash
chmod -R 755 uploads/
chmod -R 755 logs/
```

### 6. Download Local Dependencies (Optional)

To use local copies of external libraries instead of CDN-hosted versions:

```bash
php tools/dependency_manager.php
```

Then, set `USE_LOCAL_ASSETS` to `true` in `environment.php`.

### 7. Access the Application

1. Configure your web server to point to the project directory.
2. Access the admin panel at: http://localhost/church/admin/
3. Login with default credentials:
   - Username: admin
   - Password: admin123

## Development Workflow

### URL Management

When working with URLs, always use the environment constants:

```php
// Instead of:
$url = 'https://example.com/church/page.php';

// Use:
$url = SITE_URL . '/page.php';
```

### Email Templates

When creating or editing email templates:

1. Use placeholders for URLs:
   ```html
   <a href="{{SITE_URL}}/track.php?id=123">Click here</a>
   ```

2. Use the template preview feature to test templates before sending

### Database Changes

When making database schema changes:

1. Create a migration script in the `admin/db_migrations/` directory
2. Document the changes in the script header
3. Test the migration in your local environment before committing

### Testing Emails

To test email functionality without sending actual emails:

1. Configure environment.php to use a test email service or log emails:
   ```php
   define('EMAIL_LOG_ONLY', true); // Log emails instead of sending
   ```

2. Check the `logs/email_log.txt` file for email content

## Utilities

### Tools Directory

The `tools/` directory contains utilities to help with development:

- `url_audit.php` - Scans for hardcoded URLs
- `url_updater.php` - Updates hardcoded URLs to use constants
- `db_url_updater.php` - Updates URLs in database content
- `dependency_manager.php` - Downloads local copies of CDN libraries

See `tools/README.md` for detailed usage instructions.

### PhpMailer Configuration

For email delivery in development:

1. Configure an SMTP server in your environment.php:
   ```php
   define('SMTP_HOST', 'localhost');
   define('SMTP_PORT', 1025);
   define('SMTP_USERNAME', '');
   define('SMTP_PASSWORD', '');
   define('SMTP_SECURE', '');
   ```

2. Or use a tool like Mailhog for local email testing

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Verify MySQL is running
   - Check that your database credentials are correct
   - Ensure the database exists with the correct collation

2. **File Permission Issues**
   - Make sure upload and log directories are writable
   - Check that your web server user has appropriate permissions

3. **Email Sending Issues**
   - Verify SMTP settings
   - Check email logs for error messages
   - Consider using a local mail testing tool like Mailhog

4. **Asset Loading Issues**
   - If using local assets, ensure they've been downloaded 
   - Check browser console for 404 errors on assets
   - Verify that paths in references.php match actual file locations

### Getting Help

If you encounter issues not covered in this guide:

1. Check the issue tracker on GitHub
2. Review the logs in `logs/` directory
3. Contact the development team for assistance

## Code Standards

When contributing to the project, please follow these guidelines:

1. Use PSR-4 autoloading standards
2. Follow PSR-12 coding style
3. Document your code with PHPDoc comments
4. Use meaningful variable and function names
5. Keep functions small and focused
6. Write unit tests for new functionality
7. Always use environment constants for URLs and paths

## Release Process

### Creating a New Release

1. Update the version number in `version.php`
2. Update the CHANGELOG.md with a summary of changes
3. Tag the release in Git:
   ```bash
   git tag -a v1.0.0 -m "Version 1.0.0"
   git push origin v1.0.0
   ```

### Deploying to Staging

1. Push changes to the staging branch
2. Run database migrations if applicable
3. Test all functionality in the staging environment
4. Verify email templates work correctly

### Deploying to Production

1. Create a backup of the production database
2. Push changes to the main branch
3. Run database migrations if applicable
4. Verify the application works correctly in production 