<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';

// Handle donation status updates
if (isset($_POST['update_status']) && isset($_POST['donation_id']) && isset($_POST['status'])) {
    $donation_id = filter_var($_POST['donation_id'], FILTER_VALIDATE_INT);
    $status = filter_var($_POST['status'], FILTER_SANITIZE_STRING);
    
    if ($donation_id && in_array($status, ['pending', 'completed', 'failed', 'refunded'])) {
        try {
            $stmt = $conn->prepare("UPDATE donations SET payment_status = ? WHERE id = ?");
            $stmt->execute([$status, $donation_id]);
            
            $stmt = $conn->prepare("UPDATE payment_transactions SET payment_status = ? WHERE donation_id = ?");
            $stmt->execute([$status, $donation_id]);
            
            $message = "Donation status updated successfully.";
        } catch (PDOException $e) {
            $error = "Error updating donation status: " . $e->getMessage();
            error_log("Error updating donation status: " . $e->getMessage());
        }
    } else {
        $error = "Invalid donation ID or status.";
    }
}

// Check if donations table exists
try {
    $stmt = $conn->prepare("SHOW TABLES LIKE 'donations'");
    $stmt->execute();
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        $error = "The donations table does not exist. Please run the <a href='fix_payment_tables.php'>Fix Payment Tables</a> script first.";
        $donations = [];
        $total_pages = 0;
    } else {
        // Pagination
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = 10;
        $offset = ($page - 1) * $limit;

        // Filtering
        $status_filter = isset($_GET['status']) ? $_GET['status'] : '';
        $type_filter = isset($_GET['type']) ? $_GET['type'] : '';
        $search = isset($_GET['search']) ? $_GET['search'] : '';

        // Build query
        $query = "SELECT d.*, m.full_name as recipient_name 
                FROM donations d 
                LEFT JOIN members m ON d.recipient_id = m.id";

        $count_query = "SELECT COUNT(*) FROM donations d LEFT JOIN members m ON d.recipient_id = m.id";

        $where_clauses = [];
        $params = [];

        if ($status_filter && in_array($status_filter, ['pending', 'completed', 'failed', 'refunded'])) {
            $where_clauses[] = "d.payment_status = ?";
            $params[] = $status_filter;
        }

        if ($type_filter && in_array($type_filter, ['general', 'birthday_gift'])) {
            $where_clauses[] = "d.donation_type = ?";
            $params[] = $type_filter;
        }

        if ($search) {
            $where_clauses[] = "(d.donor_name LIKE ? OR d.donor_email LIKE ? OR m.full_name LIKE ?)";
            $search_param = "%$search%";
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
        }

        if (!empty($where_clauses)) {
            $query .= " WHERE " . implode(" AND ", $where_clauses);
            $count_query .= " WHERE " . implode(" AND ", $where_clauses);
        }

        $query .= " ORDER BY d.created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;

        // Get donations
        try {
            $stmt = $conn->prepare($query);
            $stmt->execute($params);
            $donations = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Get total count for pagination
            $count_stmt = $conn->prepare($count_query);
            $count_stmt->execute(array_slice($params, 0, -2)); // Remove limit and offset
            $total_donations = $count_stmt->fetchColumn();
            
            $total_pages = ceil($total_donations / $limit);
        } catch (PDOException $e) {
            $error = "Error fetching donations: " . $e->getMessage();
            error_log("Error fetching donations: " . $e->getMessage());
            $donations = [];
            $total_pages = 0;
        }
    }
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
    error_log("Database error: " . $e->getMessage());
    $donations = [];
    $total_pages = 0;
}

// Currency options
$currencies = [
    'USD' => ['name' => 'US Dollar', 'symbol' => '$'],
    'EUR' => ['name' => 'Euro', 'symbol' => '€'],
    'GBP' => ['name' => 'British Pound', 'symbol' => '£'],
    'ZAR' => ['name' => 'South African Rand', 'symbol' => 'R'],
    'NGN' => ['name' => 'Nigerian Naira', 'symbol' => '₦'],
    'KES' => ['name' => 'Kenyan Shilling', 'symbol' => 'KSh'],
    'UGX' => ['name' => 'Ugandan Shilling', 'symbol' => 'USh'],
    'GHS' => ['name' => 'Ghanaian Cedi', 'symbol' => 'GH₵']
];

// Set page title and header
$page_title = "Manage Donations";
$page_header = "Manage Donations";
$page_description = "View and manage all donations made through the website.";

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Manage Donations</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="payment_integration.php" class="btn btn-sm btn-outline-secondary me-2">
                        <i class="bi bi-gear"></i> Payment Settings
                    </a>
                    <a href="<?php echo SITE_URL; ?>/donate.php" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-box-arrow-up-right"></i> View Donation Page
                    </a>
                </div>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show"><?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show"><?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" action="" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Payment Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>Failed</option>
                                <option value="refunded" <?php echo $status_filter === 'refunded' ? 'selected' : ''; ?>>Refunded</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">Donation Type</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">All Types</option>
                                <option value="general" <?php echo $type_filter === 'general' ? 'selected' : ''; ?>>General</option>
                                <option value="birthday_gift" <?php echo $type_filter === 'birthday_gift' ? 'selected' : ''; ?>>Birthday Gift</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" placeholder="Donor name or email" value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">Filter</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Donations Table -->
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Donor</th>
                            <th>Amount</th>
                            <th>Type</th>
                            <th>Method</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($donations)): ?>
                            <tr>
                                <td colspan="8" class="text-center">No donations found.</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($donations as $donation): ?>
                                <?php 
                                $currency_symbol = $currencies[$donation['currency']]['symbol'] ?? '';
                                $status_class = '';
                                switch ($donation['payment_status']) {
                                    case 'completed':
                                        $status_class = 'bg-success';
                                        break;
                                    case 'pending':
                                        $status_class = 'bg-warning';
                                        break;
                                    case 'failed':
                                        $status_class = 'bg-danger';
                                        break;
                                    case 'refunded':
                                        $status_class = 'bg-info';
                                        break;
                                }
                                ?>
                                <tr>
                                    <td><?php echo $donation['id']; ?></td>
                                    <td>
                                        <div><?php echo htmlspecialchars($donation['donor_name']); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($donation['donor_email']); ?></small>
                                    </td>
                                    <td><?php echo $currency_symbol . number_format($donation['amount'], 2); ?></td>
                                    <td>
                                        <?php if ($donation['donation_type'] === 'birthday_gift'): ?>
                                            <span class="badge bg-primary">Birthday Gift</span>
                                            <?php if ($donation['recipient_name']): ?>
                                                <div><small>For: <?php echo htmlspecialchars($donation['recipient_name']); ?></small></div>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">General</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo ucfirst($donation['payment_method']); ?></td>
                                    <td><span class="badge <?php echo $status_class; ?>"><?php echo ucfirst($donation['payment_status']); ?></span></td>
                                    <td><?php echo date('M j, Y', strtotime($donation['created_at'])); ?></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton<?php echo $donation['id']; ?>" data-bs-toggle="dropdown" aria-expanded="false">
                                                Actions
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton<?php echo $donation['id']; ?>">
                                                <li><a class="dropdown-item" href="view_donation.php?id=<?php echo $donation['id']; ?>">View Details</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><h6 class="dropdown-header">Update Status</h6></li>
                                                <?php foreach (['pending', 'completed', 'failed', 'refunded'] as $status): ?>
                                                    <?php if ($status !== $donation['payment_status']): ?>
                                                        <li>
                                                            <form method="post" action="" class="status-form">
                                                                <input type="hidden" name="update_status" value="1">
                                                                <input type="hidden" name="donation_id" value="<?php echo $donation['id']; ?>">
                                                                <input type="hidden" name="status" value="<?php echo $status; ?>">
                                                                <button type="submit" class="dropdown-item"><?php echo ucfirst($status); ?></button>
                                                            </form>
                                                        </li>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Donation pagination">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&search=<?php echo urlencode($search); ?>" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&search=<?php echo urlencode($search); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&search=<?php echo urlencode($search); ?>" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
            
            <!-- Summary -->
            <div class="card mt-4">
                <div class="card-body">
                    <h5 class="card-title">Donation Summary</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="card-title">Total Donations</h6>
                                    <p class="card-text fs-4"><?php echo $total_donations; ?></p>
                                </div>
                            </div>
                        </div>
                        <?php
                        // Get summary stats
                        try {
                            // Total amount
                            $stmt = $conn->prepare("SELECT SUM(amount) as total, currency FROM donations WHERE payment_status = 'completed' GROUP BY currency");
                            $stmt->execute();
                            $totals_by_currency = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            
                            // Count by status
                            $stmt = $conn->prepare("SELECT payment_status, COUNT(*) as count FROM donations GROUP BY payment_status");
                            $stmt->execute();
                            $counts_by_status = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
                        } catch (PDOException $e) {
                            error_log("Error fetching donation summary: " . $e->getMessage());
                            $totals_by_currency = [];
                            $counts_by_status = [];
                        }
                        ?>
                        
                        <div class="col-md-3">
                            <div class="card bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="card-title">Total Amount</h6>
                                    <?php foreach ($totals_by_currency as $total): ?>
                                        <p class="card-text fs-4">
                                            <?php 
                                            $symbol = $currencies[$total['currency']]['symbol'] ?? '';
                                            echo $symbol . number_format($total['total'], 2) . ' ' . $total['currency']; 
                                            ?>
                                        </p>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="card-title">Status Breakdown</h6>
                                    <div class="d-flex justify-content-between">
                                        <span class="badge bg-success p-2 fs-6">Completed: <?php echo $counts_by_status['completed'] ?? 0; ?></span>
                                        <span class="badge bg-warning p-2 fs-6">Pending: <?php echo $counts_by_status['pending'] ?? 0; ?></span>
                                        <span class="badge bg-danger p-2 fs-6">Failed: <?php echo $counts_by_status['failed'] ?? 0; ?></span>
                                        <span class="badge bg-info p-2 fs-6">Refunded: <?php echo $counts_by_status['refunded'] ?? 0; ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>