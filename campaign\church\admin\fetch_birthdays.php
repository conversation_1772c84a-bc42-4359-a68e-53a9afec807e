<?php
// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

/**
 * Fetch all birthdays from the database
 * @return array Array of birthdays
 */
function getAllBirthdays() {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT id, full_name, birth_date, email FROM members WHERE birth_date IS NOT NULL");
        $stmt->execute();
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error fetching birthdays: " . $e->getMessage());
        return [];
    }
}

/**
 * Fetch birthdays for a specific month
 * @param int $month Month (1-12)
 * @return array Array of birthdays for the month
 */
function getBirthdaysForMonth($month) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT id, full_name, birth_date, email FROM members WHERE MONTH(birth_date) = ? AND birth_date IS NOT NULL");
        $stmt->execute([$month]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error fetching birthdays for month $month: " . $e->getMessage());
        return [];
    }
}

/**
 * Fetch birthdays for today
 * @return array Array of birthdays for today
 */
function getBirthdaysForToday() {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT id, full_name, birth_date, email FROM members WHERE DAY(birth_date) = DAY(CURDATE()) AND MONTH(birth_date) = MONTH(CURDATE()) AND birth_date IS NOT NULL");
        $stmt->execute();
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error fetching birthdays for today: " . $e->getMessage());
        return [];
    }
}

// API endpoint to get birthdays
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    switch ($_GET['action']) {
        case 'all':
            echo json_encode(getAllBirthdays());
            break;
        case 'month':
            $month = isset($_GET['month']) ? intval($_GET['month']) : date('n');
            echo json_encode(getBirthdaysForMonth($month));
            break;
        case 'today':
            echo json_encode(getBirthdaysForToday());
            break;
        default:
            echo json_encode(['error' => 'Invalid action']);
    }
    exit;
}

// If called directly without action, return all birthdays
if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    header('Content-Type: application/json');
    echo json_encode(getAllBirthdays());
} 