/**
 * Appearance Customizer JavaScript
 * Handles real-time preview and color management
 */

document.addEventListener('DOMContentLoaded', function() {
    // Color presets
    const colorPresets = {
        default: {
            primary_color: '#007bff',
            secondary_color: '#6c757d',
            success_color: '#28a745',
            danger_color: '#dc3545',
            warning_color: '#ffc107',
            info_color: '#17a2b8',
            background_color: '#ffffff',
            text_color: '#212529',
            link_color: '#007bff'
        },
        blue: {
            primary_color: '#0066cc',
            secondary_color: '#5a6c7d',
            success_color: '#28a745',
            danger_color: '#dc3545',
            warning_color: '#ffc107',
            info_color: '#17a2b8',
            background_color: '#f8f9fa',
            text_color: '#2c3e50',
            link_color: '#0066cc'
        },
        green: {
            primary_color: '#28a745',
            secondary_color: '#6c757d',
            success_color: '#20c997',
            danger_color: '#dc3545',
            warning_color: '#ffc107',
            info_color: '#17a2b8',
            background_color: '#f8f9fa',
            text_color: '#155724',
            link_color: '#28a745'
        },
        purple: {
            primary_color: '#6f42c1',
            secondary_color: '#6c757d',
            success_color: '#28a745',
            danger_color: '#dc3545',
            warning_color: '#ffc107',
            info_color: '#17a2b8',
            background_color: '#f8f9fa',
            text_color: '#4a154b',
            link_color: '#6f42c1'
        },
        orange: {
            primary_color: '#fd7e14',
            secondary_color: '#6c757d',
            success_color: '#28a745',
            danger_color: '#dc3545',
            warning_color: '#ffc107',
            info_color: '#17a2b8',
            background_color: '#fff5f0',
            text_color: '#8b4513',
            link_color: '#fd7e14'
        }
    };
    
    // Initialize color inputs
    initializeColorInputs();
    
    // Initialize preview functionality
    initializePreview();
    
    // Initialize preset buttons
    initializePresets();
    
    // Initialize typography preview
    initializeTypographyPreview();
    
    function initializeColorInputs() {
        const colorInputs = document.querySelectorAll('input[type="color"]');
        
        colorInputs.forEach(input => {
            const textInput = input.parentNode.querySelector('input[type="text"]');
            
            // Update text input when color changes
            input.addEventListener('input', function() {
                textInput.value = this.value;
                updatePreview();
            });
            
            // Update color input when text changes
            textInput.addEventListener('input', function() {
                if (isValidHexColor(this.value)) {
                    input.value = this.value;
                    updatePreview();
                }
            });
            
            // Make text input editable
            textInput.removeAttribute('readonly');
        });
    }
    
    function initializePreview() {
        const previewBtn = document.getElementById('preview-changes');
        if (previewBtn) {
            previewBtn.addEventListener('click', function() {
                togglePreviewMode();
            });
        }
        
        // Auto-preview on input changes
        const form = document.getElementById('appearance-form');
        if (form) {
            form.addEventListener('input', debounce(updatePreview, 300));
        }
    }
    
    function initializePresets() {
        const presetButtons = document.querySelectorAll('.color-preset');
        
        presetButtons.forEach(button => {
            button.addEventListener('click', function() {
                const preset = this.dataset.preset;
                if (colorPresets[preset]) {
                    applyColorPreset(colorPresets[preset]);
                    
                    // Update active state
                    presetButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });
    }
    
    function initializeTypographyPreview() {
        const fontSelect = document.getElementById('primary_font');
        const fontSizeInput = document.getElementById('font_size_base');
        const lineHeightInput = document.getElementById('line_height_base');
        
        [fontSelect, fontSizeInput, lineHeightInput].forEach(input => {
            if (input) {
                input.addEventListener('change', updateTypographyPreview);
            }
        });
        
        // Initial preview update
        updateTypographyPreview();
    }
    
    function applyColorPreset(preset) {
        Object.keys(preset).forEach(key => {
            const colorInput = document.getElementById(key);
            const textInput = colorInput ? colorInput.parentNode.querySelector('input[type="text"]') : null;
            
            if (colorInput && textInput) {
                colorInput.value = preset[key];
                textInput.value = preset[key];
            }
        });
        
        updatePreview();
    }
    
    function updatePreview() {
        const root = document.documentElement;
        
        // Update CSS custom properties
        const colorMappings = {
            'primary_color': '--bs-primary',
            'secondary_color': '--bs-secondary',
            'success_color': '--bs-success',
            'danger_color': '--bs-danger',
            'warning_color': '--bs-warning',
            'info_color': '--bs-info',
            'background_color': '--bs-body-bg',
            'text_color': '--bs-body-color',
            'link_color': '--bs-link-color'
        };
        
        Object.keys(colorMappings).forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                root.style.setProperty(colorMappings[inputId], input.value);
            }
        });
        
        // Update typography
        updateTypographyPreview();
    }
    
    function updateTypographyPreview() {
        const preview = document.getElementById('typography-preview');
        const fontSelect = document.getElementById('primary_font');
        const fontSizeInput = document.getElementById('font_size_base');
        const lineHeightInput = document.getElementById('line_height_base');
        const fontWeightNormal = document.getElementById('font_weight_normal');
        const fontWeightBold = document.getElementById('font_weight_bold');
        
        if (preview) {
            const font = fontSelect ? fontSelect.value : 'Inter';
            const fontSize = fontSizeInput ? fontSizeInput.value + 'px' : '16px';
            const lineHeight = lineHeightInput ? lineHeightInput.value : '1.5';
            const weightNormal = fontWeightNormal ? fontWeightNormal.value : '400';
            const weightBold = fontWeightBold ? fontWeightBold.value : '600';
            
            preview.style.fontFamily = `'${font}', system-ui, -apple-system, sans-serif`;
            preview.style.fontSize = fontSize;
            preview.style.lineHeight = lineHeight;
            preview.style.fontWeight = weightNormal;
            
            // Update bold elements
            const boldElements = preview.querySelectorAll('strong, b, h1, h2, h3, h4, h5, h6');
            boldElements.forEach(el => {
                el.style.fontWeight = weightBold;
            });
            
            // Update root CSS variables
            const root = document.documentElement;
            root.style.setProperty('--bs-font-sans-serif', `'${font}', system-ui, -apple-system, sans-serif`);
            root.style.setProperty('--bs-body-font-size', fontSize);
            root.style.setProperty('--bs-body-line-height', lineHeight);
        }
    }
    
    function togglePreviewMode() {
        const body = document.body;
        const previewBtn = document.getElementById('preview-changes');
        
        if (body.classList.contains('preview-mode')) {
            body.classList.remove('preview-mode');
            previewBtn.innerHTML = '<i class="bi bi-eye"></i> Preview Changes';
            previewBtn.classList.remove('btn-warning');
            previewBtn.classList.add('btn-outline-secondary');
        } else {
            body.classList.add('preview-mode');
            previewBtn.innerHTML = '<i class="bi bi-eye-slash"></i> Exit Preview';
            previewBtn.classList.remove('btn-outline-secondary');
            previewBtn.classList.add('btn-warning');
            updatePreview();
        }
    }
    
    // Reset to defaults functionality
    const resetBtn = document.getElementById('reset-defaults');
    if (resetBtn) {
        resetBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to reset all appearance settings to defaults? This cannot be undone.')) {
                applyColorPreset(colorPresets.default);
                
                // Reset typography
                document.getElementById('primary_font').value = 'Inter';
                document.getElementById('font_size_base').value = '16';
                document.getElementById('line_height_base').value = '1.5';
                document.getElementById('font_weight_normal').value = '400';
                document.getElementById('font_weight_bold').value = '600';
                
                // Reset layout settings
                const layoutInputs = [
                    'sidebar_style', 'navbar_style', 'card_style', 
                    'border_radius', 'container_max_width', 'sidebar_width'
                ];
                
                layoutInputs.forEach(inputId => {
                    const input = document.getElementById(inputId);
                    if (input) {
                        if (input.type === 'select-one') {
                            input.value = 'default';
                        } else if (inputId === 'border_radius') {
                            input.value = '0.375';
                        } else if (inputId === 'container_max_width') {
                            input.value = '1200';
                        } else if (inputId === 'sidebar_width') {
                            input.value = '250';
                        }
                    }
                });
                
                updatePreview();
            }
        });
    }
    
    // Utility functions
    function isValidHexColor(hex) {
        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);
    }
    
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Color accessibility checker
    function checkColorContrast(color1, color2) {
        // Convert hex to RGB
        const rgb1 = hexToRgb(color1);
        const rgb2 = hexToRgb(color2);
        
        if (!rgb1 || !rgb2) return null;
        
        // Calculate relative luminance
        const l1 = getRelativeLuminance(rgb1);
        const l2 = getRelativeLuminance(rgb2);
        
        // Calculate contrast ratio
        const lighter = Math.max(l1, l2);
        const darker = Math.min(l1, l2);
        const contrast = (lighter + 0.05) / (darker + 0.05);
        
        return contrast;
    }
    
    function hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }
    
    function getRelativeLuminance(rgb) {
        const rsRGB = rgb.r / 255;
        const gsRGB = rgb.g / 255;
        const bsRGB = rgb.b / 255;
        
        const r = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
        const g = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
        const b = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);
        
        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    }
    
    // Add contrast warnings
    function addContrastWarnings() {
        const bgColor = document.getElementById('background_color').value;
        const textColor = document.getElementById('text_color').value;
        const primaryColor = document.getElementById('primary_color').value;
        
        const bgTextContrast = checkColorContrast(bgColor, textColor);
        const primaryBgContrast = checkColorContrast(primaryColor, bgColor);
        
        // Remove existing warnings
        document.querySelectorAll('.contrast-warning').forEach(el => el.remove());
        
        // Add warnings if contrast is too low
        if (bgTextContrast && bgTextContrast < 4.5) {
            addWarning('background_color', 'Low contrast with text color (WCAG AA requires 4.5:1)');
        }
        
        if (primaryBgContrast && primaryBgContrast < 3) {
            addWarning('primary_color', 'Low contrast with background color');
        }
    }
    
    function addWarning(inputId, message) {
        const input = document.getElementById(inputId);
        if (input) {
            const warning = document.createElement('div');
            warning.className = 'contrast-warning text-warning small mt-1';
            warning.innerHTML = `<i class="bi bi-exclamation-triangle"></i> ${message}`;
            input.parentNode.parentNode.appendChild(warning);
        }
    }
    
    // Check contrast on color changes
    document.querySelectorAll('input[type="color"]').forEach(input => {
        input.addEventListener('change', debounce(addContrastWarnings, 500));
    });
    
    // Initial contrast check
    setTimeout(addContrastWarnings, 1000);
});
