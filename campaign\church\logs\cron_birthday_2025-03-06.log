[2025-03-06 21:30:12] Starting birthday reminders cron job
[2025-03-06 21:30:18] Starting birthday reminders cron job
[2025-03-06 21:30:53] Starting birthday reminders cron job
[2025-03-06 21:31:12] Starting birthday reminders cron job
[2025-03-06 21:32:32] Starting birthday reminders cron job
[2025-03-06 21:32:32] Starting birthday reminders cron job
[2025-03-06 21:32:33] Starting birthday reminders cron job
[2025-03-06 21:32:33] Starting birthday reminders cron job
[2025-03-06 21:32:34] Starting birthday reminders cron job
[2025-03-06 21:32:34] Starting birthday reminders cron job
[2025-03-06 21:32:37] Starting birthday reminders cron job
[2025-03-06 21:32:37] Starting birthday reminders cron job
[2025-03-06 21:32:49] Starting birthday reminders cron job
[2025-03-06 21:32:49] Starting birthday reminders cron job
[2025-03-06 21:45:44] Request received: format=json, cron_key=not set
[2025-03-06 21:45:44] Request details: {"UNIQUE_ID":"Z8oXiLQNU@xzXEVwCp1GGwAAABU","HTTP_HOST":"localhost","HTTP_CONNECTION":"keep-alive","HTTP_PRAGMA":"no-cache","HTTP_CACHE_CONTROL":"no-cache","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","HTTP_SEC_CH_UA":"\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"","HTTP_DNT":"1","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_ACCEPT":"*\/*","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"cors","HTTP_SEC_FETCH_DEST":"empty","HTTP_REFERER":"http:\/\/localhost\/church\/admin\/index.php","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9,ru;q=0.8","HTTP_COOKIE":"ajs_anonymous_id=bc2d395a-72d8-4866-8ff3-e69e06eccc79; selectedProvider=Mistral; selectedModel=codestral-latest; cachedPrompt=; PHPSESSID=31d9ef13eaed5faabb880713979b8bb6","PATH":"C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\java8path;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Program Files\\NVIDIA\\CUDNN\\v9.5\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\ProgramData\\chocolatey\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\;C:\\Program Files\\GitHub CLI\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files\\Go\\bin;d:\\NEW ERA\\JOINTEND\\extractor\\email_extractor\\ffmpeg\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;C:\\Users\\<USER>\\go\\bin;C:\\Users\\<USER>\\cli\\bin;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\java8path;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Program Files\\NVIDIA\\CUDNN\\v9.5\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\ProgramData\\chocolatey\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\;C:\\Program Files\\GitHub CLI\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files\\Go\\bin;d:\\NEW ERA\\JOINTEND\\extractor\\email_extractor\\ffmpeg\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppDa;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"","SERVER_SOFTWARE":"Apache\/2.4.33 (Win64) OpenSSL\/1.0.2u mod_fcgid\/2.3.9 PHP\/8.3.1","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/MAMP\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/MAMP\/htdocs","SERVER_ADMIN":"<EMAIL>","SCRIPT_FILENAME":"C:\/MAMP\/htdocs\/church\/birthday_reminders.php","REMOTE_PORT":"62300","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json","REQUEST_URI":"\/church\/birthday_reminders.php?format=json","SCRIPT_NAME":"\/church\/birthday_reminders.php","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741297544.0940608978271484375,"REQUEST_TIME":1741297544}
[2025-03-06 21:45:44] Access granted: via format=json
[2025-03-06 21:45:44] Starting birthday reminders cron job
[2025-03-06 21:45:44] Uncaught Exception: Too few arguments to function BirthdayReminder::__construct(), 0 passed in C:\MAMP\htdocs\church\birthday_reminders.php on line 107 and at least 1 expected in C:\MAMP\htdocs\church\send_birthday_reminders.php on line 16
[2025-03-06 21:45:44] Request received: format=json, cron_key=not set
[2025-03-06 21:45:44] Request details: {"UNIQUE_ID":"Z8oXiLQNU@xzXEVwCp1GHQAAABU","HTTP_HOST":"localhost","HTTP_ACCEPT":"*\/*","PATH":"C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\java8path;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Program Files\\NVIDIA\\CUDNN\\v9.5\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\ProgramData\\chocolatey\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\;C:\\Program Files\\GitHub CLI\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files\\Go\\bin;d:\\NEW ERA\\JOINTEND\\extractor\\email_extractor\\ffmpeg\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;C:\\Users\\<USER>\\go\\bin;C:\\Users\\<USER>\\cli\\bin;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\java8path;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Program Files\\NVIDIA\\CUDNN\\v9.5\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\ProgramData\\chocolatey\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\;C:\\Program Files\\GitHub CLI\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files\\Go\\bin;d:\\NEW ERA\\JOINTEND\\extractor\\email_extractor\\ffmpeg\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppDa;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"","SERVER_SOFTWARE":"Apache\/2.4.33 (Win64) OpenSSL\/1.0.2u mod_fcgid\/2.3.9 PHP\/8.3.1","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/MAMP\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/MAMP\/htdocs","SERVER_ADMIN":"<EMAIL>","SCRIPT_FILENAME":"C:\/MAMP\/htdocs\/church\/birthday_reminders.php","REMOTE_PORT":"62315","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json","REQUEST_URI":"\/church\/birthday_reminders.php?format=json","SCRIPT_NAME":"\/church\/birthday_reminders.php","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741297544.2296969890594482421875,"REQUEST_TIME":1741297544}
[2025-03-06 21:45:44] Access granted: via format=json
[2025-03-06 21:45:44] Starting birthday reminders cron job
[2025-03-06 21:45:44] Uncaught Exception: Too few arguments to function BirthdayReminder::__construct(), 0 passed in C:\MAMP\htdocs\church\birthday_reminders.php on line 107 and at least 1 expected in C:\MAMP\htdocs\church\send_birthday_reminders.php on line 16
[2025-03-06 23:05:43] Request received: format=json, cron_key=not set
[2025-03-06 23:05:43] Request details: {"LSPHP_ProcessGroup":"on","PATH":"\/usr\/local\/bin:\/bin:\/usr\/bin","HTTP_ACCEPT":"*\/*","HTTP_ACCEPT_ENCODING":"br","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9,ru;q=0.8","HTTP_COOKIE":"PHPSESSID=0hpvgr6qdq9lupd56vvupjvg1e","HTTP_HOST":"profilepilot.io","HTTP_PRAGMA":"no-cache","HTTP_REFERER":"https:\/\/profilepilot.io\/church\/admin\/index.php","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_FORWARDED_FOR":"***************","HTTP_X_FORWARDED_PROTO":"https","HTTP_X_REAL_IP":"***************","HTTP_X_REAL_PORT":"25471","HTTP_X_FORWARDED_PORT":"443","HTTP_X_PORT":"443","HTTP_X_LSCACHE":"1","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_SEC_CH_UA":"\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"","HTTP_DNT":"1","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"cors","HTTP_SEC_FETCH_DEST":"empty","HTTP_PRIORITY":"u=1, i","DOCUMENT_ROOT":"\/home\/<USER>\/domains\/profilepilot.io\/public_html","REMOTE_ADDR":"***************","REMOTE_PORT":"38704","SERVER_ADDR":"2a02:4780:9:1137:0:34a9:be90:2","SERVER_NAME":"profilepilot.io","SERVER_ADMIN":"","SERVER_PORT":"443","REQUEST_SCHEME":"https","REQUEST_URI":"\/church\/birthday_reminders.php?format=json","PROXY_REMOTE_ADDR":"2a02:6ea0:f217::2","HTTPS":"on","CRAWLER_USLEEP":"1000","CRAWLER_LOAD_LIMIT_ENFORCE":"25","H_PLATFORM":"Hostinger","H_TYPE":"business","H_CANARY":"false","H_STAGING":"false","SSL_PROTOCOL":"TLSv1.3","SSL_CIPHER":"TLS_AES_256_GCM_SHA384","SSL_CIPHER_USEKEYSIZE":"256","SSL_CIPHER_ALGKEYSIZE":"256","SCRIPT_FILENAME":"\/home\/<USER>\/domains\/profilepilot.io\/public_html\/church\/birthday_reminders.php","QUERY_STRING":"format=json","SCRIPT_URI":"https:\/\/profilepilot.io\/church\/birthday_reminders.php","SCRIPT_URL":"\/church\/birthday_reminders.php","SCRIPT_NAME":"\/church\/birthday_reminders.php","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_SOFTWARE":"LiteSpeed","REQUEST_METHOD":"GET","X-LSCACHE":"on,crawler,esi,combine","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741302343.513476,"REQUEST_TIME":1741302343}
[2025-03-06 23:05:43] Access granted: via format=json
[2025-03-06 23:05:43] Starting birthday reminders cron job
[2025-03-06 23:05:43] Uncaught Exception: Too few arguments to function BirthdayReminder::__construct(), 0 passed in /home/<USER>/domains/profilepilot.io/public_html/church/birthday_reminders.php on line 107 and at least 1 expected in /home/<USER>/domains/profilepilot.io/public_html/church/send_birthday_reminders.php on line 16
[2025-03-06 23:05:44] Request received: format=json, cron_key=not set
[2025-03-06 23:05:44] Request details: {"LSPHP_ProcessGroup":"on","PATH":"\/usr\/local\/bin:\/bin:\/usr\/bin","HTTP_ACCEPT":"*\/*","HTTP_ACCEPT_ENCODING":"gzip","HTTP_HOST":"profilepilot.io","HTTP_X_FORWARDED_FOR":"2a02:4780:9:1137:0:34a9:be90:1","HTTP_X_FORWARDED_PROTO":"https","HTTP_X_REAL_IP":"2a02:4780:9:1137:0:34a9:be90:1","HTTP_X_REAL_PORT":"38248","HTTP_X_FORWARDED_PORT":"443","HTTP_X_PORT":"443","HTTP_X_LSCACHE":"1","DOCUMENT_ROOT":"\/home\/<USER>\/domains\/profilepilot.io\/public_html","REMOTE_ADDR":"2a02:4780:9:1137:0:34a9:be90:1","REMOTE_PORT":"36972","SERVER_ADDR":"2a02:4780:9:1137:0:34a9:be90:2","SERVER_NAME":"profilepilot.io","SERVER_ADMIN":"","SERVER_PORT":"443","REQUEST_SCHEME":"https","REQUEST_URI":"\/church\/birthday_reminders.php?format=json","PROXY_REMOTE_ADDR":"2a02:4780:9:11::2","HTTPS":"on","CRAWLER_USLEEP":"1000","CRAWLER_LOAD_LIMIT_ENFORCE":"25","H_PLATFORM":"Hostinger","H_TYPE":"business","H_CANARY":"false","H_STAGING":"false","SSL_PROTOCOL":"TLSv1.3","SSL_CIPHER":"TLS_AES_256_GCM_SHA384","SSL_CIPHER_USEKEYSIZE":"256","SSL_CIPHER_ALGKEYSIZE":"256","SCRIPT_FILENAME":"\/home\/<USER>\/domains\/profilepilot.io\/public_html\/church\/birthday_reminders.php","QUERY_STRING":"format=json","SCRIPT_URI":"https:\/\/profilepilot.io\/church\/birthday_reminders.php","SCRIPT_URL":"\/church\/birthday_reminders.php","SCRIPT_NAME":"\/church\/birthday_reminders.php","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_SOFTWARE":"LiteSpeed","REQUEST_METHOD":"GET","X-LSCACHE":"on,crawler,esi,combine","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741302344.22382,"REQUEST_TIME":1741302344}
[2025-03-06 23:05:44] Access granted: via format=json
[2025-03-06 23:05:44] Starting birthday reminders cron job
[2025-03-06 23:05:44] Uncaught Exception: Too few arguments to function BirthdayReminder::__construct(), 0 passed in /home/<USER>/domains/profilepilot.io/public_html/church/birthday_reminders.php on line 107 and at least 1 expected in /home/<USER>/domains/profilepilot.io/public_html/church/send_birthday_reminders.php on line 16
