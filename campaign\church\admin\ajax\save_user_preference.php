<?php
/**
 * Save User Preference via AJAX
 * Handles saving user preferences like theme, language, etc.
 */

session_start();
require_once '../../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Check if request is AJAX
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid request']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON data']);
    exit;
}

// Validate required fields
if (!isset($input['key']) || !isset($input['value'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing required fields: key, value']);
    exit;
}

$key = trim($input['key']);
$value = $input['value'];
$type = $input['type'] ?? 'string';
$userId = $_SESSION['admin_id'];

// Validate preference key (security)
$allowedKeys = [
    'theme_preference',
    'language_preference', 
    'timezone_preference',
    'dashboard_layout',
    'sidebar_collapsed',
    'items_per_page',
    'notification_preferences',
    'accessibility_mode',
    'high_contrast',
    'reduced_motion',
    'font_size',
    'auto_save'
];

if (!in_array($key, $allowedKeys)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid preference key']);
    exit;
}

// Validate preference type
$allowedTypes = ['string', 'integer', 'boolean', 'json'];
if (!in_array($type, $allowedTypes)) {
    $type = 'string';
}

// Process value based on type
switch ($type) {
    case 'integer':
        $value = (int) $value;
        break;
    case 'boolean':
        $value = $value ? 1 : 0;
        break;
    case 'json':
        if (is_array($value) || is_object($value)) {
            $value = json_encode($value);
        }
        // Validate JSON
        if (!json_decode($value)) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON value']);
            exit;
        }
        break;
    case 'string':
    default:
        $value = (string) $value;
        break;
}

try {
    // Check if preference exists
    $stmt = $pdo->prepare("SELECT id FROM user_preferences WHERE admin_id = ? AND preference_key = ?");
    $stmt->execute([$userId, $key]);
    $existing = $stmt->fetch();
    
    if ($existing) {
        // Update existing preference
        $stmt = $pdo->prepare("UPDATE user_preferences SET preference_value = ?, preference_type = ?, updated_at = NOW() WHERE admin_id = ? AND preference_key = ?");
        $result = $stmt->execute([$value, $type, $userId, $key]);
    } else {
        // Insert new preference
        $stmt = $pdo->prepare("INSERT INTO user_preferences (admin_id, preference_key, preference_value, preference_type, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())");
        $result = $stmt->execute([$userId, $key, $value, $type]);
    }
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Preference saved successfully',
            'key' => $key,
            'value' => $value,
            'type' => $type
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to save preference']);
    }
    
} catch (PDOException $e) {
    error_log("User preference save error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Database error occurred']);
}
?>
