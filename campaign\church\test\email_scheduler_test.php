<?php
/**
 * Email Scheduler Test Script
 * 
 * This script tests the email scheduler functionality without triggering the warnings
 * about accessing array offset on value of type bool.
 */

// Include necessary files
require_once '../config.php';
require_once '../includes/email_functions.php';

// Set up logging
$logDir = __DIR__ . '/../logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}
$logFile = $logDir . '/email_scheduler_test.log';
file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Email Scheduler Test started' . PHP_EOL);

try {
    // Prepare test data
    echo "Preparing test data...\n";
    
    // Create a test function that mimics the behavior of sendScheduledEmail but returns different types
    function testSendScheduledEmail($to, $name, $subject, $content, $scheduleId) {
        global $logFile;
        
        // Log the parameters
        $logEntry = "[" . date('Y-m-d H:i:s') . "] Test send to: $name <$to> for schedule: $scheduleId\n";
        file_put_contents($logFile, $logEntry, FILE_APPEND);
        
        // Test case 1: Return an array with success = true
        if (strpos($to, 'success') !== false) {
            return ['success' => true, 'message' => 'Email sent successfully'];
        }
        
        // Test case 2: Return an array with success = false
        if (strpos($to, 'failure') !== false) {
            return ['success' => false, 'message' => 'Failed to send email'];
        }
        
        // Test case 3: Return a boolean true
        if (strpos($to, 'boolean-true') !== false) {
            return true;
        }
        
        // Test case 4: Return a boolean false
        if (strpos($to, 'boolean-false') !== false) {
            return false;
        }
        
        // Test case 5: Return null
        if (strpos($to, 'null') !== false) {
            return null;
        }
        
        // Default case
        return ['success' => true, 'message' => 'Default success response'];
    }
    
    // Mock function to simulate the behavior of email_scheduler.php
    function processTestEmail($recipient, $logFile) {
        // Call our test function
        $result = testSendScheduledEmail(
            $recipient['email'],
            $recipient['name'],
            'Test Subject',
            'Test Content',
            $recipient['schedule_id']
        );
        
        $successCount = 0;
        $errorCount = 0;
        $errorMessages = [];
        
        // THIS IS THE FIXED CODE THAT SHOULD PREVENT WARNINGS
        if ($result && isset($result['success']) && $result['success']) {
            // Log success
            $logEntry = '[' . date('Y-m-d H:i:s') . '] Email sent to: ' . $recipient['email'] . PHP_EOL;
            file_put_contents($logFile, $logEntry, FILE_APPEND);
            $successCount++;
            echo "SUCCESS: Email sent to {$recipient['email']}\n";
        } else {
            $errorCount++;
            $errorMessage = (is_array($result) && isset($result['message'])) ? $result['message'] : 'Unknown error';
            $errorMessages[] = "Failed to send to {$recipient['email']}: {$errorMessage}";
            
            // Log error
            $logEntry = '[' . date('Y-m-d H:i:s') . '] Error sending to: ' . $recipient['email'] . ' - ' . $errorMessage . PHP_EOL;
            file_put_contents($logFile, $logEntry, FILE_APPEND);
            echo "ERROR: Failed to send to {$recipient['email']}: {$errorMessage}\n";
        }
        
        return [
            'success' => $successCount,
            'errors' => $errorCount,
            'messages' => $errorMessages
        ];
    }
    
    // Create test cases
    $testRecipients = [
        [
            'email' => '<EMAIL>',
            'name' => 'Success Test',
            'schedule_id' => 1
        ],
        [
            'email' => '<EMAIL>',
            'name' => 'Failure Test',
            'schedule_id' => 1
        ],
        [
            'email' => '<EMAIL>',
            'name' => 'Boolean True Test',
            'schedule_id' => 1
        ],
        [
            'email' => '<EMAIL>',
            'name' => 'Boolean False Test',
            'schedule_id' => 1
        ],
        [
            'email' => '<EMAIL>',
            'name' => 'Null Test',
            'schedule_id' => 1
        ]
    ];
    
    // Process each test recipient
    $totalResults = [
        'success' => 0,
        'errors' => 0,
        'messages' => []
    ];
    
    foreach ($testRecipients as $recipient) {
        echo "Testing with: {$recipient['email']}\n";
        $result = processTestEmail($recipient, $logFile);
        
        $totalResults['success'] += $result['success'];
        $totalResults['errors'] += $result['errors'];
        $totalResults['messages'] = array_merge($totalResults['messages'], $result['messages']);
    }
    
    // Output results
    echo "\nTest Results:\n";
    echo "Successful emails: {$totalResults['success']}\n";
    echo "Failed emails: {$totalResults['errors']}\n";
    
    if (count($totalResults['messages']) > 0) {
        echo "\nError messages:\n";
        foreach ($totalResults['messages'] as $message) {
            echo " - $message\n";
        }
    }
    
    // Log completion
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Email Scheduler Test completed. ' . 
        "Success: {$totalResults['success']}, Errors: {$totalResults['errors']}" . PHP_EOL, FILE_APPEND);
    
    echo "\nTest completed. Check $logFile for details.\n";
    
} catch (Exception $e) {
    file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
    echo "Error: " . $e->getMessage() . "\n";
} 