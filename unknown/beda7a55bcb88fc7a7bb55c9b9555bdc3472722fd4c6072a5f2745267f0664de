/**
 * Theme Customizer Styles
 * Advanced theme customization interface
 */

/* Theme Customizer Container */
.theme-customizer {
    background: var(--bs-body-bg);
    border: 1px solid var(--bs-border-color);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.theme-customizer h3 {
    color: var(--bs-primary);
    margin-bottom: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.theme-customizer h3::before {
    content: "🎨";
    font-size: 1.2em;
}

/* Preset Selector */
.preset-selector {
    background: rgba(var(--bs-primary-rgb), 0.1);
    border: 1px solid rgba(var(--bs-primary-rgb), 0.2);
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 2rem;
}

.preset-selector h4 {
    margin-bottom: 1rem;
    color: var(--bs-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.preset-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.preset-card {
    background: var(--bs-body-bg);
    border: 2px solid var(--bs-border-color);
    border-radius: 0.5rem;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.preset-card:hover {
    border-color: var(--bs-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preset-card.active {
    border-color: var(--bs-primary);
    background: rgba(var(--bs-primary-rgb), 0.05);
}

.preset-card h5 {
    margin-bottom: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
}

.preset-colors {
    display: flex;
    justify-content: center;
    gap: 4px;
    margin-bottom: 0.5rem;
}

.preset-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Color Picker Enhancements */
.color-input-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.color-input-group input[type="color"] {
    width: 50px;
    height: 40px;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.375rem;
    cursor: pointer;
    padding: 0;
}

.color-input-group .hex-input {
    width: 100px;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    text-transform: uppercase;
}

.color-palette {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 0.25rem;
}

.color-swatch {
    width: 24px;
    height: 24px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.1s ease;
    padding: 0;
}

.color-swatch:hover {
    transform: scale(1.1);
    border-color: var(--bs-primary);
}

/* Range Sliders */
.range-input-group {
    margin-bottom: 1rem;
}

.range-input-group label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.range-value {
    background: var(--bs-primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 40px;
    text-align: center;
}

.range-input-group input[type="range"] {
    width: 100%;
    height: 6px;
    background: var(--bs-border-color);
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.range-input-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: var(--bs-primary);
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.2s ease;
}

.range-input-group input[type="range"]::-webkit-slider-thumb:hover {
    background: var(--bs-primary-dark, var(--bs-primary));
    transform: scale(1.1);
}

.range-input-group input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: var(--bs-primary);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Font Selector */
.font-selector {
    position: relative;
}

.font-preview {
    background: rgba(0, 0, 0, 0.02);
    border: 1px solid var(--bs-border-color);
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 0.5rem;
    text-align: center;
}

.font-preview h4 {
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.font-preview p {
    margin: 0;
    color: var(--bs-secondary);
}

/* Control Buttons */
.theme-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: center;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 0.375rem;
    margin-top: 2rem;
}

.preview-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.preview-toggle input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.theme-actions {
    margin-left: auto;
    display: flex;
    gap: 0.5rem;
}

.btn-theme {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.btn-theme-primary {
    background: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}

.btn-theme-primary:hover {
    background: var(--bs-primary-dark, var(--bs-primary));
    transform: translateY(-1px);
}

.btn-theme-secondary {
    background: transparent;
    color: var(--bs-secondary);
    border-color: var(--bs-secondary);
}

.btn-theme-secondary:hover {
    background: var(--bs-secondary);
    color: white;
}

.btn-theme-danger {
    background: transparent;
    color: var(--bs-danger);
    border-color: var(--bs-danger);
}

.btn-theme-danger:hover {
    background: var(--bs-danger);
    color: white;
}

/* Import/Export */
.import-export {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

#importFile {
    display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .theme-customizer {
        padding: 1rem;
    }
    
    .preset-grid {
        grid-template-columns: 1fr;
    }
    
    .theme-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .theme-actions {
        margin-left: 0;
        justify-content: center;
    }
    
    .color-input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .color-palette {
        justify-content: center;
    }
}

/* Dark Mode Support */
[data-theme="dark"] .theme-customizer {
    background: var(--bs-dark);
    border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .preset-selector {
    background: rgba(var(--bs-primary-rgb), 0.2);
    border-color: rgba(var(--bs-primary-rgb), 0.3);
}

[data-theme="dark"] .preset-card {
    background: var(--bs-dark);
    border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .preset-card:hover {
    border-color: var(--bs-primary);
}

[data-theme="dark"] .color-palette {
    background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .font-preview {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .theme-controls {
    background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .range-input-group input[type="range"] {
    background: rgba(255, 255, 255, 0.2);
}

/* Animation for theme changes */
.theme-transition {
    transition: all 0.3s ease;
}

/* Loading state */
.theme-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.theme-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--bs-primary);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Success/Error Messages */
.theme-message {
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.theme-message.success {
    background: rgba(var(--bs-success-rgb), 0.1);
    color: var(--bs-success);
    border: 1px solid rgba(var(--bs-success-rgb), 0.2);
}

.theme-message.error {
    background: rgba(var(--bs-danger-rgb), 0.1);
    color: var(--bs-danger);
    border: 1px solid rgba(var(--bs-danger-rgb), 0.2);
}
