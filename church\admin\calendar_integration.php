<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize variables
$message = '';
$error = '';
$calendar_settings = [];

// Function to get calendar settings
function getCalendarSettings($conn) {
    try {
        $stmt = $conn->prepare("SELECT * FROM calendar_settings WHERE 1");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = $setting['setting_value'];
        }
        
        return $result;
    } catch (PDOException $e) {
        error_log("Error fetching calendar settings: " . $e->getMessage());
        return [];
    }
}

// Function to update calendar settings
function updateCalendarSetting($conn, $key, $value) {
    try {
        // Check if setting exists
        $stmt = $conn->prepare("SELECT COUNT(*) FROM calendar_settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $exists = (int)$stmt->fetchColumn();
        
        if ($exists) {
            // Update existing setting
            $stmt = $conn->prepare("UPDATE calendar_settings SET setting_value = ? WHERE setting_key = ?");
            $stmt->execute([$value, $key]);
        } else {
            // Insert new setting
            $stmt = $conn->prepare("INSERT INTO calendar_settings (setting_key, setting_value) VALUES (?, ?)");
            $stmt->execute([$key, $value]);
        }
        
        return true;
    } catch (PDOException $e) {
        error_log("Error updating calendar setting: " . $e->getMessage());
        return false;
    }
}

// Check if calendar_settings table exists, if not create it
try {
    $stmt = $conn->prepare("SHOW TABLES LIKE 'calendar_settings'");
    $stmt->execute();
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        $sql = "CREATE TABLE calendar_settings (
            id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(255) NOT NULL UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $conn->exec($sql);
        
        // Insert default settings
        $defaultSettings = [
            'google_calendar_enabled' => '0',
            'google_client_id' => '',
            'google_client_secret' => '',
            'google_redirect_uri' => '',
            'microsoft_calendar_enabled' => '0',
            'microsoft_client_id' => '',
            'microsoft_client_secret' => '',
            'microsoft_redirect_uri' => '',
            'default_calendar_provider' => 'google',
            'add_to_calendar_enabled' => '1'
        ];
        
        foreach ($defaultSettings as $key => $value) {
            updateCalendarSetting($conn, $key, $value);
        }
    }
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
    error_log("Error creating calendar_settings table: " . $e->getMessage());
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Google Calendar Settings
        if (isset($_POST['google_calendar_settings'])) {
            $google_enabled = isset($_POST['google_calendar_enabled']) ? '1' : '0';
            $google_client_id = trim($_POST['google_client_id']);
            $google_client_secret = trim($_POST['google_client_secret']);
            $google_redirect_uri = trim($_POST['google_redirect_uri']);
            
            updateCalendarSetting($conn, 'google_calendar_enabled', $google_enabled);
            updateCalendarSetting($conn, 'google_client_id', $google_client_id);
            updateCalendarSetting($conn, 'google_client_secret', $google_client_secret);
            updateCalendarSetting($conn, 'google_redirect_uri', $google_redirect_uri);
            
            $message = "Google Calendar settings updated successfully.";
        }
        
        // Microsoft Calendar Settings
        if (isset($_POST['microsoft_calendar_settings'])) {
            $microsoft_enabled = isset($_POST['microsoft_calendar_enabled']) ? '1' : '0';
            $microsoft_client_id = trim($_POST['microsoft_client_id']);
            $microsoft_client_secret = trim($_POST['microsoft_client_secret']);
            $microsoft_redirect_uri = trim($_POST['microsoft_redirect_uri']);
            
            updateCalendarSetting($conn, 'microsoft_calendar_enabled', $microsoft_enabled);
            updateCalendarSetting($conn, 'microsoft_client_id', $microsoft_client_id);
            updateCalendarSetting($conn, 'microsoft_client_secret', $microsoft_client_secret);
            updateCalendarSetting($conn, 'microsoft_redirect_uri', $microsoft_redirect_uri);
            
            $message = "Microsoft Calendar settings updated successfully.";
        }
        
        // General Calendar Settings
        if (isset($_POST['general_calendar_settings'])) {
            $default_provider = trim($_POST['default_calendar_provider']);
            $add_to_calendar_enabled = isset($_POST['add_to_calendar_enabled']) ? '1' : '0';
            
            updateCalendarSetting($conn, 'default_calendar_provider', $default_provider);
            updateCalendarSetting($conn, 'add_to_calendar_enabled', $add_to_calendar_enabled);
            
            $message = "General calendar settings updated successfully.";
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
        error_log("Error updating calendar settings: " . $e->getMessage());
    }
}

// Get current settings
$calendar_settings = getCalendarSettings($conn);

// Set default values if not set
$defaults = [
    'google_calendar_enabled' => '0',
    'google_client_id' => '',
    'google_client_secret' => '',
    'google_redirect_uri' => '',
    'microsoft_calendar_enabled' => '0',
    'microsoft_client_id' => '',
    'microsoft_client_secret' => '',
    'microsoft_redirect_uri' => '',
    'default_calendar_provider' => 'google',
    'add_to_calendar_enabled' => '1'
];

foreach ($defaults as $key => $value) {
    if (!isset($calendar_settings[$key])) {
        $calendar_settings[$key] = $value;
    }
}

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Calendar Integration Settings</h1>
            </div>
            
            <?php if (!empty($message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>General Calendar Settings</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="add_to_calendar_enabled" name="add_to_calendar_enabled" <?php echo $calendar_settings['add_to_calendar_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="add_to_calendar_enabled">Enable "Add to Calendar" feature for birthdays and events</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="default_calendar_provider" class="form-label">Default Calendar Provider</label>
                                    <select class="form-select" id="default_calendar_provider" name="default_calendar_provider">
                                        <option value="google" <?php echo $calendar_settings['default_calendar_provider'] == 'google' ? 'selected' : ''; ?>>Google Calendar</option>
                                        <option value="microsoft" <?php echo $calendar_settings['default_calendar_provider'] == 'microsoft' ? 'selected' : ''; ?>>Microsoft Calendar</option>
                                        <option value="apple" <?php echo $calendar_settings['default_calendar_provider'] == 'apple' ? 'selected' : ''; ?>>Apple Calendar</option>
                                        <option value="ics" <?php echo $calendar_settings['default_calendar_provider'] == 'ics' ? 'selected' : ''; ?>>ICS File Download</option>
                                    </select>
                                </div>
                                
                                <button type="submit" name="general_calendar_settings" class="btn btn-primary">Save General Settings</button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Google Calendar Integration</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="google_calendar_enabled" name="google_calendar_enabled" <?php echo $calendar_settings['google_calendar_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="google_calendar_enabled">Enable Google Calendar Integration</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="google_client_id" class="form-label">Google Client ID</label>
                                    <input type="text" class="form-control" id="google_client_id" name="google_client_id" value="<?php echo htmlspecialchars($calendar_settings['google_client_id']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="google_client_secret" class="form-label">Google Client Secret</label>
                                    <input type="password" class="form-control" id="google_client_secret" name="google_client_secret" value="<?php echo htmlspecialchars($calendar_settings['google_client_secret']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="google_redirect_uri" class="form-label">Redirect URI</label>
                                    <input type="text" class="form-control" id="google_redirect_uri" name="google_redirect_uri" value="<?php echo htmlspecialchars($calendar_settings['google_redirect_uri']); ?>">
                                    <div class="form-text">Example: https://yoursite.com/church/admin/google_calendar_callback.php</div>
                                </div>
                                
                                <button type="submit" name="google_calendar_settings" class="btn btn-primary">Save Google Settings</button>
                            </form>
                            
                            <div class="mt-3">
                                <h6>Setup Instructions:</h6>
                                <ol class="small">
                                    <li>Go to the <a href="https://console.developers.google.com/" target="_blank">Google Developer Console</a></li>
                                    <li>Create a new project or select an existing one</li>
                                    <li>Enable the Google Calendar API</li>
                                    <li>Create OAuth 2.0 credentials</li>
                                    <li>Set the authorized redirect URI</li>
                                    <li>Copy the Client ID and Client Secret to the fields above</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Microsoft Calendar Integration</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="microsoft_calendar_enabled" name="microsoft_calendar_enabled" <?php echo $calendar_settings['microsoft_calendar_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="microsoft_calendar_enabled">Enable Microsoft Calendar Integration</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="microsoft_client_id" class="form-label">Microsoft Application (client) ID</label>
                                    <input type="text" class="form-control" id="microsoft_client_id" name="microsoft_client_id" value="<?php echo htmlspecialchars($calendar_settings['microsoft_client_id']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="microsoft_client_secret" class="form-label">Microsoft Client Secret</label>
                                    <input type="password" class="form-control" id="microsoft_client_secret" name="microsoft_client_secret" value="<?php echo htmlspecialchars($calendar_settings['microsoft_client_secret']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="microsoft_redirect_uri" class="form-label">Redirect URI</label>
                                    <input type="text" class="form-control" id="microsoft_redirect_uri" name="microsoft_redirect_uri" value="<?php echo htmlspecialchars($calendar_settings['microsoft_redirect_uri']); ?>">
                                    <div class="form-text">Example: https://yoursite.com/church/admin/microsoft_calendar_callback.php</div>
                                </div>
                                
                                <button type="submit" name="microsoft_calendar_settings" class="btn btn-primary">Save Microsoft Settings</button>
                            </form>
                            
                            <div class="mt-3">
                                <h6>Setup Instructions:</h6>
                                <ol class="small">
                                    <li>Go to the <a href="https://portal.azure.com/#blade/Microsoft_AAD_RegisteredApps/ApplicationsListBlade" target="_blank">Azure Portal</a></li>
                                    <li>Register a new application</li>
                                    <li>Add Microsoft Graph permissions for Calendar.ReadWrite</li>
                                    <li>Create a client secret</li>
                                    <li>Add the redirect URI</li>
                                    <li>Copy the Application (client) ID and Client Secret to the fields above</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Calendar Integration Preview</h5>
                        </div>
                        <div class="card-body">
                            <p>This is how the "Add to Calendar" button will appear to users:</p>
                            
                            <div class="mb-4 p-3 border rounded">
                                <h6>Sample Birthday Event</h6>
                                <p><strong>John Doe's Birthday</strong><br>January 15, 2023</p>
                                
                                <div class="dropdown">
                                    <button class="btn btn-outline-primary dropdown-toggle" type="button" id="calendarDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-calendar-plus"></i> Add to Calendar
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="calendarDropdown">
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-google"></i> Google Calendar</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-microsoft"></i> Outlook Calendar</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-apple"></i> Apple Calendar</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-download"></i> Download ICS File</a></li>
                                    </ul>
                                </div>
                            </div>
                            
                            <p class="text-muted small">Note: The actual functionality depends on the settings configured above.</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 