<?php
// Set headers for JSON response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Include configuration file
require_once '../config.php';

try {
    // Connect to database using the connection from config.php
    $conn = $pdo;
    
    // Check if members table exists
    $checkTableQuery = $conn->prepare("
        SELECT COUNT(*) as table_exists 
        FROM information_schema.tables 
        WHERE table_schema = :dbname 
        AND table_name = 'members'
    ");
    $checkTableQuery->execute(['dbname' => $dbname]);
    $result = $checkTableQuery->fetch(PDO::FETCH_ASSOC);
    
    if ($result['table_exists'] == 0) {
        // Create members table if it doesn't exist
        $createTableQuery = $conn->prepare("
            CREATE TABLE members (
                id INT AUTO_INCREMENT PRIMARY KEY,
                full_name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE,
                birth_date DATE NOT NULL,
                phone_number VARCHAR(20),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ");
        $createTableQuery->execute();
        
        // Add sample data for testing
        $sampleData = $conn->prepare("
            INSERT INTO members (full_name, email, birth_date, phone_number) VALUES 
            ('John Doe', '<EMAIL>', DATE_SUB(CURDATE(), INTERVAL 25 YEAR), '+1234567890'),
            ('Jane Smith', '<EMAIL>', CURDATE(), '+1987654321'),
            ('Alice Johnson', '<EMAIL>', DATE_ADD(CURDATE(), INTERVAL 5 DAY), '+1122334455')
        ");
        $sampleData->execute();
    }
    
    // Test the birthdays query
    $todayQuery = $conn->prepare("
        SELECT id, full_name as name, email, DATE_FORMAT(birth_date, '%m/%d') as date 
        FROM members 
        WHERE DATE_FORMAT(birth_date, '%m-%d') = DATE_FORMAT(NOW(), '%m-%d')
        ORDER BY name ASC
    ");
    $todayQuery->execute();
    $todayBirthdays = $todayQuery->fetchAll(PDO::FETCH_ASSOC);
    
    $upcomingQuery = $conn->prepare("
        SELECT 
            id, 
            full_name as name, 
            email, 
            DATE_FORMAT(birth_date, '%m/%d') as date,
            DATEDIFF(
                DATE_ADD(
                    birth_date,
                    INTERVAL YEAR(CURDATE()) - YEAR(birth_date) + IF(DATE_FORMAT(CURDATE(), '%m-%d') > DATE_FORMAT(birth_date, '%m-%d'), 1, 0) YEAR
                ),
                CURDATE()
            ) as days_until
        FROM members 
        WHERE 
            DATE_FORMAT(birth_date, '%m-%d') != DATE_FORMAT(NOW(), '%m-%d')
            AND DATEDIFF(
                DATE_ADD(
                    birth_date,
                    INTERVAL YEAR(CURDATE()) - YEAR(birth_date) + IF(DATE_FORMAT(CURDATE(), '%m-%d') > DATE_FORMAT(birth_date, '%m-%d'), 1, 0) YEAR
                ),
                CURDATE()
            ) BETWEEN 1 AND 30
        ORDER BY days_until ASC, full_name ASC
        LIMIT 10
    ");
    $upcomingQuery->execute();
    $upcomingBirthdays = $upcomingQuery->fetchAll(PDO::FETCH_ASSOC);
    
    // Return success response with test data
    echo json_encode([
        'success' => true,
        'message' => 'Database connection and queries are working correctly',
        'today' => $todayBirthdays,
        'upcoming' => $upcomingBirthdays
    ]);
    
} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?> 