<phpunit backupGlobals="false"
         backupStaticAttributes="false"
         bootstrap="bootstrap.php"
         colors="false"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false">
  <testsuites>
    <testsuite name="hamcrest-php">
      <directory suffix="Test.php">.</directory>
    </testsuite>
  </testsuites>

  <filter>
    <whitelist addUncoveredFilesFromWhitelist="true">
      <directory suffix=".php">../hamcrest</directory>
    </whitelist>
  </filter>
</phpunit>
