<?php
// Set headers for JSON response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Include configuration file
require_once __DIR__ . '/../config.php';

try {
    // Connect to database using the connection from config.php
    $conn = $pdo;
    
    // Get today's birthdays
    $todayQuery = $conn->prepare("
        SELECT id, full_name as name, email, DATE_FORMAT(birth_date, '%m/%d') as date 
        FROM members 
        WHERE DATE_FORMAT(birth_date, '%m-%d') = DATE_FORMAT(NOW(), '%m-%d')
        AND status = 'active'
        ORDER BY name ASC
    ");
    $todayQuery->execute();
    $todayBirthdays = $todayQuery->fetchAll(PDO::FETCH_ASSOC);
    
    // Get upcoming birthdays within next 30 days
    $upcomingQuery = $conn->prepare("
        SELECT 
            id, 
            full_name as name, 
            email, 
            DATE_FORMAT(birth_date, '%m/%d') as date,
            DATEDIFF(
                DATE_ADD(
                    birth_date,
                    INTERVAL YEAR(CURDATE()) - YEAR(birth_date) + IF(DATE_FORMAT(CURDATE(), '%m-%d') > DATE_FORMAT(birth_date, '%m-%d'), 1, 0) YEAR
                ),
                CURDATE()
            ) as days_until
        FROM members 
        WHERE 
            DATE_FORMAT(birth_date, '%m-%d') != DATE_FORMAT(NOW(), '%m-%d')
            AND status = 'active'
            AND DATEDIFF(
                DATE_ADD(
                    birth_date,
                    INTERVAL YEAR(CURDATE()) - YEAR(birth_date) + IF(DATE_FORMAT(CURDATE(), '%m-%d') > DATE_FORMAT(birth_date, '%m-%d'), 1, 0) YEAR
                ),
                CURDATE()
            ) BETWEEN 1 AND 30
        ORDER BY days_until ASC, full_name ASC
        LIMIT 10
    ");
    $upcomingQuery->execute();
    $upcomingBirthdays = $upcomingQuery->fetchAll(PDO::FETCH_ASSOC);
    
    // Return response with birthday data
    echo json_encode([
        'success' => true,
        'today' => $todayBirthdays,
        'upcoming' => $upcomingBirthdays
    ]);
    
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
