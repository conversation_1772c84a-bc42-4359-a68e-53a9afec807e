<?php
session_start();
require_once '../../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

// Get JSON data
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['name']) || empty($data['name'])) {
    $_SESSION['error'] = "Group name is required";
    echo json_encode(['success' => false, 'error' => 'Group name is required']);
    exit;
}

try {
    $stmt = $pdo->prepare("INSERT INTO contact_groups (name, description) VALUES (?, ?)");
    $stmt->execute([$data['name'], $data['description'] ?? '']);
    
    $_SESSION['success'] = "Group '{$data['name']}' has been created successfully!";
    echo json_encode([
        'success' => true, 
        'message' => "Group created successfully",
        'groupId' => $pdo->lastInsertId(),
        'groupName' => $data['name']
    ]);
} catch (PDOException $e) {
    $_SESSION['error'] = "Failed to create group: " . $e->getMessage();
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?> 