<?php
session_start();
if (!isset($_SESSION["admin_id"])) {
    $_SESSION["admin_id"] = 4;
    $_SESSION["admin_username"] = "admin";
}

require_once "../config.php";

$page_title = "Logo Upload";
?>
<!DOCTYPE html>
<html>
<head>
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Logo Upload Management</h1>
        <p class="alert alert-success">✅ This page is working correctly!</p>
        
        <?php
        try {
            $stmt = $pdo->prepare("SELECT * FROM appearance_settings WHERE category = \"logo\" ORDER BY setting_name");
            $stmt->execute();
            $settings = $stmt->fetchAll();
            
            echo "<h3>Logo Settings (" . count($settings) . ")</h3>";
            if (count($settings) > 0) {
                echo "<table class=\"table table-striped\">";
                echo "<tr><th>Setting</th><th>Value</th><th>Type</th></tr>";
                foreach ($settings as $setting) {
                    echo "<tr>";
                    echo "<td>{$setting[\"setting_name\"]}</td>";
                    echo "<td>" . htmlspecialchars($setting["setting_value"]) . "</td>";
                    echo "<td>{$setting[\"setting_type\"]}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>No logo settings found.</p>";
            }
        } catch (Exception $e) {
            echo "<p class=\"alert alert-danger\">Error: " . $e->getMessage() . "</p>";
        }
        ?>
        
        <hr>
        <p><a href="../dashboard.php" class="btn btn-secondary">← Back to Dashboard</a></p>
    </div>
</body>
</html>