<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Contact Delete Functionality</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Test Contact Delete Functionality</h2>
        
        <?php
        session_start();
        require_once '../config.php';
        $conn = $pdo;
        
        // Set admin session
        $_SESSION['admin_id'] = 1;
        
        // Generate CSRF token
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        $action = $_GET['action'] ?? 'menu';
        
        switch ($action) {
            case 'create_test_contacts':
                echo "<h3>Creating Test Contacts</h3>";
                $created_contacts = [];
                
                for ($i = 1; $i <= 5; $i++) {
                    try {
                        $email = "test_contact_{$i}_" . time() . "@example.com";
                        $name = "Test Contact $i";
                        
                        $stmt = $conn->prepare("INSERT INTO contacts (email, name, source) VALUES (?, ?, 'test')");
                        $stmt->execute([$email, $name]);
                        $contact_id = $conn->lastInsertId();
                        
                        $created_contacts[] = [
                            'id' => $contact_id,
                            'name' => $name,
                            'email' => $email
                        ];
                        
                        echo "<div class='alert alert-success'>✅ Created: $name (ID: $contact_id)</div>";
                    } catch (PDOException $e) {
                        echo "<div class='alert alert-danger'>❌ Error creating contact $i: " . $e->getMessage() . "</div>";
                    }
                }
                
                if (!empty($created_contacts)) {
                    echo "<h4>Test Individual Delete</h4>";
                    echo "<div class='row'>";
                    foreach ($created_contacts as $contact) {
                        $delete_url = "../admin/contacts.php?delete_id={$contact['id']}&csrf_token=" . $_SESSION['csrf_token'];
                        echo "<div class='col-md-6 mb-2'>";
                        echo "<div class='card'>";
                        echo "<div class='card-body'>";
                        echo "<h6 class='card-title'>{$contact['name']}</h6>";
                        echo "<p class='card-text'>{$contact['email']}</p>";
                        echo "<a href='$delete_url' class='btn btn-danger btn-sm' onclick='return confirm(\"Delete this contact?\")'>Delete Individual</a>";
                        echo "</div>";
                        echo "</div>";
                        echo "</div>";
                    }
                    echo "</div>";
                    
                    echo "<h4>Test Bulk Delete</h4>";
                    $contact_ids = array_column($created_contacts, 'id');
                    echo "<button class='btn btn-warning' onclick='testBulkDelete(" . json_encode($contact_ids) . ")'>Test Bulk Delete</button>";
                }
                break;
                
            case 'test_bulk_ajax':
                echo "<h3>Testing Bulk Delete AJAX</h3>";
                
                // Get contact IDs from POST
                $contact_ids = json_decode($_POST['contact_ids'] ?? '[]', true);
                
                if (empty($contact_ids)) {
                    echo "<div class='alert alert-danger'>No contact IDs provided</div>";
                    break;
                }
                
                echo "<p>Testing bulk delete for IDs: " . implode(', ', $contact_ids) . "</p>";
                
                // Simulate AJAX call to bulk_delete_contacts.php
                $post_data = json_encode([
                    'contact_ids' => $contact_ids,
                    'confirmation_token' => 'BULK_DELETE_CONFIRMED'
                ]);
                
                // Use cURL to make the request
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'http://localhost/campaign/church/admin/ajax/bulk_delete_contacts.php');
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($post_data)
                ]);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                echo "<h4>AJAX Response</h4>";
                echo "<p><strong>HTTP Code:</strong> $http_code</p>";
                echo "<p><strong>Response:</strong></p>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
                
                // Parse response
                $result = json_decode($response, true);
                if ($result) {
                    if ($result['success']) {
                        echo "<div class='alert alert-success'>✅ Bulk delete successful!</div>";
                        if (isset($result['results'])) {
                            echo "<p>Successfully deleted: " . $result['results']['successfully_deleted'] . " contacts</p>";
                        }
                    } else {
                        echo "<div class='alert alert-danger'>❌ Bulk delete failed: " . ($result['message'] ?? 'Unknown error') . "</div>";
                    }
                } else {
                    echo "<div class='alert alert-warning'>⚠️ Could not parse response as JSON</div>";
                }
                break;
                
            default:
                echo "<h3>Test Menu</h3>";
                echo "<div class='list-group'>";
                echo "<a href='?action=create_test_contacts' class='list-group-item list-group-item-action'>";
                echo "<h5>Create Test Contacts & Test Delete</h5>";
                echo "<p>Creates 5 test contacts and provides buttons to test individual and bulk delete</p>";
                echo "</a>";
                echo "<a href='../admin/contacts.php' class='list-group-item list-group-item-action'>";
                echo "<h5>Go to Contacts Page</h5>";
                echo "<p>View the actual contacts page to test functionality</p>";
                echo "</a>";
                echo "</div>";
                break;
        }
        ?>
        
        <div class="mt-4">
            <a href="?" class="btn btn-secondary">Back to Menu</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testBulkDelete(contactIds) {
            if (confirm('Test bulk delete for ' + contactIds.length + ' contacts?')) {
                // Create form and submit
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '?action=test_bulk_ajax';
                
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'contact_ids';
                input.value = JSON.stringify(contactIds);
                
                form.appendChild(input);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
