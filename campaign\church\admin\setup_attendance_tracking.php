<?php
// Setup script to add attendance tracking functionality
require_once '../config.php';

try {
    echo "Setting up attendance tracking...<br>";
    
    // Check if column already exists
    $stmt = $pdo->query("DESCRIBE event_rsvps");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('actually_attended', $columns)) {
        echo "Adding actually_attended column...<br>";
        
        // Add the actually_attended column
        $pdo->exec("ALTER TABLE event_rsvps 
                   ADD COLUMN actually_attended TINYINT(1) DEFAULT NULL 
                   COMMENT 'NULL = not marked, 0 = did not attend, 1 = attended'");
        
        echo "✓ Added actually_attended column<br>";
        
        // Add indexes for performance
        $pdo->exec("ALTER TABLE event_rsvps 
                   ADD INDEX idx_actually_attended (actually_attended)");
        
        echo "✓ Added attendance index<br>";
        
        $pdo->exec("ALTER TABLE event_rsvps 
                   ADD INDEX idx_event_attendance (event_id, status, actually_attended)");
        
        echo "✓ Added composite attendance index<br>";
        
    } else {
        echo "✓ actually_attended column already exists<br>";
    }
    
    echo "<br>Attendance tracking setup completed successfully!<br>";
    echo "<a href='event_attendance.php'>Go to Event Attendance Tracking</a>";
    
} catch (PDOException $e) {
    echo "Error setting up attendance tracking: " . $e->getMessage();
}
