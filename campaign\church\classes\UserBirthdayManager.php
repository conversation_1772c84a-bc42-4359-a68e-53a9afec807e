<?php

/**
 * UserBirthdayManager Class
 * 
 * Handles birthday-related functionality for users including templates,
 * message sending, and activity tracking.
 */
class UserBirthdayManager {
    private $pdo;
    private $userAuth;
    
    /**
     * Constructor
     * 
     * @param PDO $pdo Database connection
     * @param UserAuthManager $userAuth User authentication manager
     */
    public function __construct($pdo, $userAuth) {
        $this->pdo = $pdo;
        $this->userAuth = $userAuth;
    }
    
    /**
     * Get today's birthdays for a user
     * 
     * @param int $userId Current user ID (excluded from results)
     * @return array Array of members with birthdays today
     */
    public function getTodaysBirthdays($userId) {
        $stmt = $this->pdo->prepare("
            SELECT id, full_name, first_name, last_name, email, image_path, birth_date
            FROM members 
            WHERE status = 'active' 
            AND id != ? 
            AND MONTH(birth_date) = MONTH(CURDATE()) 
            AND DAY(birth_date) = DAY(CURDATE())
            ORDER BY full_name
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get upcoming birthdays (next 7 days)
     * 
     * @param int $userId Current user ID (excluded from results)
     * @param int $days Number of days to look ahead (default: 7)
     * @return array Array of members with upcoming birthdays
     */
    public function getUpcomingBirthdays($userId, $days = 7) {
        $stmt = $this->pdo->prepare("
            SELECT id, full_name, first_name, last_name, email, image_path, birth_date,
                   DATEDIFF(
                       DATE_ADD(
                           MAKEDATE(YEAR(CURDATE()), 1), 
                           INTERVAL DAYOFYEAR(birth_date) - 1 DAY
                       ), 
                       CURDATE()
                   ) AS days_until
            FROM members 
            WHERE status = 'active' 
            AND id != ?
            AND (
                (MONTH(birth_date) = MONTH(CURDATE()) AND DAY(birth_date) > DAY(CURDATE()))
                OR (MONTH(birth_date) = MONTH(DATE_ADD(CURDATE(), INTERVAL ? DAY)) 
                    AND DAY(birth_date) <= DAY(DATE_ADD(CURDATE(), INTERVAL ? DAY)))
            )
            ORDER BY MONTH(birth_date), DAY(birth_date)
            LIMIT 10
        ");
        $stmt->execute([$userId, $days, $days]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get available birthday templates for users
     * 
     * @return array Array of birthday templates grouped by category
     */
    public function getBirthdayTemplates() {
        $stmt = $this->pdo->prepare("
            SELECT
                id,
                template_name as name,
                subject as description,
                content as template_content,
                template_category as category,
                NULL as thumbnail_path,
                COALESCE(
                    (SELECT COUNT(*) FROM email_template_usage
                     WHERE template_id = email_templates.id AND template_type = 'birthday'),
                    0
                ) as usage_count
            FROM email_templates
            WHERE is_birthday_template = 1
            ORDER BY template_category, template_name
        ");
        $stmt->execute();
        $templates = $stmt->fetchAll();
        
        // Group by category
        $templatesByCategory = [];
        foreach ($templates as $template) {
            $category = $template['category'] ?: 'general';
            if (!isset($templatesByCategory[$category])) {
                $templatesByCategory[$category] = [];
            }
            $templatesByCategory[$category][] = $template;
        }
        
        return $templatesByCategory;
    }
    
    /**
     * Get a specific birthday template
     * 
     * @param int $templateId Template ID
     * @return array|null Template data or null if not found
     */
    public function getBirthdayTemplate($templateId) {
        $stmt = $this->pdo->prepare("
            SELECT id, name, description, template_content, category
            FROM birthday_templates 
            WHERE id = ? AND is_active = 1 AND is_user_selectable = 1
        ");
        $stmt->execute([$templateId]);
        return $stmt->fetch();
    }
    
    /**
     * Get recent birthday messages sent by a user
     * 
     * @param int $userId User ID
     * @param int $limit Number of messages to retrieve (default: 5)
     * @return array Array of recent birthday messages
     */
    public function getRecentBirthdayMessages($userId, $limit = 5) {
        $stmt = $this->pdo->prepare("
            SELECT ubm.*, m.full_name as recipient_name, bt.name as template_name
            FROM user_birthday_messages ubm
            LEFT JOIN members m ON ubm.recipient_id = m.id
            LEFT JOIN birthday_templates bt ON ubm.template_id = bt.id
            WHERE ubm.sender_id = ?
            ORDER BY ubm.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$userId, $limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Send a birthday message
     * 
     * @param int $senderId Sender user ID
     * @param int $recipientId Recipient member ID
     * @param int $templateId Template ID
     * @param string $customMessage Optional custom message
     * @param string $deliveryMethod Delivery method (email, sms, both)
     * @param string $scheduleDate Date to send (Y-m-d format)
     * @return array Result with success status and message
     */
    public function sendBirthdayMessage($senderId, $recipientId, $templateId, $customMessage = '', $deliveryMethod = 'email', $scheduleDate = null) {
        $result = ['success' => false, 'message' => ''];
        
        try {
            if (!$scheduleDate) {
                $scheduleDate = date('Y-m-d');
            }
            
            // Validate recipient
            $stmt = $this->pdo->prepare("
                SELECT id, full_name, first_name, email, phone_number
                FROM members 
                WHERE id = ? AND status = 'active' AND id != ?
            ");
            $stmt->execute([$recipientId, $senderId]);
            $recipient = $stmt->fetch();
            
            if (!$recipient) {
                throw new Exception('Invalid recipient selected.');
            }
            
            // Validate template
            $template = $this->getBirthdayTemplate($templateId);
            if (!$template) {
                throw new Exception('Invalid template selected.');
            }
            
            // Check if message already sent today for this recipient
            $stmt = $this->pdo->prepare("
                SELECT id FROM user_birthday_messages 
                WHERE sender_id = ? AND recipient_id = ? AND DATE(created_at) = CURDATE()
            ");
            $stmt->execute([$senderId, $recipientId]);
            if ($stmt->fetch()) {
                throw new Exception('You have already sent a birthday message to this person today.');
            }
            
            // Get sender data
            $sender = $this->userAuth->getUserById($senderId);
            if (!$sender) {
                throw new Exception('Invalid sender.');
            }
            
            // Insert birthday message record
            $stmt = $this->pdo->prepare("
                INSERT INTO user_birthday_messages 
                (sender_id, recipient_id, template_id, custom_message, scheduled_date, delivery_method, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $status = ($scheduleDate === date('Y-m-d')) ? 'sent' : 'scheduled';
            $stmt->execute([
                $senderId, $recipientId, $templateId, $customMessage, 
                $scheduleDate, $deliveryMethod, $status
            ]);
            
            $messageId = $this->pdo->lastInsertId();
            
            // If sending immediately, process the email
            if ($status === 'sent') {
                $emailResult = $this->processEmailDelivery($messageId, $sender, $recipient, $template, $customMessage);
                
                if ($emailResult['success']) {
                    // Update message status
                    $stmt = $this->pdo->prepare("UPDATE user_birthday_messages SET sent_at = NOW() WHERE id = ?");
                    $stmt->execute([$messageId]);
                    
                    // Update template usage count
                    $stmt = $this->pdo->prepare("UPDATE birthday_templates SET usage_count = usage_count + 1 WHERE id = ?");
                    $stmt->execute([$templateId]);
                    
                    // Log user activity
                    $this->userAuth->logUserActivity(
                        $senderId, 
                        'birthday_message_sent', 
                        "Sent birthday message to {$recipient['full_name']}", 
                        'birthday_message', 
                        $messageId,
                        ['recipient_id' => $recipientId, 'template_id' => $templateId]
                    );
                    
                    $result['success'] = true;
                    $result['message'] = "Birthday message sent successfully to {$recipient['full_name']}!";
                } else {
                    // Update status to failed
                    $stmt = $this->pdo->prepare("UPDATE user_birthday_messages SET status = 'failed' WHERE id = ?");
                    $stmt->execute([$messageId]);
                    
                    throw new Exception($emailResult['message']);
                }
            } else {
                $result['success'] = true;
                $result['message'] = "Birthday message scheduled for " . date('M j, Y', strtotime($scheduleDate)) . "!";
            }
            
        } catch (Exception $e) {
            $result['message'] = $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * Process email delivery for a birthday message
     * 
     * @param int $messageId Message ID
     * @param array $sender Sender data
     * @param array $recipient Recipient data
     * @param array $template Template data
     * @param string $customMessage Custom message
     * @return array Result with success status and message
     */
    private function processEmailDelivery($messageId, $sender, $recipient, $template, $customMessage = '') {
        $result = ['success' => false, 'message' => ''];
        
        try {
            // Get site settings for branding
            $sitename = 'Church Management System';
            $stmt = $this->pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
            $stmt->execute();
            $siteNameResult = $stmt->fetch();
            if ($siteNameResult) {
                $sitename = $siteNameResult['setting_value'];
            }
            
            // Prepare template content
            $emailContent = $template['template_content'];
            $emailContent = str_replace('{{recipient_name}}', $recipient['first_name'], $emailContent);
            $emailContent = str_replace('{{sender_name}}', $sender['first_name'], $emailContent);
            $emailContent = str_replace('{{church_name}}', $sitename, $emailContent);
            
            // Add custom message if provided
            if ($customMessage) {
                $customMessageHtml = '<div style="background-color: #f8f9fa; padding: 15px; margin: 20px 0; border-left: 4px solid #667eea; border-radius: 5px;">
                    <h6 style="color: #667eea; margin-bottom: 10px;">Personal Message from ' . htmlspecialchars($sender['first_name']) . ':</h6>
                    <p style="margin: 0; font-style: italic;">' . nl2br(htmlspecialchars($customMessage)) . '</p>
                </div>';
                
                // Insert custom message before closing div
                $emailContent = str_replace('</div>', $customMessageHtml . '</div>', $emailContent);
            }
            
            // Send email
            $subject = "🎉 Happy Birthday " . $recipient['first_name'] . "! 🎂";
            
            if (sendEmail($recipient['email'], $recipient['full_name'], $subject, $emailContent, true)) {
                $result['success'] = true;
                $result['message'] = 'Email sent successfully';
            } else {
                throw new Exception('Failed to send email. Please try again.');
            }
            
        } catch (Exception $e) {
            $result['message'] = $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * Get birthday message statistics for a user
     * 
     * @param int $userId User ID
     * @return array Statistics array
     */
    public function getBirthdayMessageStats($userId) {
        $stats = [
            'total_sent' => 0,
            'sent_this_month' => 0,
            'scheduled' => 0,
            'failed' => 0
        ];
        
        try {
            // Total sent
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as count FROM user_birthday_messages 
                WHERE sender_id = ? AND status = 'sent'
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch();
            $stats['total_sent'] = $result['count'];
            
            // Sent this month
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as count FROM user_birthday_messages 
                WHERE sender_id = ? AND status = 'sent' 
                AND MONTH(sent_at) = MONTH(CURDATE()) 
                AND YEAR(sent_at) = YEAR(CURDATE())
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch();
            $stats['sent_this_month'] = $result['count'];
            
            // Scheduled
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as count FROM user_birthday_messages 
                WHERE sender_id = ? AND status = 'scheduled'
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch();
            $stats['scheduled'] = $result['count'];
            
            // Failed
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as count FROM user_birthday_messages 
                WHERE sender_id = ? AND status = 'failed'
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch();
            $stats['failed'] = $result['count'];
            
        } catch (Exception $e) {
            error_log('Error getting birthday message stats: ' . $e->getMessage());
        }
        
        return $stats;
    }
}
?>
