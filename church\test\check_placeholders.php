<?php
// <PERSON><PERSON><PERSON> to verify the email system's placeholder recognition
require_once __DIR__ . '/../config.php';

try {
    echo "<h2>Email Template Placeholder Verification</h2>";
    
    // Get the raffle template
    $stmt = $pdo->prepare("SELECT id, template_name, subject, content FROM email_templates WHERE template_name = 'The Big Raffle Winner' LIMIT 1");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "<p>Template not found!</p>";
        exit;
    }
    
    echo "<p>Found template ID: " . $template['id'] . " - " . htmlspecialchars($template['template_name']) . "</p>";
    
    // Check for replyToEmail placeholder
    $has_reply_to_placeholder = strpos($template['content'], '{{replyToEmail}}') !== false;
    echo "<p>Has {{replyToEmail}} placeholder: " . ($has_reply_to_placeholder ? "Yes" : "No") . "</p>";
    
    // Check for unsubscribeLink placeholder
    $has_unsub_placeholder = strpos($template['content'], '{{unsubscribeLink}}') !== false;
    echo "<p>Has {{unsubscribeLink}} placeholder: " . ($has_unsub_placeholder ? "Yes" : "No") . "</p>";
    
    // Check for lottery numbers
    $lotto_pattern = '/<div class="lotto-ball [^"]+">(\d+)<\/div>/';
    preg_match_all($lotto_pattern, $template['content'], $matches);
    
    if (!empty($matches[1])) {
        echo "<p>Found lottery numbers: " . implode(", ", $matches[1]) . "</p>";
    } else {
        echo "<p>No lottery numbers found in template!</p>";
    }
    
    // Check the email_settings table
    echo "<h3>Email Settings Check:</h3>";
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM email_settings WHERE setting_key IN ('replyToEmail', 'reply_to_email')");
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Setting Key</th><th>Setting Value</th></tr>";
    foreach ($settings as $setting) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($setting['setting_key']) . "</td>";
        echo "<td>" . htmlspecialchars($setting['setting_value']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check which placeholders the system would actually replace
    echo "<h3>System Placeholder Check:</h3>";
    
    // Check if we have a function that handles placeholder replacement
    $placeholder_function_exists = function_exists('replace_placeholders');
    echo "<p>replace_placeholders function exists: " . ($placeholder_function_exists ? "Yes" : "No") . "</p>";
    
    // Check the database schema for email placeholder handling 
    $tables = $pdo->query("SHOW TABLES LIKE '%placeholder%'")->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>Found " . count($tables) . " placeholder-related tables: " . implode(", ", $tables) . "</p>";
    
    // Summary
    echo "<h3>Summary:</h3>";
    if ($has_reply_to_placeholder && $has_unsub_placeholder && !empty($matches[1])) {
        echo "<p style='color:green;'>✅ Template appears to be correctly formatted with fixed numbers and appropriate placeholders.</p>";
    } else {
        echo "<p style='color:red;'>❌ Template may have issues. Please check the details above.</p>";
    }
    
} catch (PDOException $e) {
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
} 