<?php
session_start();
require_once '../../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

if (!isset($_GET['group_id'])) {
    echo json_encode(['success' => false, 'error' => 'Group ID is required']);
    exit();
}

// Pagination parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
if ($limit < 5) $limit = 5;
if ($limit > 50) $limit = 50;
$offset = ($page - 1) * $limit;

// Sorting parameters
$sort_field = isset($_GET['sort']) ? $_GET['sort'] : 'name';
$sort_order = isset($_GET['order']) ? $_GET['order'] : 'ASC';
$allowed_sort_fields = ['name', 'email', 'created_at'];
if (!in_array($sort_field, $allowed_sort_fields)) {
    $sort_field = 'name';
}
$sort_order = strtoupper($sort_order) === 'ASC' ? 'ASC' : 'DESC';

try {
    // Get total count
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM contacts c 
                          JOIN contact_group_members m ON c.id = m.contact_id 
                          WHERE m.group_id = ?");
    $stmt->execute([$_GET['group_id']]);
    $total_members = $stmt->fetchColumn();
    $total_pages = ceil($total_members / $limit);
    
    // Get paginated and sorted results
    $stmt = $pdo->prepare("SELECT c.* FROM contacts c 
                          JOIN contact_group_members m ON c.id = m.contact_id 
                          WHERE m.group_id = ? 
                          ORDER BY c.$sort_field $sort_order 
                          LIMIT ? OFFSET ?");
    $stmt->execute([$_GET['group_id'], $limit, $offset]);
    $members = $stmt->fetchAll();
    
    // Calculate pagination ranges
    $start_number = $offset + 1;
    $end_number = min($offset + $limit, $total_members);
    
    echo json_encode([
        'success' => true,
        'members' => $members,
        'pagination' => [
            'total' => $total_members,
            'pages' => $total_pages,
            'current' => $page,
            'limit' => $limit,
            'start' => $start_number,
            'end' => $end_number
        ]
    ]);
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'error' => 'Error fetching group members']);
}
?> 