<?php
session_start();
if (!isset($_SESSION["admin_id"])) {
    $_SESSION["admin_id"] = 4;
    $_SESSION["admin_username"] = "admin";
}

require_once "../config.php";

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST["action"])) {
    try {
        if ($_POST["action"] === "update_setting") {
            $stmt = $pdo->prepare("
                UPDATE appearance_settings 
                SET setting_value = ?, updated_at = NOW() 
                WHERE setting_name = ?
            ");
            $stmt->execute([$_POST["setting_value"], $_POST["setting_name"]]);
            $success = "Setting updated successfully!";
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get logo settings
$stmt = $pdo->prepare("SELECT * FROM appearance_settings WHERE category = \"logo\" ORDER BY setting_name");
$stmt->execute();
$logoSettings = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo Upload - Church Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-2 bg-dark text-white p-3">
                <h5>Admin Menu</h5>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-white" href="../dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="working_custom_fields.php">
                            <i class="bi bi-ui-checks-grid"></i> Custom Fields
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white active" href="working_logo_upload.php">
                            <i class="bi bi-image"></i> Logo Upload
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="working_branding_settings.php">
                            <i class="bi bi-palette"></i> Branding Settings
                        </a>
                    </li>
                </ul>
            </div>
            <div class="col-md-10 p-4">
                <h1><i class="bi bi-image"></i> Logo Upload Management</h1>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?php echo htmlspecialchars($success); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h5>Logo Settings</h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($logoSettings) > 0): ?>
                            <?php foreach ($logoSettings as $setting): ?>
                                <form method="post" class="mb-3">
                                    <input type="hidden" name="action" value="update_setting">
                                    <input type="hidden" name="setting_name" value="<?php echo $setting[\"setting_name\"]; ?>">
                                    
                                    <div class="row align-items-end">
                                        <div class="col-md-3">
                                            <label class="form-label">
                                                <strong><?php echo ucwords(str_replace(\"_\", \" \", $setting[\"setting_name\"])); ?></strong>
                                            </label>
                                        </div>
                                        <div class="col-md-6">
                                            <?php if ($setting[\"setting_type\"] === \"file\"): ?>
                                                <input type="file" class="form-control" accept="image/*">
                                                <input type="hidden" name="setting_value" value="<?php echo htmlspecialchars($setting[\"setting_value\"]); ?>">
                                            <?php elseif ($setting[\"setting_type\"] === \"number\"): ?>
                                                <input type="number" name="setting_value" class="form-control" 
                                                       value="<?php echo htmlspecialchars($setting[\"setting_value\"]); ?>">
                                            <?php else: ?>
                                                <input type="text" name="setting_value" class="form-control" 
                                                       value="<?php echo htmlspecialchars($setting[\"setting_value\"]); ?>">
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-3">
                                            <button type="submit" class="btn btn-primary">Update</button>
                                        </div>
                                    </div>
                                    
                                    <?php if (!empty($setting[\"description\"])): ?>
                                        <div class="row">
                                            <div class="col-md-9 offset-md-3">
                                                <small class="text-muted"><?php echo htmlspecialchars($setting[\"description\"]); ?></small>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </form>
                                <hr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-muted">No logo settings found.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>