<?php
// <PERSON><PERSON><PERSON> to initialize the raffle number placeholders in the system
require_once __DIR__ . '/../config.php';

// Database connection
$conn = $pdo;

try {
    echo "<h2>Initializing Raffle Placeholders</h2>";
    
    // First, ensure we have the email_settings table
    $conn->exec("CREATE TABLE IF NOT EXISTS email_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(255) NOT NULL,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY (setting_key)
    )");
    
    // Define our random number placeholders in various formats
    $placeholders = [
        // Double-bracket format
        'number1' => rand(1, 20),
        'number2' => rand(21, 40),
        'number3' => rand(41, 60),
        'number4' => rand(61, 80),
        'number5' => rand(81, 99),
        'powerball' => rand(1, 15),
        
        // Single-bracket format
        'random_number_1' => rand(1, 20),
        'random_number_2' => rand(21, 40),
        'random_number_3' => rand(41, 60),
        'random_number_4' => rand(61, 80),
        'random_number_5' => rand(81, 99),
        'random_powerball' => rand(1, 15)
    ];
    
    // Ensure the replyToEmail placeholder is set
    $stmt = $conn->prepare("SELECT COUNT(*) FROM email_settings WHERE setting_key = 'replyToEmail'");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        // First check if we have a reply_to_email to use
        $stmt = $conn->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'reply_to_email' LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $reply_email = '' . CHURCH_EMAIL . ''; // Default
        if ($result && !empty($result['setting_value'])) {
            $reply_email = $result['setting_value'];
        }
        
        $stmt = $conn->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES ('replyToEmail', ?)");
        $stmt->execute([$reply_email]);
        echo "<p>Added replyToEmail placeholder with value: " . htmlspecialchars($reply_email) . "</p>";
    } else {
        $stmt = $conn->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'replyToEmail' LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>replyToEmail placeholder already exists with value: " . htmlspecialchars($result['setting_value'] ?? 'unknown') . "</p>";
    }
    
    // Define which placeholders require functions instead of static values
    $function_placeholders = [
        'number1', 'number2', 'number3', 'number4', 'number5', 'powerball'
    ];
    
    // Make sure we have a place to store placeholder functions
    $conn->exec("CREATE TABLE IF NOT EXISTS email_placeholder_functions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        placeholder_key VARCHAR(255) NOT NULL,
        function_code TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY (placeholder_key)
    )");
    
    // Add entries for the functional placeholders
    $added_placeholders = 0;
    foreach ($placeholders as $key => $value) {
        if (in_array($key, $function_placeholders)) {
            // First, check if this placeholder function already exists
            $stmt = $conn->prepare("SELECT COUNT(*) FROM email_placeholder_functions WHERE placeholder_key = ?");
            $stmt->execute([$key]);
            if ($stmt->fetchColumn() == 0) {
                $function_code = "return " . $value . ";"; // Static for now
                $stmt = $conn->prepare("INSERT INTO email_placeholder_functions (placeholder_key, function_code) VALUES (?, ?)");
                $stmt->execute([$key, $function_code]);
                $added_placeholders++;
                echo "<p>Added placeholder function for $key with value $value</p>";
            }
        } else {
            // For static placeholders, add them to the email_settings table
            $stmt = $conn->prepare("SELECT COUNT(*) FROM email_settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            if ($stmt->fetchColumn() == 0) {
                $stmt = $conn->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES (?, ?)");
                $stmt->execute([$key, $value]);
                $added_placeholders++;
                echo "<p>Added placeholder setting for $key with value $value</p>";
            }
        }
    }
    
    echo "<p>Added $added_placeholders new placeholders to the system</p>";
    
    // Add placeholders to settings table as well for maximum compatibility
    $added_settings = 0;
    foreach ($placeholders as $key => $value) {
        // Check if this placeholder already exists in settings
        $stmt = $conn->prepare("SELECT COUNT(*) FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        if ($stmt->fetchColumn() == 0) {
            $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)");
            $stmt->execute([$key, $value]);
            $added_settings++;
        }
    }
    
    echo "<p>Added $added_settings new placeholders to the settings table</p>";
    
    echo "<h3>Verifying email templates</h3>";
    
    // Get the existing template to verify it's using the correct placeholders
    $stmt = $conn->prepare("SELECT id, template_name, content FROM email_templates WHERE template_name = 'The Big Raffle Winner'");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($template) {
        echo "<p>Found template ID " . $template['id'] . ": " . htmlspecialchars($template['template_name']) . "</p>";
        
        // Check which placeholder format is being used
        $using_double_brackets = strpos($template['content'], '{{number1}}') !== false;
        $using_single_brackets = strpos($template['content'], '{random_number_1}') !== false;
        
        if ($using_double_brackets) {
            echo "<p>Template is using {{name}} format for placeholders</p>";
        } elseif ($using_single_brackets) {
            echo "<p>Template is using {name} format for placeholders</p>";
        } else {
            echo "<p>Template is using fixed numbers</p>";
        }
        
        // Check for reply-to email placeholders
        $using_reply_to = strpos($template['content'], '{reply_to_email}') !== false;
        $using_reply_to_email = strpos($template['content'], '{{replyToEmail}}') !== false;
        
        if ($using_reply_to) {
            echo "<p>Template is using {reply_to_email} format for reply-to</p>";
        } elseif ($using_reply_to_email) {
            echo "<p>Template is using {{replyToEmail}} format for reply-to</p>";
        } else {
            echo "<p>Template is not using dynamic reply-to email placeholder</p>";
        }
    } else {
        echo "<p>No raffle template found</p>";
    }
    
    echo "<p>Initialization complete. You can now use the email template with dynamic placeholders.</p>";
} catch (PDOException $e) {
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
} 