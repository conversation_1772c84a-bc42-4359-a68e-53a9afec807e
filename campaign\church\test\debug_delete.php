<?php
/**
 * Debug script for contact delete functionality
 */

session_start();

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

echo "<h2>Debug Contact Delete Functionality</h2>";

// Set admin session for testing
$_SESSION['admin_id'] = 1;

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

echo "<p><strong>CSRF Token:</strong> " . $_SESSION['csrf_token'] . "</p>";

// Test 1: Create a test contact
echo "<h3>Step 1: Create Test Contact</h3>";
try {
    $test_email = 'debug_test_' . time() . '@example.com';
    $test_name = 'Debug Test Contact';
    
    $stmt = $conn->prepare("INSERT INTO contacts (email, name, source) VALUES (?, ?, 'debug_test')");
    $stmt->execute([$test_email, $test_name]);
    $contact_id = $conn->lastInsertId();
    
    echo "✅ Test contact created:<br>";
    echo "ID: $contact_id<br>";
    echo "Name: $test_name<br>";
    echo "Email: $test_email<br>";
    
} catch (PDOException $e) {
    echo "❌ Error creating contact: " . $e->getMessage() . "<br>";
    exit;
}

// Test 2: Test individual delete URL
echo "<h3>Step 2: Test Individual Delete</h3>";
$delete_url = "contacts.php?delete_id=$contact_id&csrf_token=" . $_SESSION['csrf_token'];
echo "<p>Delete URL: <a href='../admin/$delete_url' target='_blank'>$delete_url</a></p>";

// Test 3: Simulate the delete process
echo "<h3>Step 3: Simulate Delete Process</h3>";
$_GET['delete_id'] = $contact_id;
$_GET['csrf_token'] = $_SESSION['csrf_token'];

// Simulate the delete logic from contacts.php
if (isset($_GET['delete_id']) && !empty($_GET['delete_id'])) {
    if (!isset($_GET['csrf_token']) || $_GET['csrf_token'] !== $_SESSION['csrf_token']) {
        echo "❌ CSRF token validation failed<br>";
    } else {
        $delete_id = intval($_GET['delete_id']);
        echo "✅ CSRF token validated<br>";
        echo "Delete ID: $delete_id<br>";
        
        try {
            $conn->beginTransaction();
            echo "✅ Transaction started<br>";
            
            // Delete associated records first
            $stmt = $conn->prepare("DELETE FROM contact_email_logs WHERE recipient_id = ?");
            $stmt->execute([$delete_id]);
            echo "✅ Deleted contact_email_logs<br>";
            
            $stmt = $conn->prepare("DELETE FROM email_tracking WHERE contact_id = ?");
            $stmt->execute([$delete_id]);
            echo "✅ Deleted email_tracking<br>";
            
            $stmt = $conn->prepare("DELETE FROM contact_group_members WHERE contact_id = ?");
            $stmt->execute([$delete_id]);
            echo "✅ Deleted contact_group_members<br>";
            
            // Delete the contact
            $stmt = $conn->prepare("DELETE FROM contacts WHERE id = ?");
            if ($stmt->execute([$delete_id])) {
                $conn->commit();
                echo "✅ Contact deleted successfully!<br>";
                
                // Verify deletion
                $stmt = $conn->prepare("SELECT COUNT(*) FROM contacts WHERE id = ?");
                $stmt->execute([$delete_id]);
                $count = $stmt->fetchColumn();
                
                if ($count == 0) {
                    echo "✅ Deletion verified - contact no longer exists<br>";
                } else {
                    echo "❌ Deletion failed - contact still exists<br>";
                }
            } else {
                $conn->rollBack();
                echo "❌ Failed to delete contact<br>";
            }
        } catch (PDOException $e) {
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            echo "❌ Error deleting contact: " . $e->getMessage() . "<br>";
        }
    }
}

// Test 4: Test bulk delete functionality
echo "<h3>Step 4: Test Bulk Delete</h3>";

// Create multiple test contacts for bulk delete
$bulk_contact_ids = [];
for ($i = 1; $i <= 3; $i++) {
    try {
        $bulk_email = "bulk_test_{$i}_" . time() . "@example.com";
        $bulk_name = "Bulk Test Contact $i";
        
        $stmt = $conn->prepare("INSERT INTO contacts (email, name, source) VALUES (?, ?, 'bulk_test')");
        $stmt->execute([$bulk_email, $bulk_name]);
        $bulk_contact_ids[] = $conn->lastInsertId();
        
        echo "✅ Created bulk test contact $i (ID: " . end($bulk_contact_ids) . ")<br>";
    } catch (PDOException $e) {
        echo "❌ Error creating bulk test contact $i: " . $e->getMessage() . "<br>";
    }
}

if (!empty($bulk_contact_ids)) {
    echo "<p>Bulk contact IDs: " . implode(', ', $bulk_contact_ids) . "</p>";
    
    // Test bulk delete AJAX handler
    echo "<h4>Testing Bulk Delete AJAX Handler</h4>";
    $bulk_delete_file = '../admin/ajax/bulk_delete_contacts.php';
    
    if (file_exists($bulk_delete_file)) {
        echo "✅ bulk_delete_contacts.php exists<br>";
        
        // Simulate AJAX request
        $post_data = json_encode([
            'contact_ids' => $bulk_contact_ids,
            'confirmation_token' => 'BULK_DELETE_CONFIRMED'
        ]);
        
        echo "AJAX data: $post_data<br>";
        
        // You would need to actually call the AJAX handler here
        // For now, just show that the data is prepared correctly
        echo "✅ Bulk delete data prepared correctly<br>";
    } else {
        echo "❌ bulk_delete_contacts.php not found<br>";
    }
}

echo "<h3>Debug Complete</h3>";
echo "<p><a href='../admin/contacts.php'>Go to Contacts Page</a></p>";
?>
