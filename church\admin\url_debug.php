<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include configurations
require_once '../config.php';

// Set header
header('Content-Type: text/html; charset=UTF-8');

// Print URL configurations
echo "<h1>URL Configuration Debug</h1>";
echo "<p><strong>Server Name:</strong> " . $_SERVER['SERVER_NAME'] . "</p>";
echo "<p><strong>Environment:</strong> " . $environment . "</p>";
echo "<p><strong>SITE_URL:</strong> " . SITE_URL . "</p>";
echo "<p><strong>ADMIN_URL:</strong> " . ADMIN_URL . "</p>";
echo "<p><strong>BASE_URL:</strong> " . BASE_URL . "</p>";
echo "<p><strong>ASSETS_URL:</strong> " . ASSETS_URL . "</p>";

// Test URL generation functions
echo "<h2>URL Generation Tests</h2>";
echo "<p><strong>admin_url_for('index.php'):</strong> " . admin_url_for('index.php') . "</p>";
echo "<p><strong>url_for('index.php'):</strong> " . url_for('index.php') . "</p>";
echo "<p><strong>get_admin_url():</strong> " . get_admin_url() . "</p>";
echo "<p><strong>get_base_url():</strong> " . get_base_url() . "</p>";

// Print some path information
echo "<h2>File Path Information</h2>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Script Filename:</strong> " . $_SERVER['SCRIPT_FILENAME'] . "</p>";
echo "<p><strong>Current File Path:</strong> " . __FILE__ . "</p>";

// Check if the admin-css-proxy.php file exists
$proxyPath = __DIR__ . '/admin-css-proxy.php';
echo "<p><strong>CSS Proxy File Exists:</strong> " . (file_exists($proxyPath) ? 'Yes' : 'No') . "</p>";
echo "<p><strong>CSS Proxy File Path:</strong> " . $proxyPath . "</p>";

// Check if the admin-style.css file exists
$cssPath = __DIR__ . '/css/admin-style.css';
echo "<p><strong>Admin CSS File Exists:</strong> " . (file_exists($cssPath) ? 'Yes' : 'No') . "</p>";
echo "<p><strong>Admin CSS File Path:</strong> " . $cssPath . "</p>";

echo "<h2>Header Links</h2>";
echo "<p>Here are some direct links to test:</p>";
echo "<p><a href='" . ADMIN_URL . "/admin-css-proxy.php' target='_blank'>Direct link to admin-css-proxy.php</a></p>";
echo "<p><a href='" . ADMIN_URL . "/css/admin-style.css' target='_blank'>Direct link to admin-style.css</a></p>";
?> 