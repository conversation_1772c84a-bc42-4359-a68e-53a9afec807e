<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Require configuration
require_once '../config.php';

// Check login
session_start();
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Database connection
$conn = $pdo;

// Get birthday templates
$stmt = $conn->prepare("
    SELECT id, template_name
    FROM email_templates 
    WHERE is_birthday_template = 1 
       OR template_name LIKE '%birthday%' 
       OR template_name LIKE '%remind%'
    ORDER BY template_name
");
$stmt->execute();
$templates = $stmt->fetchAll();

// Get members
$stmt = $conn->prepare("
    SELECT id, full_name, email, birth_date
    FROM members
    WHERE birth_date IS NOT NULL
    ORDER BY full_name
    LIMIT 20
");
$stmt->execute();
$members = $stmt->fetchAll();

// Initialize values
$preview_content = '';
$preview_subject = '';
$success_message = '';
$error_message = '';

// Process preview request
if (isset($_POST['preview'])) {
    $member_id = isset($_POST['member_id']) ? intval($_POST['member_id']) : 0;
    $template_id = isset($_POST['template_id']) ? intval($_POST['template_id']) : 0;
    $custom_date = isset($_POST['custom_date']) ? $_POST['custom_date'] : '';
    $days_ahead = isset($_POST['days_ahead']) ? intval($_POST['days_ahead']) : 0;
    
    try {
        // Get member data
        $stmt = $conn->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$member_id]);
        $member = $stmt->fetch();
        
        // Get template
        $stmt = $conn->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$template_id]);
        $template = $stmt->fetch();
        
        if (!$member) {
            throw new Exception("Member not found.");
        }
        
        if (!$template) {
            throw new Exception("Template not found.");
        }
        
        // Prepare member data for placeholders
        $memberData = [
            'full_name' => $member['full_name'],
            'email' => $member['email'],
            'phone_number' => $member['phone_number'] ?? '',
            'birth_date' => $member['birth_date'] ?? '',
            'image_path' => $member['image_path'] ?? ''
        ];
        
        // Extract first and last name
        if (!empty($member['full_name'])) {
            $nameParts = explode(' ', $member['full_name'], 2);
            $memberData['first_name'] = $nameParts[0];
            $memberData['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
        }
        
        // Add recipient-specific placeholders
        $memberData['recipient_full_name'] = $memberData['full_name'];
        $memberData['recipient_first_name'] = $memberData['first_name'];
        $memberData['recipient_email'] = $memberData['email'];
        $memberData['recipient_phone'] = $memberData['phone_number'];
        
        // Override birth date with custom date if provided
        if (!empty($custom_date)) {
            $memberData['birth_date'] = $custom_date;
        }
        
        // Calculate dates based on birth date and days ahead
        $birth_month = date('m', strtotime($memberData['birth_date']));
        $birth_day = date('d', strtotime($memberData['birth_date']));
        $birth_year = date('Y', strtotime($memberData['birth_date']));
        
        $current_year = date('Y');
        $next_year = $current_year + 1;
        
        // Calculate upcoming birthday date
        // If we're using days_ahead, simulate that day as "today"
        $simulated_today = date('Y-m-d', strtotime("+" . $days_ahead . " days"));
        $simulated_year = date('Y', strtotime($simulated_today));
        $simulated_next_year = $simulated_year + 1;
        
        // Create this year's and next year's birthday dates based on simulated today
        $this_year_birthday = $simulated_year . '-' . $birth_month . '-' . $birth_day;
        $next_year_birthday = $simulated_next_year . '-' . $birth_month . '-' . $birth_day;
        
        // Determine if birthday has passed this year already based on simulated today
        $upcoming_date = strtotime($this_year_birthday) < strtotime($simulated_today) ? $next_year_birthday : $this_year_birthday;
        
        // Calculate age on upcoming birthday
        $age = strtotime($this_year_birthday) < strtotime($simulated_today) ? 
              ($simulated_next_year - $birth_year) : 
              ($simulated_year - $birth_year);
        
        // Add days until birthday from simulated today
        $days_until = ceil((strtotime($upcoming_date) - strtotime($simulated_today)) / 86400);
        
        // Add more specific birthday placeholders
        $memberData['birthday_date'] = date('F j', strtotime($memberData['birth_date']));
        $memberData['birthday_year'] = $birth_year;
        $memberData['current_year'] = $simulated_year;
        $memberData['current_date'] = date('F j, Y', strtotime($simulated_today));
        $memberData['current_time'] = date('g:i A');
        $memberData['upcoming_birthday_date'] = date('F j, Y', strtotime($upcoming_date));
        $memberData['upcoming_birthday_day'] = date('l', strtotime($upcoming_date));
        $memberData['upcoming_birthday_formatted'] = date('l, F j, Y', strtotime($upcoming_date));
        $memberData['days_until_birthday'] = $days_until;
        $memberData['days_text'] = $days_until == 0 ? 'today' : 
                                  ($days_until == 1 ? 'tomorrow' : 
                                  "in $days_until days");
        $memberData['age'] = $age;
        $memberData['birthday_member_age'] = $age;
        
        // Also add these placeholders to "birthday_member_" equivalents
        $memberData['birthday_member_birth_date'] = $memberData['birthday_date'];
        $memberData['birthday_member_name'] = $memberData['first_name'];
        $memberData['birthday_member_full_name'] = $memberData['full_name'];
        
        // Replace placeholders
        $preview_subject = replaceTemplatePlaceholders($template['subject'], $memberData);
        $preview_content = replaceTemplatePlaceholders($template['content'], $memberData);
        
        $success_message = "Preview generated successfully.";
        
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Process send test email
if (isset($_POST['send_test'])) {
    $member_id = isset($_POST['member_id']) ? intval($_POST['member_id']) : 0;
    $template_id = isset($_POST['template_id']) ? intval($_POST['template_id']) : 0;
    $test_email = isset($_POST['test_email']) ? trim($_POST['test_email']) : '';
    $custom_date = isset($_POST['custom_date']) ? $_POST['custom_date'] : '';
    $days_ahead = isset($_POST['days_ahead']) ? intval($_POST['days_ahead']) : 0;
    
    try {
        // Validate test email
        if (empty($test_email) || !filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Please provide a valid test email address.");
        }
        
        // Get member data
        $stmt = $conn->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$member_id]);
        $member = $stmt->fetch();
        
        // Get template
        $stmt = $conn->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$template_id]);
        $template = $stmt->fetch();
        
        if (!$member) {
            throw new Exception("Member not found.");
        }
        
        if (!$template) {
            throw new Exception("Template not found.");
        }
        
        // Prepare member data for placeholders
        $memberData = [
            'full_name' => $member['full_name'],
            'email' => $member['email'],
            'phone_number' => $member['phone_number'] ?? '',
            'birth_date' => $member['birth_date'] ?? '',
            'image_path' => $member['image_path'] ?? ''
        ];
        
        // Extract first and last name
        if (!empty($member['full_name'])) {
            $nameParts = explode(' ', $member['full_name'], 2);
            $memberData['first_name'] = $nameParts[0];
            $memberData['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
        }
        
        // Add recipient-specific placeholders
        $memberData['recipient_full_name'] = $memberData['full_name'];
        $memberData['recipient_first_name'] = $memberData['first_name'];
        $memberData['recipient_email'] = $memberData['email'];
        $memberData['recipient_phone'] = $memberData['phone_number'];
        
        // Override birth date with custom date if provided
        if (!empty($custom_date)) {
            $memberData['birth_date'] = $custom_date;
        }
        
        // Calculate dates based on birth date and days ahead
        $birth_month = date('m', strtotime($memberData['birth_date']));
        $birth_day = date('d', strtotime($memberData['birth_date']));
        $birth_year = date('Y', strtotime($memberData['birth_date']));
        
        $current_year = date('Y');
        $next_year = $current_year + 1;
        
        // Calculate upcoming birthday date
        // If we're using days_ahead, simulate that day as "today"
        $simulated_today = date('Y-m-d', strtotime("+" . $days_ahead . " days"));
        $simulated_year = date('Y', strtotime($simulated_today));
        $simulated_next_year = $simulated_year + 1;
        
        // Create this year's and next year's birthday dates based on simulated today
        $this_year_birthday = $simulated_year . '-' . $birth_month . '-' . $birth_day;
        $next_year_birthday = $simulated_next_year . '-' . $birth_month . '-' . $birth_day;
        
        // Determine if birthday has passed this year already based on simulated today
        $upcoming_date = strtotime($this_year_birthday) < strtotime($simulated_today) ? $next_year_birthday : $this_year_birthday;
        
        // Calculate age on upcoming birthday
        $age = strtotime($this_year_birthday) < strtotime($simulated_today) ? 
              ($simulated_next_year - $birth_year) : 
              ($simulated_year - $birth_year);
        
        // Add days until birthday from simulated today
        $days_until = ceil((strtotime($upcoming_date) - strtotime($simulated_today)) / 86400);
        
        // Add more specific birthday placeholders
        $memberData['birthday_date'] = date('F j', strtotime($memberData['birth_date']));
        $memberData['birthday_year'] = $birth_year;
        $memberData['current_year'] = $simulated_year;
        $memberData['current_date'] = date('F j, Y', strtotime($simulated_today));
        $memberData['current_time'] = date('g:i A');
        $memberData['upcoming_birthday_date'] = date('F j, Y', strtotime($upcoming_date));
        $memberData['upcoming_birthday_day'] = date('l', strtotime($upcoming_date));
        $memberData['upcoming_birthday_formatted'] = date('l, F j, Y', strtotime($upcoming_date));
        $memberData['days_until_birthday'] = $days_until;
        $memberData['days_text'] = $days_until == 0 ? 'today' : 
                                  ($days_until == 1 ? 'tomorrow' : 
                                  "in $days_until days");
        $memberData['age'] = $age;
        $memberData['birthday_member_age'] = $age;
        
        // Also add these placeholders to "birthday_member_" equivalents
        $memberData['birthday_member_birth_date'] = $memberData['birthday_date'];
        $memberData['birthday_member_name'] = $memberData['first_name'];
        $memberData['birthday_member_full_name'] = $memberData['full_name'];
        
        // Replace placeholders
        $email_subject = replaceTemplatePlaceholders($template['subject'], $memberData);
        $email_content = replaceTemplatePlaceholders($template['content'], $memberData);
        
        // Send test email
        $sent = sendEmail(
            $test_email,
            'Test Recipient',
            $email_subject,
            $email_content,
            true,
            $memberData
        );
        
        if ($sent) {
            $success_message = "Test email sent successfully to $test_email";
        } else {
            throw new Exception("Failed to send email. " . ($last_email_error ?? "Unknown error"));
        }
        
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Page title
$page_title = "Test Birthday Emails";

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-cake me-2"></i>Birthday Email Tester</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <div class="alert alert-info">
                <p>
                    Use this tool to test birthday emails with specific dates and viewing scenarios. 
                    You can simulate how emails will look for upcoming birthdays.
                </p>
            </div>
            
            <form method="post" class="mb-4">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="member_id" class="form-label">Member</label>
                        <select id="member_id" name="member_id" class="form-select" required>
                            <option value="">Select a member...</option>
                            <?php foreach ($members as $member): ?>
                                <option value="<?php echo $member['id']; ?>" <?php echo isset($_POST['member_id']) && $_POST['member_id'] == $member['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($member['full_name']); ?> 
                                    (<?php echo htmlspecialchars(date('M d, Y', strtotime($member['birth_date']))); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="template_id" class="form-label">Template</label>
                        <select id="template_id" name="template_id" class="form-select" required>
                            <option value="">Select a template...</option>
                            <?php foreach ($templates as $template): ?>
                                <option value="<?php echo $template['id']; ?>" <?php echo isset($_POST['template_id']) && $_POST['template_id'] == $template['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($template['template_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="custom_date" class="form-label">Custom Birth Date (optional)</label>
                        <input type="date" id="custom_date" name="custom_date" class="form-control" value="<?php echo isset($_POST['custom_date']) ? htmlspecialchars($_POST['custom_date']) : ''; ?>">
                        <div class="form-text">Leave empty to use the member's actual birth date.</div>
                    </div>
                    <div class="col-md-6">
                        <label for="days_ahead" class="form-label">Simulate Days Ahead</label>
                        <input type="number" id="days_ahead" name="days_ahead" class="form-control" min="0" max="30" value="<?php echo isset($_POST['days_ahead']) ? intval($_POST['days_ahead']) : '0'; ?>">
                        <div class="form-text">How many days in the future to simulate "today" being. 0 = today.</div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="test_email" class="form-label">Send Test To Email (optional)</label>
                        <input type="email" id="test_email" name="test_email" class="form-control" value="<?php echo isset($_POST['test_email']) ? htmlspecialchars($_POST['test_email']) : ''; ?>">
                    </div>
                </div>
                
                <div class="d-flex">
                    <button type="submit" name="preview" class="btn btn-primary me-2">
                        <i class="bi bi-eye"></i> Preview
                    </button>
                    <button type="submit" name="send_test" class="btn btn-success">
                        <i class="bi bi-envelope"></i> Send Test Email
                    </button>
                </div>
            </form>
            
            <?php if (!empty($preview_content)): ?>
                <hr>
                <h5>Preview</h5>
                
                <div class="card mb-3">
                    <div class="card-header">
                        <strong>Subject:</strong> <?php echo htmlspecialchars($preview_subject); ?>
                    </div>
                    <div class="card-body">
                        <?php echo $preview_content; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 