[2025-03-27 17:17:57] Starting automated birthday system test
[2025-03-27 17:17:57] Successfully created database connection
[2025-03-27 17:17:57] BirthdayReminder system initialized
[2025-03-27 17:18:10] Test completed with status: success
[2025-03-27 17:18:10] Emails sent: 4, Failed: 0, DB logs: 17
[2025-03-27 17:18:10] Detail: Birth date field synchronization completed: 0 records updated
[2025-03-27 17:18:10] Detail: Found 3 total birthdays in next 3 days
[2025-03-27 17:18:10] Detail: Test limit set to 2 emails
[2025-03-27 17:18:10] Detail: Email test completed: 4 sent, 0 failed
[2025-03-27 17:18:10] Detail: Found 17 recent email logs in database
[2025-03-29 10:13:27] Starting automated birthday system test
[2025-03-29 10:13:27] Successfully created database connection
[2025-03-29 10:13:27] BirthdayReminder system initialized
[2025-03-29 10:13:27] Test completed with status: success
[2025-03-29 10:13:27] Emails sent: 0, Failed: 0, DB logs: 7
[2025-03-29 10:13:27] Detail: Birth date field synchronization completed: 0 records updated
[2025-03-29 10:13:27] Detail: Found 1 total birthdays in next 3 days
[2025-03-29 10:13:27] Detail: Test limit set to 2 emails
[2025-03-29 10:13:27] Detail: Email test completed: 0 sent, 0 failed
[2025-03-29 10:13:27] Detail: Found 7 recent email logs in database
[2025-03-29 10:17:00] Starting automated birthday system test
[2025-03-29 10:17:00] Successfully created database connection
[2025-03-29 10:17:00] BirthdayReminder system initialized
[2025-03-29 10:17:00] Test completed with status: success
[2025-03-29 10:17:00] Emails sent: 0, Failed: 0, DB logs: 7
[2025-03-29 10:17:00] Detail: Birth date field synchronization completed: 0 records updated
[2025-03-29 10:17:00] Detail: Found 1 total birthdays in next 3 days
[2025-03-29 10:17:00] Detail: Test limit set to 2 emails
[2025-03-29 10:17:00] Detail: Email test completed: 0 sent, 0 failed
[2025-03-29 10:17:00] Detail: Found 7 recent email logs in database
