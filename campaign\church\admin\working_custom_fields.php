<?php
/**
 * Working Custom Fields Management Page
 */

// Start session and check authentication
session_start();
if (!isset($_SESSION["admin_id"])) {
    // Redirect to login or set default admin for testing
    $_SESSION["admin_id"] = 4;
    $_SESSION["admin_username"] = "admin";
}

// Include database configuration
require_once "../config.php";

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST["action"])) {
    try {
        switch ($_POST["action"]) {
            case "create_field":
                $stmt = $pdo->prepare("
                    INSERT INTO custom_field_definitions 
                    (entity_type, field_name, field_label, field_type, is_required, field_order, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $_POST["entity_type"],
                    $_POST["field_name"],
                    $_POST["field_label"],
                    $_POST["field_type"],
                    isset($_POST["is_required"]) ? 1 : 0,
                    $_POST["field_order"] ?? 0,
                    $_SESSION["admin_id"]
                ]);
                $success = "Custom field created successfully!";
                break;
                
            case "delete_field":
                $stmt = $pdo->prepare("DELETE FROM custom_field_definitions WHERE id = ?");
                $stmt->execute([$_POST["field_id"]]);
                $success = "Custom field deleted successfully!";
                break;
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get custom fields
$entityFilter = $_GET["entity"] ?? "member";
$stmt = $pdo->prepare("
    SELECT cf.*, a.username as created_by_name
    FROM custom_field_definitions cf
    LEFT JOIN admins a ON cf.created_by = a.id
    WHERE cf.entity_type = ?
    ORDER BY cf.field_order ASC, cf.created_at DESC
");
$stmt->execute([$entityFilter]);
$customFields = $stmt->fetchAll();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Fields Management - Church Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-2 bg-dark text-white p-3">
                <h5>Admin Menu</h5>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-white" href="../dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white active" href="custom_fields.php">
                            <i class="bi bi-ui-checks-grid"></i> Custom Fields
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="logo_upload.php">
                            <i class="bi bi-image"></i> Logo Upload
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="branding_settings.php">
                            <i class="bi bi-palette"></i> Branding Settings
                        </a>
                    </li>
                </ul>
            </div>
            <div class="col-md-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="bi bi-ui-checks-grid"></i> Custom Fields Management</h1>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createFieldModal">
                        <i class="bi bi-plus-circle"></i> Add Custom Field
                    </button>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?php echo htmlspecialchars($success); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Entity Filter -->
                <div class="mb-3">
                    <label class="form-label">Filter by Entity Type:</label>
                    <select class="form-select" style="width: 200px;" onchange="window.location.href='?entity=' + this.value">
                        <option value="member" <?php echo $entityFilter === "member" ? "selected" : ""; ?>>Members</option>
                        <option value="event" <?php echo $entityFilter === "event" ? "selected" : ""; ?>>Events</option>
                        <option value="contact" <?php echo $entityFilter === "contact" ? "selected" : ""; ?>>Contacts</option>
                    </select>
                </div>

                <!-- Custom Fields Table -->
                <div class="card">
                    <div class="card-header">
                        <h5>Custom Fields for <?php echo ucfirst($entityFilter); ?>s (<?php echo count($customFields); ?>)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($customFields) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Field Name</th>
                                            <th>Label</th>
                                            <th>Type</th>
                                            <th>Required</th>
                                            <th>Order</th>
                                            <th>Created By</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($customFields as $field): ?>
                                            <tr>
                                                <td><?php echo $field["id"]; ?></td>
                                                <td><code><?php echo htmlspecialchars($field["field_name"]); ?></code></td>
                                                <td><?php echo htmlspecialchars($field["field_label"]); ?></td>
                                                <td><span class="badge bg-secondary"><?php echo $field["field_type"]; ?></span></td>
                                                <td><?php echo $field["is_required"] ? "<span class=\"badge bg-warning\">Yes</span>" : "<span class=\"badge bg-light text-dark\">No</span>"; ?></td>
                                                <td><?php echo $field["field_order"]; ?></td>
                                                <td><?php echo htmlspecialchars($field["created_by_name"] ?? "Unknown"); ?></td>
                                                <td>
                                                    <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this field?')">
                                                        <input type="hidden" name="action" value="delete_field">
                                                        <input type="hidden" name="field_id" value="<?php echo $field["id"]; ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="bi bi-inbox display-1 text-muted"></i>
                                <h4 class="text-muted">No Custom Fields Found</h4>
                                <p class="text-muted">Create your first custom field to get started.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Field Modal -->
    <div class="modal fade" id="createFieldModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="post">
                    <div class="modal-header">
                        <h5 class="modal-title">Add Custom Field</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="create_field">
                        
                        <div class="mb-3">
                            <label class="form-label">Entity Type</label>
                            <select name="entity_type" class="form-select" required>
                                <option value="member">Member</option>
                                <option value="event">Event</option>
                                <option value="contact">Contact</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Field Name</label>
                            <input type="text" name="field_name" class="form-control" required 
                                   pattern="[a-z_]+" title="Only lowercase letters and underscores allowed">
                            <div class="form-text">Use lowercase letters and underscores only (e.g., phone_number)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Field Label</label>
                            <input type="text" name="field_label" class="form-control" required>
                            <div class="form-text">Display name for the field</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Field Type</label>
                            <select name="field_type" class="form-select" required>
                                <option value="text">Text</option>
                                <option value="textarea">Textarea</option>
                                <option value="number">Number</option>
                                <option value="email">Email</option>
                                <option value="phone">Phone</option>
                                <option value="date">Date</option>
                                <option value="select">Select Dropdown</option>
                                <option value="checkbox">Checkbox</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Field Order</label>
                            <input type="number" name="field_order" class="form-control" value="0" min="0">
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" name="is_required" class="form-check-input" id="isRequired">
                            <label class="form-check-label" for="isRequired">Required Field</label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Field</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>