
/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

LOCK TABLES `email_templates` WRITE;
/*!40000 ALTER TABLE `email_templates` DISABLE KEYS */;
INSERT INTO `email_templates` VALUES (3,'Birthday Template 1','Happy Birthday, {full_name}! From Freedom Assembly Church','<div style=\"max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;\">\r\n    <div style=\"text-align: center; background-color: #ffffff; padding: 25px; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);\">\r\n        \r\n        <h1 style=\"color: #2980b9; font-size: 30px; margin-bottom: 15px;\">?? Happy Birthday! ??</h1>\r\n        \r\n        <img src=\"{member_image}\" alt=\"{full_name}\" \r\n            style=\"width: 160px; height: 160px; border-radius: 50%; margin: 15px auto; display: block; \r\n            object-fit: cover; border: 6px solid #ecf0f1; box-shadow: 0 3px 6px rgba(0,0,0,0.1);\">\r\n        \r\n        <div style=\"background-color: #ecf3fc; padding: 20px; border-radius: 10px; margin: 20px 0;\">\r\n            <p style=\"font-size: 17px; line-height: 1.6; font-weight: bold; color: #2c3e50;\">Dear {full_name},</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; color: #444;\">\r\n                On behalf of <strong>Freedom Assembly Church International</strong>, we celebrate you on this special day! ?? \r\n                May your birthday be filled with joy, love, and divine blessings.\r\n            </p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; color: #444;\">\r\n                You are a cherished part of our church family, and we pray that God?s grace and favor overflow in your life in this new year ahead.\r\n            </p>\r\n        </div>\r\n\r\n        <div style=\"background-color: #ffffff; border-left: 4px solid #3498db; padding: 20px; border-radius: 8px; margin: 20px 0;\">\r\n            <h2 style=\"color: #2980b9; font-size: 22px; margin-bottom: 12px;\">?? Birthday Scripture</h2>\r\n            <p style=\"font-style: italic; color: #555; font-size: 16px; line-height: 1.6;\">\r\n                \"For I know the plans I have for you,\" declares the LORD, \"plans to prosper you and not to harm you, plans to give you hope and a future.\" \r\n                <br><strong>- Jeremiah 29:11</strong>\r\n            </p>\r\n        </div>\r\n\r\n        <p style=\"font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;\">\r\n            May God continue to bless and guide you abundantly! ???\r\n        </p>\r\n\r\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;\">\r\n            <p style=\"color: #666; font-size: 14px;\">\r\n                With Love & Prayers,<br>\r\n                <strong>Freedom Assembly Church International</strong>\r\n            </p>\r\n        </div>\r\n\r\n    </div>\r\n</div>',1,'2025-03-02 15:07:36','2025-03-18 18:02:27','birthday'),(5,'Birthday Template 2','Happy Birthday, {full_name}! Special Wishes For You','<div style=\"max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;\">\r\n    <div style=\"text-align: center; background: linear-gradient(135deg, #ff9ff3, #6c5ce7); padding: 25px; border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);\">\r\n        \r\n        <h1 style=\"color: #fff; font-size: 32px; margin-bottom: 15px;\">???? Happy Birthday! ????</h1>\r\n        \r\n        <img src=\"{member_image}\" alt=\"{full_name}\" \r\n            style=\"width: 160px; height: 160px; border-radius: 50%; margin: 15px auto; display: block; \r\n            object-fit: cover; border: 6px solid #ecf0f1; box-shadow: 0 3px 6px rgba(0,0,0,0.1);\">\r\n        \r\n        <div style=\"background-color: #fff; padding: 20px; border-radius: 10px; margin: 20px 0;\">\r\n            <p style=\"font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;\">Dear {full_name},</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; color: #444;\">\r\n                ?? On behalf of <strong>Freedom Assembly Church International</strong>, we celebrate YOU today! \r\n                May your special day be filled with love, laughter, and countless blessings. ???\r\n            </p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; color: #444;\">\r\n                You are a cherished part of our church family, and we pray that this new year of your life brings abundant joy, divine favor, and deeper purpose in Christ. ????\r\n            </p>\r\n        </div>\r\n\r\n        <div style=\"background-color: #fff7e6; border-left: 5px solid #f39c12; padding: 20px; border-radius: 8px; margin: 20px 0;\">\r\n            <h2 style=\"color: #e67e22; font-size: 22px; margin-bottom: 12px;\">?? Birthday Scripture</h2>\r\n            <p style=\"font-style: italic; color: #555; font-size: 16px; line-height: 1.6;\">\r\n                \"For I know the plans I have for you,\" declares the LORD, \"plans to prosper you and not to harm you, plans to give you hope and a future.\" \r\n                <br><strong>- Jeremiah 29:11 ??</strong>\r\n            </p>\r\n        </div>\r\n\r\n        <p style=\"font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;\">\r\n            ???? May God continue to bless and guide you abundantly! Have a fantastic birthday celebration! ????\r\n        </p>\r\n\r\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;\">\r\n            <p style=\"color: #666; font-size: 14px;\">\r\n                With Love & Prayers, <br>\r\n                <strong>Freedom Assembly Church International ????</strong>\r\n            </p>\r\n        </div>\r\n\r\n    </div>\r\n</div>',1,'2025-03-02 15:10:22','2025-03-18 18:06:46','birthday'),(6,'Birthday Reminder Template 2','Your birthday is coming up in {days_text} - We can\'t wait to celebrate!','<div style=\"max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px; border-radius: 10px;\">\r\n    <div style=\"text-align: center; margin-bottom: 20px;\">\r\n        \r\n    </div>\r\n    \r\n    <div style=\"background-color: #fff; padding: 25px; border-radius: 12px; box-shadow: 0 5px 10px rgba(0,0,0,0.15); text-align: center;\">\r\n        <h2 style=\"color: #3498db; font-size: 26px; font-weight: bold; margin-bottom: 15px;\">?? Birthday Reminder ??</h2>\r\n        \r\n        <p style=\"font-size: 18px; color: #444;\">Dear <strong>{full_name}</strong>,</p>\r\n        \r\n        <p style=\"font-size: 18px; color: #555;\">We are thrilled to remind you that your special day is just <strong>{days_text}</strong> away! ??</p>\r\n        \r\n        <p style=\"font-size: 18px; color: #555;\">Your birthday falls on <strong>{upcoming_birthday_formatted}</strong>, and we can\'t wait to celebrate you! ??</p>\r\n        \r\n        <p style=\"font-size: 18px; color: #555;\">At Freedom Assembly Church International, we cherish you and pray for abundant blessings in your life. ????</p>\r\n        \r\n        <div style=\"text-align: center; margin: 30px 0;\">\r\n            <span style=\"display: inline-block; background-color: #3498db; color: white; padding: 14px 30px; border-radius: 8px; font-size: 18px; font-weight: bold; box-shadow: 0 4px 8px rgba(0,0,0,0.15);\">?? Happy Early Birthday! ??</span>\r\n        </div>\r\n        \r\n        <p style=\"font-size: 18px; color: #2c3e50;\">We look forward to celebrating with you on <strong>{upcoming_birthday_day}, {upcoming_birthday_date}</strong>! ??</p>\r\n        \r\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;\">\r\n            <p style=\"color: #666; font-size: 16px;\">With Love & Prayers,<br><strong>Freedom Assembly Church International Team</strong></p>\r\n        </div>\r\n    </div>\r\n    \r\n    <div style=\"text-align: center; margin-top: 20px; font-size: 14px; color: #666;\">\r\n        \r\n    </div>\r\n</div>',0,'2025-03-02 15:10:22','2025-03-18 20:16:26','general'),(7,'Birthday Template 3','Celebrating {full_name}\'s Special Day with Freedom Assembly','<div style=\"max-width: 600px; margin: 0 auto; font-family: \'Poppins\', sans-serif; color: #333; background-color: #f0f4ff; padding: 25px;\">\r\n    <div style=\"text-align: center; background: linear-gradient(135deg, #4facfe, #00f2fe); padding: 30px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">\r\n        \r\n        <h1 style=\"color: #fff; font-size: 34px; margin-bottom: 15px;\">???? Happy Birthday! ????</h1>\r\n        \r\n        <img src=\"{member_image}\" alt=\"{full_name}\" \r\n            style=\"width: 170px; height: 170px; border-radius: 50%; margin: 15px auto; display: block; \r\n            object-fit: cover; border: 6px solid #fff; box-shadow: 0 4px 8px rgba(0,0,0,0.1);\">\r\n        \r\n        <div style=\"background-color: #ffffff; padding: 20px; border-radius: 12px; margin: 20px 0;\">\r\n            <p style=\"font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;\">Dear {full_name},</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; color: #444;\">\r\n                ?? On behalf of <strong>Freedom Assembly Church International</strong>, we rejoice with you today! \r\n                May your special day overflow with happiness, love, and divine favor. ???\r\n            </p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; color: #444;\">\r\n                You are a treasured part of our family, and we pray that this year will bring you closer to your purpose and shower you with unending blessings. ????\r\n            </p>\r\n        </div>\r\n\r\n        <div style=\"background-color: #eaf6ff; border-left: 5px solid #3498db; padding: 20px; border-radius: 10px; margin: 20px 0;\">\r\n            <h2 style=\"color: #2980b9; font-size: 22px; margin-bottom: 12px;\">?? Birthday Blessing</h2>\r\n            <p style=\"font-style: italic; color: #555; font-size: 16px; line-height: 1.6;\">\r\n                \"May He give you the desire of your heart and make all your plans succeed.\" \r\n                <br><strong>- Psalm 20:4 ??</strong>\r\n            </p>\r\n        </div>\r\n\r\n        <p style=\"font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;\">\r\n            ???? Wishing you a year filled with joy, success, and divine breakthroughs! Have a beautiful birthday! ????\r\n        </p>\r\n\r\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;\">\r\n            <p style=\"color: #666; font-size: 14px;\">\r\n                With Love & Prayers, <br>\r\n                <strong>Freedom Assembly Church International ????</strong>\r\n            </p>\r\n        </div>\r\n    </div>\r\n</div>',1,'2025-03-03 07:04:44','2025-03-18 18:12:31','birthday'),(10,'Birthday Template 4','Wishing {full_name} a Blessed Birthday!','<div style=\"max-width: 600px; margin: 0 auto; font-family: \'Poppins\', sans-serif; color: #333; background-color: #fff5e1; padding: 25px;\">\r\n    <div style=\"text-align: center; background: linear-gradient(135deg, #ff7eb3, #ff758c); padding: 30px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">\r\n        \r\n        <h1 style=\"color: #fff; font-size: 34px; margin-bottom: 15px;\">???? Happy Birthday, {full_name}! ????</h1>\r\n        \r\n        <img src=\"{member_image}\" alt=\"{full_name}\" \r\n            style=\"width: 170px; height: 170px; border-radius: 50%; margin: 15px auto; display: block; \r\n            object-fit: cover; border: 6px solid #fff; box-shadow: 0 4px 8px rgba(0,0,0,0.1);\">\r\n        \r\n        <div style=\"background-color: #ffffff; padding: 20px; border-radius: 12px; margin: 20px 0;\">\r\n            <p style=\"font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;\">Dear {full_name},</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; color: #444;\">\r\n                ?? On this special day, we celebrate YOU! May your heart be filled with joy, your life with laughter, and your path with blessings. ???\r\n            </p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; color: #444;\">\r\n                You are a precious gift to our church family, and we pray that God?s grace continues to shine upon you, leading you into greater victories and boundless happiness. ????\r\n            </p>\r\n        </div>\r\n\r\n        <div style=\"background-color: #ffe6e6; border-left: 5px solid #ff4d6d; padding: 20px; border-radius: 10px; margin: 20px 0;\">\r\n            <h2 style=\"color: #d6336c; font-size: 22px; margin-bottom: 12px;\">?? Birthday Scripture</h2>\r\n            <p style=\"font-style: italic; color: #555; font-size: 16px; line-height: 1.6;\">\r\n                \"The Lord bless you and keep you; the Lord make His face shine on you and be gracious to you.\" <br><strong>- Numbers 6:24-25 ??</strong>\r\n            </p>\r\n        </div>\r\n\r\n        <p style=\"font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;\">\r\n            ???? May your year be filled with joy, love, and divine breakthroughs! Have a truly amazing birthday! ????\r\n        </p>\r\n\r\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;\">\r\n            <p style=\"color: #666; font-size: 14px;\">\r\n                With Love & Blessings, <br>\r\n                <strong>Freedom Assembly Church International ????</strong>\r\n            </p>\r\n        </div>\r\n    </div>\r\n</div>',1,'2025-03-03 08:02:16','2025-03-18 18:26:09','birthday'),(12,'Welcome Email Template','Welcome to Freedom Assembly, {full_name}!','<div style=\"max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;\">\r\n    <div style=\"text-align: center; background-color: #ffffff; padding: 30px; border-radius: 12px; box-shadow: 0 5px 10px rgba(0,0,0,0.15);\">\r\n        <h1 style=\"color: #2c3e50; font-size: 28px; font-weight: bold; margin-bottom: 20px;\">?? Welcome to Freedom Assembly Church International!</h1>\r\n        \r\n        <img src=\"{member_image}\" alt=\"{full_name}\" style=\"width: 150px; height: 150px; border-radius: 50%; margin: 20px auto; display: block; object-fit: cover; border: 5px solid #e3e6eb; box-shadow: 0 4px 8px rgba(0,0,0,0.15);\">\r\n        \r\n        <div style=\"background-color: #f9fafc; padding: 25px; border-radius: 10px; margin: 20px 0;\">\r\n            <p style=\"font-size: 18px; line-height: 1.7; color: #444;\">Dear <strong>{full_name}</strong>,</p>\r\n            <p style=\"font-size: 18px; line-height: 1.7; color: #555;\">We are overjoyed to welcome you to our church family! May this new journey bring you closer to God and fill your life with His abundant grace. ??</p>\r\n        </div>\r\n\r\n        <div style=\"background-color: #ffffff; border: 1px solid #dce1e7; border-radius: 10px; padding: 25px; margin: 20px 0;\">\r\n            <h2 style=\"color: #3498db; font-size: 22px; margin-bottom: 20px;\">?? Your Registration Details</h2>\r\n            <table style=\"width: 100%; border-collapse: collapse; text-align: left;\">\r\n                <tr>\r\n                    <td style=\"padding: 14px; border-bottom: 1px solid #eee; color: #666; font-weight: bold;\">Full Name:</td>\r\n                    <td style=\"padding: 14px; border-bottom: 1px solid #eee; color: #444;\">{full_name}</td>\r\n                </tr>\r\n                <tr>\r\n                    <td style=\"padding: 14px; border-bottom: 1px solid #eee; color: #666; font-weight: bold;\">Email:</td>\r\n                    <td style=\"padding: 14px; border-bottom: 1px solid #eee; color: #444;\">{email}</td>\r\n                </tr>\r\n                <tr>\r\n                    <td style=\"padding: 14px; color: #666; font-weight: bold;\">Phone:</td>\r\n                    <td style=\"padding: 14px; color: #444;\">{phone_number}</td>\r\n                </tr>\r\n            </table>\r\n        </div>\r\n\r\n        <p style=\"font-size: 18px; line-height: 1.7; color: #2c3e50; margin: 25px 0;\">We can\'t wait to welcome you in person at our next service! ??</p>\r\n\r\n        <a href=\"#\" style=\"background-color: #3498db; color: #ffffff; text-decoration: none; padding: 14px 28px; border-radius: 8px; display: inline-block; font-size: 18px; font-weight: bold; margin-top: 20px; box-shadow: 0 3px 6px rgba(0,0,0,0.1);\">Join Our Next Service</a>\r\n\r\n        <div style=\"margin-top: 35px; padding-top: 20px; border-top: 1px solid #ddd;\">\r\n            <p style=\"color: #666; font-size: 16px;\">With Love & Prayers,<br><strong>Freedom Assembly Church International</strong></p>\r\n        </div>\r\n    </div>\r\n</div>',0,'2025-03-03 13:05:36','2025-03-18 19:20:31','general'),(13,'Birthday Reminder Template 1','{full_name}\'s birthday is coming up in {days_text}!','<div style=\"max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 20px; border-radius: 5px;\">\r\n    <div style=\"text-align: center; margin-bottom: 20px;\">\r\n        \r\n    </div>\r\n    \r\n    <div style=\"background-color: #fff; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);\">\r\n        <h2 style=\"color: #3498db; text-align: center;\">Birthday Reminder</h2>\r\n        \r\n        <p>Dear {full_name},</p>\r\n        \r\n        <p>We\'re excited to remind you that your birthday is coming up in <strong>{days_text}</strong> on <strong>{upcoming_birthday_formatted}</strong>!</p>\r\n        \r\n        <p>At Freedom Assembly Church International, we value each member of our church family and want to make your special day memorable.</p>\r\n        \r\n        <p>We\'re looking forward to celebrating with you on {upcoming_birthday_day}, {upcoming_birthday_date}!</p>\r\n        \r\n        <div style=\"text-align: center; margin: 30px 0;\">\r\n            <div style=\"display: inline-block; background-color: #3498db; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; font-weight: bold;\">Happy Early Birthday!</div>\r\n        </div>\r\n        \r\n        <p>Many blessings,<br>\r\n        Freedom Assembly Church International Team</p>\r\n    </div>\r\n    \r\n    <div style=\"text-align: center; margin-top: 20px; font-size: 12px; color: #666;\">\r\n        <p>Freedom Assembly Church International<br>\r\n            </div>\r\n</div>',1,'2025-03-03 14:57:16','2025-03-18 18:47:34','birthday'),(14,'Member Birthday Reminder 1','Celebrate with us! {birthday_member_full_name}\'s birthday is coming up {days_text}!','<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f4f7fa;\r\n            color: #333;\r\n            -webkit-font-smoothing: antialiased;\r\n        }\r\n        .tracking-element {\r\n            position: absolute !important;\r\n            width: 1px !important;\r\n            height: 1px !important;\r\n            padding: 0 !important;\r\n            margin: -1px !important;\r\n            overflow: hidden !important;\r\n            clip: rect(0,0,0,0) !important;\r\n            white-space: nowrap !important;\r\n            border: 0 !important;\r\n            opacity: 0 !important;\r\n        }\r\n        .container {\r\n            max-width: 650px;\r\n            margin: 0 auto;\r\n            background: #ffffff;\r\n            border-radius: 15px;\r\n            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\r\n            overflow: hidden;\r\n            padding: 20px;\r\n            background: linear-gradient(135deg, #f4f7fa, #e0f7fa);\r\n        }\r\n        .birthday-card {\r\n            background: white;\r\n            border-radius: 20px;\r\n            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);\r\n            overflow: hidden;\r\n            position: relative;\r\n            text-align: center;\r\n        }\r\n        .card-header {\r\n            background: linear-gradient(135deg, #3e8e41, #6cbf6c);\r\n            padding: 60px 20px;\r\n            text-align: center;\r\n            position: relative;\r\n            overflow: hidden;\r\n            border-radius: 20px 20px 0 0;\r\n        }\r\n        .card-header h1 {\r\n            color: white;\r\n            font-size: 38px;\r\n            margin: 0;\r\n            text-shadow: 3px 3px 5px rgba(0,0,0,0.2);\r\n            font-weight: 300;\r\n            letter-spacing: 1px;\r\n        }\r\n        .photo-container {\r\n            position: relative;\r\n            width: 100%;\r\n            text-align: center;\r\n            margin-top: -75px;\r\n            padding-bottom: 20px;\r\n        }\r\n        .photo-frame {\r\n            width: 170px;\r\n            height: 170px;\r\n            display: inline-block;\r\n            border-radius: 50%;\r\n            border: 6px solid white;\r\n            box-shadow: 0 8px 25px rgba(0,0,0,0.15);\r\n            overflow: hidden;\r\n            position: relative;\r\n        }\r\n        .photo-frame img {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n            display: block;\r\n        }\r\n        .content {\r\n            padding: 25px 30px;\r\n            text-align: center;\r\n        }\r\n        .member-name {\r\n            font-size: 30px;\r\n            color: #3e8e41;\r\n            margin: 15px 0;\r\n            font-weight: bold;\r\n        }\r\n        .birthday-date {\r\n            background: #3e8e41;\r\n            color: white;\r\n            padding: 12px 20px;\r\n            border-radius: 50px;\r\n            display: inline-block;\r\n            margin: 15px 0;\r\n            font-weight: bold;\r\n            box-shadow: 0 4px 10px rgba(62, 142, 65, 0.2);\r\n        }\r\n        .celebration-text {\r\n            font-size: 18px;\r\n            line-height: 1.8;\r\n            color: #555;\r\n            margin: 20px 0;\r\n            padding: 0 20px;\r\n        }\r\n        .ways-to-celebrate {\r\n            background: #e0f7fa;\r\n            border-radius: 10px;\r\n            padding: 30px;\r\n            margin: 20px auto;\r\n            text-align: left;\r\n            max-width: 90%;\r\n        }\r\n        .ways-to-celebrate h3 {\r\n            color: #3e8e41;\r\n            margin-top: 0;\r\n            font-size: 24px;\r\n            text-align: center;\r\n        }\r\n        .ways-to-celebrate ul {\r\n            list-style-type: none;\r\n            padding: 0;\r\n            margin: 15px 0;\r\n        }\r\n        .ways-to-celebrate li {\r\n            margin: 12px 0;\r\n            padding-left: 30px;\r\n            position: relative;\r\n            font-size: 16px;\r\n        }\r\n        .ways-to-celebrate li:before {\r\n            content: \'??\';\r\n            position: absolute;\r\n            left: 10px;\r\n            color: #3e8e41;\r\n            font-size: 20px;\r\n        }\r\n        .cta-button {\r\n            display: inline-block;\r\n            background: linear-gradient(135deg, #6cbf6c, #3e8e41);\r\n            color: white;\r\n            text-decoration: none;\r\n            padding: 15px 30px;\r\n            border-radius: 50px;\r\n            font-weight: bold;\r\n            font-size: 18px;\r\n            margin-top: 20px;\r\n            transition: all 0.3s ease;\r\n            box-shadow: 0 6px 20px rgba(62, 142, 65, 0.2);\r\n        }\r\n        .cta-button:hover {\r\n            transform: translateY(-3px);\r\n            box-shadow: 0 8px 30px rgba(62, 142, 65, 0.3);\r\n        }\r\n        .footer {\r\n            text-align: center;\r\n            padding: 20px 15px;\r\n            color: #888;\r\n            font-size: 14px;\r\n            border-top: 1px solid rgba(0,0,0,0.1);\r\n            background: #f4f7fa;\r\n        }\r\n        .church-name {\r\n            color: #3e8e41;\r\n            font-weight: bold;\r\n        }\r\n        .view-online-link {\r\n            text-align: center;\r\n            padding: 15px;\r\n            color: #999;\r\n            font-size: 12px;\r\n            background: #ffffff;\r\n        }\r\n        .view-online-link a {\r\n            color: #3e8e41;\r\n            text-decoration: none;\r\n        }\r\n        .view-online-link a:hover {\r\n            text-decoration: underline;\r\n        }\r\n        .divider {\r\n            height: 1px;\r\n            background: linear-gradient(to right, transparent, rgba(0,0,0,0.1), transparent);\r\n            margin: 25px 0;\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <!-- Tracking Pixel - Hidden -->\r\n    <div class=\"tracking-element\" aria-hidden=\"true\">\r\n        {tracking_pixel}\r\n    </div>\r\n\r\n    <div class=\"container\">\r\n        <div class=\"birthday-card\">\r\n            <div class=\"card-header\">\r\n                <h1>Upcoming Birthday Celebration</h1>\r\n            </div>\r\n            \r\n            <div class=\"photo-container\">\r\n                <div class=\"photo-frame\">\r\n                    <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\">\r\n                </div>\r\n            </div>\r\n            \r\n            <div class=\"content\">\r\n                <div class=\"member-name\">{birthday_member_name}</div>\r\n                \r\n                <div class=\"birthday-date\">\r\n                    {upcoming_birthday_formatted}\r\n                </div>\r\n                \r\n                <div class=\"celebration-text\">\r\n                    <p>Dear {full_name},</p>\r\n                    <p>We are delighted to announce that our cherished church family member {birthday_member_name} \r\n                    will be celebrating their {birthday_member_age}th birthday {days_text}!</p>\r\n                    <p>Let\'s come together to make this celebration truly special.</p>\r\n                </div>\r\n                \r\n                <div class=\"divider\"></div>\r\n                \r\n                <div class=\"ways-to-celebrate\">\r\n                    <h3>Ways to Make Their Day Special</h3>\r\n                    <ul>\r\n                        <li>Send a heartfelt birthday message or prayer</li>\r\n                        <li>Share your favorite memory with them</li>\r\n                        <li>Send an encouraging scripture verse</li>\r\n                        <li>Give them a call to wish them personally</li>\r\n                    </ul>\r\n                </div>\r\n                \r\n                <a href=\"mailto:{birthday_member_email}?subject=Happy%20Birthday%20{birthday_member_name}!&body=Dear%20{birthday_member_name},%0D%0A%0D%0AWishing%20you%20a%20blessed%20birthday%20filled%20with%20God\'s%20grace%20and%20joy!%0D%0A%0D%0A[Your%20message%20here]%0D%0A%0D%0ABlessings,%0D%0A{full_name}\" class=\"cta-button\">\r\n                    Send Birthday Wishes\r\n                </a>\r\n            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <p>With Blessings,<br>\r\n                <span class=\"church-name\">Freedom Assembly Church</span></p>\r\n                            </div>\r\n        </div>\r\n        \r\n        <div class=\"view-online-link\">\r\n            <a href=\"{view_online_url}\">View this email in your browser</a>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>',1,'2025-03-03 15:03:11','2025-03-18 19:46:46','birthday'),(15,'Birthday Reminder Template 2','Your Birthday is Coming Up {days_text}!','<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n    <title>Your Birthday Reminder</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Poppins\", Arial, sans-serif;\r\n            background-color: #f4f6fc;\r\n            color: #333;\r\n        }\r\n        .container {\r\n            max-width: 650px;\r\n            margin: 0 auto;\r\n            padding: 30px;\r\n        }\r\n        .card {\r\n            background-color: #ffffff;\r\n            border-radius: 16px;\r\n            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\r\n            padding: 40px;\r\n            position: relative;\r\n            overflow: hidden;\r\n            border: 1px solid #e2e8f0;\r\n        }\r\n        .card::before {\r\n            content: \"\";\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            width: 100%;\r\n            height: 6px;\r\n            background: linear-gradient(90deg, #6a73f9, #d67aff);\r\n        }\r\n        .header {\r\n            text-align: center;\r\n            margin-bottom: 25px;\r\n        }\r\n        .header h1 {\r\n            color: #6a73f9;\r\n            font-size: 32px;\r\n            margin-bottom: 5px;\r\n            font-weight: 600;\r\n        }\r\n        .member-photo {\r\n            width: 180px;\r\n            height: 180px;\r\n            border-radius: 50%;\r\n            object-fit: cover;\r\n            margin: 0 auto 20px;\r\n            display: block;\r\n            border: 5px solid #f4f6fc;\r\n            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\r\n        }\r\n        .content {\r\n            color: #4a5568;\r\n            line-height: 1.8;\r\n            font-size: 18px;\r\n            margin-bottom: 25px;\r\n        }\r\n        .highlight-box {\r\n            background-color: #f0f5ff;\r\n            border-radius: 12px;\r\n            padding: 25px;\r\n            margin: 25px 0;\r\n            text-align: center;\r\n            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .date-highlight {\r\n            color: #6a73f9;\r\n            font-weight: bold;\r\n            font-size: 22px;\r\n            display: block;\r\n            margin: 15px 0;\r\n        }\r\n        .age-highlight {\r\n            background: linear-gradient(90deg, #6a73f9, #d67aff);\r\n            color: white;\r\n            font-weight: bold;\r\n            font-size: 20px;\r\n            padding: 6px 18px;\r\n            border-radius: 30px;\r\n            display: inline-block;\r\n            margin: 10px 0;\r\n        }\r\n        .footer {\r\n            color: #718096;\r\n            font-size: 14px;\r\n            margin-top: 30px;\r\n            text-align: center;\r\n            border-top: 1px solid #e2e8f0;\r\n            padding-top: 20px;\r\n        }\r\n        .button {\r\n            display: inline-block;\r\n            background: linear-gradient(90deg, #6a73f9, #d67aff);\r\n            color: white;\r\n            text-decoration: none;\r\n            padding: 14px 28px;\r\n            border-radius: 40px;\r\n            font-weight: 600;\r\n            margin: 20px 0;\r\n            transition: transform 0.3s ease, background 0.3s ease;\r\n        }\r\n        .button:hover {\r\n            transform: translateY(-3px);\r\n            background: linear-gradient(90deg, #4a56f6, #ad5eff);\r\n        }\r\n        .scripture {\r\n            font-style: italic;\r\n            color: #718096;\r\n            padding: 18px;\r\n            border-left: 5px solid #6a73f9;\r\n            margin: 20px 0;\r\n            background-color: #f0f5ff;\r\n        }\r\n        .social-icons {\r\n            margin-top: 20px;\r\n            text-align: center;\r\n        }\r\n        .social-icons a {\r\n            margin: 0 10px;\r\n            color: #4a5568;\r\n            font-size: 22px;\r\n            text-decoration: none;\r\n            transition: color 0.3s ease;\r\n        }\r\n        .social-icons a:hover {\r\n            color: #6a73f9;\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>Happy Birthday Soon!</h1>\r\n            </div>\r\n            \r\n            <img src=\"{member_image}\" alt=\"{full_name}\" class=\"member-photo\">\r\n            \r\n            <div class=\"content\">\r\n                <p>Hello <strong>{full_name}</strong>,</p>\r\n                <p>We wanted to remind you that your birthday is coming up <strong>{days_text}</strong>!</p>\r\n                \r\n                <div class=\"highlight-box\">\r\n                    <p>Your special day is on:</p>\r\n                    <span class=\"date-highlight\">{upcoming_birthday_formatted}</span>\r\n                    <p>You will be turning:</p>\r\n                    <span class=\"age-highlight\">{birthday_member_age} years</span>\r\n                </div>\r\n                \r\n                <p>We hope you have a wonderful celebration filled with joy and blessings!</p>\r\n                \r\n                <div class=\"scripture\">\r\n                    \"For I know the plans I have for you,\" declares the LORD, \"plans to prosper you and not to harm you, plans to give you hope and a future.\" - Jeremiah 29:11\r\n                </div>\r\n                \r\n                <p>May this new year of your life be filled with God\'s grace and favor!</p>\r\n                \r\n                            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <p>Blessings,<br><strong>Freedom Assembly Church</strong></p>\r\n                                \r\n                <div class=\"social-icons\">\r\n                    <a href=\"#\">&#x1F4F1;</a> <!-- Phone -->\r\n                    <a href=\"#\">&#x2709;</a> <!-- Email -->\r\n                    <a href=\"#\">&#x1F310;</a> <!-- Globe -->\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>',1,'2025-03-03 17:58:42','2025-03-19 10:59:53','birthday'),(17,'Member Birthday Reminder 5','Join us in celebrating a birthday {days_text}!','<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #eef5ff;\r\n            color: #333;\r\n            text-align: center;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 40px auto;\r\n            padding: 20px;\r\n        }\r\n        .card {\r\n            background: white;\r\n            border-radius: 16px;\r\n            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\r\n            overflow: hidden;\r\n            transition: all 0.3s ease;\r\n        }\r\n        .card:hover {\r\n            transform: translateY(-10px);\r\n            box-shadow: 0 15px 45px rgba(0, 0, 0, 0.2);\r\n        }\r\n        .header {\r\n            background: linear-gradient(135deg, #0062ff, #00c6ff);\r\n            padding: 40px;\r\n            color: white;\r\n            border-bottom: 1px solid #fff;\r\n        }\r\n        .header h1 {\r\n            font-size: 26px;\r\n            margin: 0;\r\n            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\r\n        }\r\n        .content {\r\n            padding: 20px;\r\n        }\r\n        .photo-frame {\r\n            position: relative;\r\n            margin: 20px auto;\r\n            width: 140px;\r\n            height: 140px;\r\n            border-radius: 50%;\r\n            border: 5px solid white;\r\n            overflow: hidden;\r\n            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\r\n        }\r\n        .member-photo {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n            transition: transform 0.3s ease;\r\n        }\r\n        .member-photo:hover {\r\n            transform: scale(1.05);\r\n        }\r\n        .member-name {\r\n            font-size: 24px;\r\n            font-weight: bold;\r\n            color: #0062ff;\r\n            margin-top: 15px;\r\n            text-transform: capitalize;\r\n            letter-spacing: 1px;\r\n        }\r\n        .birthday-info {\r\n            background: #f0f7ff;\r\n            padding: 15px;\r\n            border-radius: 10px;\r\n            margin: 20px auto;\r\n            display: inline-block;\r\n            text-align: center;\r\n            width: 90%;\r\n            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .birthday-date, .birthday-age {\r\n            font-weight: bold;\r\n            font-size: 18px;\r\n            color: #0062ff;\r\n            margin: 10px 0;\r\n        }\r\n        .message {\r\n            font-size: 16px;\r\n            line-height: 1.6;\r\n            margin: 20px 0;\r\n        }\r\n        .wish-list {\r\n            text-align: left;\r\n            background: #f8f9fa;\r\n            border-radius: 10px;\r\n            padding: 20px;\r\n            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .wish-list h3 {\r\n            color: #0062ff;\r\n            margin-top: 0;\r\n            font-size: 20px;\r\n        }\r\n        .wish-list ul {\r\n            padding-left: 20px;\r\n        }\r\n        .wish-list li {\r\n            margin: 8px 0;\r\n            font-size: 16px;\r\n            color: #555;\r\n        }\r\n        .cta-button {\r\n            display: inline-block;\r\n            background: linear-gradient(135deg, #0062ff, #00c6ff);\r\n            color: white;\r\n            text-decoration: none;\r\n            padding: 14px 25px;\r\n            border-radius: 30px;\r\n            font-weight: bold;\r\n            transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n            margin-top: 25px;\r\n            font-size: 18px;\r\n            text-transform: uppercase;\r\n        }\r\n        .cta-button:hover {\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 8px 25px rgba(0, 98, 255, 0.3);\r\n        }\r\n        .footer {\r\n            padding: 20px;\r\n            background: #eef5ff;\r\n            color: #718096;\r\n            font-size: 14px;\r\n        }\r\n        .footer-links {\r\n            text-align: center;\r\n            padding: 20px 0;\r\n            border-top: 1px solid #eee;\r\n            margin-top: 20px;\r\n        }\r\n        .footer-links .view-online {\r\n            color: #0062ff;\r\n            text-decoration: none;\r\n            font-size: 12px;\r\n        }\r\n        @media (max-width: 600px) {\r\n            .container {\r\n                padding: 15px;\r\n            }\r\n            .photo-frame {\r\n                width: 120px;\r\n                height: 120px;\r\n            }\r\n            .cta-button {\r\n                padding: 12px 20px;\r\n                font-size: 16px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>Upcoming Birthday!</h1>\r\n            </div>\r\n            <div class=\"content\">\r\n                <p>Hello <strong>{full_name}</strong>,</p>\r\n                <p>We are excited to celebrate a special birthday in our church family <strong>{days_text}</strong>!</p>\r\n                \r\n                <div class=\"photo-frame\">\r\n                    <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\" class=\"member-photo\">\r\n                </div>\r\n                \r\n                <div class=\"member-name\">{birthday_member_name}</div>\r\n                \r\n                <div class=\"birthday-info\">\r\n                    <p>Birthday on:</p>\r\n                    <div class=\"birthday-date\">{upcoming_birthday_formatted}</div>\r\n                    <div class=\"birthday-age\">{birthday_member_age} years</div>\r\n                </div>\r\n                \r\n                <div class=\"message\">\r\n                    <p>Let\'s make this day memorable for {birthday_member_name}!</p>\r\n                </div>\r\n                \r\n                <div class=\"wish-list\">\r\n                    <h3>Ways to Celebrate:</h3>\r\n                    <ul>\r\n                        <li>Send a thoughtful message or prayer</li>\r\n                        <li>Give them a call</li>\r\n                        <li>Share an inspiring scripture verse</li>\r\n                        <li>Consider a small gift or card</li>\r\n                    </ul>\r\n                </div>\r\n                \r\n                <a href=\"#\" class=\"cta-button\">Send Birthday Wishes</a>\r\n            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <p>With blessings,<br><strong>Freedom Assembly Church</strong></p>\r\n               \r\n            </div>\r\n        </div>\r\n    </div>\r\n    \r\n    <!-- Tracking Pixel & View Online Link (optional) -->\r\n    <div class=\"tracking-element\" aria-hidden=\"true\">\r\n        {tracking_pixel}\r\n    </div>\r\n    <div class=\"footer-links\">\r\n        <a href=\"{view_online_url}\" class=\"view-online\">View in browser</a>\r\n    </div>\r\n</body>\r\n</html>',1,'2025-03-03 18:33:51','2025-03-18 20:13:47','birthday'),(18,'Member Upcoming Birthday Notification 2','Celebrate with us! {birthday_member_name}\'s birthday is coming up {days_text}!','<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n    <title>Birthday Celebration</title>\r\n    <link href=\"https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap\" rel=\"stylesheet\">\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \'Poppins\', sans-serif;\r\n            background-color: #f7f6f9;\r\n            color: #333;\r\n        }\r\n        .container {\r\n            max-width: 700px;\r\n            margin: 0 auto;\r\n            padding: 20px;\r\n        }\r\n        .card {\r\n            background-color: #ffffff;\r\n            border-radius: 15px;\r\n            box-shadow: 0 10px 30px rgba(95, 46, 234, 0.1);\r\n            overflow: hidden;\r\n            position: relative;\r\n            transition: all 0.3s ease;\r\n        }\r\n        .card:hover {\r\n            transform: translateY(-5px);\r\n            box-shadow: 0 15px 40px rgba(95, 46, 234, 0.2);\r\n        }\r\n        .card-header {\r\n            background: linear-gradient(135deg, #5f2eea, #a485fd);\r\n            color: white;\r\n            padding: 40px;\r\n            text-align: center;\r\n            border-bottom: 5px solid #e9e3ff;\r\n        }\r\n        .card-header h1 {\r\n            margin: 0;\r\n            font-size: 36px;\r\n            font-weight: bold;\r\n        }\r\n        .card-header p {\r\n            margin: 10px 0 0;\r\n            opacity: 0.9;\r\n            font-size: 18px;\r\n        }\r\n        .card-body {\r\n            padding: 35px;\r\n        }\r\n        .greeting {\r\n            font-size: 18px;\r\n            margin-bottom: 20px;\r\n            line-height: 1.6;\r\n        }\r\n        .member-profile {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            position: relative;\r\n            margin: 40px 0;\r\n        }\r\n        .floating-balloons {\r\n            position: absolute;\r\n            width: 100%;\r\n            height: 100%;\r\n            top: -30px;\r\n            left: 0;\r\n            background-image: url(\"https://image.shutterstock.com/image-vector/balloons-seamless-pattern-illustration-vector-background-260nw-1291324466.jpg\");\r\n            background-size: cover;\r\n            opacity: 0.1;\r\n            z-index: 1;\r\n        }\r\n        .profile-photo {\r\n            width: 180px;\r\n            height: 180px;\r\n            border-radius: 50%;\r\n            border: 5px solid #f7f6f9;\r\n            box-shadow: 0 10px 20px rgba(95, 46, 234, 0.3);\r\n            overflow: hidden;\r\n            position: relative;\r\n            z-index: 2;\r\n        }\r\n        .profile-photo img {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n        }\r\n        .profile-name {\r\n            margin: 15px 0;\r\n            font-size: 28px;\r\n            font-weight: bold;\r\n            color: #5f2eea;\r\n            z-index: 2;\r\n        }\r\n        .birthday-details {\r\n            background-color: #ffffff;\r\n            border-radius: 12px;\r\n            padding: 20px;\r\n            margin: 25px 0;\r\n            text-align: center;\r\n            border: 1px solid #e9e3ff;\r\n        }\r\n        .birthday-date {\r\n            font-size: 22px;\r\n            font-weight: bold;\r\n            color: #5f2eea;\r\n            margin-bottom: 10px;\r\n        }\r\n        .birthday-countdown {\r\n            background: linear-gradient(135deg, #5f2eea, #a485fd);\r\n            color: white;\r\n            padding: 10px 25px;\r\n            border-radius: 25px;\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n            margin-top: 15px;\r\n        }\r\n        .celebration-suggestions {\r\n            background-color: #ffffff;\r\n            border-radius: 12px;\r\n            padding: 25px;\r\n            margin: 30px 0;\r\n        }\r\n        .celebration-suggestions h3 {\r\n            color: #5f2eea;\r\n            margin-top: 0;\r\n            font-size: 20px;\r\n        }\r\n        .celebration-list {\r\n            display: grid;\r\n            grid-template-columns: 1fr 1fr;\r\n            gap: 20px;\r\n            margin-top: 20px;\r\n        }\r\n        .celebration-item {\r\n            background-color: #f7f6f9;\r\n            padding: 15px;\r\n            border-radius: 8px;\r\n            border-left: 5px solid #5f2eea;\r\n            font-size: 16px;\r\n            text-align: center;\r\n            transition: background 0.3s ease;\r\n        }\r\n        .celebration-item:hover {\r\n            background-color: #e9e3ff;\r\n        }\r\n        .action-button {\r\n            display: block;\r\n            width: 240px;\r\n            margin: 35px auto;\r\n            padding: 16px;\r\n            background: linear-gradient(135deg, #5f2eea, #a485fd);\r\n            color: white;\r\n            text-decoration: none;\r\n            text-align: center;\r\n            border-radius: 40px;\r\n            font-weight: bold;\r\n            font-size: 18px;\r\n            box-shadow: 0 6px 18px rgba(95, 46, 234, 0.2);\r\n            transition: all 0.3s ease;\r\n        }\r\n        .action-button:hover {\r\n            transform: translateY(-3px);\r\n            box-shadow: 0 8px 20px rgba(95, 46, 234, 0.3);\r\n        }\r\n        .scripture {\r\n            font-style: italic;\r\n            color: #777;\r\n            text-align: center;\r\n            margin: 25px 0;\r\n            padding: 18px;\r\n            border-top: 1px solid #e9e3ff;\r\n            border-bottom: 1px solid #e9e3ff;\r\n        }\r\n        .footer {\r\n            background-color: #f7f6f9;\r\n            border-top: 1px solid #e9e3ff;\r\n            padding: 25px;\r\n            text-align: center;\r\n            color: #666;\r\n            font-size: 14px;\r\n        }\r\n        .church-name {\r\n            color: #5f2eea;\r\n            font-weight: bold;\r\n        }\r\n        @media (max-width: 600px) {\r\n            .celebration-list {\r\n                grid-template-columns: 1fr;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"card-header\">\r\n                <h1>Birthday Celebration</h1>\r\n                <p>Let\'s celebrate a special member of our church family!</p>\r\n            </div>\r\n            <div class=\"card-body\">\r\n                <div class=\"greeting\">\r\n                    <p>Dear <strong>{full_name}</strong>,</p>\r\n                    <p>We\'re excited to share that one of our beloved church members will be celebrating a birthday <strong>{days_text}</strong>!</p>\r\n                </div>\r\n                <div class=\"member-profile\">\r\n                    <div class=\"floating-balloons\"></div>\r\n                    <div class=\"profile-photo\">\r\n                        <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\">\r\n                    </div>\r\n                    <h2 class=\"profile-name\">{birthday_member_name}</h2>\r\n                </div>\r\n                <div class=\"birthday-details\">\r\n                    <p>Birthday Date:</p>\r\n                    <div class=\"birthday-date\">{upcoming_birthday_formatted}</div>\r\n                    <p>Turning:</p>\r\n                    <div class=\"birthday-countdown\">{birthday_member_age} Years Old</div>\r\n                </div>\r\n                <p>Let\'s make {birthday_member_name}\'s birthday special by showing our love and appreciation!</p>\r\n                <div class=\"celebration-suggestions\">\r\n                    <h3>Ways to Celebrate:</h3>\r\n                    <div class=\"celebration-list\">\r\n                        <div class=\"celebration-item\">Send a personal message of blessing</div>\r\n                        <div class=\"celebration-item\">Offer a prayer for their new year</div>\r\n                        <div class=\"celebration-item\">Share a meaningful scripture</div>\r\n                        <div class=\"celebration-item\">Consider a small gift or card</div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"scripture\">\r\n                    \"This is the day that the LORD has made; let us rejoice and be glad in it.\" - Psalm 118:24\r\n                </div>\r\n                <a href=\"#\" class=\"action-button\">Send Birthday Wishes</a>\r\n            </div>\r\n            <div class=\"footer\">\r\n                <p>With blessings from,<br><span class=\"church-name\">Freedom Assembly Church</span></p>\r\n                            </div>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>',1,'2025-03-03 18:33:56','2025-03-18 20:08:43','birthday'),(19,'Member Birthday Reminder 4','Join us in celebrating a birthday {days_text}!','<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f9fafb;\r\n            color: #2d3748;\r\n            line-height: 1.6;\r\n        }\r\n        .container {\r\n            max-width: 650px;\r\n            margin: 20px auto;\r\n            padding: 0 10px;\r\n        }\r\n        .card {\r\n            background: #ffffff;\r\n            border-radius: 25px;\r\n            box-shadow: 0 8px 40px rgba(255, 107, 0, 0.2);\r\n            overflow: hidden;\r\n            width: 100%;\r\n            margin-top: 30px;\r\n        }\r\n        .header {\r\n            background: linear-gradient(120deg, #ff6b00, #ff9e40);\r\n            padding: 40px 20px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n            border-radius: 25px 25px 0 0;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 28px;\r\n            font-weight: bold;\r\n            letter-spacing: 1px;\r\n            text-transform: uppercase;\r\n            text-shadow: 2px 2px 8px rgba(255, 107, 0, 0.3);\r\n        }\r\n        .content {\r\n            padding: 20px;\r\n            text-align: center;\r\n        }\r\n        .greeting {\r\n            font-size: 18px;\r\n            margin-bottom: 20px;\r\n            color: #4a5568;\r\n            font-weight: 500;\r\n        }\r\n        .photo-container {\r\n            margin: 0 auto 20px;\r\n            width: 160px;\r\n            height: 160px;\r\n            border-radius: 50%;\r\n            border: 5px solid #fff;\r\n            box-shadow: 0 6px 25px rgba(255, 107, 0, 0.3);\r\n            overflow: hidden;\r\n        }\r\n        .photo img {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n        }\r\n        .member-name {\r\n            font-size: 24px;\r\n            font-weight: 700;\r\n            color: #ff6b00;\r\n            margin: 10px 0;\r\n        }\r\n        .birthday-info {\r\n            background: #fff5e0;\r\n            border-radius: 15px;\r\n            padding: 25px;\r\n            margin: 30px 0;\r\n            border: 2px solid #ff9e40;\r\n            text-align: center;\r\n        }\r\n        .birthday-date {\r\n            font-size: 20px;\r\n            font-weight: 700;\r\n            color: #ff6b00;\r\n        }\r\n        .birthday-age {\r\n            font-size: 18px;\r\n            font-weight: 600;\r\n            color: white;\r\n            background: #ff6b00;\r\n            padding: 8px 20px;\r\n            border-radius: 20px;\r\n            margin-top: 15px;\r\n        }\r\n        .suggestions {\r\n            background: #fef8f2;\r\n            padding: 20px;\r\n            border-radius: 15px;\r\n            margin: 20px 0;\r\n            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .suggestions h3 {\r\n            font-size: 22px;\r\n            font-weight: 700;\r\n            color: #ff6b00;\r\n            margin: 0 0 20px;\r\n            text-align: center;\r\n        }\r\n        .suggestions ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .suggestions li {\r\n            padding: 12px 0;\r\n            color: #4a5568;\r\n            font-size: 16px;\r\n            border-bottom: 1px solid #e2e8f0;\r\n        }\r\n        .suggestions li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .cta-button {\r\n            display: inline-block;\r\n            background: linear-gradient(120deg, #ff6b00, #ff9e40);\r\n            color: #ffffff;\r\n            text-decoration: none;\r\n            padding: 12px 35px;\r\n            border-radius: 30px;\r\n            font-weight: 700;\r\n            font-size: 16px;\r\n            margin: 20px 0;\r\n            box-shadow: 0 6px 20px rgba(255, 107, 0, 0.2);\r\n            transition: all 0.3s ease;\r\n        }\r\n        .cta-button:hover {\r\n            transform: translateY(-4px);\r\n            box-shadow: 0 8px 25px rgba(255, 107, 0, 0.4);\r\n        }\r\n        .footer {\r\n            background: #f9fafb;\r\n            padding: 20px;\r\n            text-align: center;\r\n            font-size: 14px;\r\n            color: #718096;\r\n            border-top: 2px solid #f2f2f2;\r\n        }\r\n        .church-name {\r\n            color: #ff6b00;\r\n            font-weight: 700;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 15px;\r\n            font-size: 12px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #718096;\r\n            text-decoration: none;\r\n        }\r\n        .unsubscribe a:hover {\r\n            text-decoration: underline;\r\n        }\r\n        /* Mobile view optimized */\r\n        @media screen and (max-width: 480px) {\r\n            .container {\r\n                max-width: 320px;\r\n            }\r\n            .card {\r\n                border-radius: 20px;\r\n                box-shadow: 0 6px 25px rgba(255, 107, 0, 0.2);\r\n            }\r\n            .header h1 {\r\n                font-size: 22px;\r\n            }\r\n            .content {\r\n                padding: 15px;\r\n            }\r\n            .member-name {\r\n                font-size: 20px;\r\n            }\r\n            .birthday-info {\r\n                padding: 15px;\r\n                margin: 15px 0;\r\n            }\r\n            .birthday-date {\r\n                font-size: 18px;\r\n            }\r\n            .cta-button {\r\n                padding: 10px 28px;\r\n                font-size: 14px;\r\n                margin: 10px auto;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>?? Birthday Celebration Alert! ??</h1>\r\n            </div>\r\n            <div class=\"content\">\r\n                <div class=\"greeting\">\r\n                    <p>Hello <strong>{full_name}</strong>,</p>\r\n                    <p>We have some exciting news! A special member of our church family is celebrating their birthday in just <strong>{days_text}</strong>! Let?s join together and make their day memorable.</p>\r\n                </div>\r\n\r\n                <div class=\"photo-container\">\r\n                    <div class=\"photo\">\r\n                        <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\">\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"member-name\">{birthday_member_name}</div>\r\n\r\n                <div class=\"birthday-info\">\r\n                    <p><strong>Birthday Date:</strong></p>\r\n                    <div class=\"birthday-date\">{upcoming_birthday_formatted}</div>\r\n                    <p><strong>Age:</strong></p>\r\n                    <div class=\"birthday-age\">{birthday_member_age} Years</div>\r\n                </div>\r\n\r\n                <div class=\"suggestions\">\r\n                    <h3>Ways to Make Their Day Even More Special:</h3>\r\n                    <ul>\r\n                        <li>Send a thoughtful birthday message to let them know you\'re thinking of them.</li>\r\n                        <li>Share a heartfelt prayer for their health, joy, and blessings in the coming year.</li>\r\n                        <li>Send an encouraging scripture or verse to inspire them on their special day.</li>\r\n                        <li>Surprise them with a thoughtful gift or card, even a small one goes a long way!</li>\r\n                    </ul>\r\n                </div>\r\n\r\n                <a href=\"mailto:{birthday_member_email}?subject=Happy%20Birthday%20Wishes&body=Dear%20{birthday_member_name},%0A%0AI%20wanted%20to%20wish%20you%20a%20very%20happy%20birthday!%20May%20your%20day%20be%20filled%20with%20joy.%0A%0ABlessings,%0A{full_name}\" class=\"cta-button\">Send Birthday Wishes</a>\r\n            </div>\r\n\r\n            <div class=\"footer\">\r\n                <p>With love from <span class=\"church-name\">Freedom Assembly Church</span></p>\r\n                <div class=\"unsubscribe\">\r\n                    <a href=\"#\">Unsubscribe from these updates</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    {tracking_pixel}\r\n</body>\r\n</html>',1,'2025-03-03 18:46:54','2025-03-18 20:03:08','birthday'),(20,'Member Birthday Reminder 3','Help celebrate {birthday_member_name}\'s special day {days_text}!','<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Joyous Birthday Celebration!</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f6f7fb;\r\n            color: #333;\r\n            line-height: 1.6;\r\n        }\r\n        .container {\r\n            max-width: 700px;\r\n            margin: 0 auto;\r\n            padding: 20px;\r\n        }\r\n        .card {\r\n            background-color: #fff;\r\n            border-radius: 20px;\r\n            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);\r\n            overflow: hidden;\r\n            position: relative;\r\n            transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n        }\r\n        .card:hover {\r\n            transform: scale(1.02);\r\n            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);\r\n        }\r\n        .header {\r\n            background: linear-gradient(135deg, #ff6a00, #ff8c00);\r\n            color: white;\r\n            padding: 40px 30px;\r\n            text-align: center;\r\n            border-bottom: 5px solid #fff;\r\n        }\r\n        .header h1 {\r\n            font-size: 32px;\r\n            margin: 0;\r\n            font-weight: bold;\r\n            text-transform: uppercase;\r\n        }\r\n        .header p {\r\n            font-size: 16px;\r\n            opacity: 0.9;\r\n        }\r\n        .content {\r\n            padding: 30px;\r\n            background-color: #ffffff;\r\n            text-align: center;\r\n        }\r\n        .member-container {\r\n            position: relative;\r\n            margin-bottom: 30px;\r\n        }\r\n        .celebration-icon {\r\n            position: absolute;\r\n            width: 100%;\r\n            height: 100%;\r\n            top: 0;\r\n            left: 0;\r\n            background-image: url(\'data:image/svg+xml,%3Csvg xmlns=\"http://www.w3.org/2000/svg\" width=\"200\" height=\"200\" viewBox=\"0 0 200 200\"%3E%3Cg fill=\"none\"%3E%3Cpath fill=\"%23FFB800\" d=\"M100 10l14 41.9h43.4l-35 26.6 14 41.9-35-26.6-35 26.6 14-41.9-35-26.6h43.4z\" /%3E%3C/g%3E%3C/svg%3E\');\r\n            background-repeat: no-repeat;\r\n            background-position: center;\r\n            opacity: 0.1;\r\n            z-index: 1;\r\n        }\r\n        .photo-frame {\r\n            width: 180px;\r\n            height: 180px;\r\n            margin: 0 auto;\r\n            z-index: 2;\r\n        }\r\n        .photo-circle {\r\n            width: 160px;\r\n            height: 160px;\r\n            border-radius: 50%;\r\n            border: 5px solid #fff;\r\n            overflow: hidden;\r\n            margin: 0 auto;\r\n            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n        }\r\n        .member-photo {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n        }\r\n        .member-name {\r\n            color: #ff6a00;\r\n            font-size: 28px;\r\n            font-weight: bold;\r\n            margin-top: 15px;\r\n        }\r\n        .birthday-info {\r\n            background-color: #fff9e6;\r\n            padding: 20px;\r\n            margin: 25px 0;\r\n            border-radius: 10px;\r\n            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);\r\n            border-left: 5px solid #ff6a00;\r\n            color: #4a4a4a;\r\n        }\r\n        .birthday-info p {\r\n            margin: 10px 0;\r\n            font-size: 18px;\r\n        }\r\n        .birthday-date {\r\n            font-weight: bold;\r\n            color: #ff6a00;\r\n            font-size: 22px;\r\n        }\r\n        .birthday-age {\r\n            background-color: #ff6a00;\r\n            color: white;\r\n            padding: 8px 18px;\r\n            border-radius: 20px;\r\n            font-weight: bold;\r\n            font-size: 20px;\r\n        }\r\n        .action-buttons {\r\n            margin: 30px 0;\r\n        }\r\n        .button {\r\n            display: inline-block;\r\n            background-color: #ff6a00;\r\n            color: white;\r\n            text-decoration: none;\r\n            padding: 15px 30px;\r\n            border-radius: 30px;\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n            box-shadow: 0 6px 18px rgba(255, 105, 0, 0.3);\r\n            transition: background-color 0.3s, box-shadow 0.3s ease;\r\n        }\r\n        .button:hover {\r\n            background-color: #ff8c00;\r\n            box-shadow: 0 8px 20px rgba(255, 105, 0, 0.4);\r\n        }\r\n        .button.secondary {\r\n            background-color: white;\r\n            color: #ff6a00;\r\n            border: 2px solid #ff6a00;\r\n        }\r\n        .celebration-ideas {\r\n            background-color: #fff1e6;\r\n            border-radius: 10px;\r\n            padding: 20px;\r\n            margin: 25px 0;\r\n        }\r\n        .celebration-ideas h3 {\r\n            color: #ff6a00;\r\n            margin-top: 0;\r\n            font-size: 24px;\r\n            text-align: center;\r\n        }\r\n        .celebration-ideas ul {\r\n            list-style: none;\r\n            padding: 0;\r\n        }\r\n        .celebration-ideas li {\r\n            background-color: white;\r\n            padding: 12px;\r\n            margin: 10px 0;\r\n            border-radius: 8px;\r\n            border-left: 4px solid #ff6a00;\r\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n        }\r\n        .celebration-ideas li i {\r\n            color: #ff6a00;\r\n            margin-right: 12px;\r\n        }\r\n        .message {\r\n            font-style: italic;\r\n            color: #666;\r\n            text-align: center;\r\n            padding: 25px;\r\n            background-color: #fff9e6;\r\n            border-radius: 10px;\r\n            margin: 30px 0;\r\n        }\r\n        .footer {\r\n            background-color: #f6f7fb;\r\n            padding: 20px;\r\n            text-align: center;\r\n            color: #a1a1a1;\r\n            font-size: 14px;\r\n            border-top: 3px solid #ff6a00;\r\n        }\r\n        .church-name {\r\n            font-weight: bold;\r\n            color: #ff6a00;\r\n        }\r\n        @media (max-width: 600px) {\r\n            .container {\r\n                padding: 15px;\r\n            }\r\n            .header h1 {\r\n                font-size: 28px;\r\n            }\r\n            .content {\r\n                padding: 15px;\r\n            }\r\n            .photo-frame {\r\n                width: 150px;\r\n                height: 150px;\r\n            }\r\n            .photo-circle {\r\n                width: 140px;\r\n                height: 140px;\r\n            }\r\n            .button {\r\n                padding: 12px 24px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>?? Birthday Celebration! ??</h1>\r\n                <p>Let\'s make his day unforgettable</p>\r\n            </div>\r\n            <div class=\"content\">\r\n                <p>Hello <strong>{full_name}</strong>,</p>\r\n                <p>We?re excited to celebrate <strong>{birthday_member_name}</strong>\'s special day in just <strong>{days_text}</strong>! Here\'s how you can join the celebration:</p>\r\n\r\n                <div class=\"member-container\">\r\n                    <div class=\"celebration-icon\"></div>\r\n                    <div class=\"photo-frame\">\r\n                        <div class=\"photo-circle\">\r\n                            <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\" class=\"member-photo\">\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"member-name\">{birthday_member_name}</div>\r\n                </div>\r\n\r\n                <div class=\"birthday-info\">\r\n                    <p>Birthday Date:</p>\r\n                    <div class=\"birthday-date\">{upcoming_birthday_formatted}</div>\r\n                    <p>Turning:</p>\r\n                    <div class=\"birthday-age\">{birthday_member_age} Years</div>\r\n                </div>\r\n\r\n                <div class=\"action-buttons\">\r\n                    <a href=\"mailto:{birthday_member_email}?subject=Happy%20Birthday%20Wishes&body=Dear%20{birthday_member_name},%0A%0AWishing%20you%20a%20wonderful%20birthday%20filled%20with%20joy!%20Best%20wishes%20from%20your%20church%20family.%0A%0A{full_name}\" class=\"button\">Send Birthday Wishes</a>\r\n                    <a href=\"tel:{birthday_member_phone}\" class=\"button secondary\">Call to Celebrate</a>\r\n                </div>\r\n\r\n                <div class=\"celebration-ideas\">\r\n                    <h3>Ways to Make Their Day Special</h3>\r\n                    <ul>\r\n                        <li><i>??</i> Send a thoughtful gift or card</li>\r\n                        <li><i>??</i> Share a prayer or blessing</li>\r\n                        <li><i>??</i',1,'2025-03-03 19:26:45','2025-03-18 19:59:03','birthday'),(21,'Member Upcoming Birthday Notification 1','Celebrate with us! {full_name}\'s birthday is coming up {days_text}!','<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \'Helvetica Neue\', Arial, sans-serif;\r\n            background-color: #f9f9f9;\r\n            color: #333333;\r\n            line-height: 1.5;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 30px auto;\r\n            padding: 0 20px;\r\n        }\r\n        .card {\r\n            background: #ffffff;\r\n            border-radius: 20px;\r\n            box-shadow: 0 12px 30px rgba(0, 122, 255, 0.15);\r\n            overflow: hidden;\r\n            width: 100%;\r\n        }\r\n        .header {\r\n            background: linear-gradient(135deg, #6a11cb, #2575fc);\r\n            padding: 30px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n            border-radius: 20px 20px 0 0;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 32px;\r\n            font-weight: 700;\r\n        }\r\n        .header p {\r\n            margin: 10px 0 0;\r\n            font-size: 16px;\r\n            opacity: 0.85;\r\n        }\r\n        .content {\r\n            padding: 30px;\r\n            text-align: center;\r\n        }\r\n        .greeting {\r\n            font-size: 18px;\r\n            margin: 0 0 20px;\r\n            color: #555555;\r\n        }\r\n        .profile {\r\n            margin: 20px 0;\r\n        }\r\n        .photo {\r\n            width: 150px;\r\n            height: 150px;\r\n            border-radius: 50%;\r\n            border: 5px solid #ffffff;\r\n            box-shadow: 0 10px 30px rgba(0, 122, 255, 0.2);\r\n            overflow: hidden;\r\n            margin: 0 auto;\r\n        }\r\n        .photo img {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n        }\r\n        .member-name {\r\n            font-size: 24px;\r\n            font-weight: 700;\r\n            color: #6a11cb;\r\n            margin: 15px 0;\r\n        }\r\n        .birthday-details {\r\n            background: #e9f4ff;\r\n            border-radius: 12px;\r\n            padding: 20px;\r\n            margin: 25px 0;\r\n            display: inline-block;\r\n            width: 100%;\r\n            text-align: left;\r\n        }\r\n        .birthday-date {\r\n            font-size: 18px;\r\n            font-weight: 600;\r\n            color: #2575fc;\r\n            margin-bottom: 6px;\r\n        }\r\n        .birthday-age {\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n            color: #ffffff;\r\n            background: #6a11cb;\r\n            padding: 6px 14px;\r\n            border-radius: 30px;\r\n            display: inline-block;\r\n            margin-top: 10px;\r\n        }\r\n        .countdown {\r\n            font-size: 16px;\r\n            color: #555555;\r\n            margin-top: 12px;\r\n        }\r\n        .suggestions {\r\n            background: #e9f4ff;\r\n            border-radius: 12px;\r\n            padding: 20px;\r\n            margin: 25px 0;\r\n            text-align: left;\r\n        }\r\n        .suggestions h3 {\r\n            color: #2575fc;\r\n            font-size: 18px;\r\n            margin: 0 0 10px;\r\n        }\r\n        .suggestions ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .suggestions li {\r\n            padding: 8px 0;\r\n            border-bottom: 1px solid #d3e6ff;\r\n            font-size: 16px;\r\n            color: #555555;\r\n        }\r\n        .suggestions li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .action-button {\r\n            display: inline-block;\r\n            background: linear-gradient(135deg, #6a11cb, #2575fc);\r\n            color: #ffffff;\r\n            text-decoration: none;\r\n            padding: 12px 30px;\r\n            border-radius: 25px;\r\n            font-weight: 600;\r\n            font-size: 16px;\r\n            margin: 20px 0;\r\n            box-shadow: 0 6px 18px rgba(0, 122, 255, 0.3);\r\n        }\r\n        .footer {\r\n            background: #f9f9f9;\r\n            padding: 20px;\r\n            text-align: center;\r\n            font-size: 14px;\r\n            color: #777777;\r\n            border-top: 1px solid #d3e6ff;\r\n        }\r\n        .church-name {\r\n            color: #2575fc;\r\n            font-weight: bold;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 10px;\r\n            font-size: 12px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #777777;\r\n            text-decoration: none;\r\n        }\r\n        @media (max-width: 480px) {\r\n            .container {\r\n                padding: 0 10px;\r\n            }\r\n            .header {\r\n                padding: 20px;\r\n            }\r\n            .header h1 {\r\n                font-size: 24px;\r\n            }\r\n            .content {\r\n                padding: 20px;\r\n            }\r\n            .photo {\r\n                width: 120px;\r\n                height: 120px;\r\n            }\r\n            .member-name {\r\n                font-size: 20px;\r\n            }\r\n            .action-button {\r\n                padding: 10px 25px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>?? Birthday Celebration! ??</h1>\r\n                <p>Join us in celebrating a special member of our church family</p>\r\n            </div>\r\n            \r\n            <div class=\"content\">\r\n                <div class=\"greeting\">\r\n                    <p>Hello {member_name},</p>\r\n                    <p>We?re excited to celebrate {birthday_member_name}?s birthday, just {days_until_birthday} days away!</p>\r\n                </div>\r\n                \r\n                <div class=\"profile\">\r\n                    <div class=\"photo\">\r\n                        <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\">\r\n                    </div>\r\n                    <div class=\"member-name\">{birthday_member_name}</div>\r\n                </div>\r\n                \r\n                <div class=\"birthday-details\">\r\n                    <div class=\"birthday-date\">Birthday: {upcoming_birthday_formatted}</div>\r\n                    <div class=\"birthday-age\">Turning {birthday_member_age} years</div>\r\n                    <div class=\"countdown\">Only {days_until_birthday} days left! ??</div>\r\n                </div>\r\n                \r\n                <div class=\"suggestions\">\r\n                    <h3>Ways to Bless {birthday_member_name}:</h3>\r\n                    <ul>\r\n                        <li>Send a heartfelt birthday message</li>\r\n                        <li>Pray for their growth and blessings in the coming year</li>\r\n                        <li>Share a scripture that encourages them</li>\r\n                        <li>Consider gifting something meaningful</li>\r\n                    </ul>\r\n                </div>\r\n                \r\n                <a href=\"mailto:{birthday_member_email}?subject=Happy%20Birthday%20{birthday_member_name}!&body=Dear%20{birthday_member_name},%0D%0A%0D%0AWishing%20you%20a%20joyous%20birthday%20filled%20with%20love%20and%20blessings%20from%20your%20Freedom%20Assembly%20Church%20family!%0D%0A%0D%0A[Add%20your%20personal%20message]%0D%0A%0D%0ABlessings,%0D%0A{full_name}\" class=\"action-button\">Send Birthday Wishes</a>\r\n            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <p>With blessings from <span class=\"church-name\">Freedom Assembly Church</span></p>\r\n                <div class=\"unsubscribe\">\r\n                    <a href=\"#\">Unsubscribe from these updates</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    {tracking_pixel}\r\n</body>\r\n</html>',1,'2025-03-03 19:32:05','2025-03-18 20:54:51','birthday'),(23,'Newsletter 03','A Special Message from {church_name}','<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Message from {church_name}</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f0fff0;\r\n            color: #2d3748;\r\n            line-height: 1.5;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 20px auto;\r\n            padding: 0 10px;\r\n        }\r\n        .card {\r\n            background: #ffffff;\r\n            border-radius: 20px;\r\n            box-shadow: 0 8px 25px rgba(0, 201, 0, 0.2);\r\n            overflow: hidden;\r\n            width: 100%;\r\n            border: 2px solid #00c900;\r\n        }\r\n        .header {\r\n            background: linear-gradient(135deg, #00c900, #ffeb3b);\r\n            padding: 30px 20px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 26px;\r\n            font-weight: bold;\r\n            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .header p {\r\n            margin: 6px 0 0;\r\n            font-size: 15px;\r\n            opacity: 0.95;\r\n        }\r\n        .content {\r\n            padding: 25px;\r\n            text-align: center;\r\n        }\r\n        .member-image-container {\r\n            text-align: center;\r\n            margin: 20px 0;\r\n        }\r\n        .member-image {\r\n            width: 120px;\r\n            height: 120px;\r\n            border-radius: 60px;\r\n            border: 3px solid #00c900;\r\n            box-shadow: 0 4px 12px rgba(0, 201, 0, 0.2);\r\n            object-fit: cover;\r\n        }\r\n        .greeting {\r\n            font-size: 17px;\r\n            margin: 0 0 20px;\r\n            color: #4a5568;\r\n        }\r\n        .message {\r\n            background: #f5fff5;\r\n            border-radius: 12px;\r\n            padding: 20px;\r\n            margin: 20px 0;\r\n            font-size: 16px;\r\n            color: #2d3748;\r\n            border: 2px dashed #ffeb3b;\r\n        }\r\n        .member-info {\r\n            background: #f5fff5;\r\n            border-radius: 12px;\r\n            padding: 20px;\r\n            margin: 20px 0;\r\n            text-align: left;\r\n            border: 2px solid #e0f0e0;\r\n        }\r\n        .member-info h3 {\r\n            color: #00c900;\r\n            margin: 0 0 10px;\r\n            font-size: 17px;\r\n        }\r\n        .member-info ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .member-info li {\r\n            padding: 8px 0;\r\n            border-bottom: 1px solid #e0f0e0;\r\n            font-size: 14px;\r\n            color: #4a5568;\r\n        }\r\n        .member-info li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .suggestions {\r\n            background: #f5fff5;\r\n            border-radius: 12px;\r\n            padding: 15px;\r\n            margin: 20px 0;\r\n            text-align: left;\r\n        }\r\n        .suggestions h3 {\r\n            color: #00c900;\r\n            font-size: 17px;\r\n            margin: 0 0 10px;\r\n        }\r\n        .suggestions ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .suggestions li {\r\n            padding: 8px 0;\r\n            border-bottom: 1px solid #e0f0e0;\r\n            font-size: 14px;\r\n            color: #4a5568;\r\n        }\r\n        .suggestions li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .footer {\r\n            background: #f0fff0;\r\n            padding: 20px;\r\n            text-align: center;\r\n            font-size: 12px;\r\n            color: #718096;\r\n            border-top: 1px solid #e0f0e0;\r\n        }\r\n        .church-name {\r\n            color: #00c900;\r\n            font-weight: bold;\r\n        }\r\n        .sender-info {\r\n            margin: 15px 0;\r\n            font-size: 14px;\r\n            color: #4a5568;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 8px;\r\n            font-size: 10px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #718096;\r\n            text-decoration: none;\r\n        }\r\n        @media (max-width: 480px) {\r\n            .container {\r\n                padding: 0 5px;\r\n            }\r\n            .card {\r\n                border-radius: 15px;\r\n            }\r\n            .header {\r\n                padding: 20px;\r\n            }\r\n            .header h1 {\r\n                font-size: 22px;\r\n            }\r\n            .content {\r\n                padding: 20px;\r\n            }\r\n            .message {\r\n                padding: 15px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>Message from {church_name}</h1>\r\n                <p>To our valued member</p>\r\n            </div>\r\n            \r\n            <div class=\"content\">\r\n                <div class=\"member-image-container\">\r\n                    <img src=\"{member_image}\" alt=\"{recipient_full_name}\" class=\"member-image\">\r\n                </div>\r\n                \r\n                <div class=\"greeting\">\r\n                    <p>Dear {recipient_first_name},</p>\r\n                </div>\r\n                \r\n                <div class=\"message\">\r\n                    <p>[Insert your message content here]</p>\r\n                </div>\r\n                \r\n                <div class=\"member-info\">\r\n                    <h3>Your Information:</h3>\r\n                    <ul>\r\n                        <li>Full Name: {recipient_full_name}</li>\r\n                        <li>Email: {recipient_email}</li>\r\n                        <li>Phone: {recipient_phone}</li>\r\n                    </ul>\r\n                </div>\r\n                \r\n                <div class=\"suggestions\">\r\n                    <h3>Stay Connected:</h3>\r\n                    <ul>\r\n                        <li>Join our weekly services</li>\r\n                        <li>Participate in community events</li>\r\n                        <li>Follow us on social media</li>\r\n                        <li>Share your testimonies</li>\r\n                    </ul>\r\n                </div>\r\n            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <div class=\"sender-info\">\r\n                    <p>Sent with love by {sender_name}<br>\r\n                    {sender_email}</p>\r\n                </div>\r\n                <p>This message was sent to {recipient_email} as part of a communication to {total_recipients} members.</p>\r\n                <p class=\"church-name\">{church_name}</p>\r\n                <div class=\"unsubscribe\">\r\n                    <a href=\"#\">Manage your email preferences</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>',0,'2025-03-04 16:30:38','2025-03-18 14:04:05','bulk'),(24,'Newsletter 02','Newsletter from {church_name}','<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <title>Newsletter from {church_name}</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f5f5f5;\r\n            color: #333333;\r\n            line-height: 1.6;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 20px auto;\r\n            padding: 0 10px;\r\n            background: #ffffff;\r\n            border-radius: 10px;\r\n            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .header {\r\n            background: linear-gradient(135deg, #3498db, #2ecc71);\r\n            padding: 25px 20px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n            border-top-left-radius: 10px;\r\n            border-top-right-radius: 10px;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 24px;\r\n            font-weight: 600;\r\n        }\r\n        .header p {\r\n            margin: 5px 0 0;\r\n            font-size: 14px;\r\n            opacity: 0.9;\r\n        }\r\n        .content {\r\n            padding: 20px;\r\n        }\r\n        .greeting {\r\n            font-size: 16px;\r\n            margin: 0 0 15px;\r\n            color: #444444;\r\n        }\r\n        .profile {\r\n            text-align: center;\r\n            margin: 20px 0;\r\n        }\r\n        .photo {\r\n            width: 120px;\r\n            height: 120px;\r\n            border-radius: 50%;\r\n            border: 3px solid #f5f5f5;\r\n            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);\r\n            overflow: hidden;\r\n            margin: 0 auto;\r\n        }\r\n        .photo img {\r\n            width: 100%;\r\n            height: 100%;\r\n            display: block;\r\n            object-fit: cover;\r\n        }\r\n        .section {\r\n            background: #fafafa;\r\n            border-radius: 8px;\r\n            padding: 15px;\r\n            margin: 15px 0;\r\n            border: 1px solid #e0e0e0;\r\n        }\r\n        .section h2 {\r\n            color: #3498db;\r\n            font-size: 18px;\r\n            margin: 0 0 10px;\r\n            font-weight: 600;\r\n        }\r\n        .section p {\r\n            margin: 0 0 10px;\r\n            font-size: 14px;\r\n            color: #555555;\r\n        }\r\n        .updates {\r\n            margin: 20px 0;\r\n        }\r\n        .updates h3 {\r\n            color: #2ecc71;\r\n            font-size: 16px;\r\n            margin: 0 0 10px;\r\n            font-weight: 600;\r\n        }\r\n        .updates ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .updates li {\r\n            padding: 8px 0;\r\n            border-bottom: 1px solid #e0e0e0;\r\n            font-size: 14px;\r\n            color: #555555;\r\n        }\r\n        .updates li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .footer {\r\n            background: #f5f5f5;\r\n            padding: 15px;\r\n            text-align: center;\r\n            font-size: 12px;\r\n            color: #777777;\r\n            border-top: 1px solid #e0e0e0;\r\n            border-bottom-left-radius: 10px;\r\n            border-bottom-right-radius: 10px;\r\n        }\r\n        .church-name {\r\n            color: #3498db;\r\n            font-weight: bold;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 8px;\r\n            font-size: 10px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #777777;\r\n            text-decoration: none;\r\n        }\r\n        @media (max-width: 480px) {\r\n            .container {\r\n                padding: 0 5px;\r\n            }\r\n            .header {\r\n                padding: 20px;\r\n            }\r\n            .header h1 {\r\n                font-size: 20px;\r\n            }\r\n            .content {\r\n                padding: 15px;\r\n            }\r\n            .photo {\r\n                width: 100px;\r\n                height: 100px;\r\n            }\r\n            .section h2 {\r\n                font-size: 16px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>Newsletter from {church_name}</h1>\r\n                <p>Connecting with all {total_recipients} of our members</p>\r\n            </div>\r\n            \r\n            <div class=\"content\">\r\n                <div class=\"greeting\">\r\n                    <p>Hello {recipient_first_name},</p>\r\n                    <p>Welcome to this month\'s update from {church_name}! We\'re so glad to share this with you and all {total_recipients} of our wonderful members.</p>\r\n                </div>\r\n                \r\n                <div class=\"profile\">\r\n                    <div class=\"photo\">\r\n                        <img src=\"{member_image}\" alt=\"{recipient_full_name}\">\r\n                    </div>\r\n                </div>\r\n                \r\n                <div class=\"section\">\r\n                    <h2>A Message from {sender_name}</h2>\r\n                    <p>Greetings, {recipient_full_name}! I hope this newsletter finds you well. At {church_name}, we\'re committed to fostering a community of faith, love, and support. Thank you for being a vital part of our family!</p>\r\n                </div>\r\n                \r\n                <div class=\"updates\">\r\n                    <h3>Community Updates</h3>\r\n                    <ul>\r\n                        <li>Join us this Sunday for a special service at 10 AM.</li>\r\n                        <li>Our next fellowship event is scheduled for next Saturday\'s details to follow!</li>\r\n                        <li>We\'re launching a new outreach program\'s stay tuned for ways to get involved.</li>\r\n                        <li>Prayer meetings are now every Wednesday at 7 PM.</li>\r\n                    </ul>\r\n                </div>\r\n            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <p>Sent with blessings from <span class=\"church-name\">{church_name}</span><br>By {sender_name}</p>\r\n                <div class=\"unsubscribe\">\r\n                    <a href=\"#\">Unsubscribe from these updates</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>',0,'2025-03-04 16:41:38','2025-03-18 13:58:19','bulk'),(25,'Newsletter 01','Newsletter from {church_name}','<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <title>Newsletter from {church_name}</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f7f9fc;\r\n            color: #333333;\r\n            line-height: 1.6;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 20px auto;\r\n            padding: 0 10px;\r\n            background: #ffffff;\r\n            border-radius: 10px;\r\n            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .header {\r\n            background: linear-gradient(135deg, #1e3c72, #2a5298);\r\n            padding: 30px 20px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n            border-top-left-radius: 10px;\r\n            border-top-right-radius: 10px;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 26px;\r\n            font-weight: 600;\r\n        }\r\n        .header p {\r\n            margin: 5px 0 0;\r\n            font-size: 14px;\r\n            opacity: 0.9;\r\n        }\r\n        .content {\r\n            padding: 20px;\r\n        }\r\n        .greeting {\r\n            font-size: 16px;\r\n            margin: 0 0 15px;\r\n            color: #444444;\r\n        }\r\n        .message {\r\n            background: #f0f4f8;\r\n            border-radius: 8px;\r\n            padding: 15px;\r\n            margin: 20px 0;\r\n            border: 1px solid #d1d9e6;\r\n        }\r\n        .message h2 {\r\n            color: #1e3c72;\r\n            font-size: 18px;\r\n            margin: 0 0 10px;\r\n            font-weight: 600;\r\n        }\r\n        .message p {\r\n            margin: 0 0 10px;\r\n            font-size: 14px;\r\n            color: #555555;\r\n        }\r\n        .updates {\r\n            margin: 20px 0;\r\n        }\r\n        .updates h3 {\r\n            color: #2a5298;\r\n            font-size: 16px;\r\n            margin: 0 0 10px;\r\n            font-weight: 600;\r\n        }\r\n        .updates ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .updates li {\r\n            padding: 8px 0;\r\n            border-bottom: 1px solid #e0e0e0;\r\n            font-size: 14px;\r\n            color: #555555;\r\n        }\r\n        .updates li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .cta {\r\n            text-align: center;\r\n            margin: 20px 0;\r\n        }\r\n        .cta a {\r\n            display: inline-block;\r\n            background: #2ecc71;\r\n            color: #ffffff;\r\n            text-decoration: none;\r\n            padding: 10px 20px;\r\n            border-radius: 5px;\r\n            font-weight: 600;\r\n            font-size: 14px;\r\n        }\r\n        .footer {\r\n            background: #f7f9fc;\r\n            padding: 15px;\r\n            text-align: center;\r\n            font-size: 12px;\r\n            color: #777777;\r\n            border-top: 1px solid #e0e0e0;\r\n            border-bottom-left-radius: 10px;\r\n            border-bottom-right-radius: 10px;\r\n        }\r\n        .church-name {\r\n            color: #1e3c72;\r\n            font-weight: bold;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 8px;\r\n            font-size: 10px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #777777;\r\n            text-decoration: none;\r\n        }\r\n        @media (max-width: 480px) {\r\n            .container {\r\n                padding: 0 5px;\r\n            }\r\n            .header {\r\n                padding: 20px;\r\n            }\r\n            .header h1 {\r\n                font-size: 22px;\r\n            }\r\n            .content {\r\n                padding: 15px;\r\n            }\r\n            .message h2 {\r\n                font-size: 16px;\r\n            }\r\n            .updates h3 {\r\n                font-size: 15px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"header\">\r\n            <h1>Newsletter from {church_name}</h1>\r\n            <p>Connecting with all {total_recipients} of our cherished members</p>\r\n        </div>\r\n        \r\n        <div class=\"content\">\r\n            <div class=\"greeting\">\r\n                <p>Hello {recipient_first_name},</p>\r\n                <p>Welcome to this month\'s newsletter from {church_name}. We\'re delighted to share these updates with you and all {total_recipients} members of our community.</p>\r\n            </div>\r\n            \r\n            <div class=\"message\">\r\n                <h2>A Message from {sender_name}</h2>\r\n                <p>Dear {recipient_full_name},</p>\r\n                <p>I hope this message finds you well. At {church_name}, we\'re grateful for your presence and commitment to our shared journey of faith. This newsletter is a small way to keep us connected and inspired. Thank you for being a vital part of our family!</p>\r\n            </div>\r\n            \r\n            <div class=\"updates\">\r\n                <h3>Community Updates</h3>\r\n                <ul>\r\n                    <li>Join us this Sunday for a special service at 10 AM.</li>\r\n                    <li>Our next fellowship event is scheduled for next Saturday\'s details to follow!</li>\r\n                    <li>WeG??re launching a new outreach program\'s stay tuned for ways to get involved.</li>\r\n                    <li>Prayer meetings are now every Wednesday at 7 PM.</li>\r\n                </ul>\r\n            </div>\r\n            \r\n            <div class=\"cta\">\r\n                <a href=\"#\">Visit Our Website</a>\r\n            </div>\r\n        </div>\r\n        \r\n        <div class=\"footer\">\r\n            <p>Sent with love from <span class=\"church-name\">{church_name}</span><br>By {sender_name}</p>\r\n            <div class=\"unsubscribe\">\r\n                <a href=\"#\">Unsubscribe from these updates</a>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>',0,'2025-03-04 20:45:01','2025-03-18 13:56:30','bulk'),(31,'Welcome Email Template','Welcome to Freedom Assembly Church International','<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n    <title>Welcome to Freedom Assembly</title>\r\n    <style>\r\n        body { margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f7f7f7; }\r\n        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }\r\n        .header { background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%); color: white; padding: 30px; text-align: center; }\r\n        .header h1 { margin: 0; font-size: 28px; }\r\n        .content { padding: 30px; }\r\n        .profile-image { width: 150px; height: 150px; border-radius: 75px; object-fit: cover; margin: 20px auto; display: block; border: 5px solid white; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }\r\n        .details-table { width: 100%; border-collapse: collapse; margin: 20px 0; }\r\n        .details-table th { text-align: left; padding: 10px; background-color: #f2f2f2; width: 40%; }\r\n        .details-table td { padding: 10px; border-bottom: 1px solid #eee; }\r\n        .message { line-height: 1.6; color: #333; }\r\n        .footer { background-color: #f2f2f2; padding: 20px; text-align: center; color: #666; font-size: 14px; }\r\n        .vision-box { background-color: #f0f4f8; border-left: 4px solid #2c5282; padding: 15px; margin: 20px 0; }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"header\">\r\n            <h1>Welcome to Freedom Assembly Church International</h1>\r\n            <p>Men\'s Visionaire and Billionaire</p>\r\n        </div>\r\n        <div class=\"content\">\r\n            <img src=\"{image_path}\" alt=\"{full_name}\" class=\"profile-image\">\r\n            <h2>Welcome, {full_name}!</h2>\r\n            <p class=\"message\">Dear {full_name},</p>\r\n            <p class=\"message\">Thank you for registering with Freedom Assembly Church International Men\'s Visionaire and Billionaire platform. We are delighted to have you as part of our church family!</p>\r\n            \r\n            <h3>Your Registration Details</h3>\r\n            <table class=\"details-table\">\r\n                <tr>\r\n                    <th>Full Name</th>\r\n                    <td>{full_name}</td>\r\n                </tr>\r\n                <tr>\r\n                    <th>Email</th>\r\n                    <td>{email}</td>\r\n                </tr>\r\n                <tr>\r\n                    <th>Phone</th>\r\n                    <td>{phone_number}</td>\r\n                </tr>\r\n                <tr>\r\n                    <th>Occupation</th>\r\n                    <td>{occupation}</td>\r\n                </tr>\r\n                <tr>\r\n                    <th>Address</th>\r\n                    <td>{home_address}</td>\r\n                </tr>\r\n            </table>\r\n            \r\n            <div class=\"vision-box\">\r\n                <strong>Vision:</strong> Raising Men To Stand Tall Within The Family And Community.\r\n            </div>\r\n            \r\n            <p class=\"message\">Blessings,<br>Freedom Assembly Church International</p>\r\n        </div>\r\n        <div class=\"footer\">\r\n            <p>&copy; 2025 Freedom Assembly Church International. All rights reserved.</p>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>',0,'2025-03-16 07:36:50','2025-03-16 07:48:55','general'),(32,'Member Upcoming Birthday Notification 3','Celebrate with us! {birthday_member_full_name}\'s birthday is coming up {days_text}!','<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body { \r\n            font-family: \'Roboto\', sans-serif; \r\n            margin: 0; \r\n            padding: 0; \r\n            background: #f0f8ff; \r\n            color: #333; \r\n        }\r\n\r\n        .container { \r\n            max-width: 700px; \r\n            margin: 40px auto; \r\n            background: #ffffff; \r\n            border-radius: 10px; \r\n            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); \r\n            overflow: hidden;\r\n        }\r\n\r\n        .header { \r\n            background: linear-gradient(135deg, #6a0dad, #ff4081); \r\n            color: white; \r\n            text-align: center; \r\n            padding: 40px 20px; \r\n            border-radius: 10px 10px 0 0; \r\n            box-shadow: 0 6px 12px rgba(0,0,0,0.15);\r\n        }\r\n\r\n        .header h1 {\r\n            font-size: 36px;\r\n            margin-bottom: 10px;\r\n        }\r\n\r\n        .header h3 {\r\n            font-size: 20px;\r\n            font-weight: 300;\r\n        }\r\n\r\n        .content { \r\n            padding: 30px; \r\n            background: #f9f9f9;\r\n            color: #333;\r\n        }\r\n\r\n        .member-section { \r\n            text-align: center; \r\n            margin: 30px 0; \r\n        }\r\n\r\n        .member-image { \r\n            width: 160px; \r\n            height: 160px; \r\n            border-radius: 50%; \r\n            object-fit: cover; \r\n            border: 6px solid #ffffff; \r\n            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); \r\n            margin: 20px auto; \r\n            transition: transform 0.3s ease;\r\n        }\r\n\r\n        .member-image:hover {\r\n            transform: scale(1.1);\r\n        }\r\n\r\n        .member-name { \r\n            font-size: 22px; \r\n            font-weight: bold; \r\n            color: #6a0dad; \r\n        }\r\n\r\n        .member-details { \r\n            font-size: 16px; \r\n            margin-top: 10px; \r\n        }\r\n\r\n        .celebration-list { \r\n            margin: 20px 0;\r\n        }\r\n\r\n        .celebration-list ul { \r\n            list-style-type: disc; \r\n            padding-left: 30px; \r\n            font-size: 16px;\r\n        }\r\n\r\n        .celebration-list li { \r\n            margin-bottom: 10px; \r\n        }\r\n\r\n        .scripture { \r\n            background-color: #f0e6ff; \r\n            padding: 20px; \r\n            border-radius: 10px; \r\n            margin-top: 30px; \r\n            font-style: italic; \r\n            text-align: center; \r\n            color: #6a0dad; \r\n            font-size: 18px; \r\n        }\r\n\r\n        .button { \r\n            display: inline-block; \r\n            background-color: #ff4081; \r\n            color: white; \r\n            padding: 12px 30px; \r\n            text-decoration: none; \r\n            border-radius: 50px; \r\n            font-weight: bold; \r\n            text-transform: uppercase; \r\n            letter-spacing: 1px;\r\n            transition: background-color 0.3s ease;\r\n        }\r\n\r\n        .button:hover { \r\n            background-color: #6a0dad;\r\n        }\r\n\r\n        .footer { \r\n            text-align: center; \r\n            margin-top: 30px; \r\n            font-size: 14px; \r\n            color: #777; \r\n        }\r\n\r\n        .footer p { \r\n            margin: 10px 0;\r\n        }\r\n\r\n        @media (max-width: 768px) {\r\n            .container { \r\n                width: 90%; \r\n            }\r\n\r\n            .header h1 {\r\n                font-size: 28px;\r\n            }\r\n\r\n            .header h3 {\r\n                font-size: 18px;\r\n            }\r\n\r\n            .member-image { \r\n                width: 140px; \r\n                height: 140px; \r\n            }\r\n\r\n            .button { \r\n                padding: 10px 20px; \r\n            }\r\n        }\r\n\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"header\">\r\n            <h1>Birthday Celebration!</h1>\r\n            <h3>Let\'s Celebrate This Special Day!</h3>\r\n        </div>\r\n        \r\n        <div class=\"content\">\r\n            <p>Dear {member_name},</p>\r\n            \r\n            <p>We are thrilled to announce that our dear member, <strong>{birthday_member_name}</strong>, will be celebrating his birthday {days_text}!</p>\r\n            \r\n            <div class=\"member-section\">\r\n                <img src=\"{member_image}\" alt=\"{member_image}\" class=\"member-image\">\r\n                <div class=\"member-name\">{birthday_member_full_name}</div>\r\n                <div class=\"member-details\">\r\n                    <strong>Birthday Date:</strong> {upcoming_birthday_formatted}<br>\r\n                    <strong>Turning:</strong> {birthday_member_age} Years Old\r\n                </div>\r\n            </div>\r\n            \r\n            <div class=\"celebration-list\">\r\n                <h4>How You Can Celebrate:</h4>\r\n                <ul>\r\n                    <li>Send a heartfelt note of blessing</li>\r\n                    <li>Offer a prayer for their joy and growth</li>\r\n                    <li>Gift them something meaningful</li>\r\n                    <li>Share a verse of encouragement</li>\r\n                </ul>\r\n            </div>\r\n            \r\n            <div class=\"scripture\">\r\n                \"The LORD has done great things for us, and we are filled with joy.\" - Psalm 126:3\r\n            </div>\r\n            \r\n            <div style=\"text-align: center;\">\r\n                <a href=\"#\" class=\"button\">Send Birthday Wishes</a>\r\n            </div>\r\n        </div>\r\n        \r\n        <div class=\"footer\">\r\n            <p>With love from,<br>\r\n            Freedom Assembly Church</p>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>',1,'2025-03-16 11:06:02','2025-03-18 20:09:15','birthday');
/*!40000 ALTER TABLE `email_templates` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

