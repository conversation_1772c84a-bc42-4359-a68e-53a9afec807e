<?php
// Improved Raffle Email Template with mobile-friendly formatting
require_once __DIR__ . '/../config.php';

try {
    // Template details
    $template_name = "The Big Raffle Winner (Mobile-Friendly)";
    $subject = "The Big Raffle - You're a Winner!";
    
    // Create the content with fixed lottery numbers and multiple placeholder formats
    $content = <<<'HTML'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Big Raffle - You're a Winner!</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: Arial, Helvetica, sans-serif;
            background-color: #f4f4f4;
        }
        
        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .flyer {
            background: linear-gradient(135deg, #8A2BE2, #5a1ca5);
            border-radius: 10px 10px 0 0;
            text-align: center;
            padding: 30px;
        }
        
        .winner-banner {
            background-color: #FFD700;
            color: #8A2BE2;
            font-size: 22px;
            font-weight: bold;
            padding: 10px 20px;
            border-radius: 8px;
            display: inline-block;
        }
        
        .title {
            font-size: 40px;
            font-weight: bold;
            color: #fff;
            margin: 15px 0;
        }
        
        .lotto-ball {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            font-size: 22px;
            font-weight: bold;
            color: white;
            margin: 5px;
        }
        
        .ball-1 { background-color: #FF69B4; }
        .ball-2 { background-color: #FFD700; }
        .ball-3 { background-color: #00CED1; }
        .ball-4 { background-color: #32CD32; }
        .ball-5 { background-color: #FF6347; }
        
        .powerball {
            background-color: #FF0000;
            width: 70px;
            height: 70px;
            font-size: 24px;
        }
        
        .claim-section {
            text-align: center;
            padding: 20px;
        }
        
        .claim-button {
            display: inline-block;
            padding: 15px 30px;
            background-color: #FFD700;
            color: #8A2BE2;
            font-weight: bold;
            text-decoration: none;
            border-radius: 6px;
            font-size: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .instructions {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 20px;
            margin: 20px;
        }
        
        .footer {
            background-color: #8A2BE2;
            color: white;
            padding: 15px;
            font-size: 12px;
            text-align: center;
            border-radius: 0 0 10px 10px;
        }
    </style>
</head>
<body>
    <table class="container" cellpadding="0" cellspacing="0" border="0" width="100%">
        <tr>
            <td class="flyer">
                <div class="winner-banner">CONGRATULATIONS!</div>
                <div class="title">THE BIG RAFFLE</div>
                <p style="color: white; font-size: 24px;">You're a WINNER!</p>
            </td>
        </tr>
        <tr>
            <td align="center" style="padding: 20px;">
                <div class="lotto-ball ball-1">14</div>
                <div class="lotto-ball ball-2">28</div>
                <div class="lotto-ball ball-3">53</div>
                <div class="lotto-ball ball-4">67</div>
                <div class="lotto-ball ball-5">92</div>
                <div class="lotto-ball powerball">9</div>
            </td>
        </tr>
        <tr>
            <td class="claim-section">
                <p style="font-size: 18px;">Congratulations! Your Powerball numbers have been drawn, and you're a winner! A life-changing prize awaits you.</p>
                
                <!-- Using HARDCODED email address with multiple-line breaks for mobile -->
                <a href="mailto:<EMAIL>?subject=I%20Won%20The%20Big%20Raffle&body=Hello,%0A%0AI%20received%20your%20email%20notification%20and%20I%20am%20confirming%20that%20I%20am%20the%20Powerball%20winner!%0A%0AMy%20winning%20numbers%20are:%0A%0ABall%201:%2014%0A%0ABall%202:%2028%0A%0ABall%203:%2053%0A%0ABall%204:%2067%0A%0ABall%205:%2092%0A%0APowerball:%209%0A%0A%0AMy%20contact%20details:%0A%0AName:%20%0A%0APhone:%20%0A%0AAddress:%20%0A%0A%0AI'm%20excited%20to%20claim%20my%20prize!%0A%0AThank%20you," class="claim-button">CLAIM YOUR PRIZE NOW</a>
            </td>
        </tr>
        <tr>
            <td class="instructions">
                <h3 style="color: #8A2BE2;">How to Claim Your Prize:</h3>
                <ol>
                    <li>Click the claim button above</li>
                    <li>Verify your identity</li>
                    <li>Choose your payment method</li>
                    <li>Receive your prize within 24 hours</li>
                </ol>
            </td>
        </tr>
        <tr>
            <td class="footer">
                <p>© 2025 The Big Raffle. All rights reserved.</p>
                <p>If you did not register for this raffle, please disregard this email.</p>
                <p><a href="{unsubscribe_link}" style="color: #FFD700; text-decoration: none;">Unsubscribe</a></p>
            </td>
        </tr>
    </table>
</body>
</html>
HTML;

    // Check if a template with this name already exists
    $stmt = $pdo->prepare("SELECT id FROM email_templates WHERE template_name = ?");
    $stmt->execute([$template_name]);
    $existing = $stmt->fetch();
    
    $is_birthday_template = 0;
    $template_category = 'bulk';
    
    if ($existing) {
        // Update the existing template
        echo "<h3>Updating existing raffle template</h3>";
        $stmt = $pdo->prepare("UPDATE email_templates SET subject = ?, content = ?, is_birthday_template = ?, template_category = ? WHERE id = ?");
        $stmt->execute([$subject, $content, $is_birthday_template, $template_category, $existing['id']]);
        
        echo "<p>Successfully updated template with ID: " . $existing['id'] . "</p>";
    } else {
        // Create a new template
        echo "<h3>Creating new mobile-friendly raffle template</h3>";
        $stmt = $pdo->prepare("INSERT INTO email_templates (template_name, subject, content, is_birthday_template, template_category) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$template_name, $subject, $content, $is_birthday_template, $template_category]);
        
        echo "<p>Successfully created template with ID: " . $pdo->lastInsertId() . "</p>";
    }
    
    // Add a specific setting for the raffle reply email
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM settings WHERE setting_key = 'raffle_reply_email'");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES ('raffle_reply_email', '<EMAIL>')");
        $stmt->execute();
        echo "<p>Added raffle_reply_email setting: <EMAIL></p>";
    } else {
        $stmt = $pdo->prepare("UPDATE settings SET setting_value = '<EMAIL>' WHERE setting_key = 'raffle_reply_email'");
        $stmt->execute();
        echo "<p>Updated raffle_reply_email setting: <EMAIL></p>";
    }
    
    echo "<h3>Success!</h3>";
    echo "<p>The updated template uses a hardcoded email address and improved mobile formatting.</p>";
    echo "<p>Key improvements:</p>";
    echo "<ul>";
    echo "<li>Hardcoded the reply email address: <EMAIL></li>";
    echo "<li>Added extra line breaks (%0A%0A) between each line for better mobile display</li>";
    echo "<li>Fixed lottery numbers format with proper spacing</li>";
    echo "<li>Maintained fixed lottery numbers</li>";
    echo "</ul>";
    echo "<p>You can view the template in the admin panel at <a href='../admin/email_templates.php'>Email Templates</a>.</p>";
    
} catch (PDOException $e) {
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
} 