<?php
require_once '../config.php';

header('Content-Type: application/json');

try {
    // Get all email templates, prioritizing birthday templates
    $stmt = $pdo->prepare("
        SELECT id, template_name as name, subject, template_category, is_birthday_template
        FROM email_templates
        ORDER BY is_birthday_template DESC, template_category, template_name
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode($templates);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}
?>
