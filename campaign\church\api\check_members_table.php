<?php
// Set headers for JSON response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Include configuration file
require_once '../config.php';

try {
    // Connect to database using the connection from config.php
    $conn = $pdo;
    
    // Check members table structure
    $tableInfo = $conn->query("DESCRIBE members");
    $tableStructure = $tableInfo->fetchAll(PDO::FETCH_ASSOC);
    
    // Check if specific columns exist
    $requiredColumns = ['id', 'full_name', 'email', 'birth_date', 'phone_number'];
    $missingColumns = [];
    $existingColumns = array_column($tableStructure, 'Field');
    
    foreach ($requiredColumns as $column) {
        if (!in_array($column, $existingColumns)) {
            $missingColumns[] = $column;
        }
    }
    
    // Get a sample of the data
    $sampleQuery = $conn->query("SELECT * FROM members LIMIT 5");
    $sampleData = $sampleQuery->fetchAll(PDO::FETCH_ASSOC);
    
    // Format response
    $response = [
        'success' => true,
        'message' => 'Members table check completed',
        'table_exists' => true,
        'missing_columns' => $missingColumns,
        'table_structure' => $tableStructure,
        'sample_data' => $sampleData
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
} catch(PDOException $e) {
    $response = [
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
}
?> 