<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Handle campaign actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['cancel_campaign'])) {
            $campaignId = intval($_POST['campaign_id']);
            
            $stmt = $pdo->prepare("UPDATE sms_campaigns SET status = 'cancelled' WHERE id = ? AND status IN ('scheduled', 'sending')");
            $stmt->execute([$campaignId]);
            
            if ($stmt->rowCount() > 0) {
                // Also cancel pending recipients
                $stmt = $pdo->prepare("UPDATE sms_campaign_recipients SET status = 'cancelled' WHERE campaign_id = ? AND status = 'pending'");
                $stmt->execute([$campaignId]);
                
                $success = "SMS campaign cancelled successfully.";
            } else {
                throw new Exception("Campaign cannot be cancelled or does not exist.");
            }
            
        } elseif (isset($_POST['delete_campaign'])) {
            $campaignId = intval($_POST['campaign_id']);
            
            // Check if campaign can be deleted (only draft, completed, failed, or cancelled)
            $stmt = $pdo->prepare("SELECT status FROM sms_campaigns WHERE id = ?");
            $stmt->execute([$campaignId]);
            $status = $stmt->fetchColumn();
            
            if (!in_array($status, ['draft', 'completed', 'failed', 'cancelled'])) {
                throw new Exception("Cannot delete campaign with status: " . $status);
            }
            
            $stmt = $pdo->prepare("DELETE FROM sms_campaigns WHERE id = ?");
            $stmt->execute([$campaignId]);
            
            $success = "SMS campaign deleted successfully.";
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get campaigns with pagination
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Search and filter
$search = trim($_GET['search'] ?? '');
$statusFilter = trim($_GET['status'] ?? '');

$whereConditions = [];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "campaign_name LIKE ?";
    $params[] = "%$search%";
}

if (!empty($statusFilter)) {
    $whereConditions[] = "status = ?";
    $params[] = $statusFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get total count
$countQuery = "SELECT COUNT(*) FROM sms_campaigns $whereClause";
$stmt = $pdo->prepare($countQuery);
$stmt->execute($params);
$totalCampaigns = $stmt->fetchColumn();
$totalPages = ceil($totalCampaigns / $limit);

// Get campaigns
$query = "SELECT sc.*, 
          st.template_name,
          CASE 
            WHEN sc.status = 'completed' THEN ROUND((sc.delivered_count / NULLIF(sc.sent_count, 0)) * 100, 1)
            ELSE NULL 
          END as delivery_rate
          FROM sms_campaigns sc 
          LEFT JOIN sms_templates st ON sc.template_id = st.id
          $whereClause 
          ORDER BY sc.created_at DESC 
          LIMIT $limit OFFSET $offset";

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$campaigns = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get campaign statistics
$stats = [];
try {
    $stmt = $pdo->prepare("SELECT 
        COUNT(*) as total_campaigns,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_campaigns,
        SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled_campaigns,
        SUM(CASE WHEN status = 'sending' THEN 1 ELSE 0 END) as sending_campaigns,
        SUM(sent_count) as total_sent,
        SUM(delivered_count) as total_delivered,
        SUM(failed_count) as total_failed
        FROM sms_campaigns");
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $stats = ['total_campaigns' => 0, 'completed_campaigns' => 0, 'scheduled_campaigns' => 0, 'sending_campaigns' => 0, 'total_sent' => 0, 'total_delivered' => 0, 'total_failed' => 0];
}

// Set page variables
$page_title = 'SMS Campaigns';
$page_header = 'SMS Campaigns';
$page_description = 'View and manage SMS campaigns.';

// Include header
include 'includes/header.php';
?>

<style>
.campaign-message {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-family: monospace;
    font-size: 0.875rem;
    max-height: 60px;
    overflow-y: auto;
}
.stats-card {
    border-left: 4px solid;
}
.stats-card.primary { border-left-color: #007bff; }
.stats-card.success { border-left-color: #28a745; }
.stats-card.warning { border-left-color: #ffc107; }
.stats-card.info { border-left-color: #17a2b8; }
</style>

<!-- Page Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="bi bi-megaphone me-2"></i><?php echo $page_header; ?>
    </h1>
    <div>
        <a href="bulk_sms.php" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>Create Campaign
        </a>
        <a href="sms_templates.php" class="btn btn-outline-secondary">
            <i class="bi bi-file-text me-2"></i>Templates
        </a>
        <a href="sms_analytics.php" class="btn btn-outline-info">
            <i class="bi bi-graph-up me-2"></i>Analytics
        </a>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
<?php endif; ?>

<?php if (isset($success)): ?>
    <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
<?php endif; ?>

<!-- Campaign Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card primary h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Campaigns</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['total_campaigns'] ?? 0); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-megaphone fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card success h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Messages Sent</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['total_sent'] ?? 0); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-send fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card info h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Delivered</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['total_delivered'] ?? 0); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card warning h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Delivery Rate</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php 
                            $deliveryRate = $stats['total_sent'] > 0 ? round(($stats['total_delivered'] / $stats['total_sent']) * 100, 1) : 0;
                            echo $deliveryRate . '%';
                            ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-graph-up fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">Search Campaigns</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="Search by campaign name...">
            </div>
            <div class="col-md-4">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="draft" <?php echo $statusFilter === 'draft' ? 'selected' : ''; ?>>Draft</option>
                    <option value="scheduled" <?php echo $statusFilter === 'scheduled' ? 'selected' : ''; ?>>Scheduled</option>
                    <option value="sending" <?php echo $statusFilter === 'sending' ? 'selected' : ''; ?>>Sending</option>
                    <option value="completed" <?php echo $statusFilter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                    <option value="failed" <?php echo $statusFilter === 'failed' ? 'selected' : ''; ?>>Failed</option>
                    <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search me-1"></i>Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Campaigns List -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            SMS Campaigns 
            <span class="badge bg-secondary"><?php echo number_format($totalCampaigns ?? 0); ?> total</span>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($campaigns)): ?>
            <div class="text-center py-4">
                <i class="bi bi-megaphone text-muted" style="font-size: 3rem;"></i>
                <h5 class="text-muted mt-2">No SMS Campaigns Found</h5>
                <p class="text-muted">Create your first SMS campaign to get started.</p>
                <a href="bulk_sms.php" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>Create Campaign
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Campaign Name</th>
                            <th>Message</th>
                            <th>Recipients</th>
                            <th>Status</th>
                            <th>Delivery Rate</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($campaigns as $campaign): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($campaign['campaign_name']); ?></strong>
                                    <?php if ($campaign['template_name']): ?>
                                        <br><small class="text-muted">Template: <?php echo htmlspecialchars($campaign['template_name']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="campaign-message">
                                        <?php echo htmlspecialchars(substr($campaign['message'], 0, 100)); ?>
                                        <?php if (strlen($campaign['message']) > 100): ?>...<?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <strong><?php echo number_format($campaign['total_recipients'] ?? 0); ?></strong>
                                        <br><small class="text-muted">recipients</small>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $statusColors = [
                                        'draft' => 'secondary',
                                        'scheduled' => 'info',
                                        'sending' => 'warning',
                                        'completed' => 'success',
                                        'failed' => 'danger',
                                        'cancelled' => 'dark'
                                    ];
                                    $statusColor = $statusColors[$campaign['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $statusColor; ?>">
                                        <?php echo ucfirst($campaign['status']); ?>
                                    </span>
                                    
                                    <?php if ($campaign['scheduled_at'] && $campaign['status'] === 'scheduled'): ?>
                                        <br><small class="text-muted">
                                            <?php echo date('M j, Y g:i A', strtotime($campaign['scheduled_at'])); ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($campaign['status'] === 'completed'): ?>
                                        <div class="text-center">
                                            <strong><?php echo $campaign['delivery_rate'] ?? 0; ?>%</strong>
                                            <br><small class="text-muted">
                                                <?php echo number_format($campaign['delivered_count'] ?? 0); ?>/<?php echo number_format($campaign['sent_count'] ?? 0); ?>
                                            </small>
                                        </div>
                                    <?php elseif ($campaign['status'] === 'sending'): ?>
                                        <div class="text-center">
                                            <div class="spinner-border spinner-border-sm text-warning" role="status">
                                                <span class="visually-hidden">Sending...</span>
                                            </div>
                                            <br><small class="text-muted">In progress</small>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center text-muted">-</div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo date('M j, Y', strtotime($campaign['created_at'])); ?>
                                        <br><?php echo date('g:i A', strtotime($campaign['created_at'])); ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-info" 
                                                onclick="viewCampaignDetails(<?php echo $campaign['id']; ?>)">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        
                                        <?php if (in_array($campaign['status'], ['scheduled', 'sending'])): ?>
                                            <button type="button" class="btn btn-outline-warning" 
                                                    onclick="cancelCampaign(<?php echo $campaign['id']; ?>, '<?php echo htmlspecialchars($campaign['campaign_name']); ?>')">
                                                <i class="bi bi-stop-circle"></i>
                                            </button>
                                        <?php endif; ?>
                                        
                                        <?php if (in_array($campaign['status'], ['draft', 'completed', 'failed', 'cancelled'])): ?>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteCampaign(<?php echo $campaign['id']; ?>, '<?php echo htmlspecialchars($campaign['campaign_name']); ?>')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Campaigns pagination" class="mt-3">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($statusFilter); ?>">Previous</a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($statusFilter); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($statusFilter); ?>">Next</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Cancel Campaign Modal -->
<div class="modal fade" id="cancelCampaignModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel SMS Campaign</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel the campaign "<strong id="cancelCampaignName"></strong>"?</p>
                <p class="text-warning"><small>This will stop any pending SMS messages from being sent.</small></p>
            </div>
            <div class="modal-footer">
                <form method="post" style="display: inline;">
                    <input type="hidden" id="cancel_campaign_id" name="campaign_id">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No, Keep Campaign</button>
                    <button type="submit" name="cancel_campaign" class="btn btn-warning">Yes, Cancel Campaign</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Campaign Modal -->
<div class="modal fade" id="deleteCampaignModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete SMS Campaign</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the campaign "<strong id="deleteCampaignName"></strong>"?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <form method="post" style="display: inline;">
                    <input type="hidden" id="delete_campaign_id" name="campaign_id">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="delete_campaign" class="btn btn-danger">Delete Campaign</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function viewCampaignDetails(campaignId) {
    // In a full implementation, this would open a detailed view modal
    // For now, we'll redirect to a details page or show an alert
    alert('Campaign details view would be implemented here. Campaign ID: ' + campaignId);
}

function cancelCampaign(campaignId, campaignName) {
    document.getElementById('cancel_campaign_id').value = campaignId;
    document.getElementById('cancelCampaignName').textContent = campaignName;
    
    new bootstrap.Modal(document.getElementById('cancelCampaignModal')).show();
}

function deleteCampaign(campaignId, campaignName) {
    document.getElementById('delete_campaign_id').value = campaignId;
    document.getElementById('deleteCampaignName').textContent = campaignName;
    
    new bootstrap.Modal(document.getElementById('deleteCampaignModal')).show();
}
</script>

<?php include 'includes/footer.php'; ?>
