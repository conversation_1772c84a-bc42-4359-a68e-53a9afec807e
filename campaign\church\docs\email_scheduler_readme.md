# Email Scheduling System Documentation

## Overview

The Email Scheduling System allows administrators to create and manage email campaigns with precise scheduling controls. This feature enables sending emails at specified times, with configurable batch sizes and sending rates, while providing detailed tracking and reporting.

## Features

- **Schedule Creation**: Set start and end times for email campaigns
- **Rate Limiting**: Control how many emails are sent per hour
- **Batch Processing**: Configure the minimum interval between emails
- **Campaign Management**: Pause, resume, or cancel scheduled campaigns
- **Recipient Management**: Select recipients by groups
- **Detailed Tracking**: Monitor open and click rates
- **Progress Monitoring**: View real-time progress of email campaigns
- **Comprehensive Logging**: Track all actions and errors

## Installation

### Database Setup

1. Import the database tables by running the SQL script:
   ```
   mysql -u username -p database_name < sql/email_scheduling_tables.sql
   ```

### Cron Job Setup

To process scheduled emails automatically, set up a cron job that runs every 5 minutes:

#### For Linux/Unix Servers:

1. Edit the crontab:
   ```
   crontab -e
   ```

2. Add the following line to run the script every 5 minutes:
   ```
   */5 * * * * wget -q -O /dev/null "https://yourdomain.com/cron/process_scheduled_emails.php?cron_key=fac_2024_secure_cron_8x9q2p5m"
   ```

#### For Hostinger or Similar Web Hosting:

1. Go to your hosting control panel
2. Navigate to the Cron Jobs section
3. Create a new cron job with the following settings:
   - Frequency: Every 5 minutes
   - Command: `wget -q -O /dev/null "https://yourdomain.com/cron/process_scheduled_emails.php?cron_key=fac_2024_secure_cron_8x9q2p5m"`

## Usage

### Creating a New Email Schedule

1. Navigate to Admin > Email Scheduler
2. Fill out the "Create New Email Schedule" form:
   - Campaign Name: A descriptive name for the campaign
   - Email Template: Select a pre-created email template
   - Schedule Type: One-time, recurring, or continuous
   - Start Date/Time: When to begin sending emails
   - End Date/Time: When to stop sending emails (optional for one-time)
   - Emails Per Hour: Maximum number of emails to send per hour
   - Minimum Interval: Minimum time between sending each email
   - Tracking Options: Enable/disable open and click tracking
   - Custom Subject: Override the template subject (optional)
   - Select Recipients: Choose which groups to send to
   - Custom Content: Override the template content (optional)
3. Click "Create Schedule" to save the schedule

### Managing Email Schedules

From the "Scheduled Email Campaigns" table, you can:

- View campaign details by clicking the eye icon
- Activate a pending campaign by clicking the play button
- Pause an active campaign by clicking the pause button
- Resume a paused campaign by clicking the play button
- Mark a campaign as completed by clicking the check button

### Viewing Campaign Details

Click the eye icon next to a campaign to view:

- Campaign details and settings
- Progress summary with statistics
- Recipient list with status information
- Activity logs for the campaign
- Email content preview (if custom content was provided)

## Troubleshooting

### Common Issues

1. **Emails not sending**:
   - Check that the cron job is running correctly
   - Verify SMTP settings in the email_settings table
   - Check the logs for any error messages

2. **Tracking not working**:
   - Ensure the tracking scripts (track_open.php and track_click.php) are accessible
   - Verify that tracking is enabled for the campaign

3. **Rate limiting issues**:
   - Adjust the emails_per_hour and min_interval_seconds settings
   - Check server resources to ensure they can handle the sending rate

### Logs

Check the following logs for troubleshooting:

- `logs/scheduled_emails.log`: General logs for the scheduler
- Email schedule logs in the database (viewable in the campaign details)

## Security Considerations

- The cron job is protected with a secure key to prevent unauthorized access
- Email content is sanitized to prevent injection attacks
- All user inputs are validated and sanitized

## Maintenance

- Regularly check the logs for any issues
- Clean up completed campaigns periodically
- Monitor server resources during high-volume campaigns

## Integration with Existing Systems

This email scheduling system integrates with:

- Existing email templates
- Contact groups
- Email tracking system
- Admin activity logs
