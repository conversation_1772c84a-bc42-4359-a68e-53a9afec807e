[2025-03-29 10:54:07] Test birthday email names script started.
[2025-03-29 10:54:07] Using template: Birthday Template 1
[2025-03-29 10:54:07] Testing with 2 recipients
[2025-03-29 10:54:07] Processing recipient: <PERSON><PERSON> (<EMAIL>)
[2025-03-29 10:54:07] Created test schedule with ID: 39
[2025-03-29 10:54:07] Original subject: Happy Birthday, {full_name}! From Freedom Assembly Church
[2025-03-29 10:54:07] Processed subject: Happy Birthday, <PERSON><PERSON>! From Freedom Assembly Church
[2025-03-29 10:54:07] First 200 chars of processed content: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">
    <div style="text-align: center; background-color: #ffffff; p
[2025-03-29 10:54:10] Test birthday email sent successfully.
[2025-03-29 10:54:10] Processing recipient: <PERSON> (gbo<PERSON><EMAIL>)
[2025-03-29 10:54:10] Created test schedule with ID: 40
[2025-03-29 10:54:10] Original subject: Happy Birthday, {full_name}! From Freedom Assembly Church
[2025-03-29 10:54:10] Processed subject: Happy Birthday, <PERSON> Mike! From Freedom Assembly Church
[2025-03-29 10:54:10] First 200 chars of processed content: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">
    <div style="text-align: center; background-color: #ffffff; p
[2025-03-29 10:54:13] Test birthday email sent successfully.
