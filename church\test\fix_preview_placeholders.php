<?php
// <PERSON><PERSON><PERSON> to fix template preview for lottery numbers
require_once __DIR__ . '/../config.php';

try {
    // Template ID
    $template_id = 53; // The ID of the unique numbers template
    
    // Get the template
    $stmt = $pdo->prepare("SELECT id, template_name, subject, content FROM email_templates WHERE id = ?");
    $stmt->execute([$template_id]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "<h2>Error: Template not found</h2>";
        exit;
    }
    
    echo "<h2>Fixing Preview for Template: " . htmlspecialchars($template['template_name']) . "</h2>";
    
    // The template content already has placeholders for the numbers
    // We need to modify the admin/preview_template.php file to replace these placeholders with actual numbers
    
    $preview_file = __DIR__ . '/../admin/preview_template.php';
    
    if (!file_exists($preview_file)) {
        echo "<p>❌ Error: Could not find preview_template.php file at: " . htmlspecialchars($preview_file) . "</p>";
        exit;
    }
    
    // Read the current content
    $current_content = file_get_contents($preview_file);
    
    // Check if our preview fix code is already present
    if (strpos($current_content, 'replaceLotteryPlaceholders') !== false) {
        echo "<p>✅ Preview fix is already present in the template preview system.</p>";
    } else {
        // Make a backup of the original file
        $backup_file = $preview_file . '.bak';
        if (file_put_contents($backup_file, $current_content)) {
            echo "<p>✅ Created backup of original preview file at: " . htmlspecialchars($backup_file) . "</p>";
        } else {
            echo "<p>❌ Warning: Could not create backup of original preview file.</p>";
        }
        
        // Find where template content is retrieved from the database
        $pattern = '/\$template\s*=\s*\$stmt->fetch\(\);/';
        
        if (preg_match($pattern, $current_content, $matches, PREG_OFFSET_CAPTURE)) {
            $match_position = $matches[0][1];
            
            // Add our lottery number replacement function after template is fetched
            $replacement_code = <<<'PHP'
$template = $stmt->fetch();

// Replace lottery number placeholders with sample values for preview
function replaceLotteryPlaceholders($content) {
    // Check if this is a lottery template (contains lottery ball placeholders)
    if (strpos($content, '{BALL1}') !== false || 
        strpos($content, '{BALL2}') !== false ||
        strpos($content, '{BALL3}') !== false) {
        
        // Use fixed sample numbers for preview
        $ball1 = 14;
        $ball2 = 28;
        $ball3 = 53;
        $ball4 = 67;
        $ball5 = 92;
        $powerball = 9;
        
        // Replace placeholders with sample numbers
        $content = str_replace(
            ['{BALL1}', '{BALL2}', '{BALL3}', '{BALL4}', '{BALL5}', '{POWERBALL}'],
            [$ball1, $ball2, $ball3, $ball4, $ball5, $powerball],
            $content
        );
    }
    
    return $content;
}

// Apply replacement to template content
if ($template) {
    $template['content'] = replaceLotteryPlaceholders($template['content']);
}
PHP;
            
            // Replace the fetch line with our modified version that includes the replacement function
            $new_content = substr($current_content, 0, $match_position) . $replacement_code . substr($current_content, $match_position + strlen($matches[0][0]));
            
            // Update the file with our preview fix
            if (file_put_contents($preview_file, $new_content)) {
                echo "<p>✅ Successfully added lottery number preview fix!</p>";
            } else {
                echo "<p>❌ Error: Could not update the preview file.</p>";
                exit;
            }
        } else {
            echo "<p>❌ Error: Could not find the right position to insert preview fix.</p>";
            exit;
        }
    }
    
    echo "<h3>Success!</h3>";
    echo "<p>The template preview has been updated to show sample lottery numbers instead of placeholders.</p>";
    echo "<p>Now when you preview the template, you'll see actual numbers like 14, 28, 53, 67, 92, and 9 instead of {BALL1}, {BALL2}, etc.</p>";
    
    echo "<h3>Preview Your Template:</h3>";
    echo "<p><a href='../admin/preview_template.php?id=" . $template_id . "' style='display: inline-block; padding: 10px 20px; background-color: #8A2BE2; color: white; text-decoration: none; border-radius: 4px;'>Preview Template</a></p>";
    
} catch (Exception $e) {
    echo "<h2>Error:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
} 