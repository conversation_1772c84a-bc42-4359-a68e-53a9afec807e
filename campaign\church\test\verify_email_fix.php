<?php
/**
 * Verify Email Fix
 * 
 * This script verifies that our fix for handling both boolean and array return types from 
 * sendScheduledEmail function works correctly.
 */

require_once '../config.php';

// Set up logging
$logDir = __DIR__ . '/../logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}
$logFile = $logDir . '/verify_email_fix.log';
file_put_contents($logFile, '[' . date('Y-m-d H:i:s') . '] Verification test started' . PHP_EOL);

// Mock function to test both return types
function mockSendScheduledEmail($returnType) {
    global $logFile;
    
    // Log the test
    $logEntry = '[' . date('Y-m-d H:i:s') . '] Testing with return type: ' . $returnType . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND);
    
    // Return different types based on the parameter
    switch ($returnType) {
        case 'boolean_true':
            return true;
        case 'boolean_false':
            return false;
        case 'array_success':
            return ['success' => true, 'message' => 'Email sent successfully'];
        case 'array_failure':
            return ['success' => false, 'message' => 'Failed to send email'];
        case 'null':
            return null;
        default:
            return ['success' => true, 'message' => 'Default success response'];
    }
}

// Function to process a test email with our fixed logic
function processTestEmail($returnType) {
    global $logFile;
    
    // Mock data
    $recipient = [
        'email' => '<EMAIL>',
        'name' => 'Test User'
    ];
    
    // Get the result
    $result = mockSendScheduledEmail($returnType);
    
    // Apply our fixed condition
    if (($result === true) || (is_array($result) && isset($result['success']) && $result['success'])) {
        $logEntry = '[' . date('Y-m-d H:i:s') . '] SUCCESS: Email sent to ' . $recipient['email'] . PHP_EOL;
        file_put_contents($logFile, $logEntry, FILE_APPEND);
        echo "✅ SUCCESS (" . $returnType . "): Email sent to " . $recipient['email'] . "\n";
        return true;
    } else {
        $errorMessage = (is_array($result) && isset($result['message'])) ? $result['message'] : 'Unknown error';
        $logEntry = '[' . date('Y-m-d H:i:s') . '] ERROR: Failed to send email to ' . $recipient['email'] . ' - ' . $errorMessage . PHP_EOL;
        file_put_contents($logFile, $logEntry, FILE_APPEND);
        echo "❌ ERROR (" . $returnType . "): Failed to send email to " . $recipient['email'] . " - " . $errorMessage . "\n";
        return false;
    }
}

// Run tests with different return types
$testCases = [
    'boolean_true',
    'boolean_false',
    'array_success',
    'array_failure',
    'null'
];

$successCount = 0;
$failureCount = 0;

echo "Starting email fix verification tests...\n\n";

foreach ($testCases as $testCase) {
    $result = processTestEmail($testCase);
    if ($result) {
        $successCount++;
    } else {
        $failureCount++;
    }
}

echo "\nTest Summary:\n";
echo "============\n";
echo "Success cases: $successCount\n";
echo "Failure cases: $failureCount\n";
echo "Total cases: " . count($testCases) . "\n";

// Log completion
$logEntry = '[' . date('Y-m-d H:i:s') . '] Testing completed. Results: Successes=' . $successCount . ', Failures=' . $failureCount . PHP_EOL;
file_put_contents($logFile, $logEntry, FILE_APPEND);

echo "\nVerification complete. Check $logFile for details.\n"; 