[2025-03-29 10:31:47] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 10:32:44] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 10:32:55] Sending scheduled email to <PERSON><PERSON> <<EMAIL>>
[2025-03-29 10:33:28] Sending scheduled email to <PERSON><PERSON> <<EMAIL>>
[2025-03-29 10:33:28] Added tracking pixel with ID: scheduled_67e7be687725c0.96189198
[2025-03-29 10:34:12] Sending scheduled email to <PERSON><PERSON> <<EMAIL>>
[2025-03-29 10:34:12] Added tracking pixel with ID: scheduled_67e7be94d68966.65853745
[2025-03-29 10:34:15] Email sent <NAME_EMAIL>
[2025-03-29 10:34:15] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 10:34:15] Added tracking pixel with ID: scheduled_67e7be97ac5393.37937662
[2025-03-29 10:34:18] Email sent <NAME_EMAIL>
[2025-03-29 10:34:23] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 10:34:23] Added tracking pixel with ID: scheduled_67e7be9f56bed1.81178159
[2025-03-29 10:34:26] Email sent <NAME_EMAIL>
[2025-03-29 10:34:31] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 10:34:31] Added tracking pixel with ID: scheduled_67e7bea7610f50.68947719
[2025-03-29 10:34:34] Email sent <NAME_EMAIL>
[2025-03-29 10:34:39] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 10:34:39] Added tracking pixel with ID: scheduled_67e7beaf25fc64.04840802
[2025-03-29 10:34:41] Email sent <NAME_EMAIL>
[2025-03-29 10:34:46] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 10:34:46] Added tracking pixel with ID: scheduled_67e7beb6c93e08.********
[2025-03-29 10:34:49] Email sent <NAME_EMAIL>
[2025-03-29 10:34:54] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 10:34:54] Added tracking pixel with ID: scheduled_67e7bebe7c8e37.********
[2025-03-29 10:34:57] Email sent <NAME_EMAIL>
[2025-03-29 10:35:02] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 10:35:02] Added tracking pixel with ID: scheduled_67e7bec641b187.********
[2025-03-29 10:35:06] Email sent <NAME_EMAIL>
[2025-03-29 10:35:11] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 10:35:11] Added tracking pixel with ID: scheduled_67e7becf0e23a0.********
[2025-03-29 10:35:13] Email sent <NAME_EMAIL>
[2025-03-29 10:35:18] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 10:35:18] Added tracking pixel with ID: scheduled_67e7bed695e140.********
[2025-03-29 10:35:21] Email sent <NAME_EMAIL>
[2025-03-29 10:35:26] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 10:35:26] Added tracking pixel with ID: scheduled_67e7bede49b852.********
[2025-03-29 10:35:29] Email sent <NAME_EMAIL>
[2025-03-29 10:35:34] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 10:35:34] Added tracking pixel with ID: scheduled_67e7bee6d521c8.********
[2025-03-29 10:35:37] Email sent <NAME_EMAIL>
[2025-03-29 10:35:42] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 10:35:42] Added tracking pixel with ID: scheduled_67e7beee8bdb15.********
[2025-03-29 10:35:45] Email sent <NAME_EMAIL>
[2025-03-29 10:35:50] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 10:35:50] Added tracking pixel with ID: scheduled_67e7bef680b417.42105870
[2025-03-29 10:35:53] Email sent <NAME_EMAIL>
[2025-03-29 10:37:45] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 10:37:48] Email sent <NAME_EMAIL>
[2025-03-29 10:37:48] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 10:37:51] Email sent <NAME_EMAIL>
[2025-03-29 10:37:51] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 10:37:54] Email sent <NAME_EMAIL>
[2025-03-29 10:37:54] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 10:37:57] Email sent <NAME_EMAIL>
[2025-03-29 10:37:57] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 10:38:00] Email sent <NAME_EMAIL>
[2025-03-29 10:38:01] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 10:38:03] Email sent <NAME_EMAIL>
[2025-03-29 10:38:04] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 10:38:06] Email sent <NAME_EMAIL>
[2025-03-29 10:38:06] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 10:38:09] Email sent <NAME_EMAIL>
[2025-03-29 10:38:09] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 10:38:12] Email sent <NAME_EMAIL>
[2025-03-29 10:38:12] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 10:38:15] Email sent <NAME_EMAIL>
[2025-03-29 10:44:59] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 10:44:59] Added tracking pixel with ID: scheduled_67e7c11b4d4913.********
[2025-03-29 10:45:02] Email sent <NAME_EMAIL>
[2025-03-29 10:45:02] Sending scheduled email to Sandra Mike <<EMAIL>>
[2025-03-29 10:45:02] Added tracking pixel with ID: scheduled_67e7c11e73faf8.********
[2025-03-29 10:45:05] Email sent <NAME_EMAIL>
[2025-03-29 10:47:05] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 10:47:08] Email sent <NAME_EMAIL>
[2025-03-29 10:47:08] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 10:47:11] Email sent <NAME_EMAIL>
[2025-03-29 10:47:11] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 10:47:14] Email sent <NAME_EMAIL>
[2025-03-29 10:47:14] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 10:47:17] Email sent <NAME_EMAIL>
[2025-03-29 10:47:17] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 10:47:20] Email sent <NAME_EMAIL>
[2025-03-29 10:47:20] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 10:47:24] Email sent <NAME_EMAIL>
[2025-03-29 10:47:24] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 10:47:26] Email sent <NAME_EMAIL>
[2025-03-29 10:47:27] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 10:47:30] Email sent <NAME_EMAIL>
[2025-03-29 10:47:30] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 10:47:33] Email sent <NAME_EMAIL>
[2025-03-29 10:47:33] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 10:47:36] Email sent <NAME_EMAIL>
[2025-03-29 10:49:40] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 10:49:40] Added tracking pixel with ID: scheduled_67e7c234a1e499.********
[2025-03-29 10:49:43] Email sent <NAME_EMAIL>
[2025-03-29 10:49:43] Sending scheduled email to Sandra Mike <<EMAIL>>
[2025-03-29 10:49:43] Added tracking pixel with ID: scheduled_67e7c237543817.********
[2025-03-29 10:49:45] Email sent <NAME_EMAIL>
[2025-03-29 10:51:17] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 10:51:17] Added tracking pixel with ID: scheduled_67e7c2952861f0.********
[2025-03-29 10:51:17] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 10:51:17] Added tracking pixel with ID: scheduled_67e7c2955715c7.42303764
[2025-03-29 10:51:17] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 10:51:17] Added tracking pixel with ID: scheduled_67e7c295852b48.92825058
[2025-03-29 10:51:17] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 10:51:17] Added tracking pixel with ID: scheduled_67e7c295b24084.74598165
[2025-03-29 10:51:17] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 10:51:17] Added tracking pixel with ID: scheduled_67e7c295dbfe80.********
[2025-03-29 10:51:18] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 10:51:18] Added tracking pixel with ID: scheduled_67e7c2960a36d1.********
[2025-03-29 10:51:18] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 10:51:18] Added tracking pixel with ID: scheduled_67e7c29634b546.********
[2025-03-29 10:51:18] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 10:51:18] Added tracking pixel with ID: scheduled_67e7c2965e9096.********
[2025-03-29 10:51:18] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 10:51:18] Added tracking pixel with ID: scheduled_67e7c2967ff6d5.********
[2025-03-29 10:51:18] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 10:51:18] Added tracking pixel with ID: scheduled_67e7c296a287c6.********
[2025-03-29 10:54:07] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 10:54:07] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:54:07] Member data: {"full_name":"Godwin Bointa","first_name":"Godwin","last_name":"Bointa","email":"<EMAIL>","recipient_full_name":"Godwin Bointa","recipient_first_name":"Godwin","recipient_email":"<EMAIL>","id":27,"birth_date":"1985-03-28","upcoming_birthday_date":"March 28, 2026","upcoming_birthday_day":"Saturday","upcoming_birthday_formatted":"Saturday, March 28, 2026","days_until_birthday":364,"days_text":"in 364 days","age":41,"birthday_member_age":41,"birthday_member_name":"Godwin","birthday_member_full_name":"Godwin Bointa"}
[2025-03-29 10:54:07] Processed subject: Happy Birthday, Godwin Bointa! From Freedom Assembly Church
[2025-03-29 10:54:07] Processed body (first 200 chars): <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">
    <div style="text-align: center; background-color: #ffffff; p...
[2025-03-29 10:54:07] Added tracking pixel with ID: scheduled_67e7c33fc98bc2.94950198
[2025-03-29 10:54:10] Email sent <NAME_EMAIL>
[2025-03-29 10:54:10] Sending scheduled email to Sandra Mike <<EMAIL>>
[2025-03-29 10:54:10] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:54:10] Member data: {"full_name":"Sandra Mike","first_name":"Sandra","last_name":"Mike","email":"<EMAIL>","recipient_full_name":"Sandra Mike","recipient_first_name":"Sandra","recipient_email":"<EMAIL>","id":28,"birth_date":"1963-03-27","upcoming_birthday_date":"March 27, 2026","upcoming_birthday_day":"Friday","upcoming_birthday_formatted":"Friday, March 27, 2026","days_until_birthday":363,"days_text":"in 363 days","age":63,"birthday_member_age":63,"birthday_member_name":"Sandra","birthday_member_full_name":"Sandra Mike"}
[2025-03-29 10:54:10] Processed subject: Happy Birthday, Sandra Mike! From Freedom Assembly Church
[2025-03-29 10:54:10] Processed body (first 200 chars): <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">
    <div style="text-align: center; background-color: #ffffff; p...
[2025-03-29 10:54:10] Added tracking pixel with ID: scheduled_67e7c34294dca3.97235117
[2025-03-29 10:54:13] Email sent <NAME_EMAIL>
[2025-03-29 10:56:44] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 10:56:44] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:56:44] Member data: {"full_name":"Ndivhuwo119","first_name":"Ndivhuwo119","last_name":"","email":"<EMAIL>","recipient_full_name":"Ndivhuwo119","recipient_first_name":"Ndivhuwo119","recipient_email":"<EMAIL>","id":11761}
[2025-03-29 10:56:44] Processed subject: Newsletter from Freedom Assembly Church International
[2025-03-29 10:56:44] Processed body (first 200 chars): <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=ed...
[2025-03-29 10:56:44] Added tracking pixel with ID: scheduled_67e7c3dccd9547.60507504
[2025-03-29 10:56:44] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 10:56:44] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:56:44] Member data: {"full_name":"Bointa04","first_name":"Bointa04","last_name":"","email":"<EMAIL>","recipient_full_name":"Bointa04","recipient_first_name":"Bointa04","recipient_email":"<EMAIL>","id":11762}
[2025-03-29 10:56:44] Processed subject: Newsletter from Freedom Assembly Church International
[2025-03-29 10:56:45] Processed body (first 200 chars): <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=ed...
[2025-03-29 10:56:45] Added tracking pixel with ID: scheduled_67e7c3dd022c65.66009947
[2025-03-29 10:56:45] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 10:56:45] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:56:45] Member data: {"full_name":"Lilbossbaby11","first_name":"Lilbossbaby11","last_name":"","email":"<EMAIL>","recipient_full_name":"Lilbossbaby11","recipient_first_name":"Lilbossbaby11","recipient_email":"<EMAIL>","id":11763}
[2025-03-29 10:56:45] Processed subject: Newsletter from Freedom Assembly Church International
[2025-03-29 10:56:45] Processed body (first 200 chars): <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=ed...
[2025-03-29 10:56:45] Added tracking pixel with ID: scheduled_67e7c3dd300f70.42627405
[2025-03-29 10:56:45] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 10:56:45] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:56:45] Member data: {"full_name":"Saddafasif2","first_name":"Saddafasif2","last_name":"","email":"<EMAIL>","recipient_full_name":"Saddafasif2","recipient_first_name":"Saddafasif2","recipient_email":"<EMAIL>","id":11764}
[2025-03-29 10:56:45] Processed subject: Newsletter from Freedom Assembly Church International
[2025-03-29 10:56:45] Processed body (first 200 chars): <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=ed...
[2025-03-29 10:56:45] Added tracking pixel with ID: scheduled_67e7c3dd615414.50091789
[2025-03-29 10:56:45] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 10:56:45] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:56:45] Member data: {"full_name":"Enespaul002","first_name":"Enespaul002","last_name":"","email":"<EMAIL>","recipient_full_name":"Enespaul002","recipient_first_name":"Enespaul002","recipient_email":"<EMAIL>","id":11765}
[2025-03-29 10:56:45] Processed subject: Newsletter from Freedom Assembly Church International
[2025-03-29 10:56:45] Processed body (first 200 chars): <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=ed...
[2025-03-29 10:56:45] Added tracking pixel with ID: scheduled_67e7c3dd872188.03507667
[2025-03-29 10:56:45] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 10:56:45] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:56:45] Member data: {"full_name":"Support","first_name":"Support","last_name":"","email":"<EMAIL>","recipient_full_name":"Support","recipient_first_name":"Support","recipient_email":"<EMAIL>","id":11766}
[2025-03-29 10:56:45] Processed subject: Newsletter from Freedom Assembly Church International
[2025-03-29 10:56:45] Processed body (first 200 chars): <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=ed...
[2025-03-29 10:56:45] Added tracking pixel with ID: scheduled_67e7c3ddac5889.65735290
[2025-03-29 10:56:45] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 10:56:45] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:56:45] Member data: {"full_name":"Info","first_name":"Info","last_name":"","email":"<EMAIL>","recipient_full_name":"Info","recipient_first_name":"Info","recipient_email":"<EMAIL>","id":11767}
[2025-03-29 10:56:45] Processed subject: Newsletter from Freedom Assembly Church International
[2025-03-29 10:56:45] Processed body (first 200 chars): <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=ed...
[2025-03-29 10:56:45] Added tracking pixel with ID: scheduled_67e7c3ddd77072.********
[2025-03-29 10:56:46] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 10:56:46] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:56:46] Member data: {"full_name":"Account","first_name":"Account","last_name":"","email":"<EMAIL>","recipient_full_name":"Account","recipient_first_name":"Account","recipient_email":"<EMAIL>","id":11768}
[2025-03-29 10:56:46] Processed subject: Newsletter from Freedom Assembly Church International
[2025-03-29 10:56:46] Processed body (first 200 chars): <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=ed...
[2025-03-29 10:56:46] Added tracking pixel with ID: scheduled_67e7c3de0b6f28.********
[2025-03-29 10:56:46] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 10:56:46] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:56:46] Member data: {"full_name":"Henjoybid","first_name":"Henjoybid","last_name":"","email":"<EMAIL>","recipient_full_name":"Henjoybid","recipient_first_name":"Henjoybid","recipient_email":"<EMAIL>","id":11769}
[2025-03-29 10:56:46] Processed subject: Newsletter from Freedom Assembly Church International
[2025-03-29 10:56:46] Processed body (first 200 chars): <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=ed...
[2025-03-29 10:56:46] Added tracking pixel with ID: scheduled_67e7c3de339b28.91519859
[2025-03-29 10:56:46] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 10:56:46] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:56:46] Member data: {"full_name":"Eggnipawe","first_name":"Eggnipawe","last_name":"","email":"<EMAIL>","recipient_full_name":"Eggnipawe","recipient_first_name":"Eggnipawe","recipient_email":"<EMAIL>","id":11770}
[2025-03-29 10:56:46] Processed subject: Newsletter from Freedom Assembly Church International
[2025-03-29 10:56:46] Processed body (first 200 chars): <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=ed...
[2025-03-29 10:56:46] Added tracking pixel with ID: scheduled_67e7c3de55f564.65551841
[2025-03-29 10:57:03] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 10:57:03] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:57:03] Member data: {"full_name":"Ndivhuwo119","first_name":"Ndivhuwo119","last_name":"","email":"<EMAIL>","recipient_full_name":"Ndivhuwo119","recipient_first_name":"Ndivhuwo119","recipient_email":"<EMAIL>","id":11761}
[2025-03-29 10:57:03] Processed subject: Happy Birthday, Ndivhuwo119! Special Wishes For You
[2025-03-29 10:57:03] Processed body (first 200 chars): <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">

    <div style="text-align: center; background: linear-gradien...
[2025-03-29 10:57:03] Added tracking pixel with ID: scheduled_67e7c3efe752b2.63908398
[2025-03-29 10:57:04] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 10:57:04] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:57:04] Member data: {"full_name":"Bointa04","first_name":"Bointa04","last_name":"","email":"<EMAIL>","recipient_full_name":"Bointa04","recipient_first_name":"Bointa04","recipient_email":"<EMAIL>","id":11762}
[2025-03-29 10:57:04] Processed subject: Happy Birthday, Bointa04! Special Wishes For You
[2025-03-29 10:57:04] Processed body (first 200 chars): <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">

    <div style="text-align: center; background: linear-gradien...
[2025-03-29 10:57:04] Added tracking pixel with ID: scheduled_67e7c3f0219b03.34723692
[2025-03-29 10:57:04] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 10:57:04] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:57:04] Member data: {"full_name":"Lilbossbaby11","first_name":"Lilbossbaby11","last_name":"","email":"<EMAIL>","recipient_full_name":"Lilbossbaby11","recipient_first_name":"Lilbossbaby11","recipient_email":"<EMAIL>","id":11763}
[2025-03-29 10:57:04] Processed subject: Happy Birthday, Lilbossbaby11! Special Wishes For You
[2025-03-29 10:57:04] Processed body (first 200 chars): <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">

    <div style="text-align: center; background: linear-gradien...
[2025-03-29 10:57:04] Added tracking pixel with ID: scheduled_67e7c3f055eeb2.53031832
[2025-03-29 10:57:04] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 10:57:04] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:57:04] Member data: {"full_name":"Saddafasif2","first_name":"Saddafasif2","last_name":"","email":"<EMAIL>","recipient_full_name":"Saddafasif2","recipient_first_name":"Saddafasif2","recipient_email":"<EMAIL>","id":11764}
[2025-03-29 10:57:04] Processed subject: Happy Birthday, Saddafasif2! Special Wishes For You
[2025-03-29 10:57:04] Processed body (first 200 chars): <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">

    <div style="text-align: center; background: linear-gradien...
[2025-03-29 10:57:04] Added tracking pixel with ID: scheduled_67e7c3f08592f3.17281092
[2025-03-29 10:57:04] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 10:57:04] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:57:04] Member data: {"full_name":"Enespaul002","first_name":"Enespaul002","last_name":"","email":"<EMAIL>","recipient_full_name":"Enespaul002","recipient_first_name":"Enespaul002","recipient_email":"<EMAIL>","id":11765}
[2025-03-29 10:57:04] Processed subject: Happy Birthday, Enespaul002! Special Wishes For You
[2025-03-29 10:57:04] Processed body (first 200 chars): <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">

    <div style="text-align: center; background: linear-gradien...
[2025-03-29 10:57:04] Added tracking pixel with ID: scheduled_67e7c3f0afcf68.43293323
[2025-03-29 10:57:04] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 10:57:04] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:57:04] Member data: {"full_name":"Support","first_name":"Support","last_name":"","email":"<EMAIL>","recipient_full_name":"Support","recipient_first_name":"Support","recipient_email":"<EMAIL>","id":11766}
[2025-03-29 10:57:04] Processed subject: Happy Birthday, Support! Special Wishes For You
[2025-03-29 10:57:04] Processed body (first 200 chars): <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">

    <div style="text-align: center; background: linear-gradien...
[2025-03-29 10:57:04] Added tracking pixel with ID: scheduled_67e7c3f0d95105.09839493
[2025-03-29 10:57:05] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 10:57:05] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:57:05] Member data: {"full_name":"Info","first_name":"Info","last_name":"","email":"<EMAIL>","recipient_full_name":"Info","recipient_first_name":"Info","recipient_email":"<EMAIL>","id":11767}
[2025-03-29 10:57:05] Processed subject: Happy Birthday, Info! Special Wishes For You
[2025-03-29 10:57:05] Processed body (first 200 chars): <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">

    <div style="text-align: center; background: linear-gradien...
[2025-03-29 10:57:05] Added tracking pixel with ID: scheduled_67e7c3f10f37d0.********
[2025-03-29 10:57:05] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 10:57:05] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:57:05] Member data: {"full_name":"Account","first_name":"Account","last_name":"","email":"<EMAIL>","recipient_full_name":"Account","recipient_first_name":"Account","recipient_email":"<EMAIL>","id":11768}
[2025-03-29 10:57:05] Processed subject: Happy Birthday, Account! Special Wishes For You
[2025-03-29 10:57:05] Processed body (first 200 chars): <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">

    <div style="text-align: center; background: linear-gradien...
[2025-03-29 10:57:05] Added tracking pixel with ID: scheduled_67e7c3f140eae8.********
[2025-03-29 10:57:05] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 10:57:05] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:57:05] Member data: {"full_name":"Henjoybid","first_name":"Henjoybid","last_name":"","email":"<EMAIL>","recipient_full_name":"Henjoybid","recipient_first_name":"Henjoybid","recipient_email":"<EMAIL>","id":11769}
[2025-03-29 10:57:05] Processed subject: Happy Birthday, Henjoybid! Special Wishes For You
[2025-03-29 10:57:05] Processed body (first 200 chars): <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">

    <div style="text-align: center; background: linear-gradien...
[2025-03-29 10:57:05] Added tracking pixel with ID: scheduled_67e7c3f1719e04.61521960
[2025-03-29 10:57:05] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 10:57:05] SMTP Configuration: Host=smtp.hostinger.com, Port=465, Secure=ssl
[2025-03-29 10:57:05] Member data: {"full_name":"Eggnipawe","first_name":"Eggnipawe","last_name":"","email":"<EMAIL>","recipient_full_name":"Eggnipawe","recipient_first_name":"Eggnipawe","recipient_email":"<EMAIL>","id":11770}
[2025-03-29 10:57:05] Processed subject: Happy Birthday, Eggnipawe! Special Wishes For You
[2025-03-29 10:57:05] Processed body (first 200 chars): <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">

    <div style="text-align: center; background: linear-gradien...
[2025-03-29 10:57:05] Added tracking pixel with ID: scheduled_67e7c3f19fcc19.00241361
[2025-03-29 10:58:27] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 10:58:30] Email sent <NAME_EMAIL>
[2025-03-29 10:58:30] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 10:58:33] Email sent <NAME_EMAIL>
[2025-03-29 10:58:33] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 10:58:36] Email sent <NAME_EMAIL>
[2025-03-29 10:58:36] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 10:58:39] Email sent <NAME_EMAIL>
[2025-03-29 10:58:39] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 10:58:42] Email sent <NAME_EMAIL>
[2025-03-29 10:58:42] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 10:58:45] Email sent <NAME_EMAIL>
[2025-03-29 10:58:45] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 10:58:47] Email sent <NAME_EMAIL>
[2025-03-29 10:58:48] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 10:58:51] Email sent <NAME_EMAIL>
[2025-03-29 10:58:51] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 10:58:54] Email sent <NAME_EMAIL>
[2025-03-29 10:58:54] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 10:58:57] Email sent <NAME_EMAIL>
[2025-03-29 11:11:21] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 11:11:21] Added tracking pixel with ID: scheduled_67e7c749e38e55.********
[2025-03-29 11:11:24] Email sent <NAME_EMAIL>
[2025-03-29 11:11:24] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 11:11:24] Added tracking pixel with ID: scheduled_67e7c74c8e8fc6.********
[2025-03-29 11:11:27] Email sent <NAME_EMAIL>
[2025-03-29 11:11:32] Sending scheduled email to Sandra Mike <<EMAIL>>
[2025-03-29 11:11:32] Added tracking pixel with ID: scheduled_67e7c7546799f3.********
[2025-03-29 11:11:35] Email sent <NAME_EMAIL>
[2025-03-29 11:11:40] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 11:11:40] Added tracking pixel with ID: scheduled_67e7c75c94c2a6.06336469
[2025-03-29 11:11:43] Email sent <NAME_EMAIL>
[2025-03-29 11:11:48] Sending scheduled email to Sandra Mike <<EMAIL>>
[2025-03-29 11:11:48] Added tracking pixel with ID: scheduled_67e7c7644eeb51.38030878
[2025-03-29 11:11:50] Email sent <NAME_EMAIL>
[2025-03-29 11:11:55] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 11:11:55] Added tracking pixel with ID: scheduled_67e7c76bd2d7e9.71633811
[2025-03-29 11:11:59] Email sent <NAME_EMAIL>
[2025-03-29 11:14:50] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 11:14:53] Email sent <NAME_EMAIL>
[2025-03-29 11:14:53] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 11:14:57] Email sent <NAME_EMAIL>
[2025-03-29 11:14:57] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 11:15:00] Email sent <NAME_EMAIL>
[2025-03-29 11:15:00] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 11:15:03] Email sent <NAME_EMAIL>
[2025-03-29 11:15:03] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 11:15:06] Email sent <NAME_EMAIL>
[2025-03-29 11:15:06] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 11:15:09] Email sent <NAME_EMAIL>
[2025-03-29 11:15:09] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 11:15:12] Email sent <NAME_EMAIL>
[2025-03-29 11:15:12] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 11:15:15] Email sent <NAME_EMAIL>
[2025-03-29 11:15:15] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 11:15:18] Email sent <NAME_EMAIL>
[2025-03-29 11:15:18] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 11:15:21] Email sent <NAME_EMAIL>
[2025-03-29 11:22:37] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 11:22:40] Email sent <NAME_EMAIL>
[2025-03-29 11:22:40] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 11:22:42] Email sent <NAME_EMAIL>
[2025-03-29 11:22:43] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 11:22:47] Email sent <NAME_EMAIL>
[2025-03-29 11:22:47] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 11:22:50] Email sent <NAME_EMAIL>
[2025-03-29 11:22:50] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 11:22:53] Email sent <NAME_EMAIL>
[2025-03-29 11:22:53] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 11:22:58] Email sent <NAME_EMAIL>
[2025-03-29 11:22:59] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 11:23:01] Email sent <NAME_EMAIL>
[2025-03-29 11:23:03] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 11:23:06] Email sent <NAME_EMAIL>
[2025-03-29 11:23:07] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 11:23:10] Email sent <NAME_EMAIL>
[2025-03-29 11:23:10] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 11:23:13] Email sent <NAME_EMAIL>
[2025-03-29 11:25:00] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 11:25:04] Email sent <NAME_EMAIL>
[2025-03-29 11:25:04] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 11:25:07] Email sent <NAME_EMAIL>
[2025-03-29 11:25:07] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 11:25:10] Email sent <NAME_EMAIL>
[2025-03-29 11:25:10] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 11:25:14] Email sent <NAME_EMAIL>
[2025-03-29 11:25:14] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 11:25:16] Email sent <NAME_EMAIL>
[2025-03-29 11:25:16] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 11:25:19] Email sent <NAME_EMAIL>
[2025-03-29 11:25:20] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 11:25:23] Email sent <NAME_EMAIL>
[2025-03-29 11:25:23] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 11:25:27] Email sent <NAME_EMAIL>
[2025-03-29 11:25:27] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 11:25:30] Email sent <NAME_EMAIL>
[2025-03-29 11:25:30] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 11:25:33] Email sent <NAME_EMAIL>
[2025-03-29 11:28:40] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 11:28:42] Email sent <NAME_EMAIL>
[2025-03-29 11:28:42] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 11:28:45] Email sent <NAME_EMAIL>
[2025-03-29 11:28:45] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 11:28:49] Email sent <NAME_EMAIL>
[2025-03-29 11:28:49] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 11:28:51] Email sent <NAME_EMAIL>
[2025-03-29 11:28:51] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 11:28:54] Email sent <NAME_EMAIL>
[2025-03-29 11:28:54] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 11:28:58] Email sent <NAME_EMAIL>
[2025-03-29 11:28:58] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 11:29:00] Email sent <NAME_EMAIL>
[2025-03-29 11:29:00] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 11:29:03] Email sent <NAME_EMAIL>
[2025-03-29 11:29:03] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 11:29:06] Email sent <NAME_EMAIL>
[2025-03-29 11:29:06] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 11:29:10] Email sent <NAME_EMAIL>
[2025-03-29 11:33:11] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 11:33:14] Email sent <NAME_EMAIL>
[2025-03-29 11:33:14] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 11:33:17] Email sent <NAME_EMAIL>
[2025-03-29 11:33:17] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 11:33:20] Email sent <NAME_EMAIL>
[2025-03-29 11:33:20] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 11:33:23] Email sent <NAME_EMAIL>
[2025-03-29 11:33:23] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 11:33:26] Email sent <NAME_EMAIL>
[2025-03-29 11:33:26] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 11:33:29] Email sent <NAME_EMAIL>
[2025-03-29 11:33:29] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 11:33:33] Email sent <NAME_EMAIL>
[2025-03-29 11:33:33] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 11:33:36] Email sent <NAME_EMAIL>
[2025-03-29 11:33:36] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 11:33:39] Email sent <NAME_EMAIL>
[2025-03-29 11:33:39] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 11:33:42] Email sent <NAME_EMAIL>
[2025-03-29 11:34:22] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 11:34:24] Email sent <NAME_EMAIL>
[2025-03-29 11:34:24] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 11:34:28] Email sent <NAME_EMAIL>
[2025-03-29 11:34:28] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 11:34:31] Email sent <NAME_EMAIL>
[2025-03-29 11:34:31] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 11:34:34] Email sent <NAME_EMAIL>
[2025-03-29 11:34:34] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 11:34:37] Email sent <NAME_EMAIL>
[2025-03-29 11:34:37] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 11:34:39] Email sent <NAME_EMAIL>
[2025-03-29 11:34:40] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 11:34:42] Email sent <NAME_EMAIL>
[2025-03-29 11:34:42] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 11:34:45] Email sent <NAME_EMAIL>
[2025-03-29 11:34:45] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 11:34:48] Email sent <NAME_EMAIL>
[2025-03-29 11:34:48] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 11:34:51] Email sent <NAME_EMAIL>
[2025-03-29 11:37:39] Sending scheduled email to Ndivhuwo Machiba <<EMAIL>>
[2025-03-29 11:37:42] Email sent <NAME_EMAIL>
[2025-03-29 11:37:42] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 11:37:44] Email sent <NAME_EMAIL>
[2025-03-29 11:37:45] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 11:37:48] Email sent <NAME_EMAIL>
[2025-03-29 11:37:48] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 11:37:50] Email sent <NAME_EMAIL>
[2025-03-29 11:37:51] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 11:37:53] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:37:54] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 11:37:57] Email sent <NAME_EMAIL>
[2025-03-29 11:37:58] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 11:38:00] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:38:00] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 11:38:03] Email sent <NAME_EMAIL>
[2025-03-29 11:38:03] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 11:38:05] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:38:05] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 11:38:09] Email sent <NAME_EMAIL>
[2025-03-29 11:38:26] Sending scheduled email to Ndivhuwo Machiba <<EMAIL>>
[2025-03-29 11:38:29] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:38:29] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 11:38:33] Email sent <NAME_EMAIL>
[2025-03-29 11:38:33] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 11:38:36] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:38:36] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 11:38:39] Email sent <NAME_EMAIL>
[2025-03-29 11:38:39] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 11:38:42] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:38:42] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 11:38:44] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:38:45] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 11:38:47] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:38:47] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 11:38:50] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:38:51] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 11:38:54] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:38:54] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 11:38:57] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:41:12] Sending scheduled email to Ndivhuwo Machiba <<EMAIL>>
[2025-03-29 11:41:14] Email sent <NAME_EMAIL>
[2025-03-29 11:41:15] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 11:41:19] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:41:20] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 11:41:22] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:41:23] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 11:41:25] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:41:25] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 11:41:28] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:41:28] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 11:41:31] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:41:32] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 11:41:34] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:41:34] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 11:41:37] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:41:37] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 11:41:39] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:41:40] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 11:41:43] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:45:45] Sending scheduled email to Ndivhuwo Machiba <<EMAIL>>
[2025-03-29 11:45:48] Email sent <NAME_EMAIL>
[2025-03-29 11:45:48] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 11:45:50] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:45:51] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 11:45:54] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:45:54] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 11:45:57] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:45:57] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 11:46:00] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:46:00] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 11:46:03] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:46:03] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 11:46:06] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:46:06] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 11:46:09] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:46:09] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 11:46:12] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:46:12] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 11:46:14] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:51:06] Sending scheduled email to Ndivhuwo Machiba <<EMAIL>>
[2025-03-29 11:51:09] Email sent <NAME_EMAIL>
[2025-03-29 11:51:09] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 11:51:11] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:51:12] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 11:51:14] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:51:14] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 11:51:17] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:51:17] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 11:51:19] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:51:20] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 11:51:22] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:51:22] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 11:51:25] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:51:25] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 11:51:28] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:51:28] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 11:51:30] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:51:31] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 11:51:33] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:57:02] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 11:57:02] Added tracking pixel with ID: scheduled_67e7d1fe28b006.********
[2025-03-29 11:57:03] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:57:03] [DEBUG] CLIENT -> SERVER: EHLO USER

[2025-03-29 11:57:03] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:57:03] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:57:03] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:57:03] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:57:03] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:57:03] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:57:04] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:57:04] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:57:04] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:57:04] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:57:02 +0100

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: To: Ndivhuwo119 <<EMAIL>>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: Subject: Celebrating Ndivhuwo119's Special Day with Freedom Assembly

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: Message-ID: <lP9q2cpHEUHAWvt6tsO3gbZvTAIUv1MVmQSHGbWk@USER>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: 'Poppins', sans-serif; color: #333; background-color: #eef7ff; padding: 25px;">

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:     <div style="text-align: center; background: linear-gradient(135deg, #4facfe, #00c6fb); padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         <h1 style="color: #fff; font-size: 36px; margin-bottom: 15px;">🎉🎂 Happy Birthday, Ndivhuwo119! 🎂🎉</h1>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         <img src="http://localhost/church/assets/img/default-profile.jpg" alt="Ndivhuwo119" 

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             style="width: 170px; height: 170px; border-radius: 50%; margin: 15px auto; display: block; 

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             object-fit: cover; border: 6px solid #fff; box-shadow: 0 5px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #ffffff; padding: 20px; border-radius: 15px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;">Dear Ndivhuwo119,</p>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:                 🎈 On behalf of <strong>Freedom Assembly Church International</strong>, we celebrate you today! 

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:                 May your special day overflow with happiness, love, and divine favor. 💖🎊

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:                 You are a cherished part of our family, and we pray that this year brings you closer to your destiny and showers you with abundant blessings. 🌟🙏

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #eaf6ff; border-left: 5px solid #3498db; padding: 20px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             <h2 style="color: #2980b9; font-size: 22px; margin-bottom: 12px;">💖 Birthday Blessing ✨</h2>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             <p style="font-style: italic; color: #555; font-size: 16px; line-height: 1.6;">

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:                 "May He give you the desire of your heart and make all your plans succeed." 

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:                 <br><strong>- Psalm 20:4 🙏📖</strong>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             🌟 Wishing you a year filled with joy, success, and divine breakthroughs! Enjoy your special day! 🎊🎂🥳

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         </p>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         <a href="{special_birthday_gift_link}" style="display: inline-block; background-color: #ff6b6b; color: #fff; padding: 12px 20px; border-radius: 8px; text-decoration: none; font-weight: bold; margin-top: 15px; box-shadow: 0 3px 8px rgba(0,0,0,0.2);">

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             🎁 Enjoy Your Day!

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         </a>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:                 With Love & Prayers, <br>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:                 <strong>Freedom Assembly Church International ❤️🙏</strong>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: </div><img src="http://localhost/church/track.php?id=scheduled_67e7d1fe28b006.********&type=scheduled" alt="" width="1" height="1" style="display:none;width:1px;height:1px;">

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:04] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:57:05] [DEBUG] SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4ZPvV82rzpz2mRM1

[2025-03-29 11:57:05] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:57:05] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:57:05] Email sent <NAME_EMAIL>
[2025-03-29 11:57:10] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 11:57:10] Added tracking pixel with ID: scheduled_67e7d206536da3.58908818
[2025-03-29 11:57:11] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:57:11] [DEBUG] CLIENT -> SERVER: EHLO USER

[2025-03-29 11:57:11] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:57:11] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:57:12] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:57:12] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:57:12] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:57:12] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:57:12] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:57:12] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:57:12] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:57:12] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:57:13] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:57:13] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:57:32] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:57:10 +0100

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: To: Bointa04 <<EMAIL>>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: Subject: Celebrating Bointa04's Special Day with Freedom Assembly

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: Message-ID: <8nnNVwnBsQp2UG7mcbgxNgsqjdL0G1AyNUSiv9uA@USER>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: 'Poppins', sans-serif; color: #333; background-color: #eef7ff; padding: 25px;">

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:     <div style="text-align: center; background: linear-gradient(135deg, #4facfe, #00c6fb); padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         <h1 style="color: #fff; font-size: 36px; margin-bottom: 15px;">🎉🎂 Happy Birthday, Bointa04! 🎂🎉</h1>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         <img src="http://localhost/church/assets/img/default-profile.jpg" alt="Bointa04" 

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             style="width: 170px; height: 170px; border-radius: 50%; margin: 15px auto; display: block; 

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             object-fit: cover; border: 6px solid #fff; box-shadow: 0 5px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #ffffff; padding: 20px; border-radius: 15px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;">Dear Bointa04,</p>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:                 🎈 On behalf of <strong>Freedom Assembly Church International</strong>, we celebrate you today! 

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:                 May your special day overflow with happiness, love, and divine favor. 💖🎊

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:                 You are a cherished part of our family, and we pray that this year brings you closer to your destiny and showers you with abundant blessings. 🌟🙏

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #eaf6ff; border-left: 5px solid #3498db; padding: 20px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             <h2 style="color: #2980b9; font-size: 22px; margin-bottom: 12px;">💖 Birthday Blessing ✨</h2>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             <p style="font-style: italic; color: #555; font-size: 16px; line-height: 1.6;">

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:                 "May He give you the desire of your heart and make all your plans succeed." 

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:                 <br><strong>- Psalm 20:4 🙏📖</strong>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             🌟 Wishing you a year filled with joy, success, and divine breakthroughs! Enjoy your special day! 🎊🎂🥳

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         </p>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         <a href="{special_birthday_gift_link}" style="display: inline-block; background-color: #ff6b6b; color: #fff; padding: 12px 20px; border-radius: 8px; text-decoration: none; font-weight: bold; margin-top: 15px; box-shadow: 0 3px 8px rgba(0,0,0,0.2);">

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             🎁 Enjoy Your Day!

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         </a>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:                 With Love & Prayers, <br>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:                 <strong>Freedom Assembly Church International ❤️🙏</strong>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: </div><img src="http://localhost/church/track.php?id=scheduled_67e7d206536da3.58908818&type=scheduled" alt="" width="1" height="1" style="display:none;width:1px;height:1px;">

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:32] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:57:33] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:57:33] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:57:33] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:57:33] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:57:33] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:57:33] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:57:38] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 11:57:38] Added tracking pixel with ID: scheduled_67e7d2224952a0.20907611
[2025-03-29 11:57:39] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:57:39] [DEBUG] CLIENT -> SERVER: EHLO USER

[2025-03-29 11:57:39] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:57:39] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:57:39] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:57:39] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:57:39] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:57:39] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:57:40] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:57:40] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:57:40] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:57:40] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:57:38 +0100

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: To: Lilbossbaby11 <<EMAIL>>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: Subject: Celebrating Lilbossbaby11's Special Day with Freedom Assembly

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: Message-ID: <Sgq7eK1pLsNocvaug6z730xlQ1X2AWxM4xguMlZMM@USER>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: 'Poppins', sans-serif; color: #333; background-color: #eef7ff; padding: 25px;">

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:     <div style="text-align: center; background: linear-gradient(135deg, #4facfe, #00c6fb); padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         <h1 style="color: #fff; font-size: 36px; margin-bottom: 15px;">🎉🎂 Happy Birthday, Lilbossbaby11! 🎂🎉</h1>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         <img src="http://localhost/church/assets/img/default-profile.jpg" alt="Lilbossbaby11" 

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             style="width: 170px; height: 170px; border-radius: 50%; margin: 15px auto; display: block; 

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             object-fit: cover; border: 6px solid #fff; box-shadow: 0 5px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #ffffff; padding: 20px; border-radius: 15px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;">Dear Lilbossbaby11,</p>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:                 🎈 On behalf of <strong>Freedom Assembly Church International</strong>, we celebrate you today! 

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:                 May your special day overflow with happiness, love, and divine favor. 💖🎊

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:                 You are a cherished part of our family, and we pray that this year brings you closer to your destiny and showers you with abundant blessings. 🌟🙏

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #eaf6ff; border-left: 5px solid #3498db; padding: 20px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             <h2 style="color: #2980b9; font-size: 22px; margin-bottom: 12px;">💖 Birthday Blessing ✨</h2>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             <p style="font-style: italic; color: #555; font-size: 16px; line-height: 1.6;">

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:                 "May He give you the desire of your heart and make all your plans succeed." 

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:                 <br><strong>- Psalm 20:4 🙏📖</strong>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             🌟 Wishing you a year filled with joy, success, and divine breakthroughs! Enjoy your special day! 🎊🎂🥳

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         </p>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         <a href="{special_birthday_gift_link}" style="display: inline-block; background-color: #ff6b6b; color: #fff; padding: 12px 20px; border-radius: 8px; text-decoration: none; font-weight: bold; margin-top: 15px; box-shadow: 0 3px 8px rgba(0,0,0,0.2);">

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             🎁 Enjoy Your Day!

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         </a>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:                 With Love & Prayers, <br>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:                 <strong>Freedom Assembly Church International ❤️🙏</strong>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: </div><img src="http://localhost/church/track.php?id=scheduled_67e7d2224952a0.20907611&type=scheduled" alt="" width="1" height="1" style="display:none;width:1px;height:1px;">

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:57:40] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:57:40] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:57:40] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:57:40] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:57:40] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:57:41] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:57:46] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 11:57:46] Added tracking pixel with ID: scheduled_67e7d22a1b0914.73724226
[2025-03-29 11:57:47] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:57:47] [DEBUG] CLIENT -> SERVER: EHLO USER

[2025-03-29 11:57:47] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:57:47] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:57:47] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:57:47] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:57:47] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:57:47] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:57:48] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:57:48] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:57:48] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:57:48] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:57:46 +0100

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: To: Saddafasif2 <<EMAIL>>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: Subject: Celebrating Saddafasif2's Special Day with Freedom Assembly

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: Message-ID: <cVEUh36yHl6QxLCKia4aNNazGIVPfOu6olAuXD0YpU@USER>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: 'Poppins', sans-serif; color: #333; background-color: #eef7ff; padding: 25px;">

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:     <div style="text-align: center; background: linear-gradient(135deg, #4facfe, #00c6fb); padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         <h1 style="color: #fff; font-size: 36px; margin-bottom: 15px;">🎉🎂 Happy Birthday, Saddafasif2! 🎂🎉</h1>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         <img src="http://localhost/church/assets/img/default-profile.jpg" alt="Saddafasif2" 

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             style="width: 170px; height: 170px; border-radius: 50%; margin: 15px auto; display: block; 

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             object-fit: cover; border: 6px solid #fff; box-shadow: 0 5px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #ffffff; padding: 20px; border-radius: 15px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;">Dear Saddafasif2,</p>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:                 🎈 On behalf of <strong>Freedom Assembly Church International</strong>, we celebrate you today! 

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:                 May your special day overflow with happiness, love, and divine favor. 💖🎊

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:                 You are a cherished part of our family, and we pray that this year brings you closer to your destiny and showers you with abundant blessings. 🌟🙏

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #eaf6ff; border-left: 5px solid #3498db; padding: 20px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             <h2 style="color: #2980b9; font-size: 22px; margin-bottom: 12px;">💖 Birthday Blessing ✨</h2>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             <p style="font-style: italic; color: #555; font-size: 16px; line-height: 1.6;">

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:                 "May He give you the desire of your heart and make all your plans succeed." 

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:                 <br><strong>- Psalm 20:4 🙏📖</strong>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             🌟 Wishing you a year filled with joy, success, and divine breakthroughs! Enjoy your special day! 🎊🎂🥳

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         </p>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         <a href="{special_birthday_gift_link}" style="display: inline-block; background-color: #ff6b6b; color: #fff; padding: 12px 20px; border-radius: 8px; text-decoration: none; font-weight: bold; margin-top: 15px; box-shadow: 0 3px 8px rgba(0,0,0,0.2);">

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             🎁 Enjoy Your Day!

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         </a>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:                 With Love & Prayers, <br>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:                 <strong>Freedom Assembly Church International ❤️🙏</strong>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: </div><img src="http://localhost/church/track.php?id=scheduled_67e7d22a1b0914.73724226&type=scheduled" alt="" width="1" height="1" style="display:none;width:1px;height:1px;">

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:57:48] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:57:48] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:57:48] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:57:48] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:57:48] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:57:49] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:57:54] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 11:57:54] Added tracking pixel with ID: scheduled_67e7d2321fede3.20938505
[2025-03-29 11:57:54] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:57:54] [DEBUG] CLIENT -> SERVER: EHLO USER

[2025-03-29 11:57:55] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:57:55] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:57:55] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:57:55] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:57:55] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:57:55] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:57:55] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:57:55] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:57:55] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:57:55] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:57:55] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:57:55] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:57:56] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:57:54 +0100

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: To: Enespaul002 <<EMAIL>>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: Subject: Celebrating Enespaul002's Special Day with Freedom Assembly

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: Message-ID: <63NWfPN3nQC5x9Tmyfghnit1I0nRZVPXxVErV6t4mr0@USER>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: 'Poppins', sans-serif; color: #333; background-color: #eef7ff; padding: 25px;">

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:     <div style="text-align: center; background: linear-gradient(135deg, #4facfe, #00c6fb); padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         <h1 style="color: #fff; font-size: 36px; margin-bottom: 15px;">🎉🎂 Happy Birthday, Enespaul002! 🎂🎉</h1>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         <img src="http://localhost/church/assets/img/default-profile.jpg" alt="Enespaul002" 

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             style="width: 170px; height: 170px; border-radius: 50%; margin: 15px auto; display: block; 

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             object-fit: cover; border: 6px solid #fff; box-shadow: 0 5px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #ffffff; padding: 20px; border-radius: 15px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;">Dear Enespaul002,</p>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:                 🎈 On behalf of <strong>Freedom Assembly Church International</strong>, we celebrate you today! 

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:                 May your special day overflow with happiness, love, and divine favor. 💖🎊

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:                 You are a cherished part of our family, and we pray that this year brings you closer to your destiny and showers you with abundant blessings. 🌟🙏

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #eaf6ff; border-left: 5px solid #3498db; padding: 20px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             <h2 style="color: #2980b9; font-size: 22px; margin-bottom: 12px;">💖 Birthday Blessing ✨</h2>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             <p style="font-style: italic; color: #555; font-size: 16px; line-height: 1.6;">

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:                 "May He give you the desire of your heart and make all your plans succeed." 

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:                 <br><strong>- Psalm 20:4 🙏📖</strong>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             🌟 Wishing you a year filled with joy, success, and divine breakthroughs! Enjoy your special day! 🎊🎂🥳

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         </p>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         <a href="{special_birthday_gift_link}" style="display: inline-block; background-color: #ff6b6b; color: #fff; padding: 12px 20px; border-radius: 8px; text-decoration: none; font-weight: bold; margin-top: 15px; box-shadow: 0 3px 8px rgba(0,0,0,0.2);">

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             🎁 Enjoy Your Day!

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         </a>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:                 With Love & Prayers, <br>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:                 <strong>Freedom Assembly Church International ❤️🙏</strong>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: </div><img src="http://localhost/church/track.php?id=scheduled_67e7d2321fede3.20938505&type=scheduled" alt="" width="1" height="1" style="display:none;width:1px;height:1px;">

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:57:56] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:57:56] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:57:56] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:57:56] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:57:56] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:57:56] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:58:01] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 11:58:01] Added tracking pixel with ID: scheduled_67e7d239c8bac4.58100629
[2025-03-29 11:58:03] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:58:03] [DEBUG] CLIENT -> SERVER: EHLO USER

[2025-03-29 11:58:03] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:58:03] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:58:03] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:58:03] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:58:03] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:58:03] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:58:04] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:58:04] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:58:04] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:58:04] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:58:01 +0100

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: To: Support <<EMAIL>>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: Subject: Celebrating Support's Special Day with Freedom Assembly

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: Message-ID: <x3i1LfDvWp5knsEbC78Il5yOQLDfPN0yMpi7ngKamE@USER>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: 'Poppins', sans-serif; color: #333; background-color: #eef7ff; padding: 25px;">

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:     <div style="text-align: center; background: linear-gradient(135deg, #4facfe, #00c6fb); padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         <h1 style="color: #fff; font-size: 36px; margin-bottom: 15px;">🎉🎂 Happy Birthday, Support! 🎂🎉</h1>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         <img src="http://localhost/church/assets/img/default-profile.jpg" alt="Support" 

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             style="width: 170px; height: 170px; border-radius: 50%; margin: 15px auto; display: block; 

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             object-fit: cover; border: 6px solid #fff; box-shadow: 0 5px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #ffffff; padding: 20px; border-radius: 15px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;">Dear Support,</p>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:                 🎈 On behalf of <strong>Freedom Assembly Church International</strong>, we celebrate you today! 

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:                 May your special day overflow with happiness, love, and divine favor. 💖🎊

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:                 You are a cherished part of our family, and we pray that this year brings you closer to your destiny and showers you with abundant blessings. 🌟🙏

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #eaf6ff; border-left: 5px solid #3498db; padding: 20px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             <h2 style="color: #2980b9; font-size: 22px; margin-bottom: 12px;">💖 Birthday Blessing ✨</h2>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             <p style="font-style: italic; color: #555; font-size: 16px; line-height: 1.6;">

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:                 "May He give you the desire of your heart and make all your plans succeed." 

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:                 <br><strong>- Psalm 20:4 🙏📖</strong>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             🌟 Wishing you a year filled with joy, success, and divine breakthroughs! Enjoy your special day! 🎊🎂🥳

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         </p>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         <a href="{special_birthday_gift_link}" style="display: inline-block; background-color: #ff6b6b; color: #fff; padding: 12px 20px; border-radius: 8px; text-decoration: none; font-weight: bold; margin-top: 15px; box-shadow: 0 3px 8px rgba(0,0,0,0.2);">

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             🎁 Enjoy Your Day!

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         </a>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:                 With Love & Prayers, <br>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:                 <strong>Freedom Assembly Church International ❤️🙏</strong>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: </div><img src="http://localhost/church/track.php?id=scheduled_67e7d239c8bac4.58100629&type=scheduled" alt="" width="1" height="1" style="display:none;width:1px;height:1px;">

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:04] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:58:05] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:58:05] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:58:05] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:58:05] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:58:05] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:58:05] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:58:10] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 11:58:10] Added tracking pixel with ID: scheduled_67e7d2424097e2.28654332
[2025-03-29 11:58:11] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:58:11] [DEBUG] CLIENT -> SERVER: EHLO USER

[2025-03-29 11:58:11] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:58:11] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:58:12] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:58:12] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:58:12] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:58:12] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:58:12] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:58:12] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:58:12] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:58:12] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:58:12] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:58:12] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:58:13] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:58:10 +0100

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: To: Info <<EMAIL>>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: Subject: Celebrating Info's Special Day with Freedom Assembly

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: Message-ID: <Qi2KVCzn5MS9nJqJo8E6RfDW11Qj3gLw7NXe8Kngs@USER>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: 'Poppins', sans-serif; color: #333; background-color: #eef7ff; padding: 25px;">

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:     <div style="text-align: center; background: linear-gradient(135deg, #4facfe, #00c6fb); padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         <h1 style="color: #fff; font-size: 36px; margin-bottom: 15px;">🎉🎂 Happy Birthday, Info! 🎂🎉</h1>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         <img src="http://localhost/church/assets/img/default-profile.jpg" alt="Info" 

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             style="width: 170px; height: 170px; border-radius: 50%; margin: 15px auto; display: block; 

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             object-fit: cover; border: 6px solid #fff; box-shadow: 0 5px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #ffffff; padding: 20px; border-radius: 15px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;">Dear Info,</p>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:                 🎈 On behalf of <strong>Freedom Assembly Church International</strong>, we celebrate you today! 

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:                 May your special day overflow with happiness, love, and divine favor. 💖🎊

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:                 You are a cherished part of our family, and we pray that this year brings you closer to your destiny and showers you with abundant blessings. 🌟🙏

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #eaf6ff; border-left: 5px solid #3498db; padding: 20px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             <h2 style="color: #2980b9; font-size: 22px; margin-bottom: 12px;">💖 Birthday Blessing ✨</h2>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             <p style="font-style: italic; color: #555; font-size: 16px; line-height: 1.6;">

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:                 "May He give you the desire of your heart and make all your plans succeed." 

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:                 <br><strong>- Psalm 20:4 🙏📖</strong>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             🌟 Wishing you a year filled with joy, success, and divine breakthroughs! Enjoy your special day! 🎊🎂🥳

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         </p>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         <a href="{special_birthday_gift_link}" style="display: inline-block; background-color: #ff6b6b; color: #fff; padding: 12px 20px; border-radius: 8px; text-decoration: none; font-weight: bold; margin-top: 15px; box-shadow: 0 3px 8px rgba(0,0,0,0.2);">

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             🎁 Enjoy Your Day!

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         </a>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:                 With Love & Prayers, <br>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:                 <strong>Freedom Assembly Church International ❤️🙏</strong>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: </div><img src="http://localhost/church/track.php?id=scheduled_67e7d2424097e2.28654332&type=scheduled" alt="" width="1" height="1" style="display:none;width:1px;height:1px;">

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:58:13] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:58:13] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:58:13] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:58:13] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:58:13] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:58:13] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:58:18] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 11:58:18] Added tracking pixel with ID: scheduled_67e7d24aa69977.********
[2025-03-29 11:58:19] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:58:19] [DEBUG] CLIENT -> SERVER: EHLO USER

[2025-03-29 11:58:19] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:58:19] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:58:20] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:58:20] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:58:20] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:58:20] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:58:20] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:58:20] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:58:20] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:58:20] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:58:20] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:58:20] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:58:21] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:58:18 +0100

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: To: Account <<EMAIL>>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: Subject: Celebrating Account's Special Day with Freedom Assembly

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: Message-ID: <vhNqQefC7gcLwkt5oQkFKUIHfsxhimmXD6x4FVBE@USER>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: 'Poppins', sans-serif; color: #333; background-color: #eef7ff; padding: 25px;">

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:     <div style="text-align: center; background: linear-gradient(135deg, #4facfe, #00c6fb); padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         <h1 style="color: #fff; font-size: 36px; margin-bottom: 15px;">🎉🎂 Happy Birthday, Account! 🎂🎉</h1>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         <img src="http://localhost/church/assets/img/default-profile.jpg" alt="Account" 

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             style="width: 170px; height: 170px; border-radius: 50%; margin: 15px auto; display: block; 

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             object-fit: cover; border: 6px solid #fff; box-shadow: 0 5px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #ffffff; padding: 20px; border-radius: 15px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;">Dear Account,</p>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:                 🎈 On behalf of <strong>Freedom Assembly Church International</strong>, we celebrate you today! 

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:                 May your special day overflow with happiness, love, and divine favor. 💖🎊

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:                 You are a cherished part of our family, and we pray that this year brings you closer to your destiny and showers you with abundant blessings. 🌟🙏

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #eaf6ff; border-left: 5px solid #3498db; padding: 20px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             <h2 style="color: #2980b9; font-size: 22px; margin-bottom: 12px;">💖 Birthday Blessing ✨</h2>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             <p style="font-style: italic; color: #555; font-size: 16px; line-height: 1.6;">

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:                 "May He give you the desire of your heart and make all your plans succeed." 

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:                 <br><strong>- Psalm 20:4 🙏📖</strong>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             🌟 Wishing you a year filled with joy, success, and divine breakthroughs! Enjoy your special day! 🎊🎂🥳

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         </p>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         <a href="{special_birthday_gift_link}" style="display: inline-block; background-color: #ff6b6b; color: #fff; padding: 12px 20px; border-radius: 8px; text-decoration: none; font-weight: bold; margin-top: 15px; box-shadow: 0 3px 8px rgba(0,0,0,0.2);">

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             🎁 Enjoy Your Day!

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         </a>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:                 With Love & Prayers, <br>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:                 <strong>Freedom Assembly Church International ❤️🙏</strong>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: </div><img src="http://localhost/church/track.php?id=scheduled_67e7d24aa69977.********&type=scheduled" alt="" width="1" height="1" style="display:none;width:1px;height:1px;">

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:58:21] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:58:21] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:58:21] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:58:21] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:58:21] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:58:21] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:58:26] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 11:58:26] Added tracking pixel with ID: scheduled_67e7d252af8d32.30596028
[2025-03-29 11:58:27] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:58:27] [DEBUG] CLIENT -> SERVER: EHLO USER

[2025-03-29 11:58:27] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:58:27] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:58:28] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:58:28] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:58:28] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:58:28] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:58:28] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:58:28] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:58:26 +0100

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: To: Henjoybid <<EMAIL>>

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: Subject: Celebrating Henjoybid's Special Day with Freedom Assembly

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: Message-ID: <okiIwrMMk8PWBP80k19Ld24VeCHAujZ2lkxVVVf9M@USER>

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: 'Poppins', sans-serif; color: #333; background-color: #eef7ff; padding: 25px;">

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:     <div style="text-align: center; background: linear-gradient(135deg, #4facfe, #00c6fb); padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:         <h1 style="color: #fff; font-size: 36px; margin-bottom: 15px;">🎉🎂 Happy Birthday, Henjoybid! 🎂🎉</h1>

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:         <img src="http://localhost/church/assets/img/default-profile.jpg" alt="Henjoybid" 

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:             style="width: 170px; height: 170px; border-radius: 50%; margin: 15px auto; display: block; 

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:             object-fit: cover; border: 6px solid #fff; box-shadow: 0 5px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #ffffff; padding: 20px; border-radius: 15px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;">Dear Henjoybid,</p>

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:                 🎈 On behalf of <strong>Freedom Assembly Church International</strong>, we celebrate you today! 

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:                 May your special day overflow with happiness, love, and divine favor. 💖🎊

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:                 You are a cherished part of our family, and we pray that this year brings you closer to your destiny and showers you with abundant blessings. 🌟🙏

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #eaf6ff; border-left: 5px solid #3498db; padding: 20px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:             <h2 style="color: #2980b9; font-size: 22px; margin-bottom: 12px;">💖 Birthday Blessing ✨</h2>

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:             <p style="font-style: italic; color: #555; font-size: 16px; line-height: 1.6;">

[2025-03-29 11:58:28] [DEBUG] CLIENT -> SERVER:                 "May He give you the desire of your heart and make all your plans succeed." 

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:                 <br><strong>- Psalm 20:4 🙏📖</strong>

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:             🌟 Wishing you a year filled with joy, success, and divine breakthroughs! Enjoy your special day! 🎊🎂🥳

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:         </p>

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:         <a href="{special_birthday_gift_link}" style="display: inline-block; background-color: #ff6b6b; color: #fff; padding: 12px 20px; border-radius: 8px; text-decoration: none; font-weight: bold; margin-top: 15px; box-shadow: 0 3px 8px rgba(0,0,0,0.2);">

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:             🎁 Enjoy Your Day!

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:         </a>

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:                 With Love & Prayers, <br>

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:                 <strong>Freedom Assembly Church International ❤️🙏</strong>

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER: </div><img src="http://localhost/church/track.php?id=scheduled_67e7d252af8d32.30596028&type=scheduled" alt="" width="1" height="1" style="display:none;width:1px;height:1px;">

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:58:29] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:58:29] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:58:29] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:58:29] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:58:29] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:58:29] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:58:34] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 11:58:34] Added tracking pixel with ID: scheduled_67e7d25a674a73.34087530
[2025-03-29 11:58:35] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:58:35] [DEBUG] CLIENT -> SERVER: EHLO USER

[2025-03-29 11:58:35] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:58:35] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:58:35] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:58:35] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:58:36] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:58:36] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:58:36] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:58:36] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:58:36] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:58:36] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:58:37] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:58:37] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:58:34 +0100

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: To: Eggnipawe <<EMAIL>>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: Subject: Celebrating Eggnipawe's Special Day with Freedom Assembly

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: Message-ID: <d36xciHzVYESQmy3PmOYEtwUlZ0WePMUDDyAyY5FE@USER>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: 'Poppins', sans-serif; color: #333; background-color: #eef7ff; padding: 25px;">

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:     <div style="text-align: center; background: linear-gradient(135deg, #4facfe, #00c6fb); padding: 30px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         <h1 style="color: #fff; font-size: 36px; margin-bottom: 15px;">🎉🎂 Happy Birthday, Eggnipawe! 🎂🎉</h1>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         <img src="http://localhost/church/assets/img/default-profile.jpg" alt="Eggnipawe" 

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             style="width: 170px; height: 170px; border-radius: 50%; margin: 15px auto; display: block; 

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             object-fit: cover; border: 6px solid #fff; box-shadow: 0 5px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #ffffff; padding: 20px; border-radius: 15px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;">Dear Eggnipawe,</p>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:                 🎈 On behalf of <strong>Freedom Assembly Church International</strong>, we celebrate you today! 

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:                 May your special day overflow with happiness, love, and divine favor. 💖🎊

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; color: #444;">

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:                 You are a cherished part of our family, and we pray that this year brings you closer to your destiny and showers you with abundant blessings. 🌟🙏

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         <div style="background-color: #eaf6ff; border-left: 5px solid #3498db; padding: 20px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             <h2 style="color: #2980b9; font-size: 22px; margin-bottom: 12px;">💖 Birthday Blessing ✨</h2>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             <p style="font-style: italic; color: #555; font-size: 16px; line-height: 1.6;">

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:                 "May He give you the desire of your heart and make all your plans succeed." 

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:                 <br><strong>- Psalm 20:4 🙏📖</strong>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             🌟 Wishing you a year filled with joy, success, and divine breakthroughs! Enjoy your special day! 🎊🎂🥳

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         </p>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         <a href="{special_birthday_gift_link}" style="display: inline-block; background-color: #ff6b6b; color: #fff; padding: 12px 20px; border-radius: 8px; text-decoration: none; font-weight: bold; margin-top: 15px; box-shadow: 0 3px 8px rgba(0,0,0,0.2);">

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             🎁 Enjoy Your Day!

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         </a>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:                 With Love & Prayers, <br>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:                 <strong>Freedom Assembly Church International ❤️🙏</strong>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:             </p>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: </div><img src="http://localhost/church/track.php?id=scheduled_67e7d25a674a73.34087530&type=scheduled" alt="" width="1" height="1" style="display:none;width:1px;height:1px;">

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:58:37] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:58:37] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:58:37] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:58:37] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:58:37] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:58:37] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:58:56] Sending scheduled email to Ndivhuwo Machiba <<EMAIL>>
[2025-03-29 11:58:57] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:58:57] [DEBUG] CLIENT -> SERVER: EHLO localhost

[2025-03-29 11:58:58] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:58:58] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:58:58] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:58:58] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:58:58] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:58:58] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:58:58] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:58:58] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:58:58] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:58:58] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:58:59] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:58:59] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:58:56 +0100

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: To: Ndivhuwo Machiba <<EMAIL>>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: Subject: Newsletter from Church

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: Message-ID: <mOP0D34784LEuEckXnhrz6YGXN6trsfHt3fitFuV2s@localhost>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: <!DOCTYPE html>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: <html lang="en">

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: <head>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:     <meta http-equiv="X-UA-Compatible" content="IE=edge">

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:     <title>Newsletter from Church</title>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:     <style>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         body {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-family: "Segoe UI", Arial, sans-serif;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             background-color: #f7f9fc;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             color: #333333;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             line-height: 1.6;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .container {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             max-width: 600px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             margin: 20px auto;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             padding: 0 10px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             background: #ffffff;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             border-radius: 10px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .header {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             background: linear-gradient(135deg, #1e3c72, #2a5298);

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             padding: 30px 20px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             border-top-left-radius: 10px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             border-top-right-radius: 10px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .header h1 {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-size: 26px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .header p {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             margin: 5px 0 0;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             opacity: 0.9;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .content {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             padding: 20px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .greeting {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             margin: 0 0 15px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             color: #444444;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .message {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             background: #f0f4f8;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             border-radius: 8px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             border: 1px solid #d1d9e6;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .message h2 {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-size: 18px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .message p {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .updates {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .updates h3 {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             color: #2a5298;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .updates ul {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             list-style: none;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .updates li {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             padding: 8px 0;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             border-bottom: 1px solid #e0e0e0;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .updates li:last-child {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             border-bottom: none;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .cta {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .cta a {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             display: inline-block;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             background: #2ecc71;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             padding: 10px 20px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             border-radius: 5px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .footer {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             background: #f7f9fc;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-size: 12px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             border-top: 1px solid #e0e0e0;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             border-bottom-left-radius: 10px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             border-bottom-right-radius: 10px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .church-name {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-weight: bold;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .unsubscribe {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             margin-top: 8px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             font-size: 10px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         .unsubscribe a {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         @media (max-width: 480px) {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             .container {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 padding: 0 5px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             .header {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 padding: 20px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             .header h1 {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 font-size: 22px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             .content {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 padding: 15px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             .message h2 {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 font-size: 16px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             .updates h3 {

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 font-size: 15px;

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:     </style>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: </head>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: <body>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:     <div class="container">

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         <div class="header">

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             <h1>Newsletter from Church</h1>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             <p>Connecting with all  of our cherished members</p>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         <div class="content">

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             <div class="greeting">

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 <p>Hello Ndivhuwo,</p>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 <p>Welcome to this month's newsletter from Church. We're delighted to share these updates with you and all  members of our community.</p>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             <div class="message">

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 <h2>A Message from </h2>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 <p>Dear Ndivhuwo Machiba,</p>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 <p>I hope this message finds you well. At Church, we're grateful for your presence and commitment to our shared journey of faith. This newsletter is a small way to keep us connected and inspired. Thank you for being a vital part of our family!</p>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             <div class="updates">

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 <h3>Community Updates</h3>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 <ul>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                     <li>Join us this Sunday for a special service at 10 AM.</li>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                     <li>Our next fellowship event is scheduled for next Saturday's details to follow!</li>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                     <li>WeG??re launching a new outreach program's stay tuned for ways to get involved.</li>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                     <li>Prayer meetings are now every Wednesday at 7 PM.</li>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 </ul>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             <div class="cta">

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 <a href="#">Visit Our Website</a>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         <div class="footer">

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             <p>Sent with love from <span class="church-name">Church</span><br>By </p>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             <div class="unsubscribe">

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:                 <a href="#">Unsubscribe from these updates</a>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: </body>

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: </html><img src="http://localhost/track.php?id=track_67e7d270ced176.78826953&type=open" width="1" height="1" alt="" />

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:58:59] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:58:59] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:58:59] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:58:59] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:58:59] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:58:59] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:58:59] Sending scheduled email to Godwin Bointa <<EMAIL>>
[2025-03-29 11:59:00] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:59:00] [DEBUG] CLIENT -> SERVER: EHLO localhost

[2025-03-29 11:59:00] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:59:00] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:59:01] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:01] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:01] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:59:01] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:59:01] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:59:01] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:59:00 +0100

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: To: Godwin Bointa <<EMAIL>>

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: Subject: Newsletter from Church

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: Message-ID: <uTVUpFvumwLzGx1bq1w6DDSAnuPjQR9PBYUPGpeKqN0@localhost>

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: <!DOCTYPE html>

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: <html lang="en">

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER: <head>

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:     <meta http-equiv="X-UA-Compatible" content="IE=edge">

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:     <title>Newsletter from Church</title>

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:     <style>

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:         body {

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             font-family: "Segoe UI", Arial, sans-serif;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             background-color: #f7f9fc;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             color: #333333;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             line-height: 1.6;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:         .container {

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             max-width: 600px;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             margin: 20px auto;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             padding: 0 10px;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             background: #ffffff;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             border-radius: 10px;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:         .header {

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             background: linear-gradient(135deg, #1e3c72, #2a5298);

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             padding: 30px 20px;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             border-top-left-radius: 10px;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             border-top-right-radius: 10px;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:         .header h1 {

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             font-size: 26px;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:         .header p {

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             margin: 5px 0 0;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             opacity: 0.9;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:         .content {

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             padding: 20px;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:         .greeting {

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             margin: 0 0 15px;

[2025-03-29 11:59:01] [DEBUG] CLIENT -> SERVER:             color: #444444;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .message {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             background: #f0f4f8;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             border-radius: 8px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             border: 1px solid #d1d9e6;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .message h2 {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             font-size: 18px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .message p {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .updates {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .updates h3 {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             color: #2a5298;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .updates ul {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             list-style: none;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .updates li {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             padding: 8px 0;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             border-bottom: 1px solid #e0e0e0;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .updates li:last-child {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             border-bottom: none;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .cta {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .cta a {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             display: inline-block;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             background: #2ecc71;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             padding: 10px 20px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             border-radius: 5px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .footer {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             background: #f7f9fc;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             font-size: 12px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             border-top: 1px solid #e0e0e0;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             border-bottom-left-radius: 10px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             border-bottom-right-radius: 10px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .church-name {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             font-weight: bold;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .unsubscribe {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             margin-top: 8px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             font-size: 10px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         .unsubscribe a {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         @media (max-width: 480px) {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             .container {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 padding: 0 5px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             .header {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 padding: 20px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             .header h1 {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 font-size: 22px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             .content {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 padding: 15px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             .message h2 {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 font-size: 16px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             .updates h3 {

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 font-size: 15px;

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:     </style>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER: </head>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER: <body>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:     <div class="container">

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         <div class="header">

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             <h1>Newsletter from Church</h1>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             <p>Connecting with all  of our cherished members</p>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         <div class="content">

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             <div class="greeting">

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 <p>Hello Godwin,</p>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 <p>Welcome to this month's newsletter from Church. We're delighted to share these updates with you and all  members of our community.</p>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             <div class="message">

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 <h2>A Message from </h2>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 <p>Dear Godwin Bointa,</p>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 <p>I hope this message finds you well. At Church, we're grateful for your presence and commitment to our shared journey of faith. This newsletter is a small way to keep us connected and inspired. Thank you for being a vital part of our family!</p>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             <div class="updates">

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 <h3>Community Updates</h3>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 <ul>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                     <li>Join us this Sunday for a special service at 10 AM.</li>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                     <li>Our next fellowship event is scheduled for next Saturday's details to follow!</li>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                     <li>WeG??re launching a new outreach program's stay tuned for ways to get involved.</li>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                     <li>Prayer meetings are now every Wednesday at 7 PM.</li>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 </ul>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             <div class="cta">

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 <a href="#">Visit Our Website</a>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         <div class="footer">

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             <p>Sent with love from <span class="church-name">Church</span><br>By </p>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             <div class="unsubscribe">

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:                 <a href="#">Unsubscribe from these updates</a>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER: </body>

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER: </html><img src="http://localhost/track.php?id=track_67e7d273eeed12.59697281&type=open" width="1" height="1" alt="" />

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:59:02] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:02] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:02] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:59:02] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:59:02] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:59:02] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:59:02] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 11:59:03] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:59:03] [DEBUG] CLIENT -> SERVER: EHLO localhost

[2025-03-29 11:59:03] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:59:03] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:59:03] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:59:03] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:03] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:04] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:59:04] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:59:04] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:59:04] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:59:02 +0100

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: To: Lilbossbaby11 <<EMAIL>>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: Subject: Newsletter from Church

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: Message-ID: <q6JttGOyItTyc3EbTRyPZF24ICP72dU5aTXZZx5OsE@localhost>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: <!DOCTYPE html>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: <html lang="en">

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: <head>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:     <meta http-equiv="X-UA-Compatible" content="IE=edge">

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:     <title>Newsletter from Church</title>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:     <style>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         body {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-family: "Segoe UI", Arial, sans-serif;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             background-color: #f7f9fc;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             color: #333333;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             line-height: 1.6;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .container {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             max-width: 600px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             margin: 20px auto;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             padding: 0 10px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             background: #ffffff;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             border-radius: 10px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .header {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             background: linear-gradient(135deg, #1e3c72, #2a5298);

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             padding: 30px 20px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             border-top-left-radius: 10px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             border-top-right-radius: 10px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .header h1 {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-size: 26px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .header p {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             margin: 5px 0 0;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             opacity: 0.9;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .content {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             padding: 20px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .greeting {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             margin: 0 0 15px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             color: #444444;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .message {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             background: #f0f4f8;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             border-radius: 8px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             border: 1px solid #d1d9e6;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .message h2 {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-size: 18px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .message p {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .updates {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .updates h3 {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             color: #2a5298;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .updates ul {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             list-style: none;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .updates li {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             padding: 8px 0;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             border-bottom: 1px solid #e0e0e0;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .updates li:last-child {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             border-bottom: none;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .cta {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .cta a {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             display: inline-block;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             background: #2ecc71;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             padding: 10px 20px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             border-radius: 5px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .footer {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             background: #f7f9fc;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-size: 12px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             border-top: 1px solid #e0e0e0;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             border-bottom-left-radius: 10px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             border-bottom-right-radius: 10px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .church-name {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-weight: bold;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .unsubscribe {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             margin-top: 8px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             font-size: 10px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         .unsubscribe a {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         @media (max-width: 480px) {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             .container {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 padding: 0 5px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             .header {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 padding: 20px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             .header h1 {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 font-size: 22px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             .content {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 padding: 15px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             .message h2 {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 font-size: 16px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             .updates h3 {

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 font-size: 15px;

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:     </style>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: </head>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: <body>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:     <div class="container">

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         <div class="header">

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             <h1>Newsletter from Church</h1>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             <p>Connecting with all  of our cherished members</p>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         <div class="content">

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             <div class="greeting">

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 <p>Hello Lilbossbaby11,</p>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 <p>Welcome to this month's newsletter from Church. We're delighted to share these updates with you and all  members of our community.</p>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             <div class="message">

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 <h2>A Message from </h2>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 <p>Dear Lilbossbaby11,</p>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 <p>I hope this message finds you well. At Church, we're grateful for your presence and commitment to our shared journey of faith. This newsletter is a small way to keep us connected and inspired. Thank you for being a vital part of our family!</p>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             <div class="updates">

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 <h3>Community Updates</h3>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 <ul>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                     <li>Join us this Sunday for a special service at 10 AM.</li>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                     <li>Our next fellowship event is scheduled for next Saturday's details to follow!</li>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                     <li>WeG??re launching a new outreach program's stay tuned for ways to get involved.</li>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                     <li>Prayer meetings are now every Wednesday at 7 PM.</li>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 </ul>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             <div class="cta">

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 <a href="#">Visit Our Website</a>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         <div class="footer">

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             <p>Sent with love from <span class="church-name">Church</span><br>By </p>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             <div class="unsubscribe">

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:                 <a href="#">Unsubscribe from these updates</a>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: </body>

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: </html><img src="http://localhost/track.php?id=track_67e7d276b31440.19136356&type=open" width="1" height="1" alt="" />

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:04] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:59:05] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:05] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:05] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:59:05] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:59:05] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:59:05] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:59:05] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 11:59:06] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:59:06] [DEBUG] CLIENT -> SERVER: EHLO localhost

[2025-03-29 11:59:06] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:59:06] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:59:06] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:59:06] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:06] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:59:06] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:07] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:59:07] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:59:07] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:59:07] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:59:05 +0100

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: To: Saddafasif2 <<EMAIL>>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: Subject: Newsletter from Church

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: Message-ID: <1zz85kaYd5MC1j7cNl5yXHsy3SRgGJMHJJAeGVTuQ@localhost>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: <!DOCTYPE html>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: <html lang="en">

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: <head>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:     <meta http-equiv="X-UA-Compatible" content="IE=edge">

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:     <title>Newsletter from Church</title>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:     <style>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         body {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-family: "Segoe UI", Arial, sans-serif;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             background-color: #f7f9fc;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             color: #333333;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             line-height: 1.6;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .container {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             max-width: 600px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             margin: 20px auto;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             padding: 0 10px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             background: #ffffff;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             border-radius: 10px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .header {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             background: linear-gradient(135deg, #1e3c72, #2a5298);

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             padding: 30px 20px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             border-top-left-radius: 10px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             border-top-right-radius: 10px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .header h1 {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-size: 26px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .header p {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             margin: 5px 0 0;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             opacity: 0.9;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .content {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             padding: 20px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .greeting {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             margin: 0 0 15px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             color: #444444;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .message {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             background: #f0f4f8;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             border-radius: 8px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             border: 1px solid #d1d9e6;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .message h2 {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-size: 18px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .message p {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .updates {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .updates h3 {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             color: #2a5298;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .updates ul {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             list-style: none;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .updates li {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             padding: 8px 0;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             border-bottom: 1px solid #e0e0e0;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .updates li:last-child {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             border-bottom: none;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .cta {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .cta a {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             display: inline-block;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             background: #2ecc71;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             padding: 10px 20px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             border-radius: 5px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .footer {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             background: #f7f9fc;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-size: 12px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             border-top: 1px solid #e0e0e0;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             border-bottom-left-radius: 10px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             border-bottom-right-radius: 10px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .church-name {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-weight: bold;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .unsubscribe {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             margin-top: 8px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             font-size: 10px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         .unsubscribe a {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         @media (max-width: 480px) {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             .container {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 padding: 0 5px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             .header {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 padding: 20px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             .header h1 {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 font-size: 22px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             .content {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 padding: 15px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             .message h2 {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 font-size: 16px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             .updates h3 {

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 font-size: 15px;

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:     </style>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: </head>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: <body>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:     <div class="container">

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         <div class="header">

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             <h1>Newsletter from Church</h1>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             <p>Connecting with all  of our cherished members</p>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         <div class="content">

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             <div class="greeting">

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 <p>Hello Saddafasif2,</p>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 <p>Welcome to this month's newsletter from Church. We're delighted to share these updates with you and all  members of our community.</p>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             <div class="message">

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 <h2>A Message from </h2>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 <p>Dear Saddafasif2,</p>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 <p>I hope this message finds you well. At Church, we're grateful for your presence and commitment to our shared journey of faith. This newsletter is a small way to keep us connected and inspired. Thank you for being a vital part of our family!</p>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             <div class="updates">

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 <h3>Community Updates</h3>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 <ul>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                     <li>Join us this Sunday for a special service at 10 AM.</li>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                     <li>Our next fellowship event is scheduled for next Saturday's details to follow!</li>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                     <li>WeG??re launching a new outreach program's stay tuned for ways to get involved.</li>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                     <li>Prayer meetings are now every Wednesday at 7 PM.</li>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 </ul>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             <div class="cta">

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 <a href="#">Visit Our Website</a>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         <div class="footer">

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             <p>Sent with love from <span class="church-name">Church</span><br>By </p>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             <div class="unsubscribe">

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:                 <a href="#">Unsubscribe from these updates</a>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: </body>

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: </html><img src="http://localhost/track.php?id=track_67e7d27972cfe3.46926349&type=open" width="1" height="1" alt="" />

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:07] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:59:08] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:08] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:08] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:59:08] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:59:08] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:59:08] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:59:08] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 11:59:09] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:59:09] [DEBUG] CLIENT -> SERVER: EHLO localhost

[2025-03-29 11:59:09] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:59:09] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:59:09] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:59:09] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:09] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:59:09] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:10] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:59:10] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:59:10] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:59:10] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:59:08 +0100

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: To: Enespaul002 <<EMAIL>>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: Subject: Newsletter from Church

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: Message-ID: <C4ielZPcwDr5iJWzcVUAnvIr58COYv0CXRNH9kJvUM@localhost>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: <!DOCTYPE html>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: <html lang="en">

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: <head>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:     <meta http-equiv="X-UA-Compatible" content="IE=edge">

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:     <title>Newsletter from Church</title>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:     <style>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         body {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-family: "Segoe UI", Arial, sans-serif;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             background-color: #f7f9fc;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             color: #333333;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             line-height: 1.6;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .container {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             max-width: 600px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             margin: 20px auto;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             padding: 0 10px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             background: #ffffff;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             border-radius: 10px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .header {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             background: linear-gradient(135deg, #1e3c72, #2a5298);

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             padding: 30px 20px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             border-top-left-radius: 10px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             border-top-right-radius: 10px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .header h1 {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-size: 26px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .header p {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             margin: 5px 0 0;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             opacity: 0.9;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .content {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             padding: 20px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .greeting {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             margin: 0 0 15px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             color: #444444;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .message {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             background: #f0f4f8;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             border-radius: 8px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             border: 1px solid #d1d9e6;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .message h2 {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-size: 18px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .message p {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .updates {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .updates h3 {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             color: #2a5298;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .updates ul {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             list-style: none;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .updates li {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             padding: 8px 0;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             border-bottom: 1px solid #e0e0e0;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .updates li:last-child {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             border-bottom: none;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .cta {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .cta a {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             display: inline-block;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             background: #2ecc71;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             padding: 10px 20px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             border-radius: 5px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .footer {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             background: #f7f9fc;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-size: 12px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             border-top: 1px solid #e0e0e0;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             border-bottom-left-radius: 10px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             border-bottom-right-radius: 10px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .church-name {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-weight: bold;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .unsubscribe {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             margin-top: 8px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             font-size: 10px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         .unsubscribe a {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         @media (max-width: 480px) {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             .container {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 padding: 0 5px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             .header {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 padding: 20px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             .header h1 {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 font-size: 22px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             .content {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 padding: 15px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             .message h2 {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 font-size: 16px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             .updates h3 {

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 font-size: 15px;

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:     </style>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: </head>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: <body>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:     <div class="container">

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         <div class="header">

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             <h1>Newsletter from Church</h1>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             <p>Connecting with all  of our cherished members</p>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         <div class="content">

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             <div class="greeting">

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 <p>Hello Enespaul002,</p>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 <p>Welcome to this month's newsletter from Church. We're delighted to share these updates with you and all  members of our community.</p>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             <div class="message">

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 <h2>A Message from </h2>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 <p>Dear Enespaul002,</p>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 <p>I hope this message finds you well. At Church, we're grateful for your presence and commitment to our shared journey of faith. This newsletter is a small way to keep us connected and inspired. Thank you for being a vital part of our family!</p>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             <div class="updates">

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 <h3>Community Updates</h3>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 <ul>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                     <li>Join us this Sunday for a special service at 10 AM.</li>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                     <li>Our next fellowship event is scheduled for next Saturday's details to follow!</li>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                     <li>WeG??re launching a new outreach program's stay tuned for ways to get involved.</li>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                     <li>Prayer meetings are now every Wednesday at 7 PM.</li>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 </ul>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             <div class="cta">

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 <a href="#">Visit Our Website</a>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         <div class="footer">

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             <p>Sent with love from <span class="church-name">Church</span><br>By </p>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             <div class="unsubscribe">

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:                 <a href="#">Unsubscribe from these updates</a>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: </body>

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: </html><img src="http://localhost/track.php?id=track_67e7d27c7d9839.96382699&type=open" width="1" height="1" alt="" />

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:10] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:59:11] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:11] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:11] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:59:11] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:59:11] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:59:11] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:59:11] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 11:59:12] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:59:12] [DEBUG] CLIENT -> SERVER: EHLO localhost

[2025-03-29 11:59:12] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:59:12] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:59:12] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:59:12] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:12] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:59:12] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:13] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:59:13] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:59:13] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:59:13] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:59:11 +0100

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: To: Support <<EMAIL>>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: Subject: Newsletter from Church

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: Message-ID: <aqrc1hXpEvp9X3lxhkxop9LOOnFSQvbx6836HM5bwI@localhost>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: <!DOCTYPE html>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: <html lang="en">

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: <head>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:     <meta http-equiv="X-UA-Compatible" content="IE=edge">

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:     <title>Newsletter from Church</title>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:     <style>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         body {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-family: "Segoe UI", Arial, sans-serif;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             background-color: #f7f9fc;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             color: #333333;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             line-height: 1.6;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .container {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             max-width: 600px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             margin: 20px auto;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             padding: 0 10px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             background: #ffffff;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             border-radius: 10px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .header {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             background: linear-gradient(135deg, #1e3c72, #2a5298);

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             padding: 30px 20px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             border-top-left-radius: 10px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             border-top-right-radius: 10px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .header h1 {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-size: 26px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .header p {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             margin: 5px 0 0;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             opacity: 0.9;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .content {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             padding: 20px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .greeting {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             margin: 0 0 15px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             color: #444444;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .message {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             background: #f0f4f8;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             border-radius: 8px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             border: 1px solid #d1d9e6;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .message h2 {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-size: 18px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .message p {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .updates {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .updates h3 {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             color: #2a5298;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .updates ul {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             list-style: none;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .updates li {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             padding: 8px 0;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             border-bottom: 1px solid #e0e0e0;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .updates li:last-child {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             border-bottom: none;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .cta {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .cta a {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             display: inline-block;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             background: #2ecc71;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             padding: 10px 20px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             border-radius: 5px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .footer {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             background: #f7f9fc;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-size: 12px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             border-top: 1px solid #e0e0e0;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             border-bottom-left-radius: 10px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             border-bottom-right-radius: 10px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .church-name {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-weight: bold;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .unsubscribe {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             margin-top: 8px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             font-size: 10px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         .unsubscribe a {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         @media (max-width: 480px) {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             .container {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 padding: 0 5px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             .header {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 padding: 20px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             .header h1 {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 font-size: 22px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             .content {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 padding: 15px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             .message h2 {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 font-size: 16px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             .updates h3 {

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 font-size: 15px;

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:     </style>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: </head>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: <body>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:     <div class="container">

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         <div class="header">

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             <h1>Newsletter from Church</h1>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             <p>Connecting with all  of our cherished members</p>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         <div class="content">

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             <div class="greeting">

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 <p>Hello Support,</p>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 <p>Welcome to this month's newsletter from Church. We're delighted to share these updates with you and all  members of our community.</p>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             <div class="message">

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 <h2>A Message from </h2>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 <p>Dear Support,</p>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 <p>I hope this message finds you well. At Church, we're grateful for your presence and commitment to our shared journey of faith. This newsletter is a small way to keep us connected and inspired. Thank you for being a vital part of our family!</p>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             <div class="updates">

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 <h3>Community Updates</h3>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 <ul>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                     <li>Join us this Sunday for a special service at 10 AM.</li>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                     <li>Our next fellowship event is scheduled for next Saturday's details to follow!</li>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                     <li>WeG??re launching a new outreach program's stay tuned for ways to get involved.</li>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                     <li>Prayer meetings are now every Wednesday at 7 PM.</li>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 </ul>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             <div class="cta">

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 <a href="#">Visit Our Website</a>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         <div class="footer">

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             <p>Sent with love from <span class="church-name">Church</span><br>By </p>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             <div class="unsubscribe">

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:                 <a href="#">Unsubscribe from these updates</a>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: </body>

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: </html><img src="http://localhost/track.php?id=track_67e7d27f640953.59472871&type=open" width="1" height="1" alt="" />

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:59:13] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:13] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:13] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:59:13] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:59:13] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:59:14] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:59:14] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 11:59:15] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:59:15] [DEBUG] CLIENT -> SERVER: EHLO localhost

[2025-03-29 11:59:15] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:59:15] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:59:15] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:59:15] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:15] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:59:15] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:15] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:59:15] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:59:16] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:59:16] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:59:16] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:59:14 +0100

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: To: Info <<EMAIL>>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: Subject: Newsletter from Church

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: Message-ID: <RkPJ3mTz8pCt5bFAsVcaTcj8VhqSF6Fk6Bauv0s6g@localhost>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: <!DOCTYPE html>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: <html lang="en">

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: <head>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:     <meta http-equiv="X-UA-Compatible" content="IE=edge">

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:     <title>Newsletter from Church</title>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:     <style>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         body {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-family: "Segoe UI", Arial, sans-serif;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             background-color: #f7f9fc;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             color: #333333;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             line-height: 1.6;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .container {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             max-width: 600px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             margin: 20px auto;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             padding: 0 10px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             background: #ffffff;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             border-radius: 10px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .header {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             background: linear-gradient(135deg, #1e3c72, #2a5298);

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             padding: 30px 20px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             border-top-left-radius: 10px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             border-top-right-radius: 10px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .header h1 {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-size: 26px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .header p {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             margin: 5px 0 0;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             opacity: 0.9;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .content {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             padding: 20px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .greeting {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             margin: 0 0 15px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             color: #444444;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .message {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             background: #f0f4f8;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             border-radius: 8px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             border: 1px solid #d1d9e6;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .message h2 {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-size: 18px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .message p {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .updates {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .updates h3 {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             color: #2a5298;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .updates ul {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             list-style: none;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .updates li {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             padding: 8px 0;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             border-bottom: 1px solid #e0e0e0;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .updates li:last-child {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             border-bottom: none;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .cta {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .cta a {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             display: inline-block;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             background: #2ecc71;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             padding: 10px 20px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             border-radius: 5px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .footer {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             background: #f7f9fc;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-size: 12px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             border-top: 1px solid #e0e0e0;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             border-bottom-left-radius: 10px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             border-bottom-right-radius: 10px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .church-name {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-weight: bold;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .unsubscribe {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             margin-top: 8px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             font-size: 10px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         .unsubscribe a {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         @media (max-width: 480px) {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             .container {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 padding: 0 5px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             .header {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 padding: 20px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             .header h1 {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 font-size: 22px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             .content {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 padding: 15px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             .message h2 {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 font-size: 16px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             .updates h3 {

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 font-size: 15px;

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:     </style>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: </head>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: <body>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:     <div class="container">

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         <div class="header">

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             <h1>Newsletter from Church</h1>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             <p>Connecting with all  of our cherished members</p>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         <div class="content">

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             <div class="greeting">

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 <p>Hello Info,</p>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 <p>Welcome to this month's newsletter from Church. We're delighted to share these updates with you and all  members of our community.</p>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             <div class="message">

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 <h2>A Message from </h2>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 <p>Dear Info,</p>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 <p>I hope this message finds you well. At Church, we're grateful for your presence and commitment to our shared journey of faith. This newsletter is a small way to keep us connected and inspired. Thank you for being a vital part of our family!</p>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             <div class="updates">

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 <h3>Community Updates</h3>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 <ul>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                     <li>Join us this Sunday for a special service at 10 AM.</li>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                     <li>Our next fellowship event is scheduled for next Saturday's details to follow!</li>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                     <li>WeG??re launching a new outreach program's stay tuned for ways to get involved.</li>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                     <li>Prayer meetings are now every Wednesday at 7 PM.</li>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 </ul>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             <div class="cta">

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 <a href="#">Visit Our Website</a>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         <div class="footer">

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             <p>Sent with love from <span class="church-name">Church</span><br>By </p>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             <div class="unsubscribe">

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:                 <a href="#">Unsubscribe from these updates</a>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: </body>

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: </html><img src="http://localhost/track.php?id=track_67e7d282320ad9.40328929&type=open" width="1" height="1" alt="" />

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:59:16] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:16] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:16] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:59:16] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:59:16] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:59:17] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:59:17] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 11:59:17] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:59:17] [DEBUG] CLIENT -> SERVER: EHLO localhost

[2025-03-29 11:59:18] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:59:18] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:59:18] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:59:18] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:18] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:59:18] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:18] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:59:18] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:59:18] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:59:18] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:59:18] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:59:18] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:59:19] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:59:17 +0100

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: To: Account <<EMAIL>>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: Subject: Newsletter from Church

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: Message-ID: <uzPSrSmSv58ytRbqc8iEPw7RDXJnNEYAqTGau3Jv0@localhost>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: <!DOCTYPE html>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: <html lang="en">

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: <head>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:     <meta http-equiv="X-UA-Compatible" content="IE=edge">

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:     <title>Newsletter from Church</title>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:     <style>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         body {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-family: "Segoe UI", Arial, sans-serif;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             background-color: #f7f9fc;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             color: #333333;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             line-height: 1.6;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .container {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             max-width: 600px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             margin: 20px auto;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             padding: 0 10px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             background: #ffffff;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             border-radius: 10px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .header {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             background: linear-gradient(135deg, #1e3c72, #2a5298);

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             padding: 30px 20px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             border-top-left-radius: 10px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             border-top-right-radius: 10px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .header h1 {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-size: 26px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .header p {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             margin: 5px 0 0;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             opacity: 0.9;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .content {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             padding: 20px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .greeting {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             margin: 0 0 15px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             color: #444444;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .message {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             background: #f0f4f8;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             border-radius: 8px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             border: 1px solid #d1d9e6;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .message h2 {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-size: 18px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .message p {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .updates {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .updates h3 {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             color: #2a5298;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .updates ul {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             list-style: none;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .updates li {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             padding: 8px 0;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             border-bottom: 1px solid #e0e0e0;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .updates li:last-child {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             border-bottom: none;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .cta {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .cta a {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             display: inline-block;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             background: #2ecc71;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             padding: 10px 20px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             border-radius: 5px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .footer {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             background: #f7f9fc;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-size: 12px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             border-top: 1px solid #e0e0e0;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             border-bottom-left-radius: 10px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             border-bottom-right-radius: 10px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .church-name {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-weight: bold;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .unsubscribe {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             margin-top: 8px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             font-size: 10px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         .unsubscribe a {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         @media (max-width: 480px) {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             .container {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 padding: 0 5px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             .header {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 padding: 20px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             .header h1 {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 font-size: 22px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             .content {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 padding: 15px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             .message h2 {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 font-size: 16px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             .updates h3 {

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 font-size: 15px;

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:     </style>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: </head>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: <body>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:     <div class="container">

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         <div class="header">

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             <h1>Newsletter from Church</h1>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             <p>Connecting with all  of our cherished members</p>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         <div class="content">

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             <div class="greeting">

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 <p>Hello Account,</p>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 <p>Welcome to this month's newsletter from Church. We're delighted to share these updates with you and all  members of our community.</p>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             <div class="message">

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 <h2>A Message from </h2>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 <p>Dear Account,</p>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 <p>I hope this message finds you well. At Church, we're grateful for your presence and commitment to our shared journey of faith. This newsletter is a small way to keep us connected and inspired. Thank you for being a vital part of our family!</p>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             <div class="updates">

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 <h3>Community Updates</h3>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 <ul>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                     <li>Join us this Sunday for a special service at 10 AM.</li>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                     <li>Our next fellowship event is scheduled for next Saturday's details to follow!</li>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                     <li>WeG??re launching a new outreach program's stay tuned for ways to get involved.</li>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                     <li>Prayer meetings are now every Wednesday at 7 PM.</li>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 </ul>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             <div class="cta">

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 <a href="#">Visit Our Website</a>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         <div class="footer">

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             <p>Sent with love from <span class="church-name">Church</span><br>By </p>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             <div class="unsubscribe">

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:                 <a href="#">Unsubscribe from these updates</a>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: </body>

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: </html><img src="http://localhost/track.php?id=track_67e7d285364732.94237149&type=open" width="1" height="1" alt="" />

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:59:19] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:19] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:19] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:59:19] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:59:19] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:59:19] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:59:19] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 11:59:21] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:59:21] [DEBUG] CLIENT -> SERVER: EHLO localhost

[2025-03-29 11:59:21] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:59:21] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:59:21] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:59:21] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:21] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:59:21] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:22] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:59:22] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:59:22] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:59:22] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:59:19 +0100

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: To: Henjoybid <<EMAIL>>

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: Subject: Newsletter from Church

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: Message-ID: <exKppjs7cDzhyYrkv9j1GHhbv19e5jVxDuWJW5lY@localhost>

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: <!DOCTYPE html>

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: <html lang="en">

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER: <head>

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:     <meta http-equiv="X-UA-Compatible" content="IE=edge">

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:     <title>Newsletter from Church</title>

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:     <style>

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         body {

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             font-family: "Segoe UI", Arial, sans-serif;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             background-color: #f7f9fc;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             color: #333333;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             line-height: 1.6;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         .container {

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             max-width: 600px;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             margin: 20px auto;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             padding: 0 10px;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             background: #ffffff;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             border-radius: 10px;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         .header {

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             background: linear-gradient(135deg, #1e3c72, #2a5298);

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             padding: 30px 20px;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             border-top-left-radius: 10px;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             border-top-right-radius: 10px;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         .header h1 {

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             font-size: 26px;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         .header p {

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             margin: 5px 0 0;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             opacity: 0.9;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         .content {

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             padding: 20px;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         .greeting {

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             margin: 0 0 15px;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             color: #444444;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         .message {

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             background: #f0f4f8;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             border-radius: 8px;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:             border: 1px solid #d1d9e6;

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:22] [DEBUG] CLIENT -> SERVER:         .message h2 {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             font-size: 18px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         .message p {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         .updates {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         .updates h3 {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             color: #2a5298;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         .updates ul {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             list-style: none;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         .updates li {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             padding: 8px 0;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             border-bottom: 1px solid #e0e0e0;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         .updates li:last-child {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             border-bottom: none;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         .cta {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         .cta a {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             display: inline-block;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             background: #2ecc71;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             padding: 10px 20px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             border-radius: 5px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         .footer {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             background: #f7f9fc;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             font-size: 12px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             border-top: 1px solid #e0e0e0;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             border-bottom-left-radius: 10px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             border-bottom-right-radius: 10px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         .church-name {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             font-weight: bold;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         .unsubscribe {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             margin-top: 8px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             font-size: 10px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         .unsubscribe a {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         @media (max-width: 480px) {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             .container {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 padding: 0 5px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             .header {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 padding: 20px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             .header h1 {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 font-size: 22px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             .content {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 padding: 15px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             .message h2 {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 font-size: 16px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             .updates h3 {

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 font-size: 15px;

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:     </style>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER: </head>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER: <body>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:     <div class="container">

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         <div class="header">

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             <h1>Newsletter from Church</h1>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             <p>Connecting with all  of our cherished members</p>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         <div class="content">

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             <div class="greeting">

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 <p>Hello Henjoybid,</p>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 <p>Welcome to this month's newsletter from Church. We're delighted to share these updates with you and all  members of our community.</p>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             <div class="message">

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 <h2>A Message from </h2>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 <p>Dear Henjoybid,</p>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 <p>I hope this message finds you well. At Church, we're grateful for your presence and commitment to our shared journey of faith. This newsletter is a small way to keep us connected and inspired. Thank you for being a vital part of our family!</p>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             <div class="updates">

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 <h3>Community Updates</h3>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 <ul>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                     <li>Join us this Sunday for a special service at 10 AM.</li>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                     <li>Our next fellowship event is scheduled for next Saturday's details to follow!</li>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                     <li>WeG??re launching a new outreach program's stay tuned for ways to get involved.</li>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                     <li>Prayer meetings are now every Wednesday at 7 PM.</li>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 </ul>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             <div class="cta">

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 <a href="#">Visit Our Website</a>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         <div class="footer">

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             <p>Sent with love from <span class="church-name">Church</span><br>By </p>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             <div class="unsubscribe">

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:                 <a href="#">Unsubscribe from these updates</a>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER: </body>

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER: </html><img src="http://localhost/track.php?id=track_67e7d287c3e2d1.13100607&type=open" width="1" height="1" alt="" />

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:59:23] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:23] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:23] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:59:23] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:59:23] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:59:23] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 11:59:23] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 11:59:24] [DEBUG] SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-03-29 11:59:24] [DEBUG] CLIENT -> SERVER: EHLO localhost

[2025-03-29 11:59:24] [DEBUG] SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE ********
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-03-29 11:59:24] [DEBUG] CLIENT -> SERVER: AUTH LOGIN

[2025-03-29 11:59:24] [DEBUG] SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-03-29 11:59:24] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:24] [DEBUG] SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-03-29 11:59:24] [DEBUG] CLIENT -> SERVER: [credentials hidden]
[2025-03-29 11:59:24] [DEBUG] SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-03-29 11:59:24] [DEBUG] CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-03-29 11:59:25] [DEBUG] SERVER -> CLIENT: 250 2.1.0 Ok

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-03-29 11:59:25] [DEBUG] SERVER -> CLIENT: 250 2.1.5 Ok

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: DATA

[2025-03-29 11:59:25] [DEBUG] SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: Date: Sat, 29 Mar 2025 11:59:23 +0100

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: To: Eggnipawe <<EMAIL>>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: From: Support Team <<EMAIL>>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: Subject: Newsletter from Church

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: Message-ID: <wdybXj7mVuMoB9F9wvhI6OqysgiDbGaaDT4onV4WgqM@localhost>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: MIME-Version: 1.0

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: <!DOCTYPE html>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: <html lang="en">

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: <head>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:     <meta http-equiv="X-UA-Compatible" content="IE=edge">

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:     <title>Newsletter from Church</title>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:     <style>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         body {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-family: "Segoe UI", Arial, sans-serif;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             background-color: #f7f9fc;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             color: #333333;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             line-height: 1.6;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .container {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             max-width: 600px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             margin: 20px auto;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             padding: 0 10px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             background: #ffffff;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             border-radius: 10px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .header {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             background: linear-gradient(135deg, #1e3c72, #2a5298);

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             padding: 30px 20px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             border-top-left-radius: 10px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             border-top-right-radius: 10px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .header h1 {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-size: 26px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .header p {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             margin: 5px 0 0;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             opacity: 0.9;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .content {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             padding: 20px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .greeting {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             margin: 0 0 15px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             color: #444444;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .message {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             background: #f0f4f8;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             border-radius: 8px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             border: 1px solid #d1d9e6;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .message h2 {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-size: 18px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .message p {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .updates {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .updates h3 {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             color: #2a5298;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-size: 16px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             margin: 0 0 10px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .updates ul {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             list-style: none;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             padding: 0;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             margin: 0;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .updates li {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             padding: 8px 0;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             border-bottom: 1px solid #e0e0e0;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             color: #555555;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .updates li:last-child {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             border-bottom: none;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .cta {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             margin: 20px 0;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .cta a {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             display: inline-block;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             background: #2ecc71;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             color: #ffffff;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             padding: 10px 20px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             border-radius: 5px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-weight: 600;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-size: 14px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .footer {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             background: #f7f9fc;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             padding: 15px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             text-align: center;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-size: 12px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             border-top: 1px solid #e0e0e0;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             border-bottom-left-radius: 10px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             border-bottom-right-radius: 10px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .church-name {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             color: #1e3c72;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-weight: bold;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .unsubscribe {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             margin-top: 8px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             font-size: 10px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         .unsubscribe a {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             color: #777777;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             text-decoration: none;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         @media (max-width: 480px) {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             .container {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 padding: 0 5px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             .header {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 padding: 20px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             .header h1 {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 font-size: 22px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             .content {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 padding: 15px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             .message h2 {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 font-size: 16px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             .updates h3 {

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 font-size: 15px;

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         }

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:     </style>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: </head>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: <body>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:     <div class="container">

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         <div class="header">

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             <h1>Newsletter from Church</h1>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             <p>Connecting with all  of our cherished members</p>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         <div class="content">

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             <div class="greeting">

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 <p>Hello Eggnipawe,</p>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 <p>Welcome to this month's newsletter from Church. We're delighted to share these updates with you and all  members of our community.</p>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             <div class="message">

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 <h2>A Message from </h2>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 <p>Dear Eggnipawe,</p>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 <p>I hope this message finds you well. At Church, we're grateful for your presence and commitment to our shared journey of faith. This newsletter is a small way to keep us connected and inspired. Thank you for being a vital part of our family!</p>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             <div class="updates">

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 <h3>Community Updates</h3>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 <ul>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                     <li>Join us this Sunday for a special service at 10 AM.</li>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                     <li>Our next fellowship event is scheduled for next Saturday's details to follow!</li>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                     <li>WeG??re launching a new outreach program's stay tuned for ways to get involved.</li>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                     <li>Prayer meetings are now every Wednesday at 7 PM.</li>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 </ul>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             <div class="cta">

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 <a href="#">Visit Our Website</a>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         <div class="footer">

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             <p>Sent with love from <span class="church-name">Church</span><br>By </p>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             <div class="unsubscribe">

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:                 <a href="#">Unsubscribe from these updates</a>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:             </div>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:         </div>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER:     </div>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: </body>

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: </html><img src="http://localhost/track.php?id=track_67e7d28badf438.78466191&type=open" width="1" height="1" alt="" />

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: 

[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: .

[2025-03-29 11:59:25] [DEBUG] SERVER -> CLIENT: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:25] [DEBUG] SMTP ERROR: DATA END command failed: 451 4.7.1 Ratelimit "hostinger_out_ratelimit" exceeded

[2025-03-29 11:59:25] [DEBUG] SMTP Error: data not accepted.
[2025-03-29 11:59:25] Error sending email: SMTP Error: data not accepted.
[2025-03-29 11:59:25] [DEBUG] CLIENT -> SERVER: QUIT

[2025-03-29 11:59:26] [DEBUG] SERVER -> CLIENT: 221 2.0.0 Bye

[2025-03-29 12:03:18] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 12:03:21] Email sent <NAME_EMAIL>
[2025-03-29 12:03:21] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 12:03:24] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:03:24] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 12:03:27] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:03:27] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 12:03:30] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:03:30] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 12:03:33] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:03:33] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 12:03:36] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:03:36] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 12:03:38] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:03:39] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 12:03:42] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:03:42] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 12:03:44] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:03:45] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 12:03:47] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:05:44] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 12:05:46] Email sent <NAME_EMAIL>
[2025-03-29 12:05:46] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 12:05:49] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:05:49] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 12:05:51] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:05:52] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 12:05:54] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:05:55] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 12:05:57] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:05:58] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 12:06:00] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:06:00] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 12:06:03] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:06:03] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 12:06:06] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:06:06] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 12:06:09] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:06:09] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 12:06:12] Error sending email: SMTP Error: data not accepted.
[2025-03-29 12:45:44] Sending scheduled email to Ndivhuwo119 <<EMAIL>>
[2025-03-29 12:45:47] Email sent <NAME_EMAIL>
[2025-03-29 12:45:47] Sending scheduled email to Bointa04 <<EMAIL>>
[2025-03-29 12:45:50] Email sent <NAME_EMAIL>
[2025-03-29 12:45:50] Sending scheduled email to Lilbossbaby11 <<EMAIL>>
[2025-03-29 12:45:53] Email sent <NAME_EMAIL>
[2025-03-29 12:45:53] Sending scheduled email to Saddafasif2 <<EMAIL>>
[2025-03-29 12:45:56] Email sent <NAME_EMAIL>
[2025-03-29 12:45:56] Sending scheduled email to Enespaul002 <<EMAIL>>
[2025-03-29 12:45:58] Email sent <NAME_EMAIL>
[2025-03-29 12:45:59] Sending scheduled email to Support <<EMAIL>>
[2025-03-29 12:46:02] Email sent <NAME_EMAIL>
[2025-03-29 12:46:02] Sending scheduled email to Info <<EMAIL>>
[2025-03-29 12:46:06] Email sent <NAME_EMAIL>
[2025-03-29 12:46:06] Sending scheduled email to Account <<EMAIL>>
[2025-03-29 12:46:08] Email sent <NAME_EMAIL>
[2025-03-29 12:46:08] Sending scheduled email to Henjoybid <<EMAIL>>
[2025-03-29 12:46:11] Email sent <NAME_EMAIL>
[2025-03-29 12:46:11] Sending scheduled email to Eggnipawe <<EMAIL>>
[2025-03-29 12:46:14] Email sent <NAME_EMAIL>
