<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Require configuration
require_once '../config.php';

// Check login
session_start();
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Database connection
$conn = $pdo;

// Helper function to extract placeholders
function extractPlaceholders($text) {
    $placeholders = [];
    preg_match_all('/{([^}]+)}/', $text, $matches);
    return $matches[0];
}

// Get all birthday templates
$stmt = $conn->prepare("
    SELECT id, template_name, subject, content 
    FROM email_templates 
    WHERE is_birthday_template = 1 
       OR template_name LIKE '%birthday%' 
       OR template_name LIKE '%remind%'
    ORDER BY template_name
");
$stmt->execute();
$templates = $stmt->fetchAll();

// Page title
$page_title = "Debug Email Template Placeholders";

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-bug-fill me-2"></i>Email Template Placeholder Debugger</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <p>This tool helps identify what placeholders are used in each birthday-related email template.</p>
            </div>
            
            <?php if (empty($templates)): ?>
                <div class="alert alert-warning">No birthday-related templates found.</div>
            <?php else: ?>
                <h6>Found <?php echo count($templates); ?> birthday-related templates</h6>
                
                <div class="mt-4">
                    <h5>Available Placeholders</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <ul class="list-group">
                                <li class="list-group-item list-group-item-primary">Basic Placeholders</li>
                                <li class="list-group-item">{full_name} - Full name of recipient</li>
                                <li class="list-group-item">{first_name} - First name of recipient</li>
                                <li class="list-group-item">{email} - Email address</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="list-group">
                                <li class="list-group-item list-group-item-success">Date/Time Placeholders</li>
                                <li class="list-group-item">{birthday_date} - Birthday date (Month Day)</li>
                                <li class="list-group-item">{upcoming_birthday_date} - Next birthday (Month Day, Year)</li>
                                <li class="list-group-item">{upcoming_birthday_day} - Day of week for next birthday</li>
                                <li class="list-group-item">{days_text} - "today", "tomorrow", or "in X days"</li>
                                <li class="list-group-item">{age} - Age on upcoming birthday</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <ul class="list-group">
                                <li class="list-group-item list-group-item-info">Miscellaneous Placeholders</li>
                                <li class="list-group-item">{birthday_member_name} - First name of birthday person</li>
                                <li class="list-group-item">{birthday_member_full_name} - Full name of birthday person</li>
                                <li class="list-group-item">{birthday_member_age} - Age on birthday</li>
                                <li class="list-group-item">{church_name} - Church name from settings</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h5>Template Analysis</h5>
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Template Name</th>
                                <th>Subject Placeholders</th>
                                <th>Content Placeholders</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($templates as $template): ?>
                                <?php 
                                    $subjectPlaceholders = extractPlaceholders($template['subject']);
                                    $contentPlaceholders = extractPlaceholders($template['content']);
                                ?>
                                <tr>
                                    <td><?php echo $template['id']; ?></td>
                                    <td><?php echo htmlspecialchars($template['template_name']); ?></td>
                                    <td>
                                        <?php if (empty($subjectPlaceholders)): ?>
                                            <span class="text-muted">No placeholders</span>
                                        <?php else: ?>
                                            <ul class="list-unstyled mb-0">
                                                <?php foreach ($subjectPlaceholders as $placeholder): ?>
                                                    <li><?php echo htmlspecialchars($placeholder); ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (empty($contentPlaceholders)): ?>
                                            <span class="text-muted">No placeholders</span>
                                        <?php else: ?>
                                            <ul class="list-unstyled mb-0">
                                                <?php foreach ($contentPlaceholders as $placeholder): ?>
                                                    <li><?php echo htmlspecialchars($placeholder); ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="edit_template.php?id=<?php echo $template['id']; ?>" class="btn btn-sm btn-primary">
                                            <i class="bi bi-pencil"></i> Edit
                                        </a>
                                        <a href="preview_template.php?id=<?php echo $template['id']; ?>" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i> Preview
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-4">
                    <h5>Test Placeholder Replacement</h5>
                    <form method="post" action="debug_placeholders.php" class="mt-3">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="test_template" class="form-label">Template ID</label>
                                <select name="test_template" id="test_template" class="form-select">
                                    <option value="">Select a template...</option>
                                    <?php foreach ($templates as $template): ?>
                                        <option value="<?php echo $template['id']; ?>"><?php echo htmlspecialchars($template['template_name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="test_member" class="form-label">Member ID</label>
                                <select name="test_member" id="test_member" class="form-select">
                                    <option value="">Select a member...</option>
                                    <?php 
                                    // Get a few sample members
                                    $stmt = $conn->query("SELECT id, full_name, email FROM members ORDER BY id LIMIT 10");
                                    $members_found = false;
                                    while ($row = $stmt->fetch()): 
                                        $members_found = true;
                                    ?>
                                        <option value="<?php echo $row['id']; ?>"><?php echo htmlspecialchars($row['full_name']); ?> (<?php echo htmlspecialchars($row['email']); ?>)</option>
                                    <?php endwhile; 
                                    
                                    if (!$members_found):
                                    ?>
                                        <option value="" disabled>No members found in database</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                        <button type="submit" name="test_replacement" class="btn btn-primary">Test Replacement</button>
                    </form>
                    
                    <?php
                    // Handle test replacement
                    if (isset($_POST['test_replacement']) && !empty($_POST['test_template']) && !empty($_POST['test_member'])) {
                        $templateId = intval($_POST['test_template']);
                        $memberId = intval($_POST['test_member']);
                        
                        // Get template
                        $stmt = $conn->prepare("SELECT * FROM email_templates WHERE id = ?");
                        $stmt->execute([$templateId]);
                        $template = $stmt->fetch();
                        
                        // Get member
                        $stmt = $conn->prepare("SELECT * FROM members WHERE id = ?");
                        $stmt->execute([$memberId]);
                        $member = $stmt->fetch();
                        
                        if ($template && $member) {
                            // Prepare data for replacement similar to send_birthday.php
                            $memberData = [
                                'full_name' => $member['full_name'],
                                'email' => $member['email'],
                                'phone_number' => $member['phone_number'] ?? '',
                                'birth_date' => $member['birth_date'] ?? '',
                                'image_path' => $member['image_path'] ?? ''
                            ];
                            
                            // Extract first and last name
                            if (!empty($member['full_name'])) {
                                $nameParts = explode(' ', $member['full_name'], 2);
                                $memberData['first_name'] = $nameParts[0];
                                $memberData['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
                            }
                            
                            // Add recipient placeholders
                            $memberData['recipient_full_name'] = $memberData['full_name'];
                            $memberData['recipient_first_name'] = $memberData['first_name'];
                            $memberData['recipient_email'] = $memberData['email'];
                            $memberData['recipient_phone'] = $memberData['phone_number'];
                            
                            // Calculate dates and age
                            $birth_month = date('m', strtotime($memberData['birth_date'] ?? 'now'));
                            $birth_day = date('d', strtotime($memberData['birth_date'] ?? 'now'));
                            $birth_year = date('Y', strtotime($memberData['birth_date'] ?? 'now'));
                            $current_year = date('Y');
                            $next_year = $current_year + 1;
                            
                            // Determine upcoming birthday
                            $this_year_birthday = $current_year . '-' . $birth_month . '-' . $birth_day;
                            $next_year_birthday = $next_year . '-' . $birth_month . '-' . $birth_day;
                            $upcoming_date = strtotime($this_year_birthday) < time() ? $next_year_birthday : $this_year_birthday;
                            
                            // Calculate age
                            $age = strtotime($this_year_birthday) < time() ? 
                                  ($next_year - $birth_year) : 
                                  ($current_year - $birth_year);
                            
                            // Add days until birthday
                            $days_until = ceil((strtotime($upcoming_date) - time()) / 86400);
                            
                            // Add birthday placeholders
                            $memberData['birthday_date'] = date('F j', strtotime($memberData['birth_date'] ?? 'now'));
                            $memberData['birthday_year'] = $birth_year;
                            $memberData['current_year'] = $current_year;
                            $memberData['current_date'] = date('F j, Y');
                            $memberData['current_time'] = date('g:i A');
                            $memberData['upcoming_birthday_date'] = date('F j, Y', strtotime($upcoming_date));
                            $memberData['upcoming_birthday_day'] = date('l', strtotime($upcoming_date));
                            $memberData['upcoming_birthday_formatted'] = date('l, F j, Y', strtotime($upcoming_date));
                            $memberData['days_until_birthday'] = $days_until;
                            $memberData['days_text'] = $days_until == 0 ? 'today' : 
                                                     ($days_until == 1 ? 'tomorrow' : 
                                                     "in $days_until days");
                            $memberData['age'] = $age;
                            $memberData['birthday_member_age'] = $age;
                            
                            // Birthday member equivalents
                            $memberData['birthday_member_birth_date'] = $memberData['birthday_date'];
                            $memberData['birthday_member_name'] = $memberData['first_name'];
                            $memberData['birthday_member_full_name'] = $memberData['full_name'];
                            
                            // Replace placeholders
                            $replaced_subject = replaceTemplatePlaceholders($template['subject'], $memberData);
                            $replaced_content = replaceTemplatePlaceholders($template['content'], $memberData);
                            
                            // Debug values
                            echo '<div class="alert alert-success mt-3">Replacement completed</div>';
                            echo '<div class="card mt-3">';
                            echo '<div class="card-header"><strong>Debug Information</strong></div>';
                            echo '<div class="card-body">';
                            
                            echo '<h6>Member Data Values:</h6>';
                            echo '<pre>' . htmlspecialchars(print_r($memberData, true)) . '</pre>';
                            
                            echo '<h6>Original Subject:</h6>';
                            echo '<pre>' . htmlspecialchars($template['subject']) . '</pre>';
                            
                            echo '<h6>Replaced Subject:</h6>';
                            echo '<pre>' . htmlspecialchars($replaced_subject) . '</pre>';
                            
                            echo '<h6>Replaced Content (Preview):</h6>';
                            echo '<div class="border p-3 bg-light">' . $replaced_content . '</div>';
                            
                            echo '</div></div>';
                        }
                    }
                    ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 