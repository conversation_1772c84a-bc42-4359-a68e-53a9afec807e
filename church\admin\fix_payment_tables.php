<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';
$logs = [];

// Function to check if a table exists
function tableExists($conn, $tableName) {
    try {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$tableName]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        error_log("Error checking if table exists: " . $e->getMessage());
        return false;
    }
}

try {
    // Start transaction
    $conn->beginTransaction();
    $logs[] = "Started database transaction";
    
    // Drop existing tables if they exist (in reverse order of dependencies)
    $tables_to_drop = ['donation_notifications', 'payment_transactions', 'donations'];
    
    foreach ($tables_to_drop as $table) {
        if (tableExists($conn, $table)) {
            $sql = "DROP TABLE IF EXISTS $table";
            $conn->exec($sql);
            $logs[] = "Dropped table $table";
        } else {
            $logs[] = "Table $table does not exist, no need to drop";
        }
    }
    
    // Create donations table with correct data types
    $sql = "CREATE TABLE IF NOT EXISTS donations (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        donor_name VARCHAR(255) NOT NULL,
        donor_email VARCHAR(255) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) NOT NULL,
        payment_method ENUM('paypal', 'stripe') NOT NULL,
        payment_status ENUM('pending', 'completed', 'failed', 'refunded') NOT NULL DEFAULT 'pending',
        transaction_id VARCHAR(255) NOT NULL,
        donation_type ENUM('general', 'birthday_gift') NOT NULL DEFAULT 'general',
        recipient_id INT(11) DEFAULT NULL,
        message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (recipient_id) REFERENCES members(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    $conn->exec($sql);
    $logs[] = "Created donations table with correct data types";

    // Create payment_transactions table with correct data types
    $sql = "CREATE TABLE IF NOT EXISTS payment_transactions (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        donation_id INT(11) NOT NULL,
        payment_provider ENUM('paypal', 'stripe') NOT NULL,
        provider_transaction_id VARCHAR(255) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) NOT NULL,
        payment_status VARCHAR(50) NOT NULL,
        payment_method_details TEXT,
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (donation_id) REFERENCES donations(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    $conn->exec($sql);
    $logs[] = "Created payment_transactions table with correct data types";

    // Create donation_notifications table with correct data types
    $sql = "CREATE TABLE IF NOT EXISTS donation_notifications (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        donation_id INT(11) NOT NULL,
        notification_type ENUM('donor_receipt', 'admin_notification', 'recipient_notification') NOT NULL,
        recipient_email VARCHAR(255) NOT NULL,
        sent_at TIMESTAMP NULL DEFAULT NULL,
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (donation_id) REFERENCES donations(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    $conn->exec($sql);
    $logs[] = "Created donation_notifications table with correct data types";

    // Commit transaction
    $conn->commit();
    $logs[] = "Committed database transaction";
    $message = "Payment tables fixed successfully!";

} catch (PDOException $e) {
    // Rollback transaction on error
    if ($conn->inTransaction()) {
        $conn->rollBack();
        $logs[] = "Rolled back database transaction due to error";
    }
    $error = "Database error: " . $e->getMessage();
    error_log("Error fixing payment tables: " . $e->getMessage());
}

// Set page title and header
$page_title = "Fix Payment Tables";
$page_header = "Fix Payment Tables";
$page_description = "Fix database tables for the payment system.";

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Fix Payment Tables</h1>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show"><?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show"><?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Operation Log</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group">
                        <?php foreach ($logs as $log): ?>
                            <li class="list-group-item"><?php echo htmlspecialchars($log); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <h5>Fixed Tables:</h5>
                    <ul>
                        <li><strong>donations</strong> - Stores donation records</li>
                        <li><strong>payment_transactions</strong> - Stores detailed payment transaction records</li>
                        <li><strong>donation_notifications</strong> - Tracks email notifications for donations</li>
                    </ul>
                    
                    <p>These tables are now ready to handle:</p>
                    <ul>
                        <li>Online donations through PayPal and Stripe</li>
                        <li>Birthday gift donations</li>
                        <li>Payment transaction tracking</li>
                        <li>Email notification tracking</li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-4">
                <a href="payment_integration.php" class="btn btn-primary me-2">Go to Payment Integration Settings</a>
                <a href="donations.php" class="btn btn-secondary">Go to Donations</a>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 