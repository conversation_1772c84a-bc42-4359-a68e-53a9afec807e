<?php
// <PERSON><PERSON><PERSON> to check how the email system processes placeholders
require_once __DIR__ . '/../config.php';

try {
    echo "<h2>Email System Placeholder Processing Check</h2>";
    
    // Get the email functions
    echo "<h3>Checking Email Functions:</h3>";
    
    // Look for email sending functions in config.php
    $config_file = file_get_contents(__DIR__ . '/../config.php');
    
    // Check for send_email function
    if (function_exists('send_email')) {
        echo "<p>✅ Found send_email function in config.php</p>";
        // Show function signature
        $reflection = new ReflectionFunction('send_email');
        echo "<p>Function parameters: " . implode(', ', array_map(function($param) {
            return '$' . $param->getName();
        }, $reflection->getParameters())) . "</p>";
    } else {
        echo "<p>❌ send_email function not found!</p>";
    }
    
    // Check for placeholder replacement in the code
    $placeholder_patterns = [
        'replyToEmail' => preg_match('/\{\{replyToEmail\}\}/', $config_file),
        'unsubscribeLink' => preg_match('/\{\{unsubscribeLink\}\}/', $config_file),
        '{{placeholder}}' => preg_match('/\{\{([^}]+)\}\}/', $config_file)
    ];
    
    echo "<h3>Placeholder Processing Pattern Check:</h3>";
    echo "<ul>";
    foreach ($placeholder_patterns as $pattern => $found) {
        echo "<li>" . $pattern . ": " . ($found ? "✅ Found" : "❌ Not found") . "</li>";
    }
    echo "</ul>";
    
    // Try to find the placeholder replacement code
    echo "<h3>Searching for Placeholder Replacement Code:</h3>";
    
    // Common patterns for placeholder replacement
    $replacement_patterns = [
        'str_replace(\'{{\', $' => preg_match('/str_replace\([\'"]{{[\'"], \$/', $config_file),
        'preg_replace(\'/\{\{([^}]+)\}\}/\'' => preg_match('/preg_replace\([\'"]\/\{\{([^}]+)\}\}\/[\'"]/', $config_file),
        'strtr($content, $placeholders)' => preg_match('/strtr\([^,]+, \$[^)]+\)/', $config_file)
    ];
    
    $found_replacement = false;
    foreach ($replacement_patterns as $pattern => $found) {
        if ($found) {
            echo "<p>✅ Found potential placeholder replacement using: " . htmlspecialchars($pattern) . "</p>";
            $found_replacement = true;
        }
    }
    
    if (!$found_replacement) {
        echo "<p>❌ No common placeholder replacement patterns found</p>";
        
        // Try to find any strings that might be related to placeholders
        preg_match_all('/placeholder|replace|template|content/i', $config_file, $matches);
        if (!empty($matches[0])) {
            $unique_matches = array_unique($matches[0]);
            echo "<p>Related terms found in config.php: " . implode(", ", $unique_matches) . "</p>";
        }
    }
    
    // Find which email templates have placeholders 
    $email_template_check = $pdo->prepare("SELECT id, template_name, content FROM email_templates 
                                        WHERE content LIKE '%{{%}}%' LIMIT 5");
    $email_template_check->execute();
    $placeholder_templates = $email_template_check->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Templates Using {{placeholder}} Format:</h3>";
    if (count($placeholder_templates) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Template Name</th><th>Placeholders Used</th></tr>";
        
        foreach ($placeholder_templates as $template) {
            preg_match_all('/\{\{([^}]+)\}\}/', $template['content'], $placeholders);
            $unique_placeholders = array_unique($placeholders[1]);
            
            echo "<tr>";
            echo "<td>" . $template['id'] . "</td>";
            echo "<td>" . htmlspecialchars($template['template_name']) . "</td>";
            echo "<td>" . htmlspecialchars(implode(", ", $unique_placeholders)) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No templates found using {{placeholder}} format</p>";
    }
    
    // Recommendations
    echo "<h3>Recommendations:</h3>";
    echo "<p>Based on our analysis, we recommend the following actions:</p>";
    echo "<ol>";
    echo "<li>Ensure the email system supports both {{placeholder}} format</li>";
    echo "<li>Check that replyToEmail is being correctly replaced in emails</li>";
    echo "<li>Fixed lottery numbers are working as intended</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
} 