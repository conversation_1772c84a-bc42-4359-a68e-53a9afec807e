<?php
session_start();

// Check for flash messages
$flash_message = '';
$flash_type = '';
if (isset($_SESSION['flash_message'])) {
    $flash_message = $_SESSION['flash_message'];
    $flash_type = $_SESSION['flash_type'] ?? 'success';
    unset($_SESSION['flash_message']);
    unset($_SESSION['flash_type']);
}

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';
$template = null;

// Check if editing existing template
if (isset($_GET['edit_id']) && !empty($_GET['edit_id'])) {
    $edit_id = intval($_GET['edit_id']);
    $stmt = $conn->prepare("SELECT * FROM email_templates WHERE id = ?");
    $stmt->execute([$edit_id]);
    $template = $stmt->fetch();
    
    if (!$template) {
        $_SESSION['flash_message'] = "Template not found.";
        $_SESSION['flash_type'] = 'danger';
        header("Location: email_templates.php");
        exit();
    }
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data and validate
    if (!isset($_POST['template_name']) || !isset($_POST['subject']) || !isset($_POST['content'])) {
        $error = "Form data is incomplete. Please fill in all required fields.";
    } else {
        $template_name = trim($_POST['template_name']);
        $subject = trim($_POST['subject']);
        $content = trim($_POST['content']);
        $template_category = $_POST['template_category'] ?? 'general';
        
        // Set is_birthday_template based on category
        $is_birthday_template = ($template_category === 'birthday') ? 1 : 0;
        
        // Validate required fields
        if (empty($template_name) || empty($subject) || empty($content)) {
            $error = "All fields are required!";
        } else {
            // Validate template name length against database column definition
            if (strlen($template_name) > 50) {
                $error = "Template name is too long. Maximum length is 50 characters.";
            } 
            // Validate subject length
            else if (strlen($subject) > 255) {
                $error = "Subject is too long. Maximum length is 255 characters.";
            }
            else {
                if (isset($_POST['template_id']) && !empty($_POST['template_id'])) {
                    // Update existing template
                    $template_id = intval($_POST['template_id']);
                    
                    $stmt = $conn->prepare("UPDATE email_templates SET template_name = ?, subject = ?, content = ?, is_birthday_template = ?, template_category = ? WHERE id = ?");
                    if ($stmt->execute([$template_name, $subject, $content, $is_birthday_template, $template_category, $template_id])) {
                        $_SESSION['flash_message'] = "Template updated successfully!";
                        $_SESSION['flash_type'] = 'success';
                        header("Location: email_templates.php");
                        exit();
                    } else {
                        $error = "Error updating template: " . $conn->errorInfo()[2];
                    }
                } else {
                    // Create new template
                    try {
                        $stmt = $conn->prepare("INSERT INTO email_templates (template_name, subject, content, is_birthday_template, template_category) VALUES (?, ?, ?, ?, ?)");
                        if ($stmt->execute([$template_name, $subject, $content, $is_birthday_template, $template_category])) {
                            $_SESSION['flash_message'] = "Template created successfully!";
                            $_SESSION['flash_type'] = 'success';
                            header("Location: email_templates.php");
                            exit();
                        } else {
                            $error = "Error creating template: " . $conn->errorInfo()[2];
                        }
                    } catch (PDOException $e) {
                        $error = "Database error: " . $e->getMessage();
                    }
                }
            }
        }
    }
}

// Set page variables
$page_title = isset($template) ? 'Edit Template: ' . $template['template_name'] : 'Create Email Template';

// Include header
include 'includes/header.php';

// Display error messages
if (!empty($error)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($error) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}
?>

<!-- Instructions panel -->
<div class="alert alert-info mb-4 instruction-panel">
    <div class="d-flex justify-content-between align-items-start">
        <h5><i class="bi bi-info-circle-fill me-2"></i>Creating Email Templates</h5>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <p class="mb-2">This page allows you to create or edit email templates for various communications.</p>
    <ul class="mb-0">
        <li><strong>Template Name:</strong> Give your template a descriptive name</li>
        <li><strong>Subject Line:</strong> Create an engaging subject that recipients will see</li>
        <li><strong>Template Category:</strong> Choose the purpose of this template</li>
        <li><strong>Content:</strong> Design your email using the rich text editor</li>
    </ul>
    <div class="mt-2">
        <strong>Placeholders:</strong> Use {full_name}, {first_name}, {church_name}, etc. to personalize emails.
        <br>
        <strong>Note:</strong> Newsletter templates will not include member profile images to maintain a consistent appearance.
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-8">
        <h2><?php echo isset($template) ? 'Edit Template' : 'Create New Template'; ?></h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="email_templates.php" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Templates
        </a>
    </div>
</div>

<!-- Template Form -->
<div class="card">
    <div class="card-body">
        <?php if (isset($error) && strpos($error, 'Error creating template') !== false): ?>
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i> 
            There was a problem saving your template. Please check that all fields are filled correctly.
            <ul>
                <li>Template name must be unique and less than 50 characters</li>
                <li>Subject must be less than 255 characters</li>
                <li>Content must not be empty</li>
            </ul>
        </div>
        <?php endif; ?>
        
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . (isset($template) ? '?edit_id=' . $template['id'] : '')); ?>">
            <?php if (isset($template)): ?>
            <input type="hidden" name="template_id" value="<?php echo $template['id']; ?>">
            <?php endif; ?>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="template_name" class="form-label">Template Name</label>
                    <input type="text" class="form-control" id="template_name" name="template_name" 
                           value="<?php echo isset($template) ? htmlspecialchars($template['template_name']) : ''; ?>" 
                           required data-bs-toggle="tooltip" data-bs-placement="top" 
                           title="Give your template a descriptive name (e.g., 'Monthly Newsletter', 'Birthday Greeting')">
                </div>
                <div class="col-md-6">
                    <label for="subject" class="form-label">Email Subject</label>
                    <input type="text" class="form-control" id="subject" name="subject" 
                           value="<?php echo isset($template) ? htmlspecialchars($template['subject']) : ''; ?>" 
                           required data-bs-toggle="tooltip" data-bs-placement="top" 
                           title="This is what recipients will see in their inbox. You can use placeholders like {full_name}.">
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="template_category" class="form-label">Template Type</label>
                    <select class="form-select" id="template_category" name="template_category" required data-bs-toggle="tooltip" data-bs-placement="top" title="Select the type of template. This determines how the template will be used in the system.">
                        <option value="">Select template type...</option>
                        <optgroup label="Common Types">
                            <option value="general" <?php echo (isset($template) && $template['template_category'] == 'general') ? 'selected' : ''; ?>>General Purpose</option>
                            <option value="bulk" <?php echo (isset($template) && $template['template_category'] == 'bulk') ? 'selected' : ''; ?>>Bulk Email</option>
                            <option value="newsletter" <?php echo (isset($template) && $template['template_category'] == 'newsletter') ? 'selected' : ''; ?>>Newsletter</option>
                        </optgroup>
                        <optgroup label="Special Types">
                            <option value="birthday" <?php echo (isset($template) && $template['template_category'] == 'birthday') ? 'selected' : ''; ?>>Birthday Email</option>
                            <option value="welcome" <?php echo (isset($template) && $template['template_category'] == 'welcome') ? 'selected' : ''; ?>>Welcome Email</option>
                            <option value="reminder" <?php echo (isset($template) && $template['template_category'] == 'reminder') ? 'selected' : ''; ?>>Reminder</option>
                        </optgroup>
                        <optgroup label="Other">
                            <option value="announcement" <?php echo (isset($template) && $template['template_category'] == 'announcement') ? 'selected' : ''; ?>>Announcement</option>
                            <option value="event" <?php echo (isset($template) && $template['template_category'] == 'event') ? 'selected' : ''; ?>>Event</option>
                            <option value="custom" <?php echo (isset($template) && $template['template_category'] == 'custom') ? 'selected' : ''; ?>>Custom</option>
                        </optgroup>
                    </select>
                    <div class="form-text mt-2">
                        <i class="bi bi-info-circle me-1"></i>
                        <span id="categoryDescription">Select a template type to see its description</span>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="content" class="form-label">Email Content</label>
                <textarea class="form-control <?php echo isset($error) && strpos($error, 'content') !== false ? 'is-invalid' : ''; ?>" 
                    id="content" name="content" rows="10" required><?php echo isset($template) ? htmlspecialchars($template['content']) : (isset($_POST['content']) ? htmlspecialchars($_POST['content']) : ''); ?></textarea>
                <div class="form-text">
                    <p>Use the following placeholders in your templates:</p>
                    
                    <!-- Placeholder Tabs -->
                    <ul class="nav nav-tabs" id="placeholderTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">General</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="bulk-tab" data-bs-toggle="tab" data-bs-target="#bulk" type="button" role="tab" aria-controls="bulk" aria-selected="false">Bulk Email</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="birthday-tab" data-bs-toggle="tab" data-bs-target="#birthday" type="button" role="tab" aria-controls="birthday" aria-selected="false">Birthday</button>
                        </li>
                    </ul>
                    
                    <div class="tab-content pt-3" id="placeholderTabsContent">
                        <!-- General Placeholders Tab -->
                        <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Placeholder</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>{full_name}</code></td>
                                            <td>Member's full name</td>
                                        </tr>
                                        <tr>
                                            <td><code>{member_name}</code></td>
                                            <td>Member's first name</td>
                                        </tr>
                                        <tr>
                                            <td><code>{email}</code></td>
                                            <td>Member's email address</td>
                                        </tr>
                                        <tr>
                                            <td><code>{phone_number}</code></td>
                                            <td>Member's phone number</td>
                                        </tr>
                                        <tr>
                                            <td><code>{member_image}</code></td>
                                            <td>Member's profile image</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- Bulk Email Placeholders Tab -->
                        <div class="tab-pane fade" id="bulk" role="tabpanel" aria-labelledby="bulk-tab">
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Placeholder</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>{recipient_full_name}</code></td>
                                            <td>Recipient's full name</td>
                                        </tr>
                                        <tr>
                                            <td><code>{recipient_first_name}</code></td>
                                            <td>Recipient's first name</td>
                                        </tr>
                                        <tr>
                                            <td><code>{recipient_email}</code></td>
                                            <td>Recipient's email address</td>
                                        </tr>
                                        <tr>
                                            <td><code>{recipient_phone}</code></td>
                                            <td>Recipient's phone number</td>
                                        </tr>
                                        <tr>
                                            <td><code>{sender_name}</code></td>
                                            <td>Sender's name (admin)</td>
                                        </tr>
                                        <tr>
                                            <td><code>{sender_email}</code></td>
                                            <td>Sender's email (admin)</td>
                                        </tr>
                                        <tr>
                                            <td><code>{total_recipients}</code></td>
                                            <td>Total number of recipients</td>
                                        </tr>
                                        <tr>
                                            <td><code>{church_name}</code></td>
                                            <td>Church name</td>
                                        </tr>
                                        <tr>
                                            <td><code>{current_date}</code></td>
                                            <td>Current date (e.g., January 1, 2024)</td>
                                        </tr>
                                        <tr>
                                            <td><code>{current_time}</code></td>
                                            <td>Current time (e.g., 3:30 PM)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- Birthday Placeholders Tab -->
                        <div class="tab-pane fade" id="birthday" role="tabpanel" aria-labelledby="birthday-tab">
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Placeholder</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>{birth_date}</code></td>
                                            <td>Member's birth date</td>
                                        </tr>
                                        <tr>
                                            <td><code>{age}</code></td>
                                            <td>Member's current age</td>
                                        </tr>
                                        <tr>
                                            <td><code>{days_until_birthday}</code></td>
                                            <td>Days until next birthday</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <p class="text-muted mt-3"><small>Note: The older <code>[name]</code> syntax is supported for backward compatibility but new templates should use the curly braces syntax.</small></p>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Preview</label>
                <div class="border p-3 rounded">
                    <iframe id="preview-frame" style="width: 100%; height: 500px; border: none;"></iframe>
                </div>
            </div>
            
            <div class="text-end">
                <a href="email_templates.php" class="btn btn-secondary me-2">Cancel</a>
                <button type="submit" class="btn btn-primary"><?php echo isset($template) ? 'Update Template' : 'Create Template'; ?></button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const contentField = document.getElementById('content');
    const previewFrame = document.getElementById('preview-frame');
    const templateCategorySelect = document.getElementById('template_category');
    
    if (contentField && previewFrame) {
        // Initial preview
        updatePreview();
        
        // Update preview on input
        contentField.addEventListener('input', updatePreview);
        
        function updatePreview() {
            let content = contentField.value;
            const category = templateCategorySelect ? templateCategorySelect.value : 'general';
            
            // Create sample member data based on template category
            const sampleMember = {
                full_name: 'John Doe',
                first_name: 'John',
                email: '<EMAIL>',
                phone_number: '(*************',
                member_image: '../uploads/default-profile.jpg'
            };
            
            // Add bulk-specific placeholders if it's a bulk template
            if (category === 'bulk') {
                Object.assign(sampleMember, {
                    recipient_full_name: 'John Doe',
                    recipient_first_name: 'John',
                    recipient_email: '<EMAIL>',
                    recipient_phone: '(*************',
                    sender_name: 'Admin User',
                    sender_email: '<EMAIL>',
                    total_recipients: '50',
                    church_name: 'Freedom Assembly Church'
                });
            }
            
            // Add birthday-specific placeholders if it's a birthday template
            if (category === 'birthday') {
                Object.assign(sampleMember, {
                    birth_date: 'January 15',
                    age: '30',
                    days_until_birthday: '5'
                });
            }
            
            // Replace placeholders with sample values
            for (const [key, value] of Object.entries(sampleMember)) {
                content = content.replace(new RegExp(`\\{${key}\\}`, 'g'), value);
                content = content.replace(new RegExp(`\\[${key}\\]`, 'g'), value);
            }
            
            // Create a complete HTML document for the iframe
            const previewHtml = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                    <title>Email Preview</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                    </style>
                </head>
                <body>
                    ${content}
                </body>
                </html>
            `;
            
            // Update the iframe content
            const frameDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;
            frameDoc.open();
            frameDoc.write(previewHtml);
            frameDoc.close();
        }
    }

    const categorySelect = document.getElementById('template_category');
    const categoryDescription = document.getElementById('categoryDescription');
    const descriptions = {
        'general': 'For general purpose emails that don\'t fit other categories',
        'bulk': 'For sending mass emails to multiple recipients',
        'newsletter': 'For periodic newsletters with church updates and news',
        'birthday': 'For automated birthday greetings to members',
        'welcome': 'For welcoming new members to the church',
        'reminder': 'For event reminders and important notifications',
        'announcement': 'For church announcements and important updates',
        'event': 'For event invitations and details',
        'custom': 'For specialized emails with custom requirements'
    };

    categorySelect.addEventListener('change', function() {
        const selected = this.value;
        if (descriptions[selected]) {
            categoryDescription.textContent = descriptions[selected];
        } else {
            categoryDescription.textContent = 'Select a template type to see its description';
        }
    });

    // Set initial description if a value is selected
    if (categorySelect.value) {
        categoryDescription.textContent = descriptions[categorySelect.value] || 'Select a template type to see its description';
    }
});
</script>

<?php include_once 'includes/footer.php'; ?> 