/**
 * Language Switcher
 * Handles language switching functionality
 */

class LanguageSwitcher {
    constructor() {
        this.currentLanguage = document.documentElement.lang || 'en';
        this.init();
    }
    
    init() {
        this.createLanguageSwitcher();
        this.setupEventListeners();
        console.log('Language Switcher initialized. Current language:', this.currentLanguage);
    }
    
    createLanguageSwitcher() {
        // Check if switcher already exists
        if (document.getElementById('languageSwitcher')) {
            return;
        }
        
        // Available languages
        const languages = {
            'en': { name: 'English', flag: '🇺🇸' },
            'es': { name: 'Español', flag: '🇪🇸' },
            'fr': { name: 'Français', flag: '🇫🇷' },
            'de': { name: 'Deuts<PERSON>', flag: '🇩🇪' },
            'it': { name: 'Italiano', flag: '🇮🇹' },
            'pt': { name: 'Português', flag: '🇧🇷' },
            'nl': { name: 'Nederlands', flag: '🇳🇱' },
            'ru': { name: 'Русский', flag: '🇷🇺' },
            'zh': { name: '中文', flag: '🇨🇳' },
            'ja': { name: '日本語', flag: '🇯🇵' },
            'ko': { name: '한국어', flag: '🇰🇷' },
            'ar': { name: 'العربية', flag: '🇸🇦' }
        };
        
        // Create dropdown container
        const container = document.createElement('div');
        container.className = 'language-switcher dropdown';
        container.id = 'languageSwitcher';
        
        // Create toggle button
        const toggle = document.createElement('button');
        toggle.className = 'language-toggle dropdown-toggle';
        toggle.setAttribute('data-bs-toggle', 'dropdown');
        toggle.setAttribute('aria-expanded', 'false');
        toggle.setAttribute('aria-label', 'Select language');
        toggle.setAttribute('title', 'Select language');
        
        const currentLang = languages[this.currentLanguage] || languages['en'];
        toggle.innerHTML = `
            <span class="flag">${currentLang.flag}</span>
            <span class="lang-code">${this.currentLanguage.toUpperCase()}</span>
        `;
        
        // Create dropdown menu
        const menu = document.createElement('ul');
        menu.className = 'dropdown-menu language-menu';
        menu.setAttribute('aria-labelledby', 'languageToggle');
        
        // Add language options
        Object.entries(languages).forEach(([code, lang]) => {
            const item = document.createElement('li');
            const link = document.createElement('a');
            link.className = 'dropdown-item language-option';
            link.href = '#';
            link.setAttribute('data-language', code);
            link.setAttribute('role', 'menuitem');
            
            if (code === this.currentLanguage) {
                link.classList.add('active');
                link.setAttribute('aria-current', 'true');
            }
            
            link.innerHTML = `
                <span class="flag">${lang.flag}</span>
                <span class="name">${lang.name}</span>
                <span class="code">${code.toUpperCase()}</span>
            `;
            
            item.appendChild(link);
            menu.appendChild(item);
        });
        
        container.appendChild(toggle);
        container.appendChild(menu);
        
        // Add to sidebar or header
        const sidebar = document.querySelector('.sidebar .nav');
        const header = document.querySelector('.navbar .navbar-nav');
        
        if (sidebar) {
            // Add to sidebar
            const listItem = document.createElement('li');
            listItem.className = 'nav-item language-switcher-item';
            listItem.appendChild(container);
            sidebar.appendChild(listItem);
        } else if (header) {
            // Add to header
            const listItem = document.createElement('li');
            listItem.className = 'nav-item language-switcher-item';
            listItem.appendChild(container);
            header.appendChild(listItem);
        } else {
            // Add to body as floating element
            container.classList.add('floating-language-switcher');
            document.body.appendChild(container);
        }
    }
    
    setupEventListeners() {
        // Handle language selection
        document.addEventListener('click', (e) => {
            if (e.target.closest('.language-option')) {
                e.preventDefault();
                const option = e.target.closest('.language-option');
                const language = option.getAttribute('data-language');
                this.changeLanguage(language);
            }
        });
        
        // Handle keyboard navigation
        document.addEventListener('keydown', (e) => {
            const dropdown = e.target.closest('.language-switcher');
            if (!dropdown) return;
            
            const menu = dropdown.querySelector('.language-menu');
            const options = menu.querySelectorAll('.language-option');
            const currentIndex = Array.from(options).findIndex(opt => opt === document.activeElement);
            
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                const nextIndex = currentIndex < options.length - 1 ? currentIndex + 1 : 0;
                options[nextIndex].focus();
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                const prevIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
                options[prevIndex].focus();
            } else if (e.key === 'Enter' || e.key === ' ') {
                if (e.target.classList.contains('language-option')) {
                    e.preventDefault();
                    e.target.click();
                }
            }
        });
    }
    
    async changeLanguage(language) {
        if (language === this.currentLanguage) {
            return;
        }
        
        try {
            // Show loading state
            this.setLoadingState(true);
            
            // Send AJAX request
            const response = await fetch(ADMIN_URL + '/ajax/change_language.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ language: language })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Update current language
                this.currentLanguage = language;
                
                // Update HTML lang attribute
                document.documentElement.lang = language;
                document.documentElement.dir = result.direction || 'ltr';
                
                // Update UI
                this.updateLanguageSwitcher(language);
                
                // Announce change for screen readers
                if (window.accessibilityManager) {
                    window.accessibilityManager.announce(
                        `Language changed to ${result.language_name}`,
                        'polite'
                    );
                }
                
                // Reload page to apply language changes
                setTimeout(() => {
                    window.location.reload();
                }, 500);
                
            } else {
                throw new Error(result.error || 'Failed to change language');
            }
            
        } catch (error) {
            console.error('Language change error:', error);
            
            // Show error message
            if (window.accessibilityManager) {
                window.accessibilityManager.announce(
                    'Failed to change language. Please try again.',
                    'assertive'
                );
            }
            
            // Show visual error feedback
            this.showError('Failed to change language');
            
        } finally {
            this.setLoadingState(false);
        }
    }
    
    updateLanguageSwitcher(language) {
        const languages = {
            'en': { name: 'English', flag: '🇺🇸' },
            'es': { name: 'Español', flag: '🇪🇸' },
            'fr': { name: 'Français', flag: '🇫🇷' },
            'de': { name: 'Deutsch', flag: '🇩🇪' },
            'it': { name: 'Italiano', flag: '🇮🇹' },
            'pt': { name: 'Português', flag: '🇧🇷' },
            'nl': { name: 'Nederlands', flag: '🇳🇱' },
            'ru': { name: 'Русский', flag: '🇷🇺' },
            'zh': { name: '中文', flag: '🇨🇳' },
            'ja': { name: '日本語', flag: '🇯🇵' },
            'ko': { name: '한국어', flag: '🇰🇷' },
            'ar': { name: 'العربية', flag: '🇸🇦' }
        };
        
        // Update toggle button
        const toggle = document.querySelector('.language-toggle');
        if (toggle) {
            const lang = languages[language] || languages['en'];
            toggle.innerHTML = `
                <span class="flag">${lang.flag}</span>
                <span class="lang-code">${language.toUpperCase()}</span>
            `;
        }
        
        // Update active state in dropdown
        document.querySelectorAll('.language-option').forEach(option => {
            option.classList.remove('active');
            option.removeAttribute('aria-current');
            
            if (option.getAttribute('data-language') === language) {
                option.classList.add('active');
                option.setAttribute('aria-current', 'true');
            }
        });
    }
    
    setLoadingState(loading) {
        const toggle = document.querySelector('.language-toggle');
        if (toggle) {
            if (loading) {
                toggle.classList.add('loading');
                toggle.disabled = true;
            } else {
                toggle.classList.remove('loading');
                toggle.disabled = false;
            }
        }
    }
    
    showError(message) {
        // Create temporary error message
        const error = document.createElement('div');
        error.className = 'language-error alert alert-danger';
        error.textContent = message;
        error.style.position = 'fixed';
        error.style.top = '20px';
        error.style.right = '20px';
        error.style.zIndex = '9999';
        error.style.minWidth = '250px';
        
        document.body.appendChild(error);
        
        // Remove after 3 seconds
        setTimeout(() => {
            if (error.parentNode) {
                error.parentNode.removeChild(error);
            }
        }, 3000);
    }
    
    // Public API
    getCurrentLanguage() {
        return this.currentLanguage;
    }
    
    setLanguage(language) {
        return this.changeLanguage(language);
    }
}

// Initialize language switcher when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.languageSwitcher = new LanguageSwitcher();
});

// Export for use in other scripts
window.LanguageSwitcher = LanguageSwitcher;
