<?php
use PHPUnit\Framework\TestCase;

class EmailTemplateTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        resetTestDatabase();
    }
    
    public function testTemplatePlaceholderReplacement()
    {
        $memberData = [
            'full_name' => '<PERSON>',
            'first_name' => '<PERSON>',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'birth_date' => '1990-01-01'
        ];
        
        $content = "Hello {first_name},\n\nHappy Birthday to {full_name}!\nEmail: {email}";
        $expected = "Hello John,\n\nHappy Birthday to <PERSON>!\nEmail: <EMAIL>";
        
        $result = replaceTemplatePlaceholders($content, $memberData);
        $this->assertEquals($expected, $result);
    }
    
    public function testTemplateWithMissingData()
    {
        $memberData = [
            'first_name' => 'John'
            // Missing other fields
        ];
        
        $content = "Hello {first_name} {last_name},\nEmail: {email}";
        $result = replaceTemplatePlaceholders($content, $memberData);
        
        $this->assertStringContainsString('Hello John', $result);
        $this->assertStringContainsString('Email:', $result);
    }
    
    public function testTemplateWithCustomPlaceholders()
    {
        $memberData = [
            'first_name' => 'John',
            'custom_field' => 'Custom Value'
        ];
        
        $content = "Hello {first_name}, {custom_field}";
        $result = replaceTemplatePlaceholders($content, $memberData);
        
        $this->assertEquals("Hello John, Custom Value", $result);
    }
    
    public function testDatabaseTemplateStorage()
    {
        // Create test template
        $templateId = createTestTemplate([
            'template_name' => 'Test Template',
            'subject' => 'Test Subject {first_name}',
            'content' => 'Hello {first_name} {last_name}',
            'is_birthday_template' => 1
        ]);
        
        // Retrieve template from database
        $stmt = $GLOBALS['pdo']->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$templateId]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals('Test Template', $template['template_name']);
        $this->assertEquals('Test Subject {first_name}', $template['subject']);
        $this->assertEquals('Hello {first_name} {last_name}', $template['content']);
        $this->assertEquals(1, $template['is_birthday_template']);
    }
    
    protected function tearDown(): void
    {
        resetTestDatabase();
        parent::tearDown();
    }
} 