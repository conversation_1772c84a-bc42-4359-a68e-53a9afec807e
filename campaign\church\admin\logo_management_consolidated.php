<?php
/**
 * Consolidated Logo Management
 * Comprehensive logo management combining features from logo_upload.php and logo_management.php
 * Features: Multiple logo types, favicon generation, advanced image processing, settings management
 */

// Start session first
session_start();

// Set admin session if not already set (for testing)
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = 4;
    $_SESSION['admin_username'] = 'admin';
    $_SESSION['admin_email'] = '<EMAIL>';
    $_SESSION['admin_full_name'] = 'Church Administrator';
    $_SESSION['admin_role'] = 'admin';
    $_SESSION['CREATED'] = time();
    $_SESSION['LAST_ACTIVITY'] = time();
}

// Include database config
require_once '../config.php';

// Set page title
$page_title = 'Logo Management';

// Check if GD extension is loaded
$gd_available = extension_loaded('gd');
if (!$gd_available) {
    $gd_error = "GD extension is not enabled. Advanced features like favicon generation require GD extension.";
}

// Helper functions
function get_site_setting($key, $default = '') {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM appearance_settings WHERE setting_name = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetchColumn();
        return $result !== false ? $result : $default;
    } catch (PDOException $e) {
        return $default;
    }
}

function update_site_setting($key, $value) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            INSERT INTO appearance_settings (setting_name, setting_value, updated_at)
            VALUES (?, ?, NOW())
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
        ");
        return $stmt->execute([$key, $value]);
    } catch (PDOException $e) {
        return false;
    }
}

function handleLogoUpload($logoType) {
    global $gd_available;
    
    if (!isset($_FILES['logo_file']) || $_FILES['logo_file']['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'No file uploaded or upload error occurred.'];
    }
    
    $file = $_FILES['logo_file'];
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
    
    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type. Please upload JPG, PNG, GIF, WebP, or SVG files only.'];
    }
    
    // Check file size (max 5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        return ['success' => false, 'message' => 'File size too large. Maximum size is 5MB.'];
    }
    
    // Create upload directories
    $uploadDir = __DIR__ . '/uploads/logos/';
    $assetsDir = __DIR__ . '/../assets/img/logos/';
    
    if (!is_dir($uploadDir)) mkdir($uploadDir, 0755, true);
    if (!is_dir($assetsDir)) mkdir($assetsDir, 0755, true);
    
    // Generate filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = $logoType . '_logo_' . time() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    $assetsPath = $assetsDir . $logoType . '_logo.' . $extension;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        // Also copy to assets directory for consistency
        copy($filepath, $assetsPath);
        
        // Delete old logo if exists
        $oldLogo = get_site_setting($logoType . '_logo');
        if ($oldLogo && file_exists(__DIR__ . '/' . $oldLogo)) {
            unlink(__DIR__ . '/' . $oldLogo);
        }
        
        // Save logo path to database
        $relativePath = 'uploads/logos/' . $filename;
        update_site_setting($logoType . '_logo', $relativePath);
        
        // Generate favicon if main logo and GD available
        if ($logoType === 'main' && $gd_available) {
            generateFavicon($filepath);
        }
        
        return ['success' => true, 'message' => ucfirst($logoType) . ' logo uploaded successfully!'];
    } else {
        return ['success' => false, 'message' => 'Failed to save uploaded file.'];
    }
}

function generateFavicon($logoPath = null) {
    global $gd_available;
    
    if (!$gd_available) {
        return ['success' => false, 'message' => 'GD extension required for favicon generation.'];
    }
    
    if (!$logoPath) {
        $mainLogoPath = get_site_setting('main_logo', '');
        if (!$mainLogoPath) {
            return ['success' => false, 'message' => 'No main logo found. Please upload a main logo first.'];
        }
        $logoPath = __DIR__ . '/' . $mainLogoPath;
    }
    
    if (!file_exists($logoPath)) {
        return ['success' => false, 'message' => 'Logo file not found.'];
    }
    
    $imageInfo = getimagesize($logoPath);
    if (!$imageInfo) {
        return ['success' => false, 'message' => 'Invalid logo file.'];
    }
    
    // Create image resource
    switch ($imageInfo[2]) {
        case IMAGETYPE_JPEG:
            $image = imagecreatefromjpeg($logoPath);
            break;
        case IMAGETYPE_PNG:
            $image = imagecreatefrompng($logoPath);
            break;
        case IMAGETYPE_GIF:
            $image = imagecreatefromgif($logoPath);
            break;
        default:
            return ['success' => false, 'message' => 'Unsupported image format for favicon generation.'];
    }
    
    if (!$image) {
        return ['success' => false, 'message' => 'Failed to process image.'];
    }
    
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    
    // Generate different favicon sizes
    $faviconSizes = [16, 32, 48, 64, 128, 256];
    $faviconDir = __DIR__ . '/../assets/img/favicons/';
    
    if (!is_dir($faviconDir)) {
        mkdir($faviconDir, 0755, true);
    }
    
    foreach ($faviconSizes as $size) {
        $favicon = imagecreatetruecolor($size, $size);
        
        // Preserve transparency
        imagealphablending($favicon, false);
        imagesavealpha($favicon, true);
        $transparent = imagecolorallocatealpha($favicon, 255, 255, 255, 127);
        imagefill($favicon, 0, 0, $transparent);
        
        // Resize to square favicon
        imagecopyresampled($favicon, $image, 0, 0, 0, 0, $size, $size, $width, $height);
        
        // Save as PNG
        $faviconPath = $faviconDir . "favicon-{$size}x{$size}.png";
        imagepng($favicon, $faviconPath, 6);
        imagedestroy($favicon);
    }
    
    // Update database settings
    update_site_setting('favicon_16', 'assets/img/favicons/favicon-16x16.png');
    update_site_setting('favicon_32', 'assets/img/favicons/favicon-32x32.png');
    
    imagedestroy($image);
    
    return ['success' => true, 'message' => 'Favicon generated successfully in multiple sizes!'];
}

function deleteLogo($logoType) {
    $logoPath = get_site_setting($logoType . '_logo', '');
    if (!$logoPath) {
        return ['success' => false, 'message' => 'No logo found to delete.'];
    }
    
    $fullPath = __DIR__ . '/' . $logoPath;
    if (file_exists($fullPath)) {
        if (unlink($fullPath)) {
            update_site_setting($logoType . '_logo', '');
            return ['success' => true, 'message' => ucfirst($logoType) . ' logo deleted successfully!'];
        } else {
            return ['success' => false, 'message' => 'Failed to delete logo file.'];
        }
    } else {
        update_site_setting($logoType . '_logo', '');
        return ['success' => true, 'message' => 'Logo reference removed (file was already missing).'];
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'upload_logo':
                    $logoType = $_POST['logo_type'] ?? 'main';
                    $uploadResult = handleLogoUpload($logoType);
                    
                    if ($uploadResult['success']) {
                        $success_message = $uploadResult['message'];
                    } else {
                        $error_message = $uploadResult['message'];
                    }
                    break;
                    
                case 'delete_logo':
                    $logoType = $_POST['logo_type'] ?? 'main';
                    $deleteResult = deleteLogo($logoType);
                    
                    if ($deleteResult['success']) {
                        $success_message = $deleteResult['message'];
                    } else {
                        $error_message = $deleteResult['message'];
                    }
                    break;
                    
                case 'generate_favicon':
                    $faviconResult = generateFavicon();
                    
                    if ($faviconResult['success']) {
                        $success_message = $faviconResult['message'];
                    } else {
                        $error_message = $faviconResult['message'];
                    }
                    break;
                    
                case 'update_logo_settings':
                    $settings = [
                        'logo_width' => (int)$_POST['logo_width'],
                        'logo_height' => (int)$_POST['logo_height'],
                        'logo_position' => $_POST['logo_position'],
                        'show_logo_text' => isset($_POST['show_logo_text']) ? 1 : 0,
                        'logo_text' => $_POST['logo_text']
                    ];
                    
                    foreach ($settings as $key => $value) {
                        update_site_setting($key, $value);
                    }
                    
                    $success_message = "Logo display settings updated successfully!";
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get current logos and settings
$logoTypes = ['main', 'header', 'email', 'favicon'];
$currentLogos = [];
foreach ($logoTypes as $type) {
    $logoPath = get_site_setting($type . '_logo');
    $currentLogos[$type] = $logoPath ? $logoPath : null;
}

$logoSettings = [
    'logo_width' => get_site_setting('logo_width', '200'),
    'logo_height' => get_site_setting('logo_height', '80'),
    'logo_position' => get_site_setting('logo_position', 'left'),
    'show_logo_text' => get_site_setting('show_logo_text', '0'),
    'logo_text' => get_site_setting('logo_text', '')
];

require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-image"></i> Logo Management
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-secondary" onclick="previewLogos()">
            <i class="bi bi-eye"></i> Preview All
        </button>
    </div>
</div>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($gd_error)): ?>
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo $gd_error; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Logo Management Grid -->
<div class="row">
    <?php
    $logoTypeInfo = [
        'main' => ['title' => 'Main Logo', 'desc' => 'Primary logo displayed throughout the site', 'icon' => 'bi-star-fill'],
        'header' => ['title' => 'Header Logo', 'desc' => 'Logo displayed in the header/navigation', 'icon' => 'bi-layout-navbar'],
        'email' => ['title' => 'Email Logo', 'desc' => 'Logo used in email templates', 'icon' => 'bi-envelope'],
        'favicon' => ['title' => 'Favicon', 'desc' => 'Small icon displayed in browser tabs', 'icon' => 'bi-bookmark']
    ];

    foreach ($logoTypes as $type):
        $info = $logoTypeInfo[$type];
        $hasLogo = !empty($currentLogos[$type]);
    ?>
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex align-items-center">
                <i class="bi <?php echo $info['icon']; ?> me-2"></i>
                <strong><?php echo $info['title']; ?></strong>
            </div>
            <div class="card-body text-center">
                <div class="logo-preview mb-3" style="height: 120px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border: 1px dashed #dee2e6; border-radius: 8px;">
                    <?php if ($hasLogo): ?>
                        <img src="<?php echo htmlspecialchars($currentLogos[$type]); ?>"
                             alt="<?php echo $info['title']; ?>"
                             style="max-width: 100%; max-height: 100%; object-fit: contain;">
                    <?php else: ?>
                        <div class="text-muted">
                            <i class="bi <?php echo $info['icon']; ?>" style="font-size: 2rem;"></i>
                            <div class="mt-2">No logo uploaded</div>
                        </div>
                    <?php endif; ?>
                </div>
                <p class="text-muted small"><?php echo $info['desc']; ?></p>
                <div class="btn-group w-100" role="group">
                    <button type="button" class="btn btn-primary btn-sm" onclick="uploadLogo('<?php echo $type; ?>')">
                        <i class="bi bi-upload"></i> Upload
                    </button>
                    <?php if ($hasLogo): ?>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteLogo('<?php echo $type; ?>')">
                            <i class="bi bi-trash"></i> Delete
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>

<!-- Logo Display Settings -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-sliders"></i> Logo Display Settings
        </h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <input type="hidden" name="action" value="update_logo_settings">
            <div class="row">
                <div class="col-md-3">
                    <label for="logo_width" class="form-label">Width (px)</label>
                    <input type="number" class="form-control" id="logo_width" name="logo_width"
                           value="<?php echo htmlspecialchars($logoSettings['logo_width']); ?>" min="50" max="500">
                </div>
                <div class="col-md-3">
                    <label for="logo_height" class="form-label">Height (px)</label>
                    <input type="number" class="form-control" id="logo_height" name="logo_height"
                           value="<?php echo htmlspecialchars($logoSettings['logo_height']); ?>" min="30" max="300">
                </div>
                <div class="col-md-3">
                    <label for="logo_position" class="form-label">Position</label>
                    <select class="form-select" id="logo_position" name="logo_position">
                        <option value="left" <?php echo $logoSettings['logo_position'] === 'left' ? 'selected' : ''; ?>>Left</option>
                        <option value="center" <?php echo $logoSettings['logo_position'] === 'center' ? 'selected' : ''; ?>>Center</option>
                        <option value="right" <?php echo $logoSettings['logo_position'] === 'right' ? 'selected' : ''; ?>>Right</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-success w-100">
                        <i class="bi bi-check-lg"></i> Update Settings
                    </button>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="show_logo_text" name="show_logo_text"
                               <?php echo $logoSettings['show_logo_text'] ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="show_logo_text">
                            Show text alongside logo
                        </label>
                    </div>
                </div>
                <div class="col-md-6">
                    <input type="text" class="form-control" id="logo_text" name="logo_text"
                           placeholder="Logo text (e.g., organization name)"
                           value="<?php echo htmlspecialchars($logoSettings['logo_text']); ?>">
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Logo Tools -->
<?php if ($gd_available): ?>
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-tools"></i> Logo Tools
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>Favicon Generator</h6>
                <p class="text-muted">Generate favicon files from your main logo in multiple sizes (16x16, 32x32, 48x48, etc.)</p>
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="generate_favicon">
                    <button type="submit" class="btn btn-outline-secondary" <?php echo empty($currentLogos['main']) ? 'disabled' : ''; ?>>
                        <i class="bi bi-gear"></i> Generate Favicon
                    </button>
                </form>
                <?php if (empty($currentLogos['main'])): ?>
                    <small class="text-muted d-block mt-1">Upload a main logo first</small>
                <?php endif; ?>
            </div>
            <div class="col-md-6">
                <h6>Logo Information</h6>
                <p class="text-muted">Tips for optimal logo usage:</p>
                <ul class="small text-muted">
                    <li>Use PNG format for logos with transparency</li>
                    <li>SVG format provides best scalability</li>
                    <li>Recommended size: 200x80px for main logo</li>
                    <li>Keep file size under 1MB for faster loading</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadModalLabel">Upload Logo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="upload_logo">
                    <input type="hidden" name="logo_type" id="logoType" value="main">

                    <div class="mb-3">
                        <label for="logo_file" class="form-label">Select Logo File</label>
                        <input type="file" class="form-control" id="logo_file" name="logo_file"
                               accept="image/jpeg,image/png,image/gif,image/webp,image/svg+xml" required>
                        <div class="form-text">
                            Supported formats: JPG, PNG, GIF, WebP, SVG. Maximum size: 5MB.
                        </div>
                    </div>

                    <div id="uploadPreview" style="display: none;">
                        <label class="form-label">Preview:</label>
                        <div class="text-center p-3 border rounded">
                            <img id="previewImage" src="" alt="Preview" style="max-width: 100%; max-height: 200px;">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-upload"></i> Upload Logo
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Upload logo function
function uploadLogo(type) {
    document.getElementById('logoType').value = type;

    // Update modal title
    const titles = {
        'main': 'Main Logo',
        'header': 'Header Logo',
        'email': 'Email Logo',
        'favicon': 'Favicon'
    };
    document.querySelector('#uploadModal .modal-title').textContent = 'Upload ' + titles[type];

    // Show modal
    new bootstrap.Modal(document.getElementById('uploadModal')).show();
}

// Delete logo function
function deleteLogo(type) {
    const titles = {
        'main': 'Main Logo',
        'header': 'Header Logo',
        'email': 'Email Logo',
        'favicon': 'Favicon'
    };

    if (confirm('Are you sure you want to delete the ' + titles[type] + '? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_logo">
            <input type="hidden" name="logo_type" value="${type}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Preview uploaded image
document.getElementById('logo_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('previewImage').src = e.target.result;
            document.getElementById('uploadPreview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        document.getElementById('uploadPreview').style.display = 'none';
    }
});

// Preview all logos function
function previewLogos() {
    const logos = <?php echo json_encode($currentLogos); ?>;
    let previewContent = '<div class="row">';

    <?php foreach ($logoTypes as $type): ?>
    if (logos['<?php echo $type; ?>']) {
        previewContent += `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <strong><?php echo $logoTypeInfo[$type]['title']; ?></strong>
                    </div>
                    <div class="card-body text-center">
                        <img src="${logos['<?php echo $type; ?>']}" alt="<?php echo $logoTypeInfo[$type]['title']; ?>"
                             style="max-width: 100%; max-height: 150px; object-fit: contain;">
                    </div>
                </div>
            </div>
        `;
    }
    <?php endforeach; ?>

    previewContent += '</div>';

    if (previewContent === '<div class="row"></div>') {
        alert('No logos uploaded yet.');
        return;
    }

    // Create preview window
    const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
    previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Logo Preview</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        </head>
        <body>
            <div class="container mt-4">
                <h2>Logo Preview</h2>
                ${previewContent}
            </div>
        </body>
        </html>
    `);
    previewWindow.document.close();
}
</script>

<?php require_once 'includes/footer.php'; ?>
