<?php

namespace test\Mockery\Fixtures;

class SemiReservedWordsAsMethods
{
    function callable() {}
    function class() {}
    function trait() {}
    function extends() {}
    function implements() {}
    function static() {}
    function abstract() {}
    function final() {}
    function public() {}
    function protected() {}
    function private() {}
    function const() {}
    function enddeclare() {}
    function endfor() {}
    function endforeach() {}
    function endif() {}
    function endwhile() {}
    function and() {}
    function global() {}
    function goto() {}
    function instanceof() {}
    function insteadof() {}
    function interface() {}
    function namespace() {}
    function new() {}
    function or() {}
    function xor() {}
    function try() {}
    function use() {}
    function var() {}
    function exit() {}
    function list() {}
    function clone() {}
    function include() {}
    function include_once() {}
    function throw() {}
    function array() {}
    function print() {}
    function echo() {}
    function require() {}
    function require_once() {}
    function return() {}
    function else() {}
    function elseif() {}
    function default() {}
    function break() {}
    function continue() {}
    function switch() {}
    function yield() {}
    function function() {}
    function if() {}
    function endswitch() {}
    function finally() {}
    function for() {}
    function foreach() {}
    function declare() {}
    function case() {}
    function do() {}
    function while() {}
    function as() {}
    function catch() {}
    function die() {}
    function self() {}
    function parent() {}
}
