<?php
// Fetch reply_to_email from email_settings
require_once '../config.php';

// Database connection
$conn = $pdo;

// Get reply_to_email from settings
$reply_to_email = '' . CHURCH_EMAIL . ''; // Default value

try {
    $stmt = $conn->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'reply_to_email' LIMIT 1");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result && !empty($result['setting_value'])) {
        $reply_to_email = $result['setting_value'];
    } else {
        // Try with email_ prefix from settings table as fallback
        $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'email_reply_to_email' LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result && !empty($result['setting_value'])) {
            $reply_to_email = $result['setting_value'];
        }
    }
    
    echo "Using reply-to email: " . htmlspecialchars($reply_to_email) . "<br>";
} catch (PDOException $e) {
    echo "Error fetching reply_to_email: " . $e->getMessage() . "<br>";
    error_log("Error fetching reply_to_email: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Big Raffle - You're a Winner!</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: Arial, Helvetica, sans-serif;
            background-color: #f4f4f4;
        }
        
        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .flyer {
            background: linear-gradient(135deg, #8A2BE2, #5a1ca5);
            border-radius: 10px 10px 0 0;
            text-align: center;
            padding: 30px;
        }
        
        .winner-banner {
            background-color: #FFD700;
            color: #8A2BE2;
            font-size: 22px;
            font-weight: bold;
            padding: 10px 20px;
            border-radius: 8px;
            display: inline-block;
        }
        
        .title {
            font-size: 40px;
            font-weight: bold;
            color: #fff;
            margin: 15px 0;
        }
        
        .lotto-ball {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            font-size: 22px;
            font-weight: bold;
            color: white;
            margin: 5px;
        }
        
        .ball-1 { background-color: #FF69B4; }
        .ball-2 { background-color: #FFD700; }
        .ball-3 { background-color: #00CED1; }
        .ball-4 { background-color: #32CD32; }
        .ball-5 { background-color: #FF6347; }
        
        .powerball {
            background-color: #FF0000;
            width: 70px;
            height: 70px;
            font-size: 24px;
        }
        
        .claim-section {
            text-align: center;
            padding: 20px;
        }
        
        .claim-button {
            display: inline-block;
            padding: 15px 30px;
            background-color: #FFD700;
            color: #8A2BE2;
            font-weight: bold;
            text-decoration: none;
            border-radius: 6px;
            font-size: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .instructions {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 20px;
            margin: 20px;
        }
        
        .footer {
            background-color: #8A2BE2;
            color: white;
            padding: 15px;
            font-size: 12px;
            text-align: center;
            border-radius: 0 0 10px 10px;
        }
    </style>
</head>
<body>
    <table class="container">
        <tr>
            <td class="flyer">
                <div class="winner-banner">CONGRATULATIONS!</div>
                <div class="title">THE BIG RAFFLE</div>
                <p style="color: white; font-size: 24px;">You're a WINNER!</p>
            </td>
        </tr>
        <tr>
            <td align="center" style="padding: 20px;">
                <div class="lotto-ball ball-1">12</div>
                <div class="lotto-ball ball-2">23</div>
                <div class="lotto-ball ball-3">34</div>
                <div class="lotto-ball ball-4">45</div>
                <div class="lotto-ball ball-5">56</div>
                <div class="lotto-ball powerball">7</div>
            </td>
        </tr>
        <tr>
            <td class="claim-section">
                <p style="font-size: 18px;">Congratulations! Your Powerball numbers have been drawn, and you're a winner! A life-changing prize awaits you.</p>
                <a href="mailto:<?php echo htmlspecialchars($reply_to_email); ?>?subject=I%20Won%20The%20Big%20Raffle&body=Hello,%0A%0AI%20received%20your%20email%20notification%20and%20I%20am%20confirming%20that%20I%20am%20the%20Powerball%20winner!%0A%0AMy%20contact%20details:%0AName:%20%0APhone:%20%0AAddress:%20%0A%0AI'm%20excited%20to%20claim%20my%20prize!%0A%0AThank%20you,%0A" class="claim-button">CLAIM YOUR PRIZE NOW</a>
            </td>
        </tr>
        <tr>
            <td class="instructions">
                <h3 style="color: #8A2BE2;">How to Claim Your Prize:</h3>
                <ol>
                    <li>Click the claim button above</li>
                    <li>Verify your identity</li>
                    <li>Choose your payment method</li>
                    <li>Receive your prize within 24 hours</li>
                </ol>
            </td>
        </tr>
        <tr>
            <td class="footer">
                <p>© 2025 The Big Raffle. All rights reserved.</p>
                <p>If you did not register for this raffle, please disregard this email.</p>
                <p><a href="{{unsubscribeLink}}" style="color: #FFD700; text-decoration: none;">Unsubscribe</a></p>
            </td>
        </tr>
    </table>
</body>
</html> 