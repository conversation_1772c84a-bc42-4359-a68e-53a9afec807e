USE churchdb;

-- Update the email template with improved styling for center alignment and photo display
UPDATE email_templates 
SET content = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Birthday Celebration</title>
    <style>
        body {
            margin: 0 !important;
            padding: 0 !important;
            font-family: Arial, Helvetica, sans-serif !important;
            background-color: #f9f9f9 !important;
            color: #333333 !important;
            line-height: 1.5 !important;
            width: 100% !important;
        }
        table {
            border-collapse: collapse !important;
        }
        .container {
            width: 100% !important;
            max-width: 600px !important;
            margin: 0 auto !important;
            padding: 20px !important;
            text-align: center !important;
        }
        .header {
            background: linear-gradient(135deg, #6a11cb, #2575fc) !important;
            padding: 30px !important;
            text-align: center !important;
            color: #ffffff !important;
            border-radius: 10px 10px 0 0 !important;
        }
        .content {
            background: #ffffff !important;
            padding: 30px !important;
            border-radius: 0 0 10px 10px !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
            text-align: center !important;
        }
        .greeting {
            font-size: 18px !important;
            margin-bottom: 25px !important;
            color: #333333 !important;
            text-align: center !important;
        }
        .member-photo {
            text-align: center !important;
            margin: 20px auto !important;
            width: 150px !important;
            height: 150px !important;
            border-radius: 75px !important;
            overflow: hidden !important;
        }
        .member-photo img {
            width: 150px !important;
            height: 150px !important;
            border-radius: 50% !important;
            object-fit: cover !important;
            border: 4px solid #6a11cb !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
        }
        .birthday-details {
            background: #f8f9fa !important;
            padding: 20px !important;
            border-radius: 8px !important;
            margin: 20px auto !important;
            text-align: center !important;
            max-width: 80% !important;
        }
        .birthday-date {
            font-size: 18px !important;
            color: #2575fc !important;
            margin-bottom: 10px !important;
            text-align: center !important;
        }
        .birthday-age {
            display: inline-block !important;
            background: #6a11cb !important;
            color: #ffffff !important;
            padding: 5px 15px !important;
            border-radius: 20px !important;
            font-size: 16px !important;
            text-align: center !important;
        }
        .suggestions {
            background: #f8f9fa !important;
            padding: 20px !important;
            border-radius: 8px !important;
            margin: 20px auto !important;
            text-align: center !important;
            max-width: 80% !important;
        }
        .suggestions h3 {
            color: #2575fc !important;
            margin-top: 0 !important;
            text-align: center !important;
        }
        .suggestions ul {
            list-style-type: none !important;
            padding: 0 !important;
            margin: 0 !important;
            text-align: center !important;
        }
        .suggestions li {
            padding: 8px 0 !important;
            border-bottom: 1px solid #e9ecef !important;
            text-align: center !important;
        }
        .suggestions li:last-child {
            border-bottom: none !important;
        }
        .button {
            display: inline-block !important;
            background: linear-gradient(135deg, #6a11cb, #2575fc) !important;
            color: #ffffff !important;
            text-decoration: none !important;
            padding: 12px 30px !important;
            border-radius: 25px !important;
            margin: 20px 0 !important;
            text-align: center !important;
        }
        .footer {
            text-align: center !important;
            margin-top: 30px !important;
            color: #666666 !important;
            font-size: 14px !important;
        }
        .church-name {
            color: #2575fc !important;
            font-weight: bold !important;
        }
        
        /* Make sure email clients don\'t mess with our styling */
        * {
            -webkit-text-size-adjust: 100% !important;
            -ms-text-size-adjust: 100% !important;
        }
        
        /* Responsive styling for mobile devices */
        @media only screen and (max-width: 620px) {
            .container {
                width: 100% !important;
                padding: 10px !important;
            }
            .content {
                padding: 15px !important;
            }
            .birthday-details, .suggestions {
                max-width: 95% !important;
            }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; background-color: #f9f9f9; text-align: center;">
    <div class="container" style="width: 100%; max-width: 600px; margin: 0 auto; padding: 20px; text-align: center;">
        <div class="header" style="background: linear-gradient(135deg, #6a11cb, #2575fc); padding: 30px; text-align: center; color: #ffffff; border-radius: 10px 10px 0 0;">
            <h1 style="text-align: center;">Birthday Celebration!</h1>
            <p style="text-align: center;">Join us in celebrating a special member of our church family</p>
        </div>
        
        <div class="content" style="background: #ffffff; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center;">
            <div class="greeting" style="font-size: 18px; margin-bottom: 25px; color: #333333; text-align: center;">
                <p style="text-align: center;">Dear {recipient_full_name},</p>
                <p style="text-align: center;">We are excited to let you know that {birthday_member_full_name} will be celebrating their birthday {days_text}!</p>
            </div>
            
            <div class="member-photo" style="text-align: center; margin: 20px auto; width: 150px; height: 150px; border-radius: 75px; overflow: hidden;">
                <img src="{birthday_member_photo_url}" alt="{birthday_member_first_name}\'s photo" onerror="this.src=\'https://freedomassemblydb.online/assets/img/default-avatar.png\'; this.onerror=null;" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #6a11cb; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            </div>
            
            <div class="birthday-details" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px auto; text-align: center; max-width: 80%;">
                <div class="birthday-date" style="font-size: 18px; color: #2575fc; margin-bottom: 10px; text-align: center;">Birthday: {upcoming_birthday_formatted}</div>
                <div class="birthday-age" style="display: inline-block; background: #6a11cb; color: #ffffff; padding: 5px 15px; border-radius: 20px; font-size: 16px; text-align: center;">Turning {birthday_member_age} years</div>
            </div>
            
            <div class="suggestions" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px auto; text-align: center; max-width: 80%;">
                <h3 style="color: #2575fc; margin-top: 0; text-align: center;">Ways to Bless {birthday_member_first_name}:</h3>
                <ul style="list-style-type: none; padding: 0; margin: 0; text-align: center;">
                    <li style="padding: 8px 0; border-bottom: 1px solid #e9ecef; text-align: center;">Send a heartfelt birthday message</li>
                    <li style="padding: 8px 0; border-bottom: 1px solid #e9ecef; text-align: center;">Pray for their growth and blessings in the coming year</li>
                    <li style="padding: 8px 0; border-bottom: 1px solid #e9ecef; text-align: center;">Share a scripture that encourages them</li>
                    <li style="padding: 8px 0; text-align: center;">Consider gifting something meaningful</li>
                </ul>
            </div>
            
            <div style="text-align: center;">
                <a href="mailto:{birthday_member_email}?subject=Happy%20Birthday%20{birthday_member_first_name}!&body=Dear%20{birthday_member_first_name},%0D%0A%0D%0AWishing%20you%20a%20blessed%20birthday!%0D%0A%0D%0ABlessings,%0D%0A{recipient_full_name}" class="button" style="display: inline-block; background: linear-gradient(135deg, #6a11cb, #2575fc); color: #ffffff; text-decoration: none; padding: 12px 30px; border-radius: 25px; margin: 20px 0; text-align: center;">
                    Send Birthday Wishes
                </a>
            </div>
            
            <div class="footer" style="text-align: center; margin-top: 30px; color: #666666; font-size: 14px;">
                <p style="text-align: center;">With blessings from <span class="church-name" style="color: #2575fc; font-weight: bold;">Freedom Assembly Church</span></p>
                <p style="text-align: center;"><small><a href="{unsubscribe_link}" style="color: #666666;">Unsubscribe from these updates</a></small></p>
            </div>
        </div>
    </div>
    {tracking_pixel}
</body>
</html>'
WHERE template_name = 'Member Birthday Notification'; 