<?php
// <PERSON><PERSON><PERSON> to check reply_to_email setting in the database
require_once __DIR__ . '/../config.php';

echo "===== Checking Reply-To Email Setting =====\n\n";

// Database connection
$conn = $pdo;

try {
    // Check email_settings table for reply_to_email
    $stmt = $conn->prepare("SELECT setting_key, setting_value FROM email_settings WHERE setting_key = 'reply_to_email'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "Reply-To Email found in email_settings table: " . $result['setting_value'] . "\n";
    } else {
        echo "Reply-To Email NOT found in email_settings table.\n";
    }
    
    // Check settings table for email_reply_to_email
    $stmt = $conn->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key = 'email_reply_to_email'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "Reply-To Email found in settings table: " . $result['setting_value'] . "\n";
    } else {
        echo "Reply-To Email NOT found in settings table.\n";
    }
    
    // Check if email_templates table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'email_templates'");
    if ($check_table->rowCount() > 0) {
        echo "\nThe email_templates table exists.\n";
        
        // Check for raffle template
        $stmt = $conn->prepare("SELECT id, template_name, subject FROM email_templates WHERE template_name = 'The Big Raffle Winner'");
        $stmt->execute();
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($template) {
            echo "Raffle template found: ID=" . $template['id'] . ", Name='" . $template['template_name'] . "', Subject='" . $template['subject'] . "'\n";
        } else {
            echo "Raffle template NOT found.\n";
        }
    } else {
        echo "\nThe email_templates table does NOT exist.\n";
    }
    
} catch (PDOException $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
} 