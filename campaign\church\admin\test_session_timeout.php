<?php
// Include configuration
require_once '../config.php';

// Include session manager
require_once 'includes/session-manager.php';

// Set page title
$page_title = 'Test Session Timeout';
$page_header = 'Session Timeout Testing';
$page_description = 'This page tests the session timeout functionality.';

// Include header
include 'includes/header.php';
?>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Session Timeout Status</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>Current Session Information</h6>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Session Timeout
                            <span class="badge bg-primary rounded-pill"><?php echo SESSION_TIMEOUT; ?> seconds</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Session Created
                            <span class="badge bg-info rounded-pill"><?php echo date('Y-m-d H:i:s', $_SESSION['CREATED']); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Last Activity
                            <span class="badge bg-info rounded-pill"><?php echo date('Y-m-d H:i:s', $_SESSION['LAST_ACTIVITY']); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Time Since Last Activity
                            <span class="badge bg-warning rounded-pill"><?php echo time() - $_SESSION['LAST_ACTIVITY']; ?> seconds</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Remaining Time
                            <span class="badge bg-success rounded-pill" id="remaining-time"><?php echo getRemainingSessionTime(); ?> seconds</span>
                        </li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <h6>Testing Controls</h6>
                    <div class="d-grid gap-2">
                        <button id="test-inactive" class="btn btn-warning">
                            Simulate 10 Seconds of Inactivity
                        </button>
                        <button id="test-update" class="btn btn-primary">
                            Manually Update Last Activity
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">How Session Timeout Works</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li>Session timeout is set to <strong><?php echo SESSION_TIMEOUT; ?> seconds</strong> of inactivity</li>
                    <li>A warning message appears <strong>15 seconds</strong> before the session expires</li>
                    <li>If no activity is detected, you will be automatically logged out</li>
                    <li>User activity includes: mouse movements, clicks, keystrokes, and scrolling</li>
                    <li>The system also sends heartbeat requests every 30 seconds to keep the session alive</li>
                </ol>
                
                <div class="alert alert-primary" role="alert">
                    <strong>Note:</strong> For this test page, you'll see the session timeout warning after 
                    <?php echo SESSION_TIMEOUT - 15; ?> seconds of inactivity.
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Add extra JavaScript for the test page
$extra_js = <<<EOT
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const remainingTime = document.getElementById('remaining-time');
        const inactiveBtn = document.getElementById('test-inactive');
        const updateBtn = document.getElementById('test-update');
        
        // Update remaining time every second
        setInterval(function() {
            fetch(ADMIN_URL + '/ajax/update_activity.php', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    remainingTime.textContent = data.remainingTime + ' seconds';
                }
            })
            .catch(error => console.error('Error fetching timeout info:', error));
        }, 1000);
        
        // Simulate inactivity by temporarily removing event listeners
        inactiveBtn.addEventListener('click', function() {
            this.disabled = true;
            this.textContent = 'Simulating Inactivity...';
            
            // Store original event listeners
            const originalEvents = window.sessionEventHandlers || {};
            
            // Find all event handlers attached by the session timeout script and disable them
            const events = ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart', 'click', 'keypress'];
            events.forEach(event => {
                // This is a simple simulation and not a complete solution
                document.body.style.opacity = '0.7';
                document.body.style.pointerEvents = 'none';
            });
            
            // Re-enable after 10 seconds
            setTimeout(() => {
                document.body.style.opacity = '1';
                document.body.style.pointerEvents = 'auto';
                this.disabled = false;
                this.textContent = 'Simulate 10 Seconds of Inactivity';
            }, 10000);
        });
        
        // Manual update button
        updateBtn.addEventListener('click', function() {
            fetch(ADMIN_URL + '/ajax/update_activity.php', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    remainingTime.textContent = data.remainingTime + ' seconds';
                    
                    // Show a temporary success message
                    const msg = document.createElement('div');
                    msg.className = 'alert alert-success mt-3';
                    msg.textContent = 'Last activity time updated successfully!';
                    updateBtn.parentNode.appendChild(msg);
                    
                    // Remove the message after 3 seconds
                    setTimeout(() => msg.remove(), 3000);
                }
            })
            .catch(error => console.error('Error updating activity:', error));
        });
    });
</script>
EOT;

// Include footer
include 'includes/footer.php';
?> 