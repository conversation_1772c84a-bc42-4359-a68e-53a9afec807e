# Enable PHP execution
AddType application/x-httpd-php .php

# Enable rewrite engine
<IfModule mod_rewrite.c>
    RewriteEngine On
    # Dynamic RewriteBase - will work in any subdirectory structure
    # RewriteBase /church/admin/
    
    # Allow direct access to actual files
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule ^ - [L]
    
    # Redirect to login if not logged in
    RewriteCond %{REQUEST_URI} !login\.php
    RewriteCond %{REQUEST_URI} !db_test\.php
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^ login.php [L]
</IfModule>

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8 