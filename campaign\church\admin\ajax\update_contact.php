<?php
session_start();
require_once '../../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

// Get JSON data
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['id']) || !isset($data['email']) || empty(trim($data['email']))) {
    echo json_encode(['success' => false, 'error' => 'Contact ID and email are required']);
    exit();
}

try {
    $pdo->beginTransaction();
    
    // Update contact details
    $stmt = $pdo->prepare("UPDATE contacts SET name = ?, email = ? WHERE id = ?");
    $stmt->execute([
        trim($data['name']),
        trim($data['email']),
        $data['id']
    ]);
    
    // Update group memberships
    $stmt = $pdo->prepare("DELETE FROM contact_group_members WHERE contact_id = ?");
    $stmt->execute([$data['id']]);
    
    if (!empty($data['groups'])) {
        $stmt = $pdo->prepare("INSERT INTO contact_group_members (contact_id, group_id) VALUES (?, ?)");
        foreach ($data['groups'] as $groupId) {
            $stmt->execute([$data['id'], $groupId]);
        }
    }
    
    $pdo->commit();
    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    if ($e->getCode() == 23000) { // Duplicate entry
        echo json_encode(['success' => false, 'error' => 'A contact with this email already exists']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
}
?> 