<?php
session_start();
require_once "../config.php";

// Check if admin is logged in
if (!isset($_SESSION["admin_id"])) {
    header("Location: login.php");
    exit();
}

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST["action"])) {
    try {
        if ($_POST["action"] === "update_setting") {
            $stmt = $pdo->prepare("
                UPDATE appearance_settings 
                SET setting_value = ?, updated_at = NOW() 
                WHERE setting_name = ?
            ");
            $stmt->execute([$_POST["setting_value"], $_POST["setting_name"]]);
            $success = "Setting updated successfully!";
            
            // Regenerate CSS if appearance settings changed
            generateCustomCSS();
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Function to generate custom CSS
function generateCustomCSS() {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT setting_name, setting_value FROM appearance_settings WHERE setting_type = 'color'");
        $stmt->execute();
        $colorSettings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        $css = ":root {\n";
        foreach ($colorSettings as $name => $value) {
            $cssVar = '--' . str_replace('_', '-', $name);
            $css .= "  $cssVar: $value;\n";
        }
        $css .= "}\n";
        
        // Save CSS to file
        $cssFile = __DIR__ . '/css/custom-theme.css';
        file_put_contents($cssFile, $css);
        
    } catch (Exception $e) {
        error_log("Error generating CSS: " . $e->getMessage());
    }
}

// Get appearance settings by category
try {
    $categories = ['branding', 'theme', 'layout', 'customization'];
    $settingsByCategory = [];
    
    foreach ($categories as $category) {
        $stmt = $pdo->prepare("SELECT * FROM appearance_settings WHERE category = ? ORDER BY setting_name");
        $stmt->execute([$category]);
        $settingsByCategory[$category] = $stmt->fetchAll();
    }
} catch (Exception $e) {
    $error = "Error loading settings: " . $e->getMessage();
    $settingsByCategory = [];
}

$pageTitle = "Appearance Settings";
include "includes/header.php";
?>

<div class="container-fluid">
    <div class="row">
        <?php include "includes/sidebar.php"; ?>
        
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="bi bi-palette"></i> Appearance Settings</h1>
            </div>

            <?php if (isset($success)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs" id="appearanceTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="branding-tab" data-bs-toggle="tab" data-bs-target="#branding" type="button" role="tab">
                        <i class="bi bi-brush"></i> Branding
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="theme-tab" data-bs-toggle="tab" data-bs-target="#theme" type="button" role="tab">
                        <i class="bi bi-palette"></i> Theme
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="layout-tab" data-bs-toggle="tab" data-bs-target="#layout" type="button" role="tab">
                        <i class="bi bi-layout-sidebar"></i> Layout
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="custom-tab" data-bs-toggle="tab" data-bs-target="#custom" type="button" role="tab">
                        <i class="bi bi-code-slash"></i> Custom
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="appearanceTabContent">
                <?php foreach ($settingsByCategory as $category => $settings): ?>
                    <div class="tab-pane fade <?php echo $category === 'branding' ? 'show active' : ''; ?>" 
                         id="<?php echo $category; ?>" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5><i class="bi bi-<?php echo $category === 'branding' ? 'brush' : ($category === 'theme' ? 'palette' : ($category === 'layout' ? 'layout-sidebar' : 'code-slash')); ?>"></i> 
                                    <?php echo ucfirst($category); ?> Settings</h5>
                            </div>
                            <div class="card-body">
                                <?php if (count($settings) > 0): ?>
                                    <?php foreach ($settings as $setting): ?>
                                        <form method="post" class="mb-4">
                                            <input type="hidden" name="action" value="update_setting">
                                            <input type="hidden" name="setting_name" value="<?php echo htmlspecialchars($setting["setting_name"]); ?>">
                                            
                                            <div class="row align-items-end">
                                                <div class="col-md-3">
                                                    <label class="form-label">
                                                        <strong><?php echo ucwords(str_replace("_", " ", $setting["setting_name"])); ?></strong>
                                                    </label>
                                                </div>
                                                <div class="col-md-6">
                                                    <?php if ($setting["setting_type"] === "color"): ?>
                                                        <input type="color" name="setting_value" class="form-control form-control-color" 
                                                               value="<?php echo htmlspecialchars($setting["setting_value"]); ?>">
                                                    <?php elseif ($setting["setting_type"] === "textarea"): ?>
                                                        <textarea name="setting_value" class="form-control" rows="3"
                                                                  placeholder="Enter <?php echo strtolower(str_replace("_", " ", $setting["setting_name"])); ?>"><?php echo htmlspecialchars($setting["setting_value"]); ?></textarea>
                                                    <?php else: ?>
                                                        <input type="text" name="setting_value" class="form-control" 
                                                               value="<?php echo htmlspecialchars($setting["setting_value"]); ?>"
                                                               placeholder="Enter <?php echo strtolower(str_replace("_", " ", $setting["setting_name"])); ?>">
                                                    <?php endif; ?>
                                                </div>
                                                <div class="col-md-3">
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="bi bi-check-lg"></i> Update
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <?php if (!empty($setting["description"])): ?>
                                                <div class="row mt-1">
                                                    <div class="col-md-9 offset-md-3">
                                                        <small class="text-muted"><?php echo htmlspecialchars($setting["description"]); ?></small>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </form>
                                        <hr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="bi bi-palette display-1 text-muted"></i>
                                        <h4 class="text-muted">No <?php echo $category; ?> settings found</h4>
                                        <p class="text-muted">Settings need to be initialized. Please contact your system administrator.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<?php include "includes/footer.php"; ?>
