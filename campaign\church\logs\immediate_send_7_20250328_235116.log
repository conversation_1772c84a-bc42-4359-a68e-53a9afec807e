[2025-03-28 23:51:19] Error sending to: <EMAIL> - Failed to send email: SMTP Error: data not accepted.
[2025-03-28 23:51:23] Error sending to: <EMAIL> - Failed to send email: SMTP Error: data not accepted.
[2025-03-28 23:51:23] Exception sending to: <PERSON><EMAIL> - SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`churchtest`.`email_tracking`, CONSTRAINT `email_tracking_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE)
[2025-03-28 23:51:23] Exception sending to: <EMAIL> - SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`churchtest`.`email_tracking`, CONSTRAINT `email_tracking_ibfk_1` <PERSON>OR<PERSON><PERSON><PERSON> KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE)
[2025-03-28 23:51:23] Exception sending to: <EMAIL> - SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`churchtest`.`email_tracking`, CONSTRAINT `email_tracking_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE)
[2025-03-28 23:51:23] Exception sending to: <EMAIL> - SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`churchtest`.`email_tracking`, CONSTRAINT `email_tracking_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE)
[2025-03-28 23:51:23] Exception sending to: <EMAIL> - SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`churchtest`.`email_tracking`, CONSTRAINT `email_tracking_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE)
[2025-03-28 23:51:23] Exception sending to: <EMAIL> - SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`churchtest`.`email_tracking`, CONSTRAINT `email_tracking_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE)
[2025-03-28 23:51:23] Exception sending to: <EMAIL> - SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`churchtest`.`email_tracking`, CONSTRAINT `email_tracking_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE)
[2025-03-28 23:51:24] Exception sending to: <EMAIL> - SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`churchtest`.`email_tracking`, CONSTRAINT `email_tracking_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE)
[2025-03-28 23:51:24] Immediate send completed. Success: 0, Errors: 10
