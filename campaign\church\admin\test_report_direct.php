<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Handle report generation BEFORE any output
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'generate_report') {
        $report_type = $_POST['report_type'];
        $date_from = $_POST['date_from'];
        $date_to = $_POST['date_to'];
        $event_id = !empty($_POST['event_id']) ? (int)$_POST['event_id'] : null;

        // Generate and download the report
        generateEventReport($report_type, $date_from, $date_to, $event_id);
        exit(); // Important: exit after generating report
    }
}

function generateEventReport($type, $date_from, $date_to, $event_id = null) {
    global $conn;
    
    // Build query based on report type
    $where_conditions = [];
    $params = [];
    
    if ($date_from) {
        $where_conditions[] = "e.event_date >= ?";
        $params[] = $date_from;
    }
    
    if ($date_to) {
        $where_conditions[] = "e.event_date <= ?";
        $params[] = $date_to . ' 23:59:59';
    }
    
    if ($event_id) {
        $where_conditions[] = "e.id = ?";
        $params[] = $event_id;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    if ($type === 'attendance') {
        // Attendance report
        $sql = "
            SELECT e.title, e.event_date, e.location, e.max_attendees,
                   COUNT(er.id) as total_rsvps,
                   SUM(CASE WHEN er.status = 'attending' THEN 1 ELSE 0 END) as attending_count,
                   SUM(CASE WHEN er.status = 'not_attending' THEN 1 ELSE 0 END) as not_attending_count,
                   SUM(CASE WHEN er.status = 'maybe' THEN 1 ELSE 0 END) as maybe_count
            FROM events e
            LEFT JOIN event_rsvps er ON e.id = er.event_id
            $where_clause
            GROUP BY e.id
            ORDER BY e.event_date DESC
        ";
    } else {
        // Event summary report
        $sql = "
            SELECT e.title, e.event_date, e.location, e.max_attendees, e.is_active,
                   COUNT(er.id) as total_rsvps
            FROM events e
            LEFT JOIN event_rsvps er ON e.id = er.event_id
            $where_clause
            GROUP BY e.id
            ORDER BY e.event_date DESC
        ";
    }
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Generate PDF
    generatePDFReport($data, $type, $date_from, $date_to);
}

function generatePDFReport($data, $type, $date_from, $date_to) {
    // Clean output buffer to prevent header issues
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    $title = ucfirst($type) . " Report";
    $date_range = "";
    if ($date_from || $date_to) {
        $date_range = " (" . ($date_from ?: 'Start') . " to " . ($date_to ?: 'End') . ")";
    }
    
    header('Content-Type: text/html; charset=utf-8');
    header('Cache-Control: no-cache, must-revalidate');
    
    echo '<!DOCTYPE html>
    <html>
    <head>
        <title>' . htmlspecialchars($title) . '</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .controls { margin-bottom: 20px; text-align: center; }
            .controls button { padding: 10px 20px; margin: 0 10px; }
            @media print { .no-print { display: none; } }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>' . htmlspecialchars(get_organization_name()) . '</h1>
            <h2>' . htmlspecialchars($title . $date_range) . '</h2>
            <p>Generated on: ' . date('Y-m-d H:i:s') . '</p>
        </div>
        
        <div class="controls no-print">
            <button onclick="window.print()">Print/Save as PDF</button>
            <button onclick="closeWindow()">Close</button>
        </div>
        
        <table>
            <thead>
                <tr>';
    
    if ($type === 'attendance') {
        echo '<th>Event</th><th>Date</th><th>Location</th><th>Capacity</th><th>Total RSVPs</th><th>Attending</th><th>Not Attending</th><th>Maybe</th>';
    } else {
        echo '<th>Event</th><th>Date</th><th>Location</th><th>Capacity</th><th>Status</th><th>Total RSVPs</th>';
    }
    
    echo '</tr></thead><tbody>';
    
    if (empty($data)) {
        $colspan = ($type === 'attendance') ? 8 : 6;
        echo '<tr><td colspan="' . $colspan . '" style="text-align: center; padding: 40px;">No events found for the selected criteria.</td></tr>';
    } else {
        foreach ($data as $row) {
            echo '<tr>';
            echo '<td>' . htmlspecialchars($row['title']) . '</td>';
            echo '<td>' . date('M j, Y g:i A', strtotime($row['event_date'])) . '</td>';
            echo '<td>' . htmlspecialchars($row['location'] ?? 'TBD') . '</td>';
            echo '<td>' . ($row['max_attendees'] ?? 'Unlimited') . '</td>';
            
            if ($type === 'attendance') {
                echo '<td>' . ($row['total_rsvps'] ?? 0) . '</td>';
                echo '<td>' . ($row['attending_count'] ?? 0) . '</td>';
                echo '<td>' . ($row['not_attending_count'] ?? 0) . '</td>';
                echo '<td>' . ($row['maybe_count'] ?? 0) . '</td>';
            } else {
                echo '<td>' . (($row['is_active'] ?? 1) ? 'Active' : 'Inactive') . '</td>';
                echo '<td>' . ($row['total_rsvps'] ?? 0) . '</td>';
            }
            
            echo '</tr>';
        }
    }
    
    echo '</tbody></table>
        
        <script>
            function closeWindow() {
                if (window.opener) {
                    window.close();
                } else {
                    window.location.href = "event_reports.php";
                }
            }
        </script>
    </body>
    </html>';
    
    exit();
}

// Get events for dropdown
$events_stmt = $conn->query("SELECT id, title, event_date FROM events ORDER BY event_date DESC");
$events = $events_stmt->fetchAll(PDO::FETCH_ASSOC);

// Set page variables
$page_title = "Event Reports Test";
$page_header = "Event Reports Test";
$page_description = "Test event report generation.";

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Test Event Report Generation</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="" target="_blank">
                        <input type="hidden" name="action" value="generate_report">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="report_type" class="form-label">Report Type</label>
                                    <select class="form-select" id="report_type" name="report_type" required>
                                        <option value="">Select Report Type</option>
                                        <option value="attendance">Attendance Report</option>
                                        <option value="summary">Event Summary Report</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="event_id" class="form-label">Specific Event (Optional)</label>
                                    <select class="form-select" id="event_id" name="event_id">
                                        <option value="">All Events</option>
                                        <?php foreach ($events as $event): ?>
                                            <option value="<?= $event['id'] ?>">
                                                <?= htmlspecialchars($event['title']) ?> - <?= date('M j, Y', strtotime($event['event_date'])) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_from" class="form-label">From Date</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?= date('Y-m-01') ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_to" class="form-label">To Date</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?= date('Y-m-t') ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-file-earmark-pdf"></i> Generate Test Report
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
