<?php
session_start();

/**
 * Format and validate birth date
 * @param string|null $birthDate The birth date to format
 * @return string Formatted date or 'N/A'
 */
function formatBirthDate($birthDate) {
    if (empty($birthDate)) {
        return 'N/A';
    }
    
    try {
        $date = new DateTime($birthDate);
        return $date->format('M d, Y');
    } catch (Exception $e) {
        error_log("Error formatting birth date: " . $e->getMessage());
        return 'N/A';
    }
}

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Generate or retrieve CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Delete member
if (isset($_GET['delete_id']) && !empty($_GET['delete_id'])) {
    if (!isset($_GET['csrf_token']) || $_GET['csrf_token'] !== $_SESSION['csrf_token']) {
        $error_message = "Invalid security token. Please try again.";
    } else {
        $delete_id = intval($_GET['delete_id']);
        
        try {
            $conn->beginTransaction();
            
            // Delete associated records from email_logs first (to maintain foreign key constraints)
            $stmt = $conn->prepare("DELETE FROM email_logs WHERE member_id = ?");
            $stmt->execute([$delete_id]);
            
            // Delete from email_tracking if it exists
            $stmt = $conn->prepare("DELETE FROM email_tracking WHERE member_id = ?");
            $stmt->execute([$delete_id]);
            
            // Now delete the member
            $stmt = $conn->prepare("DELETE FROM members WHERE id = ?");
            if ($stmt->execute([$delete_id])) {
                $conn->commit();
                $success_message = "Member deleted successfully!";
            } else {
                $conn->rollBack();
                $error_message = "Error deleting member: " . implode(", ", $stmt->errorInfo());
            }
        } catch (Exception $e) {
            $conn->rollBack();
            $error_message = "Error: " . $e->getMessage();
        }
    }
}

// Set search parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Pagination setup
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$records_per_page = 10;
$offset = ($page - 1) * $records_per_page;

// Count total records for pagination
$count_query = "SELECT COUNT(*) as total FROM members";
$count_params = [];
if (!empty($search)) {
    $count_query .= " WHERE full_name LIKE ? OR email LIKE ? OR phone_number LIKE ?";
    $search_param = "%$search%";
    $count_params = [$search_param, $search_param, $search_param];
}

$stmt = $conn->prepare($count_query);
$stmt->execute($count_params);
$count_result = $stmt->fetch();
$total_records = $count_result['total'];
$total_pages = ceil($total_records / $records_per_page);

// Get members with pagination and search
$query = "SELECT 
    id, 
    full_name, 
    email, 
    phone_number, 
    CASE 
        WHEN birth_date IS NOT NULL THEN birth_date
        WHEN date_of_birth IS NOT NULL THEN date_of_birth
        ELSE NULL 
    END as birth_date,
    created_at, 
    image_path 
FROM members";

$params = [];
if (!empty($search)) {
    $query .= " WHERE full_name LIKE ? OR email LIKE ? OR phone_number LIKE ?";
    $search_param = "%$search%";
    $params = [$search_param, $search_param, $search_param];
}
$query .= " ORDER BY created_at DESC LIMIT ?, ?";

try {
    $stmt = $conn->prepare($query);

    // Bind parameters correctly
    if (!empty($params)) {
        // First bind the search parameters
        foreach ($params as $index => $value) {
            $stmt->bindValue($index + 1, $value, PDO::PARAM_STR);
        }
        // Then bind the pagination parameters
        $stmt->bindValue(count($params) + 1, $offset, PDO::PARAM_INT);
        $stmt->bindValue(count($params) + 2, $records_per_page, PDO::PARAM_INT);
    } else {
        // If no search parameters, just bind the pagination parameters
        $stmt->bindValue(1, $offset, PDO::PARAM_INT);
        $stmt->bindValue(2, $records_per_page, PDO::PARAM_INT);
    }

    $stmt->execute();
    $members = $stmt->fetchAll();
} catch (PDOException $e) {
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    $error = "Error: " . $e->getMessage();
    $members = [];
}

// Close the database connection
$conn = null;

// Set page variables
$page_title = 'Members';
$page_header = 'Church Members';
$page_description = 'Manage church members and their information.';

// Include header
include 'includes/header.php';

// Display flash message if it exists
if (isset($_SESSION['message']) && !empty($_SESSION['message'])) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($_SESSION['message']) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
    // Clear the message after displaying it
    unset($_SESSION['message']);
}

// Display error message if it exists
if (!empty($error_message)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($error_message) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}
?>

<!-- Instructions panel -->
<div class="alert alert-info mb-4 instruction-panel">
    <div class="d-flex justify-content-between align-items-start">
        <h5><i class="bi bi-info-circle-fill me-2"></i>Managing Church Members</h5>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <p class="mb-2">This page allows you to manage your church members database.</p>
    <ul class="mb-0">
        <li><strong>Add Members:</strong> Add new members to your church database</li>
        <li><strong>View Details:</strong> Click on a member's name to view their complete profile</li>
        <li><strong>Edit Information:</strong> Update member details as needed</li>
        <li><strong>Birthday Communications:</strong> Send birthday messages from the member details page</li>
    </ul>
    <div class="mt-2">
        <strong>Tip:</strong> Use the search box to quickly find specific members by name, email, or phone number.
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <!-- Search form -->
        <form action="" method="GET" class="d-flex">
            <input type="text" name="search" class="form-control me-2" placeholder="Search members..." value="<?php echo htmlspecialchars($search); ?>" data-bs-toggle="tooltip" data-bs-placement="top" title="Search by name, email, or phone number">
            <button type="submit" class="btn btn-outline-primary">Search</button>
            <?php if (!empty($search)): ?>
                <a href="members.php" class="btn btn-outline-secondary ms-2">Clear</a>
            <?php endif; ?>
        </form>
    </div>
    <div class="col-md-6 text-end">
        <a href="add_member.php" class="btn btn-primary" data-bs-toggle="tooltip" data-bs-placement="left" title="Add a new member to the church database">
            <i class="bi bi-person-plus"></i> Add New Member
        </a>
    </div>
</div>

<!-- Members Table -->
<div class="card">
    <div class="card-body">
        <?php if (count($members) > 0): ?>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Photo</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Birth Date</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($members as $member): ?>
                    <tr>
                        <td><?php echo $member['id']; ?></td>
                        <td>
                            <?php if (!empty($member['image_path'])): ?>
                                <img src="../<?php echo htmlspecialchars($member['image_path']); ?>" 
                                     alt="<?php echo htmlspecialchars($member['full_name']); ?>" 
                                     class="rounded-circle member-photo"
                                     style="width: 40px; height: 40px; object-fit: cover;">
                            <?php else: ?>
                                <img src="../assets/img/default-profile.jpg" 
                                     alt="Default Profile" 
                                     class="rounded-circle member-photo"
                                     style="width: 40px; height: 40px; object-fit: cover;">
                            <?php endif; ?>
                        </td>
                        <td><?php echo htmlspecialchars($member['full_name']); ?></td>
                        <td><?php echo htmlspecialchars($member['email']); ?></td>
                        <td><?php echo htmlspecialchars($member['phone_number'] ?? 'N/A'); ?></td>
                        <td>
                            <?php 
                            echo formatBirthDate($member['birth_date']);
                            ?>
                        </td>
                        <td><?php echo date('M d, Y', strtotime($member['created_at'])); ?></td>
                        <td>
                            <a href="view_member.php?id=<?php echo $member['id']; ?>" class="btn btn-sm btn-info">
                                <i class="bi bi-eye"></i>
                            </a>
                            <a href="edit_member.php?id=<?php echo $member['id']; ?>" class="btn btn-sm btn-primary">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <a href="#" onclick="confirmDelete(<?php echo $member['id']; ?>, '<?php echo addslashes($member['full_name']); ?>')" class="btn btn-sm btn-danger">
                                <i class="bi bi-trash"></i>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center mt-4">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo admin_url_for('members.php?page=' . ($page - 1) . '&search=' . urlencode($search)); ?>">Previous</a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                    <a class="page-link" href="<?php echo admin_url_for('members.php?page=' . $i . '&search=' . urlencode($search)); ?>"><?php echo $i; ?></a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo admin_url_for('members.php?page=' . ($page + 1) . '&search=' . urlencode($search)); ?>">Next</a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
        
        <?php else: ?>
        <p class="text-center">No members found.</p>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete <strong><span id="memberNameToDelete"></span></strong>? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="confirmDeleteButton" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script>
    function confirmDelete(memberId, memberName) {
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        document.getElementById('memberNameToDelete').textContent = memberName;
        document.getElementById('confirmDeleteButton').href = `members.php?delete_id=${memberId}&csrf_token=<?php echo $_SESSION['csrf_token']; ?>`;
        modal.show();
    }

    // Auto-search functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('search');
        const searchForm = document.getElementById('searchForm');
        const searchSpinner = document.getElementById('searchSpinner');
        let searchTimeout = null;

        searchInput.addEventListener('input', function() {
            // Show the spinner
            searchSpinner.classList.remove('d-none');
            
            // Clear any existing timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            
            // Set a new timeout to delay the search
            searchTimeout = setTimeout(function() {
                // Submit the form after the user stops typing for 500ms
                searchForm.submit();
            }, 500);
        });

        // Add click handler for member photos to show larger version
        document.querySelectorAll('.member-photo').forEach(photo => {
            photo.addEventListener('click', function() {
                const modal = new bootstrap.Modal(document.createElement('div'));
                modal.element.innerHTML = `
                    <div class="modal fade" tabindex="-1">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-body text-center p-0">
                                    <img src="${this.src}" class="img-fluid" style="max-height: 80vh;">
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal.element);
                modal.show();
                modal.element.addEventListener('hidden.bs.modal', () => {
                    modal.element.remove();
                });
            });
        });
    });
</script> 