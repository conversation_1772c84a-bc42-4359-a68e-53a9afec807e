<?php
/**
 * Test Cron Jobs
 * 
 * This script allows administrators to manually test cron jobs
 * to verify they are working correctly.
 */

// Initialize the session
session_start();

// Include config file
require_once "../config.php";

// Check if the user is logged in, if not then redirect to login page
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || $_SESSION["user_type"] !== "admin") {
    header("location: login.php");
    exit;
}

// Get admin information
$admin_id = $_SESSION["admin_id"];
$admin_name = $_SESSION["admin_name"];

// Check if a cron script was specified
if (!isset($_POST['cron_script'])) {
    $_SESSION['error_message'] = "No cron script specified";
    header("location: cron_jobs.php");
    exit;
}

$cron_script = $_POST['cron_script'];
$allowed_scripts = [
    'process_scheduled_emails',
    'process_birthday_reminders'
];

// Validate the cron script
if (!in_array($cron_script, $allowed_scripts)) {
    $_SESSION['error_message'] = "Invalid cron script specified";
    header("location: cron_jobs.php");
    exit;
}

// Set up the cron key (same as in the cron scripts)
$cron_key = 'fac_2024_secure_cron_8x9q2p5m';

// Execute the cron script
$script_path = realpath("../cron/{$cron_script}.php");
$log_output = '';
$success = false;
$error_message = '';

if (file_exists($script_path)) {
    try {
        // Create a log file for this test run
        $log_dir = __DIR__ . '/../logs';
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0755, true);
        }
        
        $log_file = $log_dir . '/test_' . $cron_script . '_' . date('Ymd_His') . '.log';
        
        // Execute the script with the cron key
        // We'll use include with output buffering to capture the output
        ob_start();
        
        // Set up the environment for the script
        $_GET['cron_key'] = $cron_key;
        $_GET['test_mode'] = 'true';
        
        // Include the script
        include($script_path);
        
        // Get the output
        $output = ob_get_clean();
        
        // Log the output
        file_put_contents($log_file, $output);
        
        // Set success message
        $success = true;
        $log_output = $output;
        
        // Log the test in the database
        $stmt = $pdo->prepare("
            INSERT INTO admin_activity_logs 
            (admin_id, activity_type, description, ip_address) 
            VALUES (?, 'cron_test', ?, ?)
        ");
        $stmt->execute([
            $admin_id,
            "Tested cron script: {$cron_script}",
            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
        
    } catch (Exception $e) {
        $error_message = "Error executing cron script: " . $e->getMessage();
        file_put_contents($log_dir . '/test_errors.log', 
            '[' . date('Y-m-d H:i:s') . '] Test error for ' . $cron_script . ': ' . $e->getMessage() . PHP_EOL, 
            FILE_APPEND
        );
    }
} else {
    $error_message = "Cron script file not found: {$cron_script}.php";
}

// Page title
$page_title = "Cron Job Test Results";

// Include header
include_once "admin_header.php";
?>

<div class="container-fluid px-4">
    <h1 class="mt-4"><?php echo $page_title; ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="cron_jobs.php">Cron Jobs Management</a></li>
        <li class="breadcrumb-item active"><?php echo $page_title; ?></li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-clock me-1"></i>
            Test Results for <?php echo htmlspecialchars(ucwords(str_replace('_', ' ', $cron_script))); ?>
        </div>
        <div class="card-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle"></i> Test Completed Successfully</h5>
                    <p>The cron script was executed successfully. See the output below for details.</p>
                </div>
            <?php else: ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-circle"></i> Test Failed</h5>
                    <p><?php echo htmlspecialchars($error_message); ?></p>
                </div>
            <?php endif; ?>
            
            <h5 class="mt-4">Script Output</h5>
            <div class="card bg-light mb-3">
                <div class="card-body">
                    <pre class="mb-0" style="max-height: 400px; overflow-y: auto;"><?php echo htmlspecialchars($log_output); ?></pre>
                </div>
            </div>
            
            <div class="mt-4">
                <a href="cron_jobs.php" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i> Back to Cron Jobs
                </a>
                
                <form method="post" action="test_cron.php" class="d-inline ms-2">
                    <input type="hidden" name="cron_script" value="<?php echo htmlspecialchars($cron_script); ?>">
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-redo"></i> Run Test Again
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-info-circle me-1"></i>
            Next Steps
        </div>
        <div class="card-body">
            <p>Now that you've tested the cron job, here are the next steps:</p>
            
            <ol>
                <li>If the test was successful, set up the actual cron job on your server as described in the <a href="cron_jobs.php">Cron Jobs Management</a> page.</li>
                <li>Monitor the logs regularly to ensure the cron job is running correctly in production.</li>
                <li>If you encountered any errors, review the error message and check the server logs for more details.</li>
            </ol>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-lightbulb"></i> Tip</h5>
                <p>For the email scheduler to work properly, make sure that:</p>
                <ul>
                    <li>Your server's email functionality is properly configured</li>
                    <li>The cron job is set to run at the appropriate intervals (every 5 minutes recommended)</li>
                    <li>The cron key in your cron job command matches the one in the script</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once "admin_footer.php";
?>
