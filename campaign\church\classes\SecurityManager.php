<?php

/**
 * SecurityManager Class
 * 
 * Handles security-related functionality for the church application
 * including input validation, authentication, CSRF protection, and data protection.
 */
class SecurityManager {
    private $pdo;
    private $sessionTokenName = 'csrf_token';
    private $lockoutThreshold = 5; // Number of failed attempts before lockout
    private $lockoutDuration = 1800; // Lockout duration in seconds (30 minutes)
    
    /**
     * Constructor
     * 
     * @param PDO $pdo Database connection
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
        
        // Initialize CSRF token if not exists
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION[$this->sessionTokenName])) {
            $_SESSION[$this->sessionTokenName] = bin2hex(random_bytes(32));
        }
    }
    
    /**
     * Get CSRF token
     * 
     * @return string Current CSRF token
     */
    public function getCSRFToken() {
        return $_SESSION[$this->sessionTokenName];
    }
    
    /**
     * Validate CSRF token
     * 
     * @param string $token Token to validate
     * @return bool True if token is valid
     */
    public function validateCSRFToken($token) {
        if (!isset($_SESSION[$this->sessionTokenName]) || $token !== $_SESSION[$this->sessionTokenName]) {
            $this->logSecurityEvent('CSRF token validation failed', [
                'provided_token' => substr($token, 0, 8) . '...',
                'expected_token' => isset($_SESSION[$this->sessionTokenName]) ? 
                    substr($_SESSION[$this->sessionTokenName], 0, 8) . '...' : 'not set'
            ]);
            return false;
        }
        return true;
    }
    
    /**
     * Generate CSRF input field
     * 
     * @return string HTML input field with CSRF token
     */
    public function generateCSRFInput() {
        return '<input type="hidden" name="csrf_token" value="' . $this->getCSRFToken() . '">';
    }
    
    /**
     * Sanitize input
     * 
     * @param string $input Input to sanitize
     * @param string $type Type of sanitization (text, email, int, float, url)
     * @return mixed Sanitized input
     */
    public function sanitizeInput($input, $type = 'text') {
        $input = trim($input);
        
        switch ($type) {
            case 'email':
                return filter_var($input, FILTER_SANITIZE_EMAIL);
            
            case 'int':
                return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
                
            case 'float':
                return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
                
            case 'url':
                return filter_var($input, FILTER_SANITIZE_URL);
                
            case 'html':
                // Allow some HTML but remove potentially dangerous tags
                return strip_tags($input, '<p><br><a><strong><em><ul><ol><li><h1><h2><h3><h4><h5><h6><blockquote>');
                
            case 'text':
            default:
                // For plain text, remove all HTML
                return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        }
    }
    
    /**
     * Validate input
     * 
     * @param string $input Input to validate
     * @param string $type Type of validation (email, int, float, url, date, phone)
     * @param array $options Additional validation options
     * @return bool True if input is valid
     */
    public function validateInput($input, $type, $options = []) {
        switch ($type) {
            case 'email':
                return filter_var($input, FILTER_VALIDATE_EMAIL) !== false;
                
            case 'int':
                $valid = filter_var($input, FILTER_VALIDATE_INT) !== false;
                if ($valid && isset($options['min']) && $input < $options['min']) {
                    return false;
                }
                if ($valid && isset($options['max']) && $input > $options['max']) {
                    return false;
                }
                return $valid;
                
            case 'float':
                $valid = filter_var($input, FILTER_VALIDATE_FLOAT) !== false;
                if ($valid && isset($options['min']) && $input < $options['min']) {
                    return false;
                }
                if ($valid && isset($options['max']) && $input > $options['max']) {
                    return false;
                }
                return $valid;
                
            case 'url':
                return filter_var($input, FILTER_VALIDATE_URL) !== false;
                
            case 'date':
                $format = $options['format'] ?? 'Y-m-d';
                $date = \DateTime::createFromFormat($format, $input);
                return $date && $date->format($format) === $input;
                
            case 'phone':
                // Basic phone validation - can be customized based on country format
                return preg_match('/^[+]?[0-9\s\-\(\)]{8,20}$/', $input) === 1;
                
            case 'password':
                // Password strength validation
                $minLength = $options['min_length'] ?? 8;
                $requireUppercase = $options['require_uppercase'] ?? true;
                $requireLowercase = $options['require_lowercase'] ?? true;
                $requireNumbers = $options['require_numbers'] ?? true;
                $requireSpecial = $options['require_special'] ?? true;
                
                if (strlen($input) < $minLength) {
                    return false;
                }
                
                if ($requireUppercase && !preg_match('/[A-Z]/', $input)) {
                    return false;
                }
                
                if ($requireLowercase && !preg_match('/[a-z]/', $input)) {
                    return false;
                }
                
                if ($requireNumbers && !preg_match('/[0-9]/', $input)) {
                    return false;
                }
                
                if ($requireSpecial && !preg_match('/[^A-Za-z0-9]/', $input)) {
                    return false;
                }
                
                return true;
                
            default:
                return true;
        }
    }
    
    /**
     * Hash password securely
     * 
     * @param string $password Password to hash
     * @return string Hashed password
     */
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
    }
    
    /**
     * Verify password
     * 
     * @param string $password Password to verify
     * @param string $hash Hash to verify against
     * @return bool True if password is correct
     */
    public function verifyPassword($password, $hash) {
        // For backward compatibility with MD5 hashes
        if (strlen($hash) === 32) {
            return md5($password) === $hash;
        }
        
        return password_verify($password, $hash);
    }
    
    /**
     * Check if account is locked
     * 
     * @param string $username Username to check
     * @return bool True if account is locked
     */
    public function isAccountLocked($username) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT failed_attempts, last_failed_attempt 
                FROM admin_login_attempts 
                WHERE username = ?
            ");
            $stmt->execute([$username]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$result) {
                return false;
            }
            
            // Check if account is locked
            if ($result['failed_attempts'] >= $this->lockoutThreshold) {
                $lockoutTime = strtotime($result['last_failed_attempt']) + $this->lockoutDuration;
                if (time() < $lockoutTime) {
                    // Account is locked
                    $remainingTime = $lockoutTime - time();
                    $this->logSecurityEvent('Account locked access attempt', [
                        'username' => $username,
                        'remaining_lockout_time' => $remainingTime . ' seconds'
                    ]);
                    return true;
                } else {
                    // Lockout period expired, reset counter
                    $this->resetFailedAttempts($username);
                    return false;
                }
            }
            
            return false;
        } catch (PDOException $e) {
            $this->logSecurityEvent('Database error checking account lock', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Record failed login attempt
     * 
     * @param string $username Username that failed login
     */
    public function recordFailedAttempt($username) {
        try {
            // Check if record exists
            $stmt = $this->pdo->prepare("
                SELECT failed_attempts 
                FROM admin_login_attempts 
                WHERE username = ?
            ");
            $stmt->execute([$username]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result) {
                // Update existing record
                $stmt = $this->pdo->prepare("
                    UPDATE admin_login_attempts 
                    SET failed_attempts = failed_attempts + 1, 
                        last_failed_attempt = NOW() 
                    WHERE username = ?
                ");
                $stmt->execute([$username]);
            } else {
                // Create new record
                $stmt = $this->pdo->prepare("
                    INSERT INTO admin_login_attempts 
                    (username, failed_attempts, last_failed_attempt) 
                    VALUES (?, 1, NOW())
                ");
                $stmt->execute([$username]);
            }
            
            $this->logSecurityEvent('Failed login attempt', [
                'username' => $username,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
        } catch (PDOException $e) {
            $this->logSecurityEvent('Database error recording failed attempt', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Reset failed attempts counter
     * 
     * @param string $username Username to reset counter for
     */
    public function resetFailedAttempts($username) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE admin_login_attempts 
                SET failed_attempts = 0, 
                    last_failed_attempt = NULL 
                WHERE username = ?
            ");
            $stmt->execute([$username]);
        } catch (PDOException $e) {
            $this->logSecurityEvent('Database error resetting failed attempts', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Encrypt sensitive data
     * 
     * @param string $data Data to encrypt
     * @return string Encrypted data
     */
    public function encryptData($data) {
        // Get encryption key from config or environment
        $encryptionKey = defined('ENCRYPTION_KEY') ? ENCRYPTION_KEY : getenv('ENCRYPTION_KEY');
        
        if (empty($encryptionKey)) {
            // Fallback to a derived key if not configured
            $encryptionKey = hash('sha256', 'church_default_key', true);
        } else if (strlen($encryptionKey) !== 32) {
            // Ensure key is 32 bytes (256 bits)
            $encryptionKey = hash('sha256', $encryptionKey, true);
        }
        
        // Generate initialization vector
        $iv = random_bytes(16);
        
        // Encrypt data
        $encrypted = openssl_encrypt(
            $data,
            'AES-256-CBC',
            $encryptionKey,
            0,
            $iv
        );
        
        // Combine IV and encrypted data
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * Decrypt sensitive data
     * 
     * @param string $encryptedData Encrypted data to decrypt
     * @return string|false Decrypted data or false on failure
     */
    public function decryptData($encryptedData) {
        // Get encryption key from config or environment
        $encryptionKey = defined('ENCRYPTION_KEY') ? ENCRYPTION_KEY : getenv('ENCRYPTION_KEY');
        
        if (empty($encryptionKey)) {
            // Fallback to a derived key if not configured
            $encryptionKey = hash('sha256', 'church_default_key', true);
        } else if (strlen($encryptionKey) !== 32) {
            // Ensure key is 32 bytes (256 bits)
            $encryptionKey = hash('sha256', $encryptionKey, true);
        }
        
        // Decode the combined data
        $combined = base64_decode($encryptedData);
        
        if ($combined === false) {
            return false;
        }
        
        // Extract IV (first 16 bytes) and encrypted data
        $iv = substr($combined, 0, 16);
        $encrypted = substr($combined, 16);
        
        // Decrypt data
        return openssl_decrypt(
            $encrypted,
            'AES-256-CBC',
            $encryptionKey,
            0,
            $iv
        );
    }
    
    /**
     * Log security event
     * 
     * @param string $event Event description
     * @param array $data Additional event data
     */
    public function logSecurityEvent($event, $data = []) {
        $logDir = dirname(__DIR__) . '/logs';
        $logFile = $logDir . '/security.log';
        
        // Ensure log directory exists
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // Prepare log entry
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user = isset($_SESSION['admin_id']) ? $_SESSION['admin_username'] : 'unauthenticated';
        $url = $_SERVER['REQUEST_URI'] ?? 'unknown';
        
        $logEntry = sprintf(
            "[%s] [%s] [%s] [%s] %s %s\n",
            $timestamp,
            $ip,
            $user,
            $url,
            $event,
            !empty($data) ? json_encode($data) : ''
        );
        
        // Write to log file
        file_put_contents($logFile, $logEntry, FILE_APPEND);
        
        // For critical events, also log to database
        $criticalEvents = [
            'Failed login attempt',
            'Account locked access attempt',
            'CSRF token validation failed',
            'Unauthorized access attempt',
            'Password change'
        ];
        
        if (in_array($event, $criticalEvents)) {
            try {
                $stmt = $this->pdo->prepare("
                    INSERT INTO security_logs 
                    (event_type, user_id, ip_address, event_data, created_at) 
                    VALUES (?, ?, ?, ?, NOW())
                ");
                
                $userId = isset($_SESSION['admin_id']) ? $_SESSION['admin_id'] : null;
                $stmt->execute([
                    $event,
                    $userId,
                    $ip,
                    json_encode($data)
                ]);
            } catch (PDOException $e) {
                // If database logging fails, at least we have the file log
                file_put_contents(
                    $logFile, 
                    "[{$timestamp}] Failed to log to database: {$e->getMessage()}\n", 
                    FILE_APPEND
                );
            }
        }
    }
    
    /**
     * Generate a random token
     * 
     * @param int $length Length of token
     * @return string Random token
     */
    public function generateToken($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
    
    /**
     * Set security headers
     */
    public function setSecurityHeaders() {
        // Content Security Policy
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; img-src 'self' data: https:; font-src 'self' data: https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.gstatic.com; connect-src 'self';");
        
        // Prevent MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // Prevent clickjacking
        header('X-Frame-Options: SAMEORIGIN');
        
        // Enable XSS protection
        header('X-XSS-Protection: 1; mode=block');
        
        // HTTP Strict Transport Security
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        
        // Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Feature Policy
        header("Permissions-Policy: camera=(), microphone=(), geolocation=()");
    }
} 