-- SQL to update the raffle numbers periodically
-- This can be run via cron job to refresh the numbers

UPDATE email_templates 
SET content = REPLACE(
    REPLACE(
        REPLACE(
            REPLACE(
                REPLACE(
                    REPLACE(
                        content, 
                        'class="lotto-ball ball-1">' + '[0-9]+' + '</div>',
                        'class="lotto-ball ball-1">' + FLOOR(1 + RAND() * 20) + '</div>'
                    ),
                    'class="lotto-ball ball-2">' + '[0-9]+' + '</div>',
                    'class="lotto-ball ball-2">' + FLOOR(21 + RAND() * 20) + '</div>'
                ),
                'class="lotto-ball ball-3">' + '[0-9]+' + '</div>',
                'class="lotto-ball ball-3">' + FLOOR(41 + RAND() * 20) + '</div>'
            ),
            'class="lotto-ball ball-4">' + '[0-9]+' + '</div>',
            'class="lotto-ball ball-4">' + FLOOR(61 + RAND() * 20) + '</div>'
        ),
        'class="lotto-ball ball-5">' + '[0-9]+' + '</div>',
        'class="lotto-ball ball-5">' + FLOOR(81 + RAND() * 19) + '</div>'
    ),
    'class="lotto-ball powerball">' + '[0-9]+' + '</div>',
    'class="lotto-ball powerball">' + FLOOR(1 + RAND() * 15) + '</div>'
)
WHERE template_name = 'The Big Raffle Winner';