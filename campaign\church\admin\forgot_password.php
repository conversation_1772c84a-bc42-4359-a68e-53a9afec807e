<?php
session_start();

// If already logged in, redirect to admin dashboard
if (isset($_SESSION['admin_id'])) {
    header("Location: dashboard.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include the SecurityManager class
require_once '../classes/SecurityManager.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize SecurityManager
$security = new SecurityManager($conn);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';

// Process password reset request
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        $username = $security->sanitizeInput($_POST['username'], 'text');
        
        // Check if username exists
        $stmt = $conn->prepare("SELECT id, email, full_name FROM admins WHERE username = ?");
        $stmt->execute([$username]);
        $admin = $stmt->fetch();
        
        if ($admin) {
            // Generate password reset token
            $token = $security->generateToken(32);
            
            // Set expiration time with a longer window (4 hours) and ensure timezone consistency
            $expires = date('Y-m-d H:i:s', strtotime('+4 hours'));
            
            // Log token generation
            error_log("Generated token: " . $token . " with expiration: " . $expires);
            
            // Save token to database
            $stmt = $conn->prepare("
                UPDATE admins 
                SET password_reset_token = ?, password_reset_expires = ? 
                WHERE id = ?
            ");
            $stmt->execute([$token, $expires, $admin['id']]);
            
            // Build reset URL - use configured ADMIN_URL
            $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? "https" : "http";
            $host = $_SERVER['HTTP_HOST'];
            
            // Use the defined ADMIN_URL constant instead of hardcoded path
            $resetUrl = ADMIN_URL . "/reset_password.php?token=" . urlencode($token);
            
            // For debugging
            error_log("Reset URL: " . $resetUrl);
            
            // Send password reset email
            $emailSent = sendPasswordResetEmail($admin, $resetUrl);
            
            if ($emailSent) {
                $success = "Password reset instructions have been sent to your email address.";
                
                // Log password reset request
                $security->logSecurityEvent('Password reset requested', [
                    'admin_id' => $admin['id'],
                    'username' => $username,
                    'expires' => $expires
                ]);
            } else {
                $error = "Failed to send password reset email. Please try again or contact an administrator.";
            }
        } else {
            // For security reasons, show the same message even if the username doesn't exist
            // This prevents username enumeration
            $success = "If your username exists in our system, password reset instructions have been sent to your email address.";
            
            // Log attempted reset for non-existent user
            $security->logSecurityEvent('Password reset attempted for non-existent user', [
                'username' => $username
            ]);
        }
    }
}

/**
 * Send password reset email
 * 
 * @param array $admin Admin data
 * @param string $resetUrl Reset URL
 * @return bool True if email sent successfully
 */
function sendPasswordResetEmail($admin, $resetUrl) {
    global $pdo;
    
    // Get email template
    $stmt = $pdo->prepare("
        SELECT subject, content 
        FROM email_templates 
        WHERE template_name LIKE '%password%reset%' 
        LIMIT 1
    ");
    $stmt->execute();
    $template = $stmt->fetch();
    
    if (!$template) {
        // Use default template if none found
        $subject = "Password Reset - Freedom Assembly Church Admin";
        $content = "
            <html>
            <body>
                <h2>Password Reset Request</h2>
                <p>Dear {full_name},</p>
                <p>We received a request to reset your password for the Freedom Assembly Church Admin Portal.</p>
                <p>To reset your password, please click the link below:</p>
                <p><a href='{reset_url}'>Reset Password</a></p>
                <p>This link will expire in 1 hour.</p>
                <p>If you did not request a password reset, please ignore this email or contact an administrator.</p>
                <p>Thank you,<br>Freedom Assembly Church Admin Team</p>
            </body>
            </html>
        ";
    } else {
        $subject = $template['subject'];
        $content = $template['content'];
    }
    
    // Replace placeholders
    $subject = str_replace('{full_name}', $admin['full_name'], $subject);
    
    // Make sure the reset URL is properly encoded for HTML
    $safeResetUrl = htmlspecialchars($resetUrl, ENT_QUOTES, 'UTF-8');
    
    $content = str_replace(
        ['{full_name}', '{reset_url}', '{expires}'],
        [$admin['full_name'], $safeResetUrl, '1 hour'],
        $content
    );
    
    // Send email using the global function
    return sendEmail(
        $admin['email'],
        $admin['full_name'],
        $subject,
        $content,
        true
    );
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Freedom Assembly Church</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 50px;
        }
        .forgot-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
        }
        .forgot-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .forgot-logo h1 {
            color: #343a40;
        }
        .error-message {
            color: #dc3545;
            margin-bottom: 20px;
        }
        .success-message {
            color: #28a745;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="forgot-container">
            <div class="forgot-logo">
                <h1>Forgot Password</h1>
                <p>Freedom Assembly Church International</p>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="error-message text-center">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="success-message text-center">
                    <?php echo $success; ?>
                </div>
                <div class="text-center mt-3">
                    <a href="login.php" class="btn btn-primary">Return to Login</a>
                </div>
            <?php else: ?>
                <div class="mb-4">
                    <p>Enter your username below and we'll send you instructions to reset your password.</p>
                </div>
                
                <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                    <?php echo $security->generateCSRFInput(); ?>
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Reset Password</button>
                    </div>
                    
                    <div class="mt-3 text-center">
                        <a href="login.php">Return to Login</a>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 