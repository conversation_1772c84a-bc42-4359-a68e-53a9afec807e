<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

if (!isset($_GET['event_id']) || !is_numeric($_GET['event_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid event ID']);
    exit();
}

$event_id = (int)$_GET['event_id'];

try {
    $stmt = $conn->prepare("
        SELECT id, file_name, file_path, file_size, upload_date
        FROM event_files 
        WHERE event_id = ? 
        ORDER BY upload_date DESC
    ");
    $stmt->execute([$event_id]);
    $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format file sizes
    foreach ($files as &$file) {
        $file['file_size_formatted'] = formatFileSize($file['file_size']);
    }
    
    header('Content-Type: application/json');
    echo json_encode($files);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}

function formatFileSize($bytes) {
    if ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
