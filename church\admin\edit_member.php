<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Check if member ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: members.php");
    exit();
}

$member_id = intval($_GET['id']);

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';

// Get member details
$stmt = $conn->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$member_id]);
$member = $stmt->fetch();

if (!$member) {
    header("Location: members.php");
    exit();
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $phone_number = trim($_POST['phone_number']);
    $birth_date = trim($_POST['birth_date']);
    $home_address = trim($_POST['home_address'] ?? '');
    $occupation = trim($_POST['occupation'] ?? '');
    $message_text = trim($_POST['message'] ?? '');
    
    // Check if email already exists (except for this member)
    $stmt = $conn->prepare("SELECT id FROM members WHERE email = ? AND id != ?");
    $stmt->execute([$email, $member_id]);
    $existing_member = $stmt->fetch();
    
    if ($existing_member) {
        $error = "Email address already exists for another member";
    } else {
        // Update member
        $stmt = $conn->prepare("
            UPDATE members SET 
            full_name = ?, 
            email = ?, 
            phone_number = ?, 
            birth_date = ?,
            home_address = ?,
            occupation = ?,
            message = ?
            WHERE id = ?
        ");
        
        if ($stmt->execute([$full_name, $email, $phone_number, $birth_date, $home_address, $occupation, $message_text, $member_id])) {
            $message = "Member information updated successfully";
            
            // Refresh member data
            $stmt = $conn->prepare("SELECT * FROM members WHERE id = ?");
            $stmt->execute([$member_id]);
            $member = $stmt->fetch();
        } else {
            $error = "Error updating member: " . $conn->errorInfo()[2];
        }
    }
}

// Close the database connection
$conn = null;

// Set page variables
$page_title = 'Edit Member';
$page_header = 'Edit Member';
$page_description = 'Update member information in the church database.';

// Include header
include 'includes/header.php';

// Display success message if it exists
if (isset($success_message) && !empty($success_message)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
             <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($success_message) . '
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>';
}

// Display error message if it exists
if (isset($error_message) && !empty($error_message)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
             <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($error_message) . '
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>';
}
?>

<div class="row mb-4">
    <div class="col-md-8">
        <h2>Edit Member</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="members.php">Members</a></li>
                <li class="breadcrumb-item active" aria-current="page">Edit Member</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="view_member.php?id=<?php echo $member_id; ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Member
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . '?id=' . $member_id; ?>">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="full_name" class="form-label required-field">Full Name</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo htmlspecialchars($member['full_name']); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label required-field">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($member['email']); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="phone_number" class="form-label">Phone Number</label>
                        <input type="text" class="form-control" id="phone_number" name="phone_number" value="<?php echo htmlspecialchars($member['phone_number'] ?? ''); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="birth_date" class="form-label required-field">Birth Date</label>
                        <input type="date" class="form-control" id="birth_date" name="birth_date" value="<?php echo htmlspecialchars($member['birth_date']); ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="occupation" class="form-label">Occupation</label>
                        <input type="text" class="form-control" id="occupation" name="occupation" value="<?php echo htmlspecialchars($member['occupation'] ?? ''); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="home_address" class="form-label">Home Address</label>
                        <textarea class="form-control" id="home_address" name="home_address" rows="3"><?php echo htmlspecialchars($member['home_address'] ?? ''); ?></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="message" class="form-label">Message</label>
                        <textarea class="form-control" id="message" name="message" rows="4"><?php echo htmlspecialchars($member['message'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-end">
                    <a href="view_member.php?id=<?php echo $member_id; ?>" class="btn btn-secondary me-2">Cancel</a>
                    <button type="submit" class="btn btn-primary">Update Member</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 