<?php
// Include the configuration file with absolute path
$config_path = __DIR__ . '/../config.php';
require_once $config_path;

// Database connection - using the connection from config.php
$conn = $pdo;

echo "<h1>Database Connection and Payment Tables Check</h1>";

// Check database connection
try {
    $conn->query("SELECT 1");
    echo "<p style='color: green;'>✓ Database connection successful</p>";
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

// Function to check if a table exists
function tableExists($conn, $tableName) {
    try {
        // Use a more reliable method to check if table exists
        $stmt = $conn->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        return in_array($tableName, $tables);
    } catch (PDOException $e) {
        echo "<p style='color: red;'>Error checking if table exists: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Check payment tables
$tables = [
    'payment_settings' => 'Payment Settings',
    'donations' => 'Donations',
    'payment_transactions' => 'Payment Transactions',
    'donation_notifications' => 'Donation Notifications'
];

echo "<h2>Payment Tables Status:</h2>";
echo "<ul>";

foreach ($tables as $table => $description) {
    if (tableExists($conn, $table)) {
        echo "<li style='color: green;'>✓ $description table exists</li>";
        
        // Show table structure for existing tables
        try {
            $stmt = $conn->prepare("DESCRIBE $table");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<ul>";
            foreach ($columns as $column) {
                echo "<li><strong>" . $column['Field'] . "</strong> - " . $column['Type'];
                if ($column['Key'] === 'PRI') {
                    echo " (Primary Key)";
                } elseif ($column['Key'] === 'MUL') {
                    echo " (Foreign Key)";
                }
                echo "</li>";
            }
            echo "</ul>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>Error getting table structure: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<li style='color: red;'>✗ $description table does not exist</li>";
    }
}

echo "</ul>";

// Check if members table exists (for foreign key reference)
if (tableExists($conn, 'members')) {
    echo "<p style='color: green;'>✓ Members table exists (required for foreign key reference)</p>";
    
    // Check members table structure
    try {
        $stmt = $conn->prepare("DESCRIBE members");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $idColumn = null;
        foreach ($columns as $column) {
            if ($column['Field'] === 'id') {
                $idColumn = $column;
                break;
            }
        }
        
        if ($idColumn) {
            echo "<p>Members table 'id' column type: <strong>" . $idColumn['Type'] . "</strong></p>";
            
            // Check if donations table exists and has the correct foreign key type
            if (tableExists($conn, 'donations')) {
                $stmt = $conn->prepare("DESCRIBE donations");
                $stmt->execute();
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $recipientIdColumn = null;
                foreach ($columns as $column) {
                    if ($column['Field'] === 'recipient_id') {
                        $recipientIdColumn = $column;
                        break;
                    }
                }
                
                if ($recipientIdColumn) {
                    echo "<p>Donations table 'recipient_id' column type: <strong>" . $recipientIdColumn['Type'] . "</strong></p>";
                    
                    if ($recipientIdColumn['Type'] === $idColumn['Type']) {
                        echo "<p style='color: green;'>✓ Foreign key types match</p>";
                    } else {
                        echo "<p style='color: red;'>✗ Foreign key types do not match! This will cause foreign key constraint errors.</p>";
                    }
                }
            }
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>Error checking members table structure: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Members table does not exist (required for foreign key reference)</p>";
}

// Add links to fix tables
echo "<div style='margin-top: 20px;'>";
echo "<a href='payment_tables.php' style='display: inline-block; padding: 10px 15px; background-color: #0d6efd; color: white; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Run Payment Tables Setup</a>";
echo "<a href='fix_payment_tables.php' style='display: inline-block; padding: 10px 15px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 5px;'>Fix Payment Tables</a>";
echo "</div>";
?> 