<?php
// Test script to simulate how the raffle template would be displayed with replaced placeholders
require_once __DIR__ . '/../config.php';

// Get the template
$template_name = "The Big Raffle Winner";
$stmt = $pdo->prepare("SELECT id, content FROM email_templates WHERE template_name = ?");
$stmt->execute([$template_name]);
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    die("Template not found: $template_name");
}

// Get the reply-to email from settings
$reply_to_email = '';
try {
    $stmt = $pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'reply_to_email' LIMIT 1");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result && !empty($result['setting_value'])) {
        $reply_to_email = $result['setting_value'];
    } else {
        $reply_to_email = '' . CHURCH_EMAIL . ''; // Default fallback
    }
} catch (Exception $e) {
    $reply_to_email = '' . CHURCH_EMAIL . ''; // Error fallback
}

// Generate random numbers
$placeholders = [
    '{random_number_1}' => rand(1, 20),
    '{random_number_2}' => rand(21, 40),
    '{random_number_3}' => rand(41, 60),
    '{random_number_4}' => rand(61, 80),
    '{random_number_5}' => rand(81, 99),
    '{random_powerball}' => rand(1, 15),
    '{reply_to_email}' => htmlspecialchars($reply_to_email),
    '{unsubscribe_link}' => 'https://freedomassemblydb.online/unsubscribe.php?email=<EMAIL>&token=abc123'
];

// Replace placeholders in the template content
$content = $template['content'];
foreach ($placeholders as $placeholder => $value) {
    $content = str_replace($placeholder, $value, $content);
}

// Output the template with replaced placeholders
echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Template Test - $template_name</title>";
echo "<style>
    body { padding: 20px; font-family: Arial, sans-serif; }
    .test-info { margin-bottom: 20px; padding: 15px; background: #f5f5f5; border-radius: 5px; }
    .template-display { border: 1px solid #ddd; padding: 20px; border-radius: 5px; }
</style>";
echo "</head>";
echo "<body>";
echo "<div class='test-info'>";
echo "<h1>Template Test: $template_name</h1>";
echo "<p>This is a simulation of how the email would be displayed with replaced placeholders.</p>";
echo "<p><strong>Template ID:</strong> " . $template['id'] . "</p>";
echo "<p><strong>Current reply-to email:</strong> " . htmlspecialchars($reply_to_email) . "</p>";
echo "<p><strong>Placeholders replaced:</strong></p>";
echo "<ul>";
foreach ($placeholders as $placeholder => $value) {
    echo "<li>" . htmlspecialchars($placeholder) . " → " . htmlspecialchars($value) . "</li>";
}
echo "</ul>";
echo "</div>";
echo "<div class='template-display'>";
echo $content;
echo "</div>";
echo "</body>";
echo "</html>"; 