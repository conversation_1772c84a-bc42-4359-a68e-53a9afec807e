<?php
/**
 * <PERSON><PERSON><PERSON>ler
 * 
 * This file provides comprehensive error handling for the Church Email Management System.
 * It includes functions for logging errors, displaying user-friendly error messages,
 * and sending error notifications to administrators.
 */

// Don't display errors directly (we'll handle them ourselves)
ini_set('display_errors', '0');
ini_set('log_errors', '1');
ini_set('error_log', __DIR__ . '/../logs/php_errors.log');

// Ensure log directory exists
if (!file_exists(__DIR__ . '/../logs')) {
    mkdir(__DIR__ . '/../logs', 0755, true);
}

// Error severity levels for human-readable output
$error_levels = [
    E_ERROR => 'Fatal Error',
    E_WARNING => 'Warning',
    E_PARSE => 'Parse Error',
    E_NOTICE => 'Notice',
    E_CORE_ERROR => 'Core Error',
    E_CORE_WARNING => 'Core Warning',
    E_COMPILE_ERROR => 'Compile Error',
    E_COMPILE_WARNING => 'Compile Warning',
    E_USER_ERROR => 'User Error',
    E_USER_WARNING => 'User Warning',
    E_USER_NOTICE => 'User Notice',
    E_STRICT => 'Strict Standards',
    E_RECOVERABLE_ERROR => 'Recoverable Error',
    E_DEPRECATED => 'Deprecated',
    E_USER_DEPRECATED => 'User Deprecated',
    E_ALL => 'All Errors'
];

/**
 * Custom error handler function
 * 
 * @param int $errno Error level
 * @param string $errstr Error message
 * @param string $errfile File where error occurred
 * @param int $errline Line number where error occurred
 * @return bool
 */
function custom_error_handler($errno, $errstr, $errfile, $errline) {
    global $error_levels;
    
    // Get error level name
    $error_type = isset($error_levels[$errno]) ? $error_levels[$errno] : 'Unknown Error';
    
    // Format the error message
    $error_message = "[$error_type] $errstr in $errfile on line $errline";
    
    // Log error to file
    error_log($error_message);
    
    // For fatal errors, display a user-friendly message
    if ($errno == E_ERROR || $errno == E_USER_ERROR || $errno == E_CORE_ERROR || $errno == E_COMPILE_ERROR) {
        ob_clean(); // Clean output buffer
        
        if (php_sapi_name() === 'cli') {
            // Command line interface
            echo "A critical error has occurred. Please check the error logs.\n";
        } else {
            // Web interface
            $is_admin_page = strpos($_SERVER['REQUEST_URI'], '/admin/') !== false;
            $error_page = $is_admin_page ? '../500.php' : '500.php';
            
            if (file_exists($error_page)) {
                header("Location: $error_page");
            } else {
                echo '<div style="background-color: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 20px;">
                    <h3>An Error Has Occurred</h3>
                    <p>We apologize for the inconvenience. The site administrators have been notified.</p>
                    <p><a href="javascript:history.back()">Go Back</a> or <a href="/">Go to Homepage</a></p>
                </div>';
            }
        }
        
        // Notify administrator for critical errors
        notify_admin_of_error($error_message);
        
        return true; // Don't execute PHP internal error handler
    }
    
    // Let PHP handle other types of errors
    return false;
}

/**
 * Custom exception handler
 * 
 * @param Exception $exception The exception object
 * @return void
 */
function custom_exception_handler($exception) {
    // Format the exception message
    $error_message = "[Uncaught Exception] " . $exception->getMessage() . 
                      " in " . $exception->getFile() . 
                      " on line " . $exception->getLine() . 
                      "\nStack trace: " . $exception->getTraceAsString();
    
    // Log error to file
    error_log($error_message);
    
    // Clean output buffer
    if (ob_get_level()) {
        ob_clean();
    }
    
    if (php_sapi_name() === 'cli') {
        // Command line interface
        echo "An uncaught exception has occurred. Please check the error logs.\n";
    } else {
        // Web interface
        $is_admin_page = strpos($_SERVER['REQUEST_URI'], '/admin/') !== false;
        $error_page = $is_admin_page ? '../500.php' : '500.php';
        
        if (file_exists($error_page)) {
            header("Location: $error_page");
        } else {
            echo '<div style="background-color: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 20px;">
                <h3>An Error Has Occurred</h3>
                <p>We apologize for the inconvenience. The site administrators have been notified.</p>
                <p><a href="javascript:history.back()">Go Back</a> or <a href="/">Go to Homepage</a></p>
            </div>';
        }
    }
    
    // Notify administrator
    notify_admin_of_error($error_message);
}

/**
 * Send error notification to administrator
 * 
 * @param string $error_message The error message
 * @return void
 */
function notify_admin_of_error($error_message) {
    // Only send notifications in production to avoid spamming during development
    if (defined('ENVIRONMENT') && ENVIRONMENT === 'production') {
        // Limit notification frequency (don't send too many emails)
        $notification_file = __DIR__ . '/../logs/last_error_notification.txt';
        $can_send = true;
        
        if (file_exists($notification_file)) {
            $last_notification_time = file_get_contents($notification_file);
            if (time() - $last_notification_time < 3600) { // Only send once per hour
                $can_send = false;
            }
        }
        
        if ($can_send && defined('ADMIN_EMAIL')) {
            // Include request data in the notification
            $request_data = '';
            $request_data .= "URI: " . $_SERVER['REQUEST_URI'] . "\n";
            $request_data .= "Method: " . $_SERVER['REQUEST_METHOD'] . "\n";
            $request_data .= "IP: " . $_SERVER['REMOTE_ADDR'] . "\n";
            $request_data .= "User Agent: " . $_SERVER['HTTP_USER_AGENT'] . "\n";
            $request_data .= "Referrer: " . (isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'N/A') . "\n";
            
            // Add session and user info if available
            if (isset($_SESSION) && !empty($_SESSION)) {
                $request_data .= "Session Data: " . print_r($_SESSION, true) . "\n";
            }
            
            // Prepare email
            $to = ADMIN_EMAIL;
            $subject = "Error Alert: Church Email Management System";
            $message = "An error has occurred on the website:\n\n";
            $message .= $error_message . "\n\n";
            $message .= "Request Details:\n";
            $message .= $request_data;
            
            // Use PHP's mail function instead of our custom sendEmail which might be causing the error
            mail($to, $subject, $message, "From: " . ADMIN_EMAIL);
            
            // Update the notification timestamp
            file_put_contents($notification_file, time());
        }
    }
}

/**
 * Format and display error message for users
 * 
 * @param string $message Error message
 * @param string $type Type of message (error, success, warning, info)
 * @return string HTML for the error message
 */
function format_error_message($message, $type = 'error') {
    // Map type to Bootstrap alert class
    $alert_class = 'alert-info';
    switch ($type) {
        case 'error':
            $alert_class = 'alert-danger';
            break;
        case 'success':
            $alert_class = 'alert-success';
            break;
        case 'warning':
            $alert_class = 'alert-warning';
            break;
        case 'info':
            $alert_class = 'alert-info';
            break;
    }
    
    return '<div class="alert ' . $alert_class . ' alert-dismissible fade show" role="alert">
        ' . $message . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

/**
 * Log a database error
 * 
 * @param string $operation The database operation being performed
 * @param Exception $exception The caught exception
 * @param array $params Parameters used in the query
 * @return void
 */
function log_db_error($operation, $exception, $params = []) {
    $error_message = "Database Error during $operation: " . $exception->getMessage();
    
    // Add query parameters if available
    if (!empty($params)) {
        $error_message .= "\nParameters: " . print_r($params, true);
    }
    
    // Add user information if available
    if (isset($_SESSION['admin_id'])) {
        $error_message .= "\nAdmin ID: " . $_SESSION['admin_id'];
    }
    
    // Log to file
    error_log($error_message);
    
    // Return a user-friendly message
    return "A database error occurred. Please try again or contact support if the problem persists.";
}

/**
 * Check if required form fields are present
 * 
 * @param array $required_fields List of required field names
 * @param array $data Data to check (typically $_POST)
 * @return array Empty if all fields are present, otherwise list of missing fields
 */
function check_required_fields($required_fields, $data) {
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || trim($data[$field]) === '') {
            $missing_fields[] = $field;
        }
    }
    
    return $missing_fields;
}

/**
 * Validate email address
 * 
 * @param string $email Email address to validate
 * @return bool True if valid, false otherwise
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate date format
 * 
 * @param string $date Date string to validate
 * @param string $format Expected format (default: Y-m-d)
 * @return bool True if valid, false otherwise
 */
function validate_date($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

// Set the custom error and exception handlers
set_error_handler('custom_error_handler');
set_exception_handler('custom_exception_handler');

// Register shutdown function to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && ($error['type'] == E_ERROR || $error['type'] == E_CORE_ERROR || $error['type'] == E_COMPILE_ERROR)) {
        // Format error message
        $error_message = "[Fatal Error] " . $error['message'] . " in " . $error['file'] . " on line " . $error['line'];
        
        // Log error
        error_log($error_message);
        
        // Clean output buffer
        if (ob_get_level()) {
            ob_clean();
        }
        
        // Display user-friendly message
        if (php_sapi_name() !== 'cli') {
            echo '<div style="background-color: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 20px;">
                <h3>An Error Has Occurred</h3>
                <p>We apologize for the inconvenience. The site administrators have been notified.</p>
                <p><a href="javascript:history.back()">Go Back</a> or <a href="/">Go to Homepage</a></p>
            </div>';
        }
        
        // Notify administrator
        notify_admin_of_error($error_message);
    }
}); 