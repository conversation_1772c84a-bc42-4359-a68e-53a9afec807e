<?php
/**
 * Debug Contacts Page
 * 
 * This page helps debug the contacts.php file to see what's happening
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = 1; // Set for testing
}

// Include the configuration file
require_once '../config.php';

echo "<h1>Debug Contacts Page</h1>";

// Check if contacts.php file exists and is readable
$contacts_file = __DIR__ . '/contacts.php';
echo "<h2>File Check</h2>";
echo "<p><strong>File exists:</strong> " . (file_exists($contacts_file) ? 'YES' : 'NO') . "</p>";
echo "<p><strong>File readable:</strong> " . (is_readable($contacts_file) ? 'YES' : 'NO') . "</p>";
echo "<p><strong>File size:</strong> " . filesize($contacts_file) . " bytes</p>";
echo "<p><strong>Last modified:</strong> " . date('Y-m-d H:i:s', filemtime($contacts_file)) . "</p>";

// Check database connection
echo "<h2>Database Check</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM contacts");
    $result = $stmt->fetch();
    echo "<p><strong>Database connection:</strong> OK</p>";
    echo "<p><strong>Total contacts:</strong> " . $result['count'] . "</p>";
    
    // Get sample contacts
    $stmt = $pdo->query("SELECT id, name, email FROM contacts LIMIT 5");
    $contacts = $stmt->fetchAll();
    
    echo "<h3>Sample Contacts:</h3>";
    if (!empty($contacts)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th></tr>";
        foreach ($contacts as $contact) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($contact['id']) . "</td>";
            echo "<td>" . htmlspecialchars($contact['name']) . "</td>";
            echo "<td>" . htmlspecialchars($contact['email']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No contacts found in database.</p>";
    }
    
} catch (Exception $e) {
    echo "<p><strong>Database error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Check if JavaScript files exist
echo "<h2>JavaScript Files Check</h2>";
$js_file = __DIR__ . '/js/contacts.js';
echo "<p><strong>contacts.js exists:</strong> " . (file_exists($js_file) ? 'YES' : 'NO') . "</p>";
if (file_exists($js_file)) {
    echo "<p><strong>contacts.js size:</strong> " . filesize($js_file) . " bytes</p>";
    echo "<p><strong>contacts.js modified:</strong> " . date('Y-m-d H:i:s', filemtime($js_file)) . "</p>";
    
    // Check if initializeBulkDelete function exists in the file
    $js_content = file_get_contents($js_file);
    echo "<p><strong>Contains initializeBulkDelete:</strong> " . (strpos($js_content, 'initializeBulkDelete') !== false ? 'YES' : 'NO') . "</p>";
}

// Check for specific HTML elements in contacts.php
echo "<h2>HTML Elements Check</h2>";
$contacts_content = file_get_contents($contacts_file);

$checks = [
    'selectAllContacts' => 'Select All checkbox',
    'contact-checkbox' => 'Individual contact checkboxes',
    'bulkActionsContainer' => 'Bulk actions container',
    'bulkDeleteBtn' => 'Bulk delete button',
    'bulkDeleteModal' => 'Bulk delete modal'
];

foreach ($checks as $search => $description) {
    $found = strpos($contacts_content, $search) !== false;
    echo "<p><strong>$description:</strong> " . ($found ? 'FOUND' : 'NOT FOUND') . "</p>";
}

// Test a simple HTML table with checkboxes
echo "<h2>Test Table with Checkboxes</h2>";
echo "<p>This is a test to see if checkboxes render correctly:</p>";

echo '<div style="border: 1px solid #ccc; padding: 10px; margin: 10px 0;">';
echo '<table border="1" cellpadding="5">';
echo '<thead>';
echo '<tr>';
echo '<th><input type="checkbox" id="testSelectAll" title="Select All"></th>';
echo '<th>Name</th>';
echo '<th>Email</th>';
echo '</tr>';
echo '</thead>';
echo '<tbody>';

if (!empty($contacts)) {
    foreach (array_slice($contacts, 0, 3) as $contact) {
        echo '<tr>';
        echo '<td><input type="checkbox" class="test-checkbox" value="' . $contact['id'] . '"></td>';
        echo '<td>' . htmlspecialchars($contact['name']) . '</td>';
        echo '<td>' . htmlspecialchars($contact['email']) . '</td>';
        echo '</tr>';
    }
} else {
    echo '<tr><td colspan="3">No contacts to display</td></tr>';
}

echo '</tbody>';
echo '</table>';
echo '</div>';

echo "<h2>Actions</h2>";
echo "<p><a href='contacts.php' target='_blank'>Open Contacts Page</a></p>";
echo "<p><a href='test_contacts_bulk_delete.php' target='_blank'>Open Test Page</a></p>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>

<script>
// Test JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('Debug page loaded');
    
    // Test select all functionality
    const selectAll = document.getElementById('testSelectAll');
    const checkboxes = document.querySelectorAll('.test-checkbox');
    
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            checkboxes.forEach(cb => cb.checked = this.checked);
        });
    }
    
    // Log what we found
    console.log('Select all checkbox:', selectAll);
    console.log('Individual checkboxes:', checkboxes.length);
});
</script>
