/**
 * Calendar Debug Utility
 * 
 * This script can be included in the admin panel to help debug calendar issues.
 * It will check if the calendar is properly displaying birthday events and provide troubleshooting information.
 */

// Calendar Debug Script
document.addEventListener('DOMContentLoaded', function() {
    // Function to log calendar state
    function logCalendarState() {
        if (!window.mainCalendar) {
            console.error('Calendar instance not found');
            return;
        }

        const calendar = window.mainCalendar;
        console.log('Calendar Debug Information:');
        console.log('Current View:', calendar.view.type);
        console.log('Current Date:', calendar.getDate().toISOString());
        console.log('Events Count:', calendar.getEvents().length);
        
        // Log view-specific information
        const view = calendar.view;
        console.log('View Title:', view.title);
        console.log('View Start:', view.activeStart);
        console.log('View End:', view.activeEnd);
        
        // Check calendar container dimensions
        const calendarEl = document.getElementById('calendar');
        if (calendarEl) {
            console.log('Calendar Container Dimensions:', {
                width: calendarEl.offsetWidth,
                height: calendarEl.offsetHeight,
                clientWidth: calendarEl.clientWidth,
                clientHeight: calendarEl.clientHeight
            });
        }
        
        // Log any rendering issues
        const scrollers = document.querySelectorAll('.fc-scroller');
        console.log('Scroller Elements:', scrollers.length);
        scrollers.forEach((scroller, index) => {
            console.log(`Scroller ${index}:`, {
                overflow: getComputedStyle(scroller).overflow,
                height: getComputedStyle(scroller).height
            });
        });
    }

    // Add debug button if in development
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        const debugBtn = document.createElement('button');
        debugBtn.innerHTML = 'Debug Calendar';
        debugBtn.className = 'btn btn-sm btn-info';
        debugBtn.style.position = 'fixed';
        debugBtn.style.bottom = '10px';
        debugBtn.style.right = '10px';
        debugBtn.style.zIndex = '9999';
        
        debugBtn.addEventListener('click', logCalendarState);
        document.body.appendChild(debugBtn);
    }

    // Log initial state after a short delay to ensure calendar is fully rendered
    setTimeout(logCalendarState, 1000);
}); 