            </div> <!-- End of main-content -->
        </div> <!-- End of row -->
    </div> <!-- End of container-fluid -->

    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <?php if (isset($extra_js)): ?>
        <?php echo $extra_js; ?>
    <?php endif; ?>
    
    <script>
        // Common JavaScript for all admin pages
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });
            
            // Auto-hide regular alerts after 5 seconds, but keep instruction panels visible
            setTimeout(function() {
                $('.alert:not(.instruction-panel)').alert('close');
            }, 5000);
            
            // Handle instruction panels persistence
            $('.instruction-panel').each(function() {
                var panelId = $(this).closest('.card, .row, .container-fluid').attr('id') || 
                              $(this).closest('section').attr('id') || 
                              window.location.pathname.split('/').pop().replace('.php', '');
                
                // Set a unique ID for this panel based on its location
                if (panelId) {
                    $(this).attr('data-panel-id', panelId + '-instruction');
                    
                    // Check if this panel was previously closed
                    if (localStorage.getItem(panelId + '-instruction') === 'closed') {
                        $(this).hide();
                        
                        // Add a "Show Instructions" button if it doesn't exist
                        if ($('#show-instructions-' + panelId).length === 0) {
                            var $showButton = $('<button>', {
                                id: 'show-instructions-' + panelId,
                                class: 'btn btn-sm btn-outline-info show-instructions-btn',
                                html: '<i class="bi bi-info-circle me-1"></i> Show Instructions',
                                'data-panel-id': panelId + '-instruction'
                            });
                            
                            // Insert the button before the first heading or at the top of the content
                            var $insertBefore = $(this).parent().find('h1, h2, h3, h4, h5').first();
                            if ($insertBefore.length > 0) {
                                $showButton.insertBefore($insertBefore);
                            } else {
                                $showButton.prependTo($(this).parent());
                            }
                        }
                    }
                }
            });
            
            // Save panel state when closed
            $(document).on('close.bs.alert', '.instruction-panel', function() {
                var panelId = $(this).data('panel-id');
                if (panelId) {
                    localStorage.setItem(panelId, 'closed');
                    
                    // Add a "Show Instructions" button
                    var $showButton = $('<button>', {
                        id: 'show-instructions-' + panelId.replace('-instruction', ''),
                        class: 'btn btn-sm btn-outline-info show-instructions-btn mt-2 mb-3',
                        html: '<i class="bi bi-info-circle me-1"></i> Show Instructions',
                        'data-panel-id': panelId
                    });
                    
                    // Insert the button where the panel was
                    $showButton.insertAfter($(this));
                }
            });
            
            // Handle click on "Show Instructions" button
            $(document).on('click', '.show-instructions-btn', function() {
                var panelId = $(this).data('panel-id');
                
                // Find the instruction panel with this ID
                $('.instruction-panel[data-panel-id="' + panelId + '"]').show();
                
                // Remove the button
                $(this).remove();
                
                // Update localStorage
                localStorage.removeItem(panelId);
            });
        });
    </script>
    
    <?php
    // Output session timeout configuration if user is logged in
    if (isset($_SESSION['admin_id'])) {
        echo getSessionTimeoutConfig();
        echo '<script src="' . ADMIN_URL . '/js/session-timeout.js?v=' . time() . '"></script>';
    }
    ?>
    
    <style>
        .show-instructions-btn {
            margin-bottom: 1rem;
        }
    </style>
</body>
</html> 