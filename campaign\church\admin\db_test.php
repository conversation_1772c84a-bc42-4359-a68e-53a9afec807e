<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include the configuration file
require_once '../config.php';

try {
    // Test database connection
    echo "<h2>Testing Database Connection</h2>";
    
    if ($pdo) {
        echo "<p style='color: green;'>✓ Database connection successful!</p>";
        
        // Check if admins table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'admins'");
        $adminTableExists = $stmt->rowCount() > 0;
        
        if ($adminTableExists) {
            echo "<p style='color: green;'>✓ Admins table exists</p>";
            
            // Check if any admin users exist
            $stmt = $pdo->query("SELECT COUNT(*) FROM admins");
            $adminCount = $stmt->fetchColumn();
            echo "<p>Number of admin users: " . $adminCount . "</p>";
        } else {
            echo "<p style='color: red;'>✗ Admins table does not exist</p>";
            
            // Create admins table
            echo "<h3>Creating admins table...</h3>";
            $sql = "CREATE TABLE admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                email VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            
            $pdo->exec($sql);
            echo "<p style='color: green;'>✓ Admins table created successfully!</p>";
            
            // Create default admin user (username: admin, password: admin123)
            $sql = "INSERT INTO admins (username, password, full_name, email) 
                   VALUES ('admin', ?, 'Church Admin', '<EMAIL>')";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([md5('admin123')]);
            
            echo "<p style='color: green;'>✓ Default admin user created!</p>";
            echo "<p>Username: admin<br>Password: admin123</p>";
        }
        
        // Check other required tables
        $requiredTables = ['members', 'email_templates', 'email_logs', 'email_tracking'];
        foreach ($requiredTables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✓ $table table exists</p>";
            } else {
                echo "<p style='color: red;'>✗ $table table does not exist</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>✗ Database connection failed!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?> 