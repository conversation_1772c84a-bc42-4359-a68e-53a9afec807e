USE churchdb;

-- Update the email template to add back the member photo section
UPDATE email_templates 
SET content = REPLACE(content, 
'<div class="greeting">
                <p>Dear {recipient_full_name},</p>
                <p>We are excited to let you know that {birthday_member_full_name} will be celebrating their birthday {days_text}!</p>
            </div>', 
'<div class="greeting">
                <p>Dear {recipient_full_name},</p>
                <p>We are excited to let you know that {birthday_member_full_name} will be celebrating their birthday {days_text}!</p>
            </div>
            
            <div class="member-photo">
                <img src="{birthday_member_photo_url}" alt="{birthday_member_first_name}\'s photo" onerror="this.src=\'https://freedomassemblydb.online/assets/img/default-avatar.png\'; this.onerror=null;">
            </div>')
WHERE template_name = 'Member Birthday Notification'; 