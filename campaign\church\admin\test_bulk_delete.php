<?php
/**
 * Test Script for Bulk Delete Functionality
 * 
 * This script tests the bulk delete functionality for both contacts and members
 * to ensure proper operation before deployment.
 */

session_start();

// Include configuration
require_once '../config.php';

// Set admin session for testing (remove in production)
$_SESSION['admin_id'] = 1;

echo "<h1>Bulk Delete Functionality Test</h1>";
echo "<p>Testing bulk delete endpoints and functionality...</p>";

// Test 1: Test bulk delete contacts endpoint
echo "<h2>Test 1: Bulk Delete Contacts Endpoint</h2>";

// Create test data
$test_contact_data = [
    'contact_ids' => [999999, 999998], // Non-existent IDs for safe testing
    'confirmation_token' => 'BULK_DELETE_CONFIRMED'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/campaign/church/admin/ajax/bulk_delete_contacts.php');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_contact_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Cookie: ' . session_name() . '=' . session_id()
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p><strong>HTTP Code:</strong> $http_code</p>";
echo "<p><strong>Response:</strong></p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

$response_data = json_decode($response, true);
if ($response_data && isset($response_data['success'])) {
    echo "<p><strong>Status:</strong> " . ($response_data['success'] ? 'SUCCESS' : 'FAILED') . "</p>";
    if (isset($response_data['message'])) {
        echo "<p><strong>Message:</strong> " . htmlspecialchars($response_data['message']) . "</p>";
    }
} else {
    echo "<p><strong>Status:</strong> INVALID RESPONSE</p>";
}

echo "<hr>";

// Test 2: Test bulk delete members endpoint
echo "<h2>Test 2: Bulk Delete Members Endpoint</h2>";

// Create test data
$test_member_data = [
    'member_ids' => [999999, 999998], // Non-existent IDs for safe testing
    'confirmation_token' => 'BULK_DELETE_CONFIRMED'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/campaign/church/admin/ajax/bulk_delete_members.php');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_member_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Cookie: ' . session_name() . '=' . session_id()
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p><strong>HTTP Code:</strong> $http_code</p>";
echo "<p><strong>Response:</strong></p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

$response_data = json_decode($response, true);
if ($response_data && isset($response_data['success'])) {
    echo "<p><strong>Status:</strong> " . ($response_data['success'] ? 'SUCCESS' : 'FAILED') . "</p>";
    if (isset($response_data['message'])) {
        echo "<p><strong>Message:</strong> " . htmlspecialchars($response_data['message']) . "</p>";
    }
} else {
    echo "<p><strong>Status:</strong> INVALID RESPONSE</p>";
}

echo "<hr>";

// Test 3: Database connection and table structure
echo "<h2>Test 3: Database Structure Verification</h2>";

try {
    // Check contacts table structure
    $stmt = $pdo->query("DESCRIBE contacts");
    $contacts_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Contacts Table Structure:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
    foreach ($contacts_columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check members table structure
    $stmt = $pdo->query("DESCRIBE members");
    $members_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Members Table Structure:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
    foreach ($members_columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check foreign key constraints
    echo "<h3>Foreign Key Constraints:</h3>";
    $stmt = $pdo->query("
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            CONSTRAINT_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM 
            INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE 
            REFERENCED_TABLE_SCHEMA = DATABASE()
            AND (TABLE_NAME = 'contacts' OR TABLE_NAME = 'members' 
                 OR REFERENCED_TABLE_NAME = 'contacts' OR REFERENCED_TABLE_NAME = 'members')
        ORDER BY TABLE_NAME, COLUMN_NAME
    ");
    $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($constraints)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Table</th><th>Column</th><th>References Table</th><th>References Column</th></tr>";
        foreach ($constraints as $constraint) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($constraint['TABLE_NAME']) . "</td>";
            echo "<td>" . htmlspecialchars($constraint['COLUMN_NAME']) . "</td>";
            echo "<td>" . htmlspecialchars($constraint['REFERENCED_TABLE_NAME']) . "</td>";
            echo "<td>" . htmlspecialchars($constraint['REFERENCED_COLUMN_NAME']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No foreign key constraints found.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p><strong>Database Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";

// Test 4: Security validation
echo "<h2>Test 4: Security Validation</h2>";

// Test without confirmation token
echo "<h3>Test without confirmation token:</h3>";
$test_data_no_token = [
    'contact_ids' => [1, 2]
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/campaign/church/admin/ajax/bulk_delete_contacts.php');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data_no_token));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Cookie: ' . session_name() . '=' . session_id()
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
curl_close($ch);

$response_data = json_decode($response, true);
echo "<p><strong>Response:</strong> " . htmlspecialchars($response) . "</p>";
echo "<p><strong>Expected:</strong> Should reject without confirmation token</p>";
echo "<p><strong>Result:</strong> " . (
    $response_data && !$response_data['success'] && 
    strpos($response_data['message'], 'Confirmation token') !== false ? 
    'PASS' : 'FAIL'
) . "</p>";

echo "<hr>";
echo "<h2>Test Summary</h2>";
echo "<p>All tests completed. Review the results above to ensure bulk delete functionality is working correctly.</p>";
echo "<p><strong>Note:</strong> This test uses non-existent IDs to avoid accidentally deleting real data.</p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
hr { margin: 20px 0; }
</style>
