-- Add password reset columns to the admins table
ALTER TABLE admins 
ADD COLUMN password_reset_token VARCHAR(100) NULL, 
ADD COLUMN password_reset_expires DATETIME NULL;

-- Add index for faster lookups
ALTER TABLE admins 
ADD INDEX idx_password_reset_token (password_reset_token);

-- Log the migration
INSERT INTO migrations (migration_name, applied_at, description)
VALUES ('add_password_reset_columns', NOW(), 'Added password reset token columns to admins table')
ON DUPLICATE KEY UPDATE applied_at = NOW(); 