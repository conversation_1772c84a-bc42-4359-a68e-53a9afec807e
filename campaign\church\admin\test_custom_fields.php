<?php
session_start();
if (!isset($_SESSION["admin_id"])) {
    $_SESSION["admin_id"] = 4;
    $_SESSION["admin_username"] = "admin";
}

require_once "../config.php";

$page_title = "Custom Fields Management";
?>
<!DOCTYPE html>
<html>
<head>
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Custom Fields Management</h1>
        <p class="alert alert-success">✅ This page is working correctly!</p>
        
        <?php
        try {
            $stmt = $pdo->prepare("SELECT * FROM custom_field_definitions ORDER BY field_order ASC");
            $stmt->execute();
            $fields = $stmt->fetchAll();
            
            echo "<h3>Custom Fields (" . count($fields) . ")</h3>";
            if (count($fields) > 0) {
                echo "<table class=\"table table-striped\">";
                echo "<tr><th>ID</th><th>Entity</th><th>Name</th><th>Label</th><th>Type</th><th>Required</th></tr>";
                foreach ($fields as $field) {
                    echo "<tr>";
                    echo "<td>{$field[\"id\"]}</td>";
                    echo "<td>{$field[\"entity_type\"]}</td>";
                    echo "<td>{$field[\"field_name\"]}</td>";
                    echo "<td>{$field[\"field_label\"]}</td>";
                    echo "<td>{$field[\"field_type\"]}</td>";
                    echo "<td>" . ($field["is_required"] ? "Yes" : "No") . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>No custom fields found.</p>";
            }
        } catch (Exception $e) {
            echo "<p class=\"alert alert-danger\">Error: " . $e->getMessage() . "</p>";
        }
        ?>
        
        <hr>
        <p><a href="../dashboard.php" class="btn btn-secondary">← Back to Dashboard</a></p>
    </div>
</body>
</html>