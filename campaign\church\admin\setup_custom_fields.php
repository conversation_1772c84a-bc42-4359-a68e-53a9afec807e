<?php
/**
 * Custom Fields Database Setup
 * Creates tables and initial data for custom field system
 */

require_once '../config.php';

try {
    $pdo->beginTransaction();
    
    // Create custom_field_definitions table
    $sql = "CREATE TABLE IF NOT EXISTS custom_field_definitions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        entity_type ENUM('member', 'event', 'email_campaign', 'sms_campaign', 'contact') NOT NULL,
        field_name VARCHAR(100) NOT NULL,
        field_label VARCHAR(200) NOT NULL,
        field_type ENUM('text', 'textarea', 'number', 'email', 'phone', 'date', 'datetime', 'select', 'multiselect', 'checkbox', 'radio', 'file', 'url') NOT NULL,
        field_options TEXT NULL COMMENT 'JSON array for select/radio options',
        is_required BOOLEAN DEFAULT FALSE,
        is_searchable BOOLEAN DEFAULT TRUE,
        is_visible_in_list BOOLEAN DEFAULT FALSE,
        field_order INT DEFAULT 0,
        validation_rules TEXT NULL COMMENT 'JSON object with validation rules',
        help_text TEXT NULL,
        default_value TEXT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by INT NULL,
        UNIQUE KEY unique_field_per_entity (entity_type, field_name),
        INDEX idx_entity_type (entity_type),
        INDEX idx_field_order (field_order),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✓ Created custom_field_definitions table\n";
    
    // Create custom_field_values table
    $sql = "CREATE TABLE IF NOT EXISTS custom_field_values (
        id INT AUTO_INCREMENT PRIMARY KEY,
        field_definition_id INT NOT NULL,
        entity_id INT NOT NULL,
        field_value TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (field_definition_id) REFERENCES custom_field_definitions(id) ON DELETE CASCADE,
        UNIQUE KEY unique_field_value (field_definition_id, entity_id),
        INDEX idx_entity_id (entity_id),
        INDEX idx_field_definition (field_definition_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✓ Created custom_field_values table\n";
    
    // Create some default custom fields for members
    $defaultFields = [
        [
            'entity_type' => 'member',
            'field_name' => 'emergency_contact',
            'field_label' => 'Emergency Contact',
            'field_type' => 'text',
            'is_required' => false,
            'is_searchable' => true,
            'is_visible_in_list' => false,
            'field_order' => 1,
            'help_text' => 'Name and phone number of emergency contact'
        ],
        [
            'entity_type' => 'member',
            'field_name' => 'dietary_restrictions',
            'field_label' => 'Dietary Restrictions',
            'field_type' => 'textarea',
            'is_required' => false,
            'is_searchable' => true,
            'is_visible_in_list' => false,
            'field_order' => 2,
            'help_text' => 'Any dietary restrictions or allergies'
        ],
        [
            'entity_type' => 'member',
            'field_name' => 'ministry_interests',
            'field_label' => 'Ministry Interests',
            'field_type' => 'multiselect',
            'field_options' => json_encode([
                'worship_team' => 'Worship Team',
                'childrens_ministry' => 'Children\'s Ministry',
                'youth_ministry' => 'Youth Ministry',
                'outreach' => 'Outreach',
                'hospitality' => 'Hospitality',
                'prayer_team' => 'Prayer Team',
                'technical_support' => 'Technical Support',
                'administration' => 'Administration'
            ]),
            'is_required' => false,
            'is_searchable' => true,
            'is_visible_in_list' => true,
            'field_order' => 3,
            'help_text' => 'Select areas of ministry interest'
        ],
        [
            'entity_type' => 'member',
            'field_name' => 'baptism_date',
            'field_label' => 'Baptism Date',
            'field_type' => 'date',
            'is_required' => false,
            'is_searchable' => true,
            'is_visible_in_list' => false,
            'field_order' => 4,
            'help_text' => 'Date of baptism'
        ],
        [
            'entity_type' => 'member',
            'field_name' => 'membership_status',
            'field_label' => 'Membership Status',
            'field_type' => 'select',
            'field_options' => json_encode([
                'visitor' => 'Visitor',
                'regular_attendee' => 'Regular Attendee',
                'member' => 'Member',
                'inactive' => 'Inactive'
            ]),
            'is_required' => false,
            'is_searchable' => true,
            'is_visible_in_list' => true,
            'field_order' => 5,
            'default_value' => 'visitor',
            'help_text' => 'Current membership status'
        ]
    ];
    
    // Insert default fields for members
    $stmt = $pdo->prepare("
        INSERT INTO custom_field_definitions 
        (entity_type, field_name, field_label, field_type, field_options, is_required, is_searchable, is_visible_in_list, field_order, help_text, default_value)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        field_label = VALUES(field_label),
        field_type = VALUES(field_type),
        field_options = VALUES(field_options),
        help_text = VALUES(help_text),
        updated_at = CURRENT_TIMESTAMP
    ");
    
    foreach ($defaultFields as $field) {
        $stmt->execute([
            $field['entity_type'],
            $field['field_name'],
            $field['field_label'],
            $field['field_type'],
            $field['field_options'] ?? null,
            $field['is_required'],
            $field['is_searchable'],
            $field['is_visible_in_list'],
            $field['field_order'],
            $field['help_text'] ?? null,
            $field['default_value'] ?? null
        ]);
    }
    
    echo "✓ Created default custom fields for members\n";
    
    // Create default custom fields for events
    $eventFields = [
        [
            'entity_type' => 'event',
            'field_name' => 'event_capacity',
            'field_label' => 'Event Capacity',
            'field_type' => 'number',
            'is_required' => false,
            'is_searchable' => true,
            'is_visible_in_list' => true,
            'field_order' => 1,
            'validation_rules' => json_encode(['min' => 1, 'max' => 10000]),
            'help_text' => 'Maximum number of attendees'
        ],
        [
            'entity_type' => 'event',
            'field_name' => 'registration_required',
            'field_label' => 'Registration Required',
            'field_type' => 'checkbox',
            'is_required' => false,
            'is_searchable' => true,
            'is_visible_in_list' => true,
            'field_order' => 2,
            'default_value' => '0',
            'help_text' => 'Check if registration is required for this event'
        ],
        [
            'entity_type' => 'event',
            'field_name' => 'event_category',
            'field_label' => 'Event Category',
            'field_type' => 'select',
            'field_options' => json_encode([
                'worship' => 'Worship Service',
                'bible_study' => 'Bible Study',
                'fellowship' => 'Fellowship',
                'outreach' => 'Outreach',
                'youth' => 'Youth Event',
                'children' => 'Children\'s Event',
                'special' => 'Special Event',
                'meeting' => 'Meeting'
            ]),
            'is_required' => false,
            'is_searchable' => true,
            'is_visible_in_list' => true,
            'field_order' => 3,
            'default_value' => 'worship',
            'help_text' => 'Category of the event'
        ],
        [
            'entity_type' => 'event',
            'field_name' => 'special_instructions',
            'field_label' => 'Special Instructions',
            'field_type' => 'textarea',
            'is_required' => false,
            'is_searchable' => true,
            'is_visible_in_list' => false,
            'field_order' => 4,
            'help_text' => 'Any special instructions for attendees'
        ]
    ];
    
    foreach ($eventFields as $field) {
        $stmt->execute([
            $field['entity_type'],
            $field['field_name'],
            $field['field_label'],
            $field['field_type'],
            $field['field_options'] ?? null,
            $field['is_required'],
            $field['is_searchable'],
            $field['is_visible_in_list'],
            $field['field_order'],
            $field['help_text'] ?? null,
            $field['default_value'] ?? null
        ]);
    }
    
    echo "✓ Created default custom fields for events\n";
    
    // Create custom field permissions table
    $sql = "CREATE TABLE IF NOT EXISTS custom_field_permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        field_definition_id INT NOT NULL,
        role ENUM('admin', 'staff', 'volunteer', 'member') NOT NULL,
        can_view BOOLEAN DEFAULT TRUE,
        can_edit BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (field_definition_id) REFERENCES custom_field_definitions(id) ON DELETE CASCADE,
        UNIQUE KEY unique_field_role (field_definition_id, role),
        INDEX idx_field_definition (field_definition_id),
        INDEX idx_role (role)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✓ Created custom_field_permissions table\n";
    
    $pdo->commit();
    echo "\n✅ Custom Fields database setup completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Access Custom Field Management at: /admin/custom_fields.php\n";
    echo "2. Configure field permissions as needed\n";
    echo "3. Add custom fields to your forms\n";
    
} catch (Exception $e) {
    $pdo->rollback();
    echo "❌ Error setting up custom fields database: " . $e->getMessage() . "\n";
    exit(1);
}
?>
