<?php
/**
 * Delete Schedule - Admin Interface
 * 
 * This file handles the deletion of email schedules.
 * It's a separate file to avoid issues with DataTables caching.
 */

// Include necessary files
require_once '../config.php';
require_once '../includes/auth_check.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['message'] = "Invalid schedule ID.";
    $_SESSION['message_type'] = "danger";
    header("Location: email_scheduler.php");
    exit;
}

$schedule_id = intval($_GET['id']);
$admin_id = $_SESSION['admin_id'];

try {
    // Begin transaction
    $pdo->beginTransaction();
    
    // First check if the schedule exists
    $stmt = $pdo->prepare("SELECT id, name FROM email_schedules WHERE id = ?");
    $stmt->execute([$schedule_id]);
    $schedule = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$schedule) {
        throw new Exception("Schedule not found.");
    }
    
    // Log the deletion
    $stmt = $pdo->prepare("
        INSERT INTO email_schedule_logs 
        (schedule_id, log_type, message) 
        VALUES (?, 'warning', ?)
    ");
    $stmt->execute([
        $schedule_id, 
        "Schedule deleted by Admin ID: $admin_id"
    ]);
    
    // Delete related records first
    // 1. Delete recipients
    $stmt = $pdo->prepare("DELETE FROM email_schedule_recipients WHERE schedule_id = ?");
    $stmt->execute([$schedule_id]);
    
    // 2. Delete settings
    $stmt = $pdo->prepare("DELETE FROM email_schedule_settings WHERE schedule_id = ?");
    $stmt->execute([$schedule_id]);
    
    // 3. Delete logs (except the deletion log we just added)
    $stmt = $pdo->prepare("DELETE FROM email_schedule_logs WHERE schedule_id = ? AND log_type != 'warning'");
    $stmt->execute([$schedule_id]);
    
    // 4. Delete the schedule itself
    $stmt = $pdo->prepare("DELETE FROM email_schedules WHERE id = ?");
    $stmt->execute([$schedule_id]);
    
    // Commit transaction
    $pdo->commit();
    
    $_SESSION['message'] = "Email schedule '{$schedule['name']}' deleted successfully!";
    $_SESSION['message_type'] = "success";
    
} catch (Exception $e) {
    // Rollback transaction on error
    $pdo->rollBack();
    
    $_SESSION['message'] = "Error deleting schedule: " . $e->getMessage();
    $_SESSION['message_type'] = "danger";
}

// Redirect back to email scheduler page
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Pragma: no-cache");
header("Expires: 0");
header("Location: email_scheduler.php?noCache=" . time());
exit; 