<?php
/**
 * URL Audit Tool
 * 
 * This script scans the codebase for hardcoded URLs and reports them.
 * It helps identify places that need to be updated to use URL constants.
 */

// Configuration
$baseDir = dirname(__DIR__); // Parent directory of tools folder
$domains = [
    'freedomassemblydb.online',
    'freedomassemblychurch.org',
    'localhost/church'
];
$extensions = [
    'php',
    'sql',
    'js',
    'txt',
    'md',
    'html'
];
$excludeDirs = [
    'vendor',
    'node_modules',
    '.git',
    'logs',
    'tools'
];

// Initialize results storage
$results = [];
$totalFiles = 0;
$totalMatches = 0;

// Main scanning function
function scanDirectory($dir, $domains, $extensions, $excludeDirs, &$results, &$totalFiles, &$totalMatches) {
    global $baseDir;
    $items = scandir($dir);
    
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') {
            continue;
        }
        
        $path = $dir . DIRECTORY_SEPARATOR . $item;
        
        // Skip excluded directories
        if (is_dir($path) && in_array($item, $excludeDirs)) {
            continue;
        }
        
        if (is_dir($path)) {
            scanDirectory($path, $domains, $extensions, $excludeDirs, $results, $totalFiles, $totalMatches);
        } else {
            // Get file extension
            $ext = pathinfo($path, PATHINFO_EXTENSION);
            
            // Only scan files with specified extensions
            if (in_array($ext, $extensions)) {
                $totalFiles++;
                
                // Scan file content
                $content = file_get_contents($path);
                
                foreach ($domains as $domain) {
                    $pattern = '/(https?:\/\/)?' . preg_quote($domain, '/') . '([\/\w\.-]*)/i';
                    $matches = [];
                    $count = preg_match_all($pattern, $content, $matches);
                    
                    if ($count > 0) {
                        $relativePath = str_replace($baseDir . DIRECTORY_SEPARATOR, '', $path);
                        $relativePath = str_replace('\\', '/', $relativePath);
                        
                        if (!isset($results[$relativePath])) {
                            $results[$relativePath] = [];
                        }
                        
                        foreach ($matches[0] as $match) {
                            $results[$relativePath][] = $match;
                            $totalMatches++;
                        }
                    }
                }
            }
        }
    }
}

// Start scanning
echo "Starting URL audit...\n";
scanDirectory($baseDir, $domains, $extensions, $excludeDirs, $results, $totalFiles, $totalMatches);

// Display results
echo "Scan completed.\n";
echo "Total files scanned: $totalFiles\n";
echo "Total URL matches found: $totalMatches\n\n";

echo "Results by file:\n";
echo "----------------------------------------\n";

foreach ($results as $file => $urls) {
    echo "File: $file\n";
    echo "Found " . count($urls) . " URL(s):\n";
    
    // Get unique URLs
    $uniqueUrls = array_unique($urls);
    
    foreach ($uniqueUrls as $url) {
        echo "  - $url\n";
    }
    
    echo "----------------------------------------\n";
}

// Export results to a JSON file for further analysis
$jsonResults = json_encode($results, JSON_PRETTY_PRINT);
file_put_contents($baseDir . '/tools/url_audit_results.json', $jsonResults);
echo "Results exported to tools/url_audit_results.json\n"; 