<?php
/**
 * ICS File Generator for Calendar Events
 * 
 * This script generates ICS files for calendar events like birthdays and church events.
 * It can be called directly with parameters or included in other files.
 */

// Set content type to text/calendar if called directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    header('Content-Type: text/calendar; charset=utf-8');
    header('Content-Disposition: attachment; filename="event.ics"');
}

/**
 * Generate ICS file content for an event
 * 
 * @param string $summary Event summary/title
 * @param string $description Event description
 * @param string $location Event location
 * @param string $start_date Start date (YYYY-MM-DD format)
 * @param string $end_date End date (YYYY-MM-DD format), defaults to start_date
 * @param string $start_time Start time (HH:MM format), optional
 * @param string $end_time End time (HH:MM format), optional
 * @param bool $all_day Whether this is an all-day event
 * @param string $recurrence Recurrence rule (e.g., "FREQ=YEARLY"), optional
 * @param string $organizer Organizer email, optional
 * @return string ICS file content
 */
function generateICSContent($summary, $description, $location = '', $start_date, $end_date = '', $start_time = '', $end_time = '', $all_day = true, $recurrence = '', $organizer = '') {
    // Set default end date to start date if not provided
    if (empty($end_date)) {
        $end_date = $start_date;
    }
    
    // Generate unique identifier
    $uid = md5(uniqid(mt_rand(), true)) . '@freedomassemblychurch.org';
    
    // Format dates for ICS
    if ($all_day) {
        // All-day event format (DATE)
        $dtstart = date('Ymd', strtotime($start_date));
        $dtend = date('Ymd', strtotime($end_date . ' +1 day')); // Add 1 day for all-day events as per iCalendar spec
        $dtstart_format = "DTSTART;VALUE=DATE:{$dtstart}";
        $dtend_format = "DTEND;VALUE=DATE:{$dtend}";
    } else {
        // Timed event format (DATE-TIME)
        $dtstart = date('Ymd\THis\Z', strtotime($start_date . ' ' . $start_time));
        $dtend = date('Ymd\THis\Z', strtotime($end_date . ' ' . $end_time));
        $dtstart_format = "DTSTART:{$dtstart}";
        $dtend_format = "DTEND:{$dtend}";
    }
    
    // Create ICS content
    $ics = "BEGIN:VCALENDAR\r\n";
    $ics .= "VERSION:2.0\r\n";
    $ics .= "PRODID:-//Freedom Assembly Church//Calendar//EN\r\n";
    $ics .= "CALSCALE:GREGORIAN\r\n";
    $ics .= "METHOD:PUBLISH\r\n";
    $ics .= "BEGIN:VEVENT\r\n";
    $ics .= "UID:{$uid}\r\n";
    $ics .= $dtstart_format . "\r\n";
    $ics .= $dtend_format . "\r\n";
    $ics .= "SUMMARY:" . escapeICSText($summary) . "\r\n";
    
    if (!empty($description)) {
        $ics .= "DESCRIPTION:" . escapeICSText($description) . "\r\n";
    }
    
    if (!empty($location)) {
        $ics .= "LOCATION:" . escapeICSText($location) . "\r\n";
    }
    
    // Add creation timestamp
    $ics .= "DTSTAMP:" . date('Ymd\THis\Z') . "\r\n";
    
    // Add recurrence rule if provided
    if (!empty($recurrence)) {
        $ics .= "RRULE:{$recurrence}\r\n";
    }
    
    // Add organizer if provided
    if (!empty($organizer)) {
        $ics .= "ORGANIZER;CN=Freedom Assembly Church:mailto:{$organizer}\r\n";
    }
    
    $ics .= "END:VEVENT\r\n";
    $ics .= "END:VCALENDAR";
    
    return $ics;
}

/**
 * Generate ICS file content for a birthday event
 * 
 * @param string $name Person's name
 * @param string $birth_date Birth date (YYYY-MM-DD format)
 * @param int $year Optional year for the event (defaults to current year)
 * @return string ICS file content
 */
function generateBirthdayICS($name, $birth_date, $year = null) {
    // If year is not provided, use current year
    if ($year === null) {
        $year = date('Y');
    }
    
    // Extract month and day from birth date
    $month = date('m', strtotime($birth_date));
    $day = date('d', strtotime($birth_date));
    
    // Create event date for the specified year
    $event_date = "{$year}-{$month}-{$day}";
    
    // Calculate age
    $birth_year = date('Y', strtotime($birth_date));
    $age = $year - $birth_year;
    
    // Create summary and description
    $summary = "{$name}'s Birthday";
    $description = "{$name} is celebrating their {$age}th birthday today!";
    
    // Set yearly recurrence
    $recurrence = "FREQ=YEARLY;INTERVAL=1";
    
    // Generate ICS content
    return generateICSContent(
        $summary,
        $description,
        '',
        $event_date,
        $event_date,
        '',
        '',
        true,
        $recurrence
    );
}

/**
 * Generate ICS file content for a church event
 * 
 * @param string $title Event title
 * @param string $description Event description
 * @param string $location Event location
 * @param string $start_date Start date (YYYY-MM-DD format)
 * @param string $end_date End date (YYYY-MM-DD format), defaults to start_date
 * @param string $start_time Start time (HH:MM format), optional
 * @param string $end_time End time (HH:MM format), optional
 * @param bool $all_day Whether this is an all-day event
 * @param string $recurrence Recurrence rule (e.g., "FREQ=WEEKLY;BYDAY=SU"), optional
 * @return string ICS file content
 */
function generateChurchEventICS($title, $description, $location, $start_date, $end_date = '', $start_time = '', $end_time = '', $all_day = false, $recurrence = '') {
    // Generate ICS content
    return generateICSContent(
        $title,
        $description,
        $location,
        $start_date,
        $end_date,
        $start_time,
        $end_time,
        $all_day,
        $recurrence,
        '<EMAIL>'
    );
}

/**
 * Escape special characters in text for ICS format
 * 
 * @param string $text Text to escape
 * @return string Escaped text
 */
function escapeICSText($text) {
    $text = str_replace(array("\\", ";", ","), array("\\\\", "\\;", "\\,"), $text);
    $text = preg_replace('/\r\n|\n|\r/', "\\n", $text);
    return $text;
}

// If called directly with parameters, generate and output ICS file
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    // Check if required parameters are provided
    if (isset($_GET['type']) && isset($_GET['id'])) {
        $type = $_GET['type'];
        $id = intval($_GET['id']);
        
        // Include the configuration file
        require_once '../config.php';
        
        // Database connection - using the connection from config.php
        $conn = $pdo;
        
        try {
            // Generate ICS based on type
            if ($type === 'birthday' && $id > 0) {
                // Get member details
                $stmt = $conn->prepare("SELECT full_name, birth_date FROM members WHERE id = ?");
                $stmt->execute([$id]);
                $member = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($member) {
                    $ics_content = generateBirthdayICS($member['full_name'], $member['birth_date']);
                    echo $ics_content;
                    exit;
                }
            } elseif ($type === 'event' && $id > 0) {
                // Get event details (assuming you have an events table)
                $stmt = $conn->prepare("SELECT title, description, location, start_date, end_date, start_time, end_time, all_day, recurrence FROM events WHERE id = ?");
                $stmt->execute([$id]);
                $event = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($event) {
                    $ics_content = generateChurchEventICS(
                        $event['title'],
                        $event['description'],
                        $event['location'],
                        $event['start_date'],
                        $event['end_date'],
                        $event['start_time'],
                        $event['end_time'],
                        $event['all_day'] == 1,
                        $event['recurrence']
                    );
                    echo $ics_content;
                    exit;
                }
            }
        } catch (PDOException $e) {
            // Log error
            error_log("Error generating ICS file: " . $e->getMessage());
        }
    }
    
    // If we get here, something went wrong
    header('HTTP/1.0 404 Not Found');
    echo "Event not found or invalid parameters.";
    exit;
}
?> 