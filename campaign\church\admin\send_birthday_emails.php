<?php
session_start();
// Add debug output
error_log("Starting send_birthday_emails.php script");

// Check if running from command line
if (php_sapi_name() === 'cli') {
    error_log("Running from command line");
    // Set admin session for CLI
    $_SESSION['admin_id'] = 1;
}

require_once __DIR__ . '/../config.php';
error_log("Config loaded");
require_once __DIR__ . '/../send_birthday_reminders.php';
error_log("BirthdayReminder class loaded");

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';
$sent_count = 0;
$failed_count = 0;

// Create reminder instance for statistics
$reminder = new BirthdayReminder($pdo);

// Get birthday statistics
$birthdaySummary = $reminder->getBirthdaySummary(7); // Get summary for next 7 days
$todayBirthdays = isset($birthdaySummary['days'][0]['count']) ? intval($birthdaySummary['days'][0]['count']) : 0;
$tomorrowBirthdays = isset($birthdaySummary['days'][1]['count']) ? intval($birthdaySummary['days'][1]['count']) : 0;
$upcomingBirthdays = 0;
for ($i = 2; $i < 7; $i++) {
    $upcomingBirthdays += isset($birthdaySummary['days'][$i]['count']) ? intval($birthdaySummary['days'][$i]['count']) : 0;
}

// Test what emails would be sent
$testResults = $reminder->testBirthdayEmails(0, 7); // Today's birthdays and 7-day reminders
$potentialEmails = isset($testResults['total_count']) ? intval($testResults['total_count']) : 0;

// Initialize progress tracking variables
$totalEmailsToSend = $potentialEmails;
$currentEmailsSent = 0;

// Get all birthday templates
$birthday_templates = [];
try {
    $query = "SELECT id, template_name FROM email_templates WHERE is_birthday_template = 1 ORDER BY template_name";
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $birthday_templates = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'Error retrieving birthday templates: ' . $e->getMessage();
    error_log($error);
}

// Get all reminder templates
$reminder_templates = [];
try {
    $query = "SELECT id, template_name FROM email_templates WHERE is_birthday_template = 0 ORDER BY template_name";
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $reminder_templates = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'Error retrieving reminder templates: ' . $e->getMessage();
    error_log($error);
}

// Get admin email from settings
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'admin_email' LIMIT 1");
$stmt->execute();
$admin_email = $stmt->fetchColumn() ?: $emailSettings['sender_email']; // fallback to sender email if not set

// Handle AJAX requests
if (isset($_POST['ajax_request']) && $_POST['ajax_request'] == 1) {
    header('Content-Type: application/json');
    $response = ['success' => false];
    
    // Handle request to get birthday members
    if (isset($_POST['get_birthday_members']) && isset($_POST['days_ahead'])) {
        $days_ahead = intval($_POST['days_ahead']);
        
        try {
            // Get members with birthdays on the specified day
            $birthdayReminder = new BirthdayReminder($pdo);
            $membersData = $birthdayReminder->getUpcomingBirthdays($days_ahead);
            
            // Format the response data
            $members = [];
            if (!empty($membersData)) {
                foreach ($membersData as $member) {
                    // Fix image path to make it relative to admin folder
                    $imagePath = '';
                    if (isset($member['image_path']) && !empty($member['image_path'])) {
                        $imagePath = '../' . ltrim($member['image_path'], '/');
                        // Ensure the image path is properly formatted
                        if (!file_exists($imagePath)) {
                            $imagePath = '../assets/img/default-avatar.png';
                        }
                    } else {
                        $imagePath = '../assets/img/default-avatar.png';
                    }
                    
                    // Calculate age
                    $age = null;
                    if (isset($member['birth_date']) && strlen($member['birth_date']) > 5) {
                        $birthYear = date('Y', strtotime($member['birth_date']));
                        $currentYear = date('Y');
                        $age = $currentYear - $birthYear;
                    }
                    
                    // Format birth date
                    $birthDate = isset($member['birth_date']) 
                        ? date('F j, Y', strtotime($member['birth_date'])) 
                        : 'Not provided';
                    
                    $members[] = [
                        'id' => $member['id'],
                        'name' => $member['full_name'] ?? 'Unknown',
                        'email' => $member['email'] ?? 'No email',
                        'phone' => $member['phone_number'] ?? 'Not provided',
                        'birth_date' => $birthDate,
                        'age' => $age ?? 'Unknown',
                        'image_path' => $imagePath
                    ];
                }
            }
            
            $response = [
                'success' => true,
                'members' => $members,
                'days_ahead' => $days_ahead,
                'count' => count($members)
            ];
        } catch (Exception $e) {
            $response = [
                'success' => false,
                'error' => 'Error retrieving members: ' . $e->getMessage()
            ];
        }
        
        // Ensure we're sending a valid JSON response
        header('Content-Type: application/json');
        
        // Encode with error handling
        $json = json_encode($response);
        if ($json === false) {
            // If JSON encoding fails, send a simpler response
            $json = json_encode([
                'success' => false,
                'error' => 'Error encoding response: ' . json_last_error_msg()
            ]);
        }
        
        echo $json;
        exit;
    }
    
    // Handle quick send birthday emails
    if (isset($_POST['send_birthday_emails'])) {
        try {
            // Create a custom error log file for debugging
            $debug_log_file = __DIR__ . '/../logs/email_debug.log';
            error_log("[" . date('Y-m-d H:i:s') . "] Starting email sending process", 3, $debug_log_file);
            
            $template_id = isset($_POST['birthday_template_id']) ? (int)$_POST['birthday_template_id'] : null;
            $days_range = isset($_POST['birthday_days_range']) ? (int)$_POST['birthday_days_range'] : 0;
            $specific_day = isset($_POST['specific_day']) ? (bool)$_POST['specific_day'] : false;
            
            error_log("[" . date('Y-m-d H:i:s') . "] Parameters: template_id=$template_id, days_range=$days_range, specific_day=" . ($specific_day ? 'true' : 'false'), 3, $debug_log_file);
            
            // Create reminder instance
            $reminder = new BirthdayReminder($pdo);
            
            // Set admin email for notifications
            if (!empty($admin_email)) {
                // Check if the method exists before calling it
                if (method_exists($reminder, 'setAdminEmail')) {
                    $reminder->setAdminEmail($admin_email);
                    error_log("[" . date('Y-m-d H:i:s') . "] Admin email set to: $admin_email", 3, $debug_log_file);
                } else {
                    // If method doesn't exist, set it directly if the property is accessible
                    error_log("[" . date('Y-m-d H:i:s') . "] setAdminEmail method not found, setting adminEmail property directly", 3, $debug_log_file);
                    // Create a new instance with the admin email
                    $reminder = new BirthdayReminder($pdo, $admin_email);
                }
            } else {
                error_log("[" . date('Y-m-d H:i:s') . "] Warning: Admin email not set", 3, $debug_log_file);
            }
            
            // Send birthday emails
            error_log("[" . date('Y-m-d H:i:s') . "] Calling sendBirthdayEmails method", 3, $debug_log_file);
            
            if ($specific_day) {
                // Only send to members with birthdays exactly on the specified day
                $results = $reminder->sendBirthdayEmails($template_id, $days_range, true);
            } else {
                // Send to all members within the range (for bulk sending)
                $results = $reminder->sendBirthdayEmails($template_id, $days_range);
            }
            
            // Log the results for debugging
            error_log("[" . date('Y-m-d H:i:s') . "] Results: " . print_r($results, true), 3, $debug_log_file);
            
            if ($results && isset($results['total_sent'])) {
                $sent_count = $results['total_sent'];
                $failed_count = $results['total_failed'] ?? 0;
                
                if ($sent_count > 0) {
                    $response = [
                        'success' => true,
                        'message' => "$sent_count birthday email(s) have been sent successfully!" . 
                                   ($failed_count > 0 ? " However, $failed_count email(s) failed to send." : ""),
                        'sent_count' => $sent_count,
                        'failed_count' => $failed_count
                    ];
                } else {
                    $response = [
                        'success' => false,
                        'error' => $failed_count > 0 ? 
                            "Failed to send $failed_count birthday email(s). Please check the logs for details." : 
                            "No birthday emails to send for the selected date range."
                    ];
                }
            } else {
                $response = [
                    'success' => false,
                    'error' => isset($results['error']) ? $results['error'] : 
                        "No birthday emails were sent. Please check if there are members with birthdays in the selected range."
                ];
            }
            
            error_log("[" . date('Y-m-d H:i:s') . "] Response: " . print_r($response, true), 3, $debug_log_file);
        } catch (Exception $e) {
            $error_message = 'Error sending birthday emails: ' . $e->getMessage();
            error_log($error_message);
            error_log("[" . date('Y-m-d H:i:s') . "] Exception: " . $e->getMessage() . "\n" . $e->getTraceAsString(), 3, $debug_log_file);
            
            $response = [
                'success' => false,
                'error' => $error_message
            ];
        }
        
        // Ensure we're sending a valid JSON response
        header('Content-Type: application/json');
        
        // Encode with error handling
        $json = json_encode($response);
        if ($json === false) {
            // If JSON encoding fails, send a simpler response
            $json_error = json_last_error_msg();
            error_log("[" . date('Y-m-d H:i:s') . "] JSON encoding error: " . $json_error . " for data: " . print_r($response, true), 3, $debug_log_file);
            
            error_log("JSON encoding error: " . $json_error . " for data: " . print_r($response, true));
            $json = json_encode([
                'success' => false,
                'error' => 'Error encoding response: ' . $json_error
            ]);
        }
        
        error_log("[" . date('Y-m-d H:i:s') . "] Final JSON response: " . $json, 3, $debug_log_file);
        
        echo $json;
        exit;
    }
}

// Process form submission for birthdays
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_birthday_emails'])) {
    try {
        // Create reminder instance
        $reminder = new BirthdayReminder($pdo);
        
        // Set specific birthday template if selected
        $template_id = isset($_POST['birthday_template_id']) && !empty($_POST['birthday_template_id']) 
            ? (int)$_POST['birthday_template_id'] 
            : null;
        
        // Send only birthday emails (today's birthdays)
        $days_range = isset($_POST['birthday_days_range']) ? (int)$_POST['birthday_days_range'] : 0;
        $results = $reminder->sendBirthdayEmails($template_id, $days_range);
        
        // Check if any emails were sent
        if ($results) {
            $sent_count = $results['total_sent'] ?? 0;
            $failed_count = $results['total_failed'] ?? 0;
            
            if ($sent_count > 0) {
                $message = "$sent_count birthday email(s) have been sent successfully!";
                if ($failed_count > 0) {
                    $message .= " However, $failed_count email(s) failed to send.";
                }
            } else if ($failed_count > 0) {
                $error = "Failed to send $failed_count birthday email(s). Please check the logs for details.";
            } else {
                $message = "No birthday emails to send for the selected date range.";
            }
        } else {
            $message = "No birthday emails to send for the selected date range.";
        }
    } catch (Exception $e) {
        $error = 'Error sending birthday emails: ' . $e->getMessage();
        error_log($error);
    }
}

// Process form submission for reminders
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_reminder_emails'])) {
    try {
        // Create reminder instance
        $reminder = new BirthdayReminder($pdo);
        
        // Set specific reminder template if selected
        $template_id = isset($_POST['reminder_template_id']) && !empty($_POST['reminder_template_id']) 
            ? (int)$_POST['reminder_template_id'] 
            : null;
        
        // Send only reminder emails (upcoming birthdays)
        $days_ahead = isset($_POST['reminder_days_ahead']) ? (int)$_POST['reminder_days_ahead'] : 3;
        $results = $reminder->sendBirthdayReminders($template_id, $days_ahead);
        
        // Check if any emails were sent
        if ($results) {
            $sent_count = $results['total_sent'] ?? 0;
            $failed_count = $results['total_failed'] ?? 0;
            
            if ($sent_count > 0) {
                $message = "$sent_count birthday reminder email(s) have been sent successfully!";
                if ($failed_count > 0) {
                    $message .= " However, $failed_count email(s) failed to send.";
                }
            } else if ($failed_count > 0) {
                $error = "Failed to send $failed_count birthday reminder email(s). Please check the logs for details.";
            } else {
                $message = "No birthday reminder emails to send for $days_ahead days ahead.";
            }
        } else {
            $message = "No birthday reminder emails to send for $days_ahead days ahead.";
        }
    } catch (Exception $e) {
        $error = 'Error sending birthday reminder emails: ' . $e->getMessage();
        error_log($error);
    }
}

// Handle the "Send All" email request
if (isset($_POST['send_all_emails'])) {
    // Get template IDs
    $memberReminderTemplateId = isset($_POST['all_member_reminder_template_id']) ? $_POST['all_member_reminder_template_id'] : null;
    $notificationTemplateId = isset($_POST['all_notification_template_id']) ? $_POST['all_notification_template_id'] : null;
    
    // Initialize the birthdayReminder object
    $birthdayReminder = new BirthdayReminder($pdo, $admin_email);
    
    try {
        // Count of successful and failed emails
        $memberReminderCount = 0;
        $notificationCount = 0;
        $failedCount = 0;
        
        // Log the template IDs for debugging
        error_log("Send All Emails - Member Reminder Template ID: " . ($memberReminderTemplateId ? $memberReminderTemplateId : "None"));
        error_log("Send All Emails - Notification Template ID: " . ($notificationTemplateId ? $notificationTemplateId : "None"));
        
        // Get all members with birthdays in the next 7 days
        $upcomingBirthdays = [];
        for ($day = 0; $day <= 7; $day++) {
            $dayMembers = $birthdayReminder->getUpcomingBirthdays($day);
            if (!empty($dayMembers)) {
                error_log("Found " . count($dayMembers) . " members with birthdays in $day days");
                foreach ($dayMembers as $member) {
                    if (!isset($upcomingBirthdays[$member['id']])) {
                        $upcomingBirthdays[$member['id']] = $member;
                        $upcomingBirthdays[$member['id']]['days_ahead'] = $day;
                        error_log("Added member ID {$member['id']} ({$member['full_name']}) with birthday in $day days");
                    }
                }
            }
        }
        
        error_log("Total unique members with birthdays in the next 7 days: " . count($upcomingBirthdays));
        
        // 1. Send member's own birthday reminders
        if ($memberReminderTemplateId && !empty($upcomingBirthdays)) {
            $birthdayReminder->setEmailType('birthday_reminder');
            error_log("Sending birthday reminders to members with upcoming birthdays");
            
            foreach ($upcomingBirthdays as $member) {
                // Get the days ahead for this member
                $daysAhead = $member['days_ahead'] ?? 0;
                error_log("Processing reminder for member ID {$member['id']} ({$member['full_name']}) with birthday in $daysAhead days");
                
                // Use the public method to send reminders for this specific day
                $memberResults = $birthdayReminder->sendBirthdayReminders($memberReminderTemplateId, $daysAhead);
                
                if (isset($memberResults['total_sent']) && $memberResults['total_sent'] > 0) {
                    $memberReminderCount += $memberResults['total_sent'];
                    $failedCount += $memberResults['total_failed'] ?? 0;
                    error_log("Sent {$memberResults['total_sent']} reminders for day $daysAhead, failed: {$memberResults['total_failed']}");
                } else {
                    $failedCount++;
                    error_log("Failed to send reminder for member ID {$member['id']} with birthday in $daysAhead days");
                }
            }
        }
        
        // 2. Send birthday notifications to all members about upcoming birthdays
        if ($notificationTemplateId && !empty($upcomingBirthdays)) {
            $birthdayReminder->setEmailType('notification');
            error_log("Sending birthday notifications to all members about upcoming birthdays");
            
            // Get notification templates
            $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Notification%' ORDER BY template_name");
            $stmt->execute();
            $notification_templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Log available templates
            error_log("Available notification templates: " . count($notification_templates));
            foreach ($notification_templates as $template) {
                error_log("Template ID: " . $template['id'] . ", Name: " . $template['template_name'] . ", Subject: " . $template['subject']);
            }
            
            foreach ($upcomingBirthdays as $birthdayMember) {
                $daysAhead = $birthdayMember['days_ahead'] ?? 0;
                error_log("Processing notifications for birthday member ID {$birthdayMember['id']} ({$birthdayMember['full_name']}) with birthday in $daysAhead days");
                
                $notificationResult = $birthdayReminder->sendMemberBirthdayNotifications(
                    $birthdayMember['id'],
                    $notificationTemplateId,
                    $daysAhead
                );
                
                if (isset($notificationResult['success'])) {
                    $notificationCount += $notificationResult['success'];
                    $failedCount += $notificationResult['failed'] ?? 0;
                    error_log("Sent {$notificationResult['success']} notifications for birthday member ID {$birthdayMember['id']}, failed: {$notificationResult['failed']}");
                } else {
                    $error = "Error sending notifications: " . ($notificationResult['error'] ?? 'Unknown error');
                    error_log($error);
                }
            }
        }
        
        // Set success message if no errors
        if (empty($error)) {
            if ($memberReminderCount > 0 || $notificationCount > 0) {
                $message = "Successfully sent: ";
                if ($memberReminderCount > 0) {
                    $message .= "$memberReminderCount member birthday reminders";
                }
                if ($notificationCount > 0) {
                    $message .= ($memberReminderCount > 0 ? ", " : "") . "$notificationCount birthday notifications to other members";
                }
                if ($failedCount > 0) {
                    $message .= " ($failedCount emails failed to send)";
                }
                error_log("Send All Emails completed: $message");
            } else {
                $message = "No reminders were sent. There may be no upcoming birthdays for the next 7 days.";
                error_log("Send All Emails completed: No reminders sent");
            }
        }
    } catch (Exception $e) {
        $error = "Error sending reminders: " . $e->getMessage();
        error_log($error);
    }
    
    // If it's an AJAX request, return JSON
    if (isset($_POST['ajax_request']) && $_POST['ajax_request'] == 1) {
        // Ensure we're sending a valid JSON response
        header('Content-Type: application/json');
        
        // Make sure we have clean data for JSON encoding
        $response = [
            'success' => empty($error),
            'message' => $message ?? '',
            'error' => $error ?? '',
            'member_reminder_count' => $memberReminderCount ?? 0,
            'notification_count' => $notificationCount ?? 0,
            'failed_count' => $failedCount ?? 0,
            'total' => ($memberReminderCount ?? 0) + ($notificationCount ?? 0) + ($failedCount ?? 0)
        ];
        
        // Encode with error handling
        $json = json_encode($response);
        if ($json === false) {
            // If JSON encoding fails, send a simpler response
            $json = json_encode([
                'success' => false,
                'error' => 'Error encoding response: ' . json_last_error_msg(),
                'message' => 'Your request was processed, but there was an error generating the response.'
            ]);
        }
        
        echo $json;
        exit;
    }
}

// Include pagination component
require_once 'includes/pagination.php';

// Get pagination parameters for email logs
$logs_page = isset($_GET['logs_page']) ? max(1, intval($_GET['logs_page'])) : 1;
$logs_limit = isset($_GET['logs_limit']) ? max(10, min(100, intval($_GET['logs_limit']))) : 20;

// Get email logs for display with pagination
$logs = [];
$logs_pagination = [];
try {
    // Get total count of email logs
    $count_query = "SELECT COUNT(*)
                    FROM email_logs el
                    LEFT JOIN email_templates et ON el.template_id = et.id
                    WHERE (et.is_birthday_template = 1 OR el.email_type = 'birthday' OR el.email_type = 'birthday_reminder')";
    $count_stmt = $pdo->prepare($count_query);
    $count_stmt->execute();
    $total_logs = $count_stmt->fetchColumn();

    // Calculate pagination
    $logs_pagination = calculate_pagination($total_logs, $logs_page, $logs_limit);

    // Get paginated email logs
    $query = "SELECT el.*, m.full_name, m.email, et.template_name, et.is_birthday_template
              FROM email_logs el
              LEFT JOIN members m ON el.member_id = m.id
              LEFT JOIN email_templates et ON el.template_id = et.id
              WHERE (et.is_birthday_template = 1 OR el.email_type = 'birthday' OR el.email_type = 'birthday_reminder')
              ORDER BY el.sent_at DESC
              LIMIT ? OFFSET ?";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(1, $logs_limit, PDO::PARAM_INT);
    $stmt->bindParam(2, $logs_pagination['offset'], PDO::PARAM_INT);
    $stmt->execute();
    $logs = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'Error retrieving email logs: ' . $e->getMessage();
    error_log($error);
}

// Set page variables
$page_title = 'Bulk Birthday Email Sender';
$page_header = 'Bulk Birthday Email Sender';
$page_description = 'Send birthday and reminder emails to members in bulk';

// Include header
include 'includes/header.php';
?>

<div class="container-fluid pt-4 px-4">
    <!-- Help Section -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-question-circle me-2"></i>How to Use This Page</h5>
            <button class="btn btn-sm btn-light" type="button" data-bs-toggle="collapse" data-bs-target="#helpContent">
                <i class="bi bi-chevron-down"></i>
            </button>
        </div>
        <div class="collapse show" id="helpContent">
            <div class="card-body">
                <h6 class="fw-bold mb-3">This page offers three ways to send birthday-related emails:</h6>
                
                <div class="mb-4">
                    <h6 class="text-primary"><i class="bi bi-1-circle me-2"></i>Quick Send (From Birthday Statistics)</h6>
                    <div class="ps-4">
                        <p>Use this method to send emails for a specific day:</p>
                        <ol>
                            <li>Find the date you want in the Birthday Summary table</li>
                            <li>Select an email template from the dropdown</li>
                            <li>Click the "Send" button next to that day</li>
                            <li>Confirm your action in the popup dialog</li>
                        </ol>
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="text-primary"><i class="bi bi-2-circle me-2"></i>Send Birthday Emails (Single Type)</h6>
                    <div class="ps-4">
                        <p>Use this to send birthday emails for a specific time range:</p>
                        <ol>
                            <li>Select a birthday email template (or leave as "Random Template")</li>
                            <li>Choose the days range (today only, next 3 days, etc.)</li>
                            <li>Click "Send Birthday Emails"</li>
                        </ol>
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="text-primary"><i class="bi bi-3-circle me-2"></i>Send All Reminder Emails (Comprehensive)</h6>
                    <div class="ps-4">
                        <p>Use this to send both types of emails (recommended for weekly sending):</p>
                        <ol>
                            <li>Select templates for both:
                                <ul>
                                    <li>Member's Own Birthday Reminder (sent to the birthday person)</li>
                                    <li>Birthday Notifications (sent to other members)</li>
                                </ul>
                            </li>
                            <li>Click "Send All Reminder Emails"</li>
                        </ol>
                    </div>
                </div>

                <div class="alert alert-info">
                    <h6 class="alert-heading"><i class="bi bi-lightbulb me-2"></i>Tips:</h6>
                    <ul class="mb-0">
                        <li>The Birthday Statistics section shows you how many birthdays are coming up</li>
                        <li>Click "Show" next to any day to see the specific members with birthdays</li>
                        <li>Recent Email Logs at the bottom show you what has been sent</li>
                        <li>Green badges indicate successful sends, red badges indicate failures</li>
                        <li>For weekly reminders, use the "Send All Reminder Emails" option</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div id="alerts-container">
        <?php if (!empty($message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
    </div>

    <!-- Add Progress Bar -->
    <div id="emailProgress" class="progress mb-3" style="display: none;">
        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
    </div>
    <div id="progressStatus" class="alert alert-info" style="display: none;"></div>

    <!-- Add Status Messages -->
    <div id="successMessages" class="alert alert-success alert-dismissible fade show" style="display: none;">
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        <span class="message-text"></span>
    </div>
    <div id="errorMessages" class="alert alert-danger alert-dismissible fade show" style="display: none;">
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        <span class="message-text"></span>
    </div>

    <!-- Birthday Statistics Dashboard -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Birthday Statistics</h5>
                    <button class="btn btn-sm btn-outline-secondary" id="refresh-stats">
                        <i class="bi bi-arrow-clockwise me-1"></i> Refresh
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Stats cards -->
                        <div class="col-md-3 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h1 class="display-4 fw-bold"><?php echo $todayBirthdays; ?></h1>
                                    <p class="mb-0">Birthdays Today</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h1 class="display-4 fw-bold"><?php echo $tomorrowBirthdays; ?></h1>
                                    <p class="mb-0">Birthdays Tomorrow</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h1 class="display-4 fw-bold"><?php 
                                        echo is_array($upcomingBirthdays) ? array_sum($upcomingBirthdays) : $upcomingBirthdays; 
                                    ?></h1>
                                    <p class="mb-0">Upcoming (Next 6 Days)</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h1 class="display-4 fw-bold"><?php echo $potentialEmails; ?></h1>
                                    <p class="mb-0">Potential Emails Today</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Birthday Summary (Next 7 Days)</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Date</th>
                                            <th>Day</th>
                                            <th>Members</th>
                                            <th style="width: 300px;">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($birthdaySummary['days'] as $day): ?>
                                            <?php if ($day['count'] > 0): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($day['date']); ?></td>
                                                    <td><?php echo htmlspecialchars($day['date_formatted']); ?></td>
                                                    <td>
                                                        <?php if ($day['count'] > 0): ?>
                                                            <span class="badge bg-primary"><?php echo $day['count']; ?> members</span>
                                                            <button class="btn btn-sm btn-outline-secondary ms-2 show-members" 
                                                                    data-bs-toggle="modal" data-bs-target="#membersModal" 
                                                                    data-day="<?php echo $day['days_ahead']; ?>"
                                                                    data-date="<?php echo htmlspecialchars($day['date_formatted']); ?>">
                                                                <i class="bi bi-people-fill"></i> Show
                                                            </button>
                                                        <?php else: ?>
                                                            <span class="text-muted">No birthdays</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center gap-2">
                                                            <select class="form-select form-select-sm quick-template-select" style="width: 200px;">
                                                                <option value="">-- Select Template --</option>
                                                                <?php foreach ($birthday_templates as $template): ?>
                                                                    <option value="<?php echo $template['id']; ?>">
                                                                        <?php echo htmlspecialchars($template['template_name']); ?>
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            </select>
                                                            <button class="btn btn-sm btn-primary send-quick-email" 
                                                                    data-type="birthday" 
                                                                    data-days="<?php echo $day['days_ahead']; ?>">
                                                                <i class="bi bi-send"></i> Send
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                        
                                        <?php if ($birthdaySummary['total_count'] == 0): ?>
                                            <tr>
                                                <td colspan="4" class="text-center">No birthdays found in the next 7 days</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Members Modal with fixed accessibility -->
    <div class="modal fade" id="membersModal" tabindex="-1" aria-labelledby="membersModalLabel">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="membersModalLabel">Members with birthdays</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="membersModalBody">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status"></div>
                        <p class="mt-2">Loading member details...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 mb-4">
            <!-- Nav tabs -->
            <ul class="nav nav-tabs mb-4" id="emailTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="birthday-tab" data-bs-toggle="tab" data-bs-target="#birthday" 
                            type="button" role="tab" aria-controls="birthday" aria-selected="true">
                        <i class="bi bi-cake2-fill me-2"></i>Send Birthday Emails
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" 
                            type="button" role="tab" aria-controls="all" aria-selected="false">
                        <i class="bi bi-envelope-fill me-2"></i>Send All Emails
                    </button>
                </li>
            </ul>
            
            <!-- Tab panes -->
            <div class="tab-content">
                <!-- Birthday Emails Tab -->
                <div class="tab-pane fade show active" id="birthday" role="tabpanel" aria-labelledby="birthday-tab">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Send Birthday Emails</h5>
                        </div>
                        <div class="card-body">
                            <p>This tool will send birthday emails to members who have their birthday today or within a specific range of days:</p>
                            
                            <form id="birthday-form" method="post" action="">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="birthday_template_id" class="form-label">Select Birthday Email Template:</label>
                                        <select name="birthday_template_id" id="birthday_template_id" class="form-select">
                                            <option value="">-- Random Template --</option>
                                            <?php foreach ($birthday_templates as $template): ?>
                                                <option value="<?php echo htmlspecialchars($template['id']); ?>">
                                                    <?php echo htmlspecialchars($template['template_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="birthday_days_range" class="form-label">Days Range:</label>
                                        <select name="birthday_days_range" id="birthday_days_range" class="form-select">
                                            <option value="0">Today only</option>
                                            <option value="1">Today and tomorrow</option>
                                            <option value="2">Next 3 days</option>
                                            <option value="5">Next 6 days</option>
                                            <option value="7">Next week</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <input type="hidden" name="send_birthday_emails" value="1">
                                <button type="submit" id="birthday-submit-btn" class="btn btn-primary">
                                    <span id="birthday-spinner" class="spinner-border spinner-border-sm d-none me-2" role="status" aria-hidden="true"></span>
                                    <i class="bi bi-envelope-fill me-2"></i>Send Birthday Emails
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- All Emails Tab -->
                <div class="tab-pane fade" id="all" role="tabpanel" aria-labelledby="all-tab">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Send All Reminder Emails</h5>
                        </div>
                        <div class="card-body">
                            <p>This tool will send two types of reminder emails:</p>
                            
                            <ol>
                                <li><strong>Member's Own Birthday Reminders:</strong> Sent directly to members with upcoming birthdays</li>
                                <li><strong>Member Birthday Notifications:</strong> Sent to all members about another member's upcoming birthday</li>
                            </ol>
                            
                            <form id="all-form" method="post" action="">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="all_member_reminder_template_id" class="form-label">Select Member's Own Birthday Reminder Template:</label>
                                        <select name="all_member_reminder_template_id" id="all_member_reminder_template_id" class="form-select">
                                            <option value="">-- Random Template --</option>
                                            <?php foreach ($reminder_templates as $template): ?>
                                                <option value="<?php echo htmlspecialchars($template['id']); ?>">
                                                    <?php echo htmlspecialchars($template['template_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <small class="form-text text-muted">This template will be sent directly to members about their own upcoming birthday.</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="all_notification_template_id" class="form-label">Select Member Birthday Notification Template:</label>
                                        <select name="all_notification_template_id" id="all_notification_template_id" class="form-select">
                                            <option value="">-- Random Template --</option>
                                            <?php 
                                            // Get notification templates
                                            $notification_templates = [];
                                            $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Notification%' ORDER BY template_name");
                                            $stmt->execute();
                                            $notification_templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                            
                                            // Log available templates
                                            error_log("Available notification templates: " . count($notification_templates));
                                            foreach ($notification_templates as $template) {
                                                error_log("Template ID: " . $template['id'] . ", Name: " . $template['template_name'] . ", Subject: " . $template['subject']);
                                            }
                                            
                                            foreach ($notification_templates as $template): 
                                            ?>
                                                <option value="<?php echo htmlspecialchars($template['id']); ?>">
                                                    <?php echo htmlspecialchars($template['template_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <small class="form-text text-muted">This template will be sent to all members about another member's upcoming birthday.</small>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    This will send reminders and notifications for all birthdays in the next 7 days.
                                </div>
                                
                                <input type="hidden" name="send_all_emails" value="1">
                                <button type="submit" id="all-submit-btn" class="btn btn-success">
                                    <span id="all-spinner" class="spinner-border spinner-border-sm d-none me-2" role="status" aria-hidden="true"></span>
                                    <i class="bi bi-envelope-fill me-2"></i>Send All Reminder Emails
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Recent Birthday Email Logs</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>Date/Time</th>
                            <th>Member</th>
                            <th>Email</th>
                            <th>Template</th>
                            <th>Type</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($logs)): ?>
                            <tr>
                                <td colspan="6" class="text-center">No email logs found</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($logs as $log): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($log['sent_at'] ?? 'Unknown'); ?></td>
                                    <td><?php echo htmlspecialchars($log['full_name'] ?? 'Unknown'); ?></td>
                                    <td><?php echo htmlspecialchars($log['email'] ?? 'Unknown'); ?></td>
                                    <td><?php echo htmlspecialchars($log['template_name'] ?? 'Unknown'); ?></td>
                                    <td>
                                        <?php if (isset($log['is_birthday_template']) && $log['is_birthday_template'] == 1): ?>
                                            <span class="badge bg-primary">Birthday</span>
                                        <?php elseif (isset($log['email_type']) && $log['email_type'] == 'birthday'): ?>
                                            <span class="badge bg-primary">Birthday</span>
                                        <?php elseif (isset($log['email_type']) && $log['email_type'] == 'birthday_reminder'): ?>
                                            <span class="badge bg-info">Birthday Reminder</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Other</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (isset($log['status']) && ($log['status'] == 'success' || $log['status'] == 'sent')): ?>
                                            <span class="badge bg-success">Success</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger" title="<?php echo htmlspecialchars($log['error_message'] ?? ''); ?>">Failed</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <?php if (!empty($logs_pagination) && $logs_pagination['total_pages'] > 1): ?>
                <?php
                // Set global variable for JavaScript
                $GLOBALS['total_pages'] = $logs_pagination['total_pages'];

                // Generate pagination
                echo generate_pagination(
                    $logs_pagination['current_page'],
                    $logs_pagination['total_pages'],
                    $logs_pagination['total_records'],
                    $logs_pagination['records_per_page'],
                    'send_birthday_emails.php',
                    [], // No additional URL parameters to preserve
                    'logs_page'
                );
                ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmSendModal" tabindex="-1" aria-labelledby="confirmSendModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmSendModalLabel">Confirm Send Emails</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    Are you sure you want to send birthday emails for this day?
                </div>
                <p class="mb-0">This action will:</p>
                <ul class="mt-2">
                    <li>Send emails to all members with birthdays on the selected day</li>
                    <li>Use the selected template for all emails</li>
                    <li>Process immediately and cannot be undone</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmSendBtn">
                    <i class="bi bi-send me-2"></i>Yes, Send Emails
                </button>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 

<!-- Move JavaScript here after jQuery is loaded -->
<script>
$(document).ready(function() {
    let emailsInProgress = false;
    let currentSendData = null;
    let currentSendButton = null;

    // Add error handling for AJAX requests
    function handleAjaxError(xhr, status, error) {
        console.error('AJAX Error:', {
            status: status,
            statusText: xhr.statusText,
            responseText: xhr.responseText,
            error: error
        });
        
        let errorMessage = 'An error occurred while processing your request.';
        let additionalInfo = '';
        
        try {
            // Try to parse the response as JSON
            const response = JSON.parse(xhr.responseText);
            if (response.error) {
                errorMessage = response.error;
            }
        } catch (e) {
            console.error('Error parsing JSON response:', e);
            
            // If not JSON, check status codes and response text
            if (xhr.status === 500) {
                errorMessage = 'Internal Server Error: ' + error;
                additionalInfo = 'The server encountered an error. Please check the server logs for details.';
            } else if (xhr.status === 404) {
                errorMessage = 'Request failed: Page not found';
            } else if (xhr.status === 403) {
                errorMessage = 'Access denied. Please refresh the page and try again.';
            } else if (xhr.status === 200) {
                // Status 200 but error in parsing - check if emails were actually sent
                errorMessage = 'There was an issue with the server response.';
                
                // Check if the response contains any indication of success
                if (xhr.responseText.includes('success') || 
                    xhr.responseText.includes('sent successfully') || 
                    xhr.responseText.includes('email(s) have been sent')) {
                    additionalInfo = 'Your emails may have been sent successfully. Please check the admin notification email to confirm.';
                } else {
                    additionalInfo = 'There may have been an issue sending emails. Please check the server logs for details.';
                }
            }
        }
        
        showError(errorMessage, additionalInfo);
        console.debug('Full error details:', {
            xhr: xhr,
            status: status,
            error: error
        });
    }

    function updateProgress(current, total) {
        try {
            const percentage = Math.round((current / total) * 100);
            $('#emailProgress')
                .show()
                .find('.progress-bar')
                .css('width', percentage + '%')
                .attr('aria-valuenow', percentage)
                .text(percentage + '%');
            
            $('#progressStatus')
                .show()
                .html(`
                    <i class="bi bi-info-circle me-2"></i>
                    Sending emails: ${current} of ${total} (${percentage}%)
                `);
        } catch (error) {
            console.error('Error updating progress:', error);
        }
    }

    function showSuccess(message) {
        try {
            const alert = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            $('#alerts-container').html(alert);
            $('html, body').animate({ scrollTop: 0 }, 'slow');
        } catch (error) {
            console.error('Error showing success message:', error);
        }
    }

    function showError(message, additionalInfo = '') {
        try {
            let alertContent = `
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            
            if (additionalInfo) {
                alertContent += `<div class="mt-2 small">${additionalInfo}</div>`;
            }
            
            alertContent += `</div>`;
            
            $('#alerts-container').html(alertContent);
            $('html, body').animate({ scrollTop: 0 }, 'slow');
            
            // Log error to console for debugging
            console.error('Error occurred:', message);
        } catch (error) {
            console.error('Error showing error message:', error);
        }
    }

    function resetProgress() {
        $('#emailProgress').hide();
        $('#progressStatus').hide();
        emailsInProgress = false;
    }

    // Modify AJAX error handling in form submission
    $('form').on('submit', function(e) {
        const formId = $(this).attr('id');
        if (formId === 'birthday-form' || formId === 'all-form') {
            e.preventDefault();
            
            if (emailsInProgress) {
                showError('Email sending is already in progress');
                return false;
            }

            emailsInProgress = true;
            const $form = $(this);
            const $submitBtn = $form.find('button[type="submit"]');
            const originalBtnText = $submitBtn.html();

            try {
                // Show loading state
                $submitBtn.prop('disabled', true)
                         .html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Sending...');

                // Reset and show initial progress
                $('#alerts-container').empty();
                updateProgress(0, <?php echo $totalEmailsToSend; ?>);

                let progressInterval = setInterval(function() {
                    const currentWidth = parseInt($('#emailProgress .progress-bar').css('width'));
                    const totalWidth = parseInt($('#emailProgress').css('width'));
                    const currentPercentage = Math.round((currentWidth / totalWidth) * 100);
                    
                    if (currentPercentage < 90) {
                        updateProgress(currentPercentage + 1, 100);
                    }
                }, 500);

                $.ajax({
                    url: window.location.href,
                    type: 'POST',
                    data: $form.serialize() + '&ajax_request=1',
                    dataType: 'json',
                    success: function(response) {
                        clearInterval(progressInterval);
                        
                        if (response.success) {
                            updateProgress(100, 100);
                            showSuccess(response.message);
                            
                            if (response.member_reminder_count !== undefined) {
                                const total = response.total || <?php echo $totalEmailsToSend; ?>;
                                const sent = response.member_reminder_count + response.notification_count;
                                updateProgress(sent, total);
                            }
                            
                            setTimeout(() => location.reload(), 3000);
                        } else {
                            showError(response.error || 'Failed to send emails');
                        }
                    },
                    error: function(xhr, status, error) {
                        clearInterval(progressInterval);
                        
                        // Check if the status is 200 but there was a parsing error
                        if (xhr.status === 200) {
                            // Try to determine if emails were sent by examining the response text
                            let responseText = xhr.responseText || '';
                            let emailsSent = responseText.includes('success') || 
                                            responseText.includes('sent successfully') || 
                                            responseText.includes('email(s) have been sent');
                            
                            let message = emailsSent ? 
                                'Your emails may have been sent successfully, but there was an issue with the server response.' :
                                'There was an issue with the server response. Emails may not have been sent.';
                            
                            let additionalInfo = emailsSent ?
                                'Please check the admin notification email to confirm which emails were sent.' :
                                'Please check the server logs for details on what went wrong.';
                            
                            showError(message, additionalInfo);
                            
                            // Add a refresh button
                            $('#alerts-container .alert').append(`
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-outline-primary refresh-page">
                                        <i class="bi bi-arrow-clockwise me-1"></i>Refresh Page
                                    </button>
                                </div>
                            `);
                            
                            // Add click handler for the refresh button
                            $('.refresh-page').click(function() {
                                location.reload();
                            });
                        } else {
                            handleAjaxError(xhr, status, error);
                        }
                    },
                    complete: function() {
                        clearInterval(progressInterval);
                        $submitBtn.prop('disabled', false).html(originalBtnText);
                        setTimeout(resetProgress, 3000);
                    }
                });
            } catch (error) {
                console.error('Error in form submission:', error);
                showError('An unexpected error occurred: ' + error.message);
                $submitBtn.prop('disabled', false).html(originalBtnText);
                emailsInProgress = false;
            }

            return false;
        }
    });

    // Quick send email handler
    $('.send-quick-email').click(function() {
        const btn = $(this);
        const daysAhead = btn.data('days');
        const templateSelect = btn.closest('td').find('.quick-template-select');
        const templateId = templateSelect.val();
        const date = btn.closest('tr').find('td:first').text();
        
        if (!templateId) {
            showError('Please select a template first');
            return;
        }
        
        currentSendData = {
            templateId: templateId,
            daysAhead: daysAhead,
            date: date
        };
        currentSendButton = btn;
        
        $('#confirmSendModal .modal-body .alert-info').html(`
            <i class="bi bi-info-circle me-2"></i>
            Are you sure you want to send birthday emails to members with birthdays on <strong>${date}</strong>?
        `);
        
        $('#confirmSendModal').modal('show');
    });
    
    // Handle confirmation modal submit
    $('#confirmSendBtn').click(function() {
        if (!currentSendData || !currentSendButton) return;
        
        const btn = currentSendButton;
        const templateSelect = btn.closest('td').find('.quick-template-select');
        
        // Show loading state
        const originalHtml = btn.html();
        btn.prop('disabled', true)
           .html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...');
        
        // Hide the modal
        $('#confirmSendModal').modal('hide');
        
        // Reset and show initial progress
        $('#alerts-container').empty();
        updateProgress(0, 100);

        let progressInterval = setInterval(function() {
            const currentWidth = parseInt($('#emailProgress .progress-bar').css('width'));
            const totalWidth = parseInt($('#emailProgress').css('width'));
            const currentPercentage = Math.round((currentWidth / totalWidth) * 100);
            
            if (currentPercentage < 90) {
                updateProgress(currentPercentage + 1, 100);
            }
        }, 500);
        
        $.ajax({
            url: window.location.href,
            method: 'POST',
            data: {
                ajax_request: 1,
                send_birthday_emails: 1,
                birthday_template_id: currentSendData.templateId,
                birthday_days_range: currentSendData.daysAhead,
                specific_day: true
            },
            dataType: 'json',
            success: function(response) {
                clearInterval(progressInterval);
                
                if (response.success) {
                    updateProgress(100, 100);
                    showSuccess(response.message);
                    templateSelect.val('');
                    setTimeout(() => location.reload(), 3000);
                } else {
                    showError(response.error || 'Unknown error occurred');
                }
            },
            error: function(xhr, status, error) {
                clearInterval(progressInterval);
                
                // Check if the status is 200 but there was a parsing error
                if (xhr.status === 200) {
                    // Try to determine if emails were sent by examining the response text
                    let responseText = xhr.responseText || '';
                    let emailsSent = responseText.includes('success') || 
                                    responseText.includes('sent successfully') || 
                                    responseText.includes('email(s) have been sent');
                    
                    let message = emailsSent ? 
                        'Your emails may have been sent successfully, but there was an issue with the server response.' :
                        'There was an issue with the server response. Emails may not have been sent.';
                    
                    let additionalInfo = emailsSent ?
                        'Please check the admin notification email to confirm which emails were sent.' :
                        'Please check the server logs for details on what went wrong.';
                    
                    showError(message, additionalInfo);
                    
                    // Add a refresh button
                    $('#alerts-container .alert').append(`
                        <div class="mt-2">
                            <button class="btn btn-sm btn-outline-primary refresh-page">
                                <i class="bi bi-arrow-clockwise me-1"></i>Refresh Page
                            </button>
                        </div>
                    `);
                    
                    // Add click handler for the refresh button
                    $('.refresh-page').click(function() {
                        location.reload();
                    });
                } else {
                    handleAjaxError(xhr, status, error);
                }
            },
            complete: function() {
                clearInterval(progressInterval);
                btn.prop('disabled', false).html(originalHtml);
                setTimeout(resetProgress, 3000);
            }
        });
    });

    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Pagination JavaScript for email logs
    window.changePaginationLimit = function(newLimit) {
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('logs_limit', newLimit);
        urlParams.set('logs_page', '1'); // Reset to first page
        window.location.href = 'send_birthday_emails.php?' + urlParams.toString();
    };

    window.goToPage = function(pageNumber) {
        const totalPages = <?php echo isset($logs_pagination['total_pages']) ? $logs_pagination['total_pages'] : 1; ?>;
        pageNumber = parseInt(pageNumber);

        if (pageNumber < 1 || pageNumber > totalPages || isNaN(pageNumber)) {
            alert('Please enter a valid page number between 1 and ' + totalPages);
            return;
        }

        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('logs_page', pageNumber);
        window.location.href = 'send_birthday_emails.php?' + urlParams.toString();
    };

    // Show members modal with improved content
    $('.show-members').click(function() {
        const day = $(this).data('day');
        const date = $(this).data('date');
                
        // Update modal title
        $('#membersModalLabel').text('Members with birthdays on ' + date);
        
        // Clear previous content and show loading
        $('#membersModalBody').html(`
            <div class="text-center p-3">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">Loading members...</p>
            </div>
        `);
        
        // Show modal immediately
        $('#membersModal').modal('show');
        
        // Load members via AJAX
        $.ajax({
            url: window.location.href,
            method: 'POST',
            data: {
                ajax_request: 1,
                get_birthday_members: 1,
                days_ahead: day
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    if (!response.members || response.members.length === 0) {
                        $('#membersModalBody').html(`
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                No members found with birthdays on this date.
                            </div>
                        `);
                        return;
                    }
                    
                    let html = '<div class="row">';
                    response.members.forEach(function(member) {
                        // Create image HTML with error handling
                        let imgHtml = `
                            <img src="${member.image_path}" 
                                 class="rounded-circle img-thumbnail" 
                                 width="120" height="120" 
                                 style="object-fit: cover;"
                                 onerror="this.onerror=null; this.src='../assets/img/default-avatar.png';"
                                 alt="${member.name}'s photo">`;
                        
                        html += `
                            <div class="col-md-6 mb-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <div class="text-center mb-3">
                                            ${imgHtml}
                                            <h5 class="mt-3 mb-0">${member.name}</h5>
                                            <p class="text-muted small mb-0">Age: ${member.age}</p>
                                        </div>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <tr>
                                                    <th><i class="bi bi-envelope me-2"></i>Email:</th>
                                                    <td>${member.email}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="bi bi-phone me-2"></i>Phone:</th>
                                                    <td>${member.phone}</td>
                                                </tr>
                                                <tr>
                                                    <th><i class="bi bi-calendar-date me-2"></i>Birth Date:</th>
                                                    <td>${member.birth_date}</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    
                    $('#membersModalBody').html(html);
                } else {
                    $('#membersModalBody').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            ${response.error || 'Error loading members information.'}
                        </div>
                    `);
                }
            },
            error: function(xhr, status, error) {
                $('#membersModalBody').html(`
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <p class="mb-0">Error loading member details. Please try again.</p>
                        <small class="d-block mt-2">${error}</small>
                    </div>
                `);
            }
        });
    });
});
</script>