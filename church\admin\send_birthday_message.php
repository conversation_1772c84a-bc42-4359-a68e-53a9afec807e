<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Set default response
$response = [
    'success' => false,
    'message' => 'An error occurred while sending the message.'
];

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate required fields
    if (
        empty($_POST['member_id']) || 
        empty($_POST['template_id']) || 
        empty($_POST['subject']) || 
        empty($_POST['recipient_email']) || 
        empty($_POST['recipient_name'])
    ) {
        $_SESSION['error'] = 'All required fields must be filled out.';
        header('Location: birthday.php');
        exit;
    }

    try {
        // Get form data
        $member_id = $_POST['member_id'];
        $template_id = $_POST['template_id'];
        $subject = $_POST['subject'];
        $custom_message = $_POST['custom_message'] ?? '';
        $recipient_email = $_POST['recipient_email'];
        $recipient_name = $_POST['recipient_name'];

        // Get the email template
        $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$template_id]);
        $template = $stmt->fetch();

        if (!$template) {
            $_SESSION['error'] = 'Email template not found.';
            header('Location: birthday.php');
            exit;
        }

        // Get member details for image path
        $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$member_id]);
        $member = $stmt->fetch();

        if (!$member) {
            // Create a basic member data array if not found
            $member = [
                'full_name' => $recipient_name,
                'email' => $recipient_email,
                'phone_number' => '',
                'image_path' => '' // No image available
            ];
        }

        // Replace placeholders in the template using the standardized function
        $subject = replaceTemplatePlaceholders($template['subject'], $member);
        $body = replaceTemplatePlaceholders($template['content'], $member);
        
        // Add custom message if provided
        if (!empty($custom_message)) {
            $body = str_replace('{custom_message}', $custom_message, $body);
        } else {
            $body = str_replace('{custom_message}', '', $body);
        }

        // Send the email
        if (sendEmail($recipient_email, $recipient_name, $subject, $body, true, $member)) {
            // Log the email
            $stmt = $pdo->prepare("INSERT INTO email_logs (member_id, template_id, subject, status, sent_at) VALUES (?, ?, ?, 'success', NOW())");
            $stmt->execute([$member_id, $template_id, $subject]);
            
            // Generate a unique tracking ID
            $tracking_id = uniqid('track_', true);
            
            // Create tracking record
            $stmt = $pdo->prepare("INSERT INTO email_tracking (member_id, tracking_id, email_type, sent_at) VALUES (?, ?, 'birthday', NOW())");
            $stmt->execute([$member_id, $tracking_id]);
            
            $_SESSION['message'] = "Birthday message sent successfully to $recipient_name.";
        } else {
            // Log the failed email
            global $last_email_error;
            $error_message = $last_email_error ?? 'Unknown error';
            
            $stmt = $pdo->prepare("INSERT INTO email_logs (member_id, template_id, subject, status, error_message, sent_at) VALUES (?, ?, ?, 'failed', ?, NOW())");
            $stmt->execute([$member_id, $template_id, $subject, $error_message]);
            
            $_SESSION['error'] = "Failed to send email. Error: $error_message";
        }
    } catch (Exception $e) {
        $_SESSION['error'] = 'Error: ' . $e->getMessage();
    }
}

// Redirect back to birthday page
header('Location: birthday.php');
exit; 