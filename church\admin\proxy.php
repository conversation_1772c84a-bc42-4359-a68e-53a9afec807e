<?php
session_start();
require_once '../config.php';

// Define SECURE_CRON_KEY if not already defined
if (!defined('SECURE_CRON_KEY')) {
    define('SECURE_CRON_KEY', 'fac_2024_secure_cron_8x9q2p5m');
}

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
    exit;
}

// Handle cron job check
if (isset($_GET['action']) && $_GET['action'] === 'check_cron') {
    header('Content-Type: application/json');
    
    try {
        // Construct the cron URL
        $cronUrl = SITE_URL . '/birthday_reminders.php?format=json&check=1&cron_key=' . SECURE_CRON_KEY;
        
        // Initialize cURL
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $cronUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => 5,
            CURLOPT_SSL_VERIFYPEER => false, // For development only
            CURLOPT_USERAGENT => 'Church Admin Panel Cron Check',
            CURLOPT_HTTPHEADER => [
                'X-Requested-With: XMLHttpRequest',
                'Cache-Control: no-cache',
                'X-Cron-Check: true'
            ]
        ]);
        
        // Execute cURL request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        curl_close($ch);
        
        // Check for cURL errors
        if ($error) {
            throw new Exception("cURL Error: " . $error);
        }
        
        // Check HTTP response code
        if ($httpCode !== 200) {
            throw new Exception("Could not access cron job endpoint. HTTP Code: " . $httpCode);
        }
        
        // Check if response is HTML instead of JSON
        if (strpos($contentType, 'text/html') !== false || strpos($response, '<!DOCTYPE') !== false || strpos($response, '<html') !== false) {
            throw new Exception("Received HTML response instead of JSON. The cron endpoint might be returning an error page.");
        }
        
        // Try to decode JSON response
        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid JSON response from cron endpoint: " . json_last_error_msg() . ". Response starts with: " . substr($response, 0, 50));
        }
        
        // Add debug information in development
        if ($environment !== 'production') {
            $data['debug'] = [
                'url' => $cronUrl,
                'http_code' => $httpCode,
                'response_size' => strlen($response),
                'content_type' => $contentType
            ];
        }
        
        echo json_encode($data);
        
    } catch (Exception $e) {
        $error = [
            'status' => 'error',
            'message' => $e->getMessage()
        ];
        
        // Add debug information in development
        if ($environment !== 'production') {
            $error['debug'] = [
                'url' => $cronUrl ?? null,
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'response_preview' => isset($response) ? substr($response, 0, 200) : null,
                'content_type' => $contentType ?? null
            ];
        }
        
        echo json_encode($error);
    }
    exit;
}

// Invalid action
header('Content-Type: application/json');
echo json_encode(['status' => 'error', 'message' => 'Invalid action']);
exit; 