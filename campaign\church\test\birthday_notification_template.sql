USE churchdb;

UPDATE email_templates 
SET content = '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        .email-container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; padding: 20px; }
        .header { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        .info-box { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 25px 0; }
        .info-item { margin: 5px 0; }
        .button-container { text-align: center; margin: 30px 0; }
        .button { display: inline-block; background-color: #3498db; color: white; padding: 12px 25px; border-radius: 4px; text-decoration: none; }
        .footer { margin-top: 30px; }
    </style>
</head>
<body>
    <div class="email-container">
        <h2 class="header">Birthday Celebration!</h2>
        <p>Dear {recipient_full_name},</p>
        <p>We''re excited to let you know that {birthday_member_full_name} will be celebrating their birthday {days_text}!</p>
        <div class="info-box">
            <p class="info-item"><strong>Date:</strong> {upcoming_birthday_formatted}</p>
            <p class="info-item"><strong>Turning:</strong> {birthday_member_age} years</p>
        </div>
        <div class="button-container">
            <div class="button">Happy Birthday Wishes!</div>
        </div>
        <p class="footer">
            Blessings,<br>
            Church Team
        </p>
    </div>
</body>
</html>',
    template_category = 'notification',
    is_birthday_template = 0
WHERE template_name = 'Member Birthday Notification'; 