<?php
// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Function to check if a table exists
function tableExists($conn, $tableName) {
    try {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$tableName]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        error_log("Error checking if table exists: " . $e->getMessage());
        return false;
    }
}

// Function to check if a setting exists
function settingExists($conn, $key) {
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM payment_settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        return $stmt->fetchColumn() > 0;
    } catch (PDOException $e) {
        error_log("Error checking if setting exists: " . $e->getMessage());
        return false;
    }
}

// Function to update a payment setting
function updatePaymentSetting($conn, $key, $value) {
    try {
        $stmt = $conn->prepare("INSERT INTO payment_settings (setting_key, setting_value) 
                               VALUES (?, ?) 
                               ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        $stmt->execute([$key, $value]);
    } catch (PDOException $e) {
        error_log("Error updating payment setting: " . $e->getMessage());
    }
}

// Check if payment_settings table exists
if (!tableExists($conn, 'payment_settings')) {
    try {
        // Create payment_settings table
        $sql = "CREATE TABLE payment_settings (
            id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(255) NOT NULL UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
        $conn->exec($sql);
        
        echo "Created payment_settings table<br>";
    } catch (PDOException $e) {
        echo "Error creating payment_settings table: " . $e->getMessage() . "<br>";
        exit;
    }
}

// Default settings
$defaultSettings = [
    'paypal_enabled' => '1',
    'paypal_client_id' => '',
    'paypal_client_secret' => '',
    'paypal_sandbox_mode' => '1',
    'stripe_enabled' => '0',
    'stripe_publishable_key' => '',
    'stripe_secret_key' => '',
    'stripe_webhook_secret' => '',
    'stripe_test_mode' => '1',
    'donations_enabled' => '1',
    'birthday_gifts_enabled' => '1',
    'minimum_donation_amount' => '5',
    'default_currency' => 'USD',
    'donation_success_message' => 'Thank you for your generous donation!',
    'donation_email_template' => 'Thank you for your donation of {amount} {currency}. Your support helps us continue our mission.',
    'payment_notification_email' => ''
];

// Insert default settings if they don't exist
foreach ($defaultSettings as $key => $value) {
    if (!settingExists($conn, $key)) {
        updatePaymentSetting($conn, $key, $value);
        echo "Added default setting: $key = $value<br>";
    }
}

echo "Payment settings check completed.<br>";
echo "<a href='payment_integration.php'>Go to Payment Integration Settings</a>";
?> 