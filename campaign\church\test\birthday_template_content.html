<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Birthday Celebration</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Helvetica Neue', <PERSON>l, sans-serif;
            background-color: #f9f9f9;
            color: #333333;
            line-height: 1.5;
        }
        .container {
            max-width: 600px;
            margin: 30px auto;
            padding: 0 20px;
        }
        .card {
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 12px 30px rgba(0, 122, 255, 0.15);
            overflow: hidden;
            width: 100%;
        }
        .header {
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            padding: 30px;
            text-align: center;
            color: #ffffff;
            border-radius: 20px 20px 0 0;
        }
        .header h1 {
            margin: 0;
            font-size: 32px;
            font-weight: 700;
        }
        .header p {
            margin: 10px 0 0;
            font-size: 16px;
            opacity: 0.85;
        }
        .content {
            padding: 30px;
            text-align: center;
        }
        .greeting {
            font-size: 18px;
            margin: 0 0 20px;
            color: #555555;
        }
        .profile {
            margin: 20px 0;
        }
        .photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 5px solid #ffffff;
            box-shadow: 0 10px 30px rgba(0, 122, 255, 0.2);
            overflow: hidden;
            margin: 0 auto;
        }
        .photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .member-name {
            font-size: 24px;
            font-weight: 700;
            color: #6a11cb;
            margin: 15px 0;
        }
        .birthday-details {
            background: #e9f4ff;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            display: inline-block;
            width: 100%;
            text-align: left;
        }
        .birthday-date {
            font-size: 18px;
            font-weight: 600;
            color: #2575fc;
            margin-bottom: 6px;
        }
        .birthday-age {
            font-size: 16px;
            font-weight: bold;
            color: #ffffff;
            background: #6a11cb;
            padding: 6px 14px;
            border-radius: 30px;
            display: inline-block;
            margin-top: 10px;
        }
        .countdown {
            font-size: 16px;
            color: #555555;
            margin-top: 12px;
        }
        .suggestions {
            background: #e9f4ff;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            text-align: left;
        }
        .suggestions h3 {
            color: #2575fc;
            font-size: 18px;
            margin: 0 0 10px;
        }
        .suggestions ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .suggestions li {
            padding: 8px 0;
            border-bottom: 1px solid #d3e6ff;
            font-size: 16px;
            color: #555555;
        }
        .suggestions li:last-child {
            border-bottom: none;
        }
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            color: #ffffff;
            text-decoration: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            box-shadow: 0 6px 18px rgba(0, 122, 255, 0.3);
        }
        .footer {
            background: #f9f9f9;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #777777;
            border-top: 1px solid #d3e6ff;
        }
        .church-name {
            color: #2575fc;
            font-weight: bold;
        }
        .unsubscribe {
            margin-top: 10px;
            font-size: 12px;
        }
        .unsubscribe a {
            color: #777777;
            text-decoration: none;
        }
        @media (max-width: 480px) {
            .container {
                padding: 0 10px;
            }
            .header {
                padding: 20px;
            }
            .header h1 {
                font-size: 24px;
            }
            .content {
                padding: 20px;
            }
            .photo {
                width: 120px;
                height: 120px;
            }
            .member-name {
                font-size: 20px;
            }
            .action-button {
                padding: 10px 25px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="header">
                <h1>🎉 Birthday Celebration! 🎉</h1>
                <p>Join us in celebrating a special member of our church family</p>
            </div>
            
            <div class="content">
                <div class="greeting">
                    <p>Dear {recipient_full_name},</p>
                    <p>We're excited to celebrate {birthday_member_full_name}'s birthday {days_text}!</p>
                </div>
                
                <div class="profile">
                    <div class="member-name">{birthday_member_full_name}</div>
                </div>
                
                <div class="birthday-details">
                    <div class="birthday-date">Birthday: {upcoming_birthday_formatted}</div>
                    <div class="birthday-age">Turning {birthday_member_age} years</div>
                </div>
                
                <div class="suggestions">
                    <h3>Ways to Bless {birthday_member_full_name}:</h3>
                    <ul>
                        <li>Send a heartfelt birthday message</li>
                        <li>Pray for their growth and blessings in the coming year</li>
                        <li>Share a scripture that encourages them</li>
                        <li>Consider gifting something meaningful</li>
                    </ul>
                </div>
                
                <a href="mailto:{birthday_member_email}?subject=Happy%20Birthday%20{birthday_member_full_name}!&body=Dear%20{birthday_member_full_name},%0D%0A%0D%0AWishing%20you%20a%20joyous%20birthday%20filled%20with%20love%20and%20blessings%20from%20your%20Freedom%20Assembly%20Church%20family!%0D%0A%0D%0A[Add%20your%20personal%20message]%0D%0A%0D%0ABlessings,%0D%0A{recipient_full_name}" class="action-button">Send Birthday Wishes</a>
            </div>
            
            <div class="footer">
                <p>With blessings from <span class="church-name">Freedom Assembly Church</span></p>
                <div class="unsubscribe">
                    <a href="{unsubscribe_link}">Unsubscribe from these updates</a>
                </div>
            </div>
        </div>
    </div>
    {tracking_pixel}
</body>
</html> 