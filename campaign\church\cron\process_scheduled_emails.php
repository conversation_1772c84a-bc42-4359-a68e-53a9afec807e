<?php
/**
 * Process Scheduled Emails Cron Job
 * 
 * This script is designed to be run via a cron job to process scheduled email campaigns.
 * It handles sending emails according to the schedule settings, including rate limiting.
 * 
 * Dynamic Cron Job Command (run every 5 minutes - replace YOUR_DOMAIN.COM/YOUR_PATH):
 * wget -q -O /dev/null "https://YOUR_DOMAIN.COM/YOUR_PATH/cron/process_scheduled_emails.php?cron_key=fac_2024_secure_cron_8x9q2p5m"
 *
 * Example for freedomassemblydb.online:
 * wget -q -O /dev/null "https://freedomassemblydb.online/campaign/church/cron/process_scheduled_emails.php?cron_key=fac_2024_secure_cron_8x9q2p5m"
 */

// Set script execution time limit to 5 minutes
set_time_limit(300);

// Define your secret key here - DO NOT SHARE THIS KEY
define('CRON_KEY', 'fac_2024_secure_cron_8x9q2p5m');

// Modified security check to allow for CLI execution
if (php_sapi_name() !== 'cli') {
    // Only check for cron_key when running via web request
    if (!isset($_GET['cron_key']) || $_GET['cron_key'] !== CRON_KEY) {
        header('HTTP/1.0 403 Forbidden');
        exit('Access Denied');
    }
}

// Include necessary files
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/email_functions.php';

// Initialize logging
$logFile = __DIR__ . '/../logs/scheduled_emails.log';
$logDir = dirname($logFile);

// Ensure log directory exists
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// Log start of execution
$log_message = '[' . date('Y-m-d H:i:s') . '] Scheduled emails cron job started.' . PHP_EOL;
file_put_contents($logFile, $log_message, FILE_APPEND);

// Check for specific schedule_id from command line
$specific_schedule_id = null;
if (isset($_SERVER['argv']) && count($_SERVER['argv']) > 1) {
    foreach ($_SERVER['argv'] as $arg) {
        if (strpos($arg, '--schedule_id=') === 0) {
            $specific_schedule_id = intval(substr($arg, 14));
            $log_message = '[' . date('Y-m-d H:i:s') . '] Processing specific schedule ID: ' . $specific_schedule_id . PHP_EOL;
            file_put_contents($logFile, $log_message, FILE_APPEND);
            break;
        }
    }
}

/**
 * EmailScheduler class to handle the processing of scheduled emails
 */
class EmailScheduler {
    public $pdo;
    private $logFile;
    
    /**
     * Constructor
     * 
     * @param PDO $pdo Database connection
     * @param string $logFile Path to log file
     */
    public function __construct($pdo, $logFile) {
        $this->pdo = $pdo;
        $this->logFile = $logFile;
    }
    
    /**
     * Log a message to the log file
     * 
     * @param string $message Message to log
     * @param string $type Log type (info, warning, error, success)
     */
    private function log($message, $type = 'info') {
        $log_message = '[' . date('Y-m-d H:i:s') . '] [' . strtoupper($type) . '] ' . $message . PHP_EOL;
        file_put_contents($this->logFile, $log_message, FILE_APPEND);
        error_log($message);
    }
    
    /**
     * Log a message to the database for a specific schedule
     * 
     * @param int $scheduleId Schedule ID
     * @param string $message Message to log
     * @param string $type Log type (info, warning, error, success)
     */
    private function logToDatabase($scheduleId, $message, $type = 'info') {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO email_schedule_logs 
                (schedule_id, log_type, message) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$scheduleId, $type, $message]);
        } catch (Exception $e) {
            $this->log("Error logging to database: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * Get active schedules
     * 
     * @return array Active schedules
     */
    private function getActiveSchedules() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    es.id, es.name, es.status, es.custom_data, es.next_run,
                    ess.template_id, ess.custom_subject, ess.custom_content, 
                    ess.track_opens, ess.track_clicks,
                    et.id as template_id, et.subject as template_subject, 
                    et.content as template_content
                FROM 
                    email_schedules es
                JOIN 
                    email_schedule_settings ess ON es.id = ess.schedule_id
                LEFT JOIN 
                    email_templates et ON ess.template_id = et.id
                WHERE 
                    es.status = 'active' 
                    AND es.next_run <= NOW() 
                ORDER BY 
                    es.created_at ASC
            ");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $this->log("Error getting active schedules: " . $e->getMessage(), 'error');
            return [];
        }
    }
    
    /**
     * Get pending recipients for a schedule
     * 
     * @param int $scheduleId Schedule ID
     * @param int $limit Maximum number of recipients to get
     * @return array Pending recipients
     */
    private function getPendingRecipients($scheduleId, $limit) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    esr.*,
                    CASE 
                        WHEN esr.recipient_type = 'member' THEN m.full_name
                        WHEN esr.recipient_type = 'contact' THEN c.name
                        WHEN esr.recipient_type = 'group' THEN NULL
                        ELSE NULL
                    END as recipient_name,
                    CASE 
                        WHEN esr.recipient_type = 'member' THEN m.email
                        WHEN esr.recipient_type = 'contact' THEN c.email
                        WHEN esr.recipient_type = 'group' THEN NULL
                        ELSE NULL
                    END as recipient_email
                FROM 
                    email_schedule_recipients esr
                LEFT JOIN 
                    members m ON esr.recipient_type = 'member' AND esr.recipient_id = m.id
                LEFT JOIN 
                    contacts c ON esr.recipient_type = 'contact' AND esr.recipient_id = c.id
                WHERE 
                    esr.schedule_id = ? 
                    AND esr.status = 'pending'
                    AND (
                        (esr.recipient_type = 'member' AND m.email IS NOT NULL) OR
                        (esr.recipient_type = 'contact' AND c.email IS NOT NULL) OR
                        esr.recipient_type = 'group'
                    )
                ORDER BY 
                    esr.id ASC
                LIMIT ?
            ");
            $stmt->execute([$scheduleId, $limit]);
            $recipients = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Handle group recipients
            $groupRecipients = [];
            foreach ($recipients as $key => $recipient) {
                if ($recipient['recipient_type'] === 'group') {
                    // Get members of this group
                    $groupId = $recipient['recipient_id'];
                    
                    // First check if we're dealing with a contact group or member group
                    $stmt = $this->pdo->prepare("SELECT id FROM contact_groups WHERE id = ?");
                    $stmt->execute([$groupId]);
                    $isContactGroup = $stmt->rowCount() > 0;
                    
                    if ($isContactGroup) {
                        // Contact group
                        $stmt = $this->pdo->prepare("
                            SELECT 
                                c.id, c.name, c.email
                            FROM 
                                contact_group_members cgm
                            JOIN 
                                contacts c ON cgm.contact_id = c.id
                            WHERE 
                                cgm.group_id = ?
                                AND c.email IS NOT NULL
                        ");
                    } else {
                        // Member group
                        $stmt = $this->pdo->prepare("
                            SELECT 
                                m.id, m.full_name as name, m.email
                            FROM 
                                group_members gm
                            JOIN 
                                members m ON gm.member_id = m.id
                            WHERE 
                                gm.group_id = ?
                                AND m.email IS NOT NULL
                        ");
                    }
                    
                    $stmt->execute([$groupId]);
                    $groupMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    // Add each group member as a recipient
                    foreach ($groupMembers as $member) {
                        $groupRecipients[] = [
                            'id' => $recipient['id'],
                            'schedule_id' => $recipient['schedule_id'],
                            'recipient_type' => 'member',
                            'recipient_id' => $member['id'],
                            'status' => 'pending',
                            'recipient_name' => $member['name'],
                            'recipient_email' => $member['email'],
                            'is_group_member' => true,
                            'group_id' => $groupId
                        ];
                    }
                    
                    // Remove the group recipient from the array
                    unset($recipients[$key]);
                }
            }
            
            // Merge individual recipients with group members
            return array_merge(array_values($recipients), $groupRecipients);
        } catch (Exception $e) {
            $this->log("Error getting pending recipients for schedule $scheduleId: " . $e->getMessage(), 'error');
            $this->logToDatabase($scheduleId, "Error getting pending recipients: " . $e->getMessage(), 'error');
            return [];
        }
    }
    
    /**
     * Update recipient status
     * 
     * @param int $recipientId Recipient ID
     * @param string $status New status
     * @param string $errorMessage Error message (if status is 'failed')
     */
    private function updateRecipientStatus($recipientId, $status, $errorMessage = null) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE email_schedule_recipients 
                SET status = ?, sent_at = ?, error_message = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            $sentAt = $status === 'sent' ? date('Y-m-d H:i:s') : null;
            $stmt->execute([$status, $sentAt, $errorMessage, $recipientId]);
        } catch (Exception $e) {
            $this->log("Error updating recipient status: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * Log email to email_logs table
     * 
     * @param int $memberId Member ID
     * @param int $templateId Template ID
     * @param int $scheduleId Schedule ID
     * @param string $subject Email subject
     * @param string $status Email status
     * @param string $errorMessage Error message (if status is 'failed')
     */
    private function logEmail($memberId, $templateId, $scheduleId, $subject, $status, $errorMessage = null) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO email_logs 
                (member_id, template_id, email_schedule_id, email_type, subject, sent_at, status, error_message) 
                VALUES (?, ?, ?, 'scheduled', ?, NOW(), ?, ?)
            ");
            $stmt->execute([$memberId, $templateId, $scheduleId, $subject, $status, $errorMessage]);
        } catch (Exception $e) {
            $this->log("Error logging email: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * Check if a schedule is complete (all recipients processed)
     * 
     * @param int $scheduleId Schedule ID
     * @return bool True if complete, false otherwise
     */
    private function isScheduleComplete($scheduleId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as count 
                FROM email_schedule_recipients 
                WHERE schedule_id = ? AND status = 'pending'
            ");
            $stmt->execute([$scheduleId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] == 0;
        } catch (Exception $e) {
            $this->log("Error checking if schedule is complete: " . $e->getMessage(), 'error');
            return false;
        }
    }
    
    /**
     * Update schedule status
     * 
     * @param int $scheduleId Schedule ID
     * @param string $status New status
     */
    private function updateScheduleStatus($scheduleId, $status) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE email_schedules 
                SET status = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$status, $scheduleId]);
            $this->logToDatabase($scheduleId, "Schedule status updated to '$status'", 'info');
        } catch (Exception $e) {
            $this->log("Error updating schedule status: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * Process a schedule
     * 
     * @param array $schedule Schedule data
     */
    public function processSchedule($schedule) {
        try {
            // Extract schedule settings from custom_data
            $custom_data = json_decode($schedule['custom_data'] ?? '{}', true);
            $schedule_type = $custom_data['schedule_type'] ?? 'one_time';
            $start_datetime = $custom_data['start_datetime'] ?? $schedule['next_run'];
            $end_datetime = $custom_data['end_datetime'] ?? null;
            $emails_per_hour = $custom_data['emails_per_hour'] ?? 100;
            $min_interval_seconds = $custom_data['min_interval_seconds'] ?? 5;
            
            // Check if schedule is still valid based on end_datetime
            if ($end_datetime && strtotime($end_datetime) < time()) {
                $this->updateScheduleStatus($schedule['id'], 'completed');
                $this->log("Schedule #{$schedule['id']} ({$schedule['name']}) completed due to end date reached.", 'info');
                return;
            }
            
            // Calculate how many emails we can send in this batch
            $batch_size = $this->calculateBatchSize($emails_per_hour);
            
            // Get pending recipients
            $recipients = $this->getPendingRecipients($schedule['id'], $batch_size);
            
            if (empty($recipients)) {
                $this->log("No pending recipients for schedule #{$schedule['id']} ({$schedule['name']}).", 'info');
                
                // If one-time schedule and no more recipients, mark as completed
                if ($schedule_type == 'one_time') {
                    $this->updateScheduleStatus($schedule['id'], 'completed');
                    $this->log("One-time schedule #{$schedule['id']} ({$schedule['name']}) completed.", 'info');
                } else {
                    // Update next_run for recurring schedules
                    $this->updateNextRun($schedule['id']);
                }
                
                return;
            }
            
            $this->log("Processing {$schedule['id']} ({$schedule['name']}): sending to " . count($recipients) . " recipients.", 'info');
            
            // Process each recipient
            foreach ($recipients as $recipient) {
                $this->processRecipient($schedule, $recipient);
                
                // Respect minimum interval between emails
                if ($min_interval_seconds > 0) {
                    sleep($min_interval_seconds);
                }
            }
            
            $this->log("Completed batch for schedule #{$schedule['id']} ({$schedule['name']}).", 'info');
            
        } catch (Exception $e) {
            $this->log("Error processing schedule #{$schedule['id']}: " . $e->getMessage(), 'error');
            $this->updateScheduleStatus($schedule['id'], 'failed');
        }
    }
    
    /**
     * Process all active schedules
     */
    public function processSchedules() {
        $this->log("Starting to process scheduled emails", 'info');
        
        // Get active schedules
        $schedules = $this->getActiveSchedules();
        $totalSchedules = count($schedules);
        
        if ($totalSchedules == 0) {
            $this->log("No active schedules found", 'info');
            return;
        }
        
        $this->log("Found $totalSchedules active schedules", 'info');
        
        // Process each schedule
        foreach ($schedules as $schedule) {
            $this->processSchedule($schedule);
        }
        
        $this->log("Finished processing scheduled emails", 'info');
    }
    
    private function calculateBatchSize($emailsPerHour) {
        // Calculate how many emails we can send in this batch
        $batchSize = min(ceil($emailsPerHour / (60 / 5)), 50); // Max 50 per batch
        return $batchSize;
    }
    
    private function updateNextRun($scheduleId) {
        // Update next_run for recurring schedules
        try {
            $stmt = $this->pdo->prepare("
                UPDATE email_schedules 
                SET next_run = DATE_ADD(NOW(), INTERVAL 1 HOUR) 
                WHERE id = ?
            ");
            $stmt->execute([$scheduleId]);
        } catch (Exception $e) {
            $this->log("Error updating next_run for schedule $scheduleId: " . $e->getMessage(), 'error');
        }
    }
    
    private function processRecipient($schedule, $recipient) {
        // Send the email
        $result = $this->sendEmail($schedule, $recipient);
        
        if ($result['success']) {
            try {
                $this->log("Email sent successfully to {$recipient['recipient_email']}", 'success');
                $this->updateRecipientStatus($recipient['id'], 'sent');
                $this->logEmail(
                    $recipient['recipient_id'],
                    $schedule['template_id'],
                    $schedule['id'],
                    !empty($schedule['custom_subject']) ? $schedule['custom_subject'] : $schedule['template_subject'],
                    'success'
                );
            } catch (Exception $dbEx) {
                // Still log success since email was sent, but record the database error
                $this->log("Email sent to {$recipient['recipient_email']} but database update failed: {$dbEx->getMessage()}", 'warning');
            }
        } else {
            $this->log("Failed to send email to {$recipient['recipient_email']}: {$result['message']}", 'error');
            try {
                $this->updateRecipientStatus($recipient['id'], 'failed', $result['message']);
                $this->logEmail(
                    $recipient['recipient_id'],
                    $schedule['template_id'],
                    $schedule['id'],
                    !empty($schedule['custom_subject']) ? $schedule['custom_subject'] : $schedule['template_subject'],
                    'failed',
                    $result['message']
                );
            } catch (Exception $dbEx) {
                // Log the database error
                $this->log("Failed to update database for failed email to {$recipient['recipient_email']}: {$dbEx->getMessage()}", 'error');
            }
        }
    }
    
    private function sendEmail($schedule, $recipient) {
        try {
            // Get email settings
            $stmt = $this->pdo->prepare("
                SELECT setting_key, setting_value 
                FROM email_settings
            ");
            $stmt->execute();
            $settings = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
            
            // Prepare email content
            $subject = !empty($schedule['custom_subject']) ? $schedule['custom_subject'] : $schedule['template_subject'];
            $content = !empty($schedule['custom_content']) ? $schedule['custom_content'] : $schedule['template_content'];
            
            // Create a properly formatted member data array for placeholder replacement
            $memberData = [
                'full_name' => $recipient['recipient_name'],
                'first_name' => explode(' ', $recipient['recipient_name'])[0] ?? '',
                'last_name' => (count(explode(' ', $recipient['recipient_name'])) > 1) ? explode(' ', $recipient['recipient_name'], 2)[1] : '',
                'email' => $recipient['recipient_email'],
                'recipient_full_name' => $recipient['recipient_name'],
                'recipient_first_name' => explode(' ', $recipient['recipient_name'])[0] ?? '',
                'recipient_email' => $recipient['recipient_email'],
                'church_name' => $settings['sender_name'] ?? 'Church',
                'current_date' => date('Y-m-d'),
                'current_time' => date('H:i')
            ];
            
            // Fetch additional member data if available
            if ($recipient['recipient_type'] === 'member' && !empty($recipient['recipient_id'])) {
                $stmt = $this->pdo->prepare("
                    SELECT * FROM members WHERE id = ?
                ");
                $stmt->execute([$recipient['recipient_id']]);
                $memberDetails = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($memberDetails) {
                    // Merge additional member details
                    $memberData = array_merge($memberDetails, $memberData);
                }
            }
            
            // Add tracking pixel if enabled
            if ($schedule['track_opens']) {
                $content .= '{tracking_pixel}';
            }
            
            // Send the email using the updated sendScheduledEmail function
            $scheduleData = [
                'id' => $schedule['id'],
                'name' => $schedule['name'],
                'track_opens' => $schedule['track_opens'],
                'track_clicks' => $schedule['track_clicks']
            ];
            
            $result = sendScheduledEmail(
                $recipient['recipient_email'],
                $recipient['recipient_name'],
                $subject,
                $content,
                $scheduleData,
                $memberData
            );
            
            if ($result) {
                $this->log("[SUCCESS] Email sent successfully to " . $recipient['recipient_email']);
                
                // Update recipient status
                $stmt = $this->pdo->prepare("
                    UPDATE email_schedule_recipients 
                    SET status = 'sent', sent_at = NOW() 
                    WHERE schedule_id = ? AND recipient_id = ? AND recipient_type = ?
                ");
                $stmt->execute([
                    $schedule['id'], 
                    $recipient['recipient_id'], 
                    $recipient['recipient_type']
                ]);
                
                // Try to log the email in sent_emails table
                try {
                    $stmt = $this->pdo->prepare("
                        INSERT INTO sent_emails 
                        (recipient_email, recipient_name, subject, content, sent_at, email_schedule_id) 
                        VALUES (?, ?, ?, ?, NOW(), ?)
                    ");
                    $stmt->execute([
                        $recipient['recipient_email'],
                        $recipient['recipient_name'],
                        $subject,
                        $content,
                        $schedule['id']
                    ]);
                } catch (Exception $e) {
                    $this->log("[ERROR] Error logging email: " . $e->getMessage());
                }
                
                return true;
            } else {
                $this->log("[ERROR] Failed to send email to " . $recipient['recipient_email']);
                
                // Update recipient status to failed
                $stmt = $this->pdo->prepare("
                    UPDATE email_schedule_recipients 
                    SET status = 'failed', error_message = 'Failed to send email' 
                    WHERE schedule_id = ? AND recipient_id = ? AND recipient_type = ?
                ");
                $stmt->execute([
                    $schedule['id'], 
                    $recipient['recipient_id'], 
                    $recipient['recipient_type']
                ]);
                
                return false;
            }
        } catch (Exception $e) {
            $this->log("[ERROR] Exception while sending email: " . $e->getMessage());
            return false;
        }
    }
}

try {
    // Initialize the scheduler
    $scheduler = new EmailScheduler($pdo, $logFile);
    
    // Process schedules
    if ($specific_schedule_id) {
        $stmt = $scheduler->pdo->prepare("
            SELECT 
                es.id, es.name, es.status, es.custom_data, es.next_run,
                ess.template_id, ess.custom_subject, ess.custom_content, 
                ess.track_opens, ess.track_clicks,
                et.id as template_id, et.subject as template_subject, 
                et.content as template_content
            FROM 
                email_schedules es
            JOIN 
                email_schedule_settings ess ON es.id = ess.schedule_id
            JOIN 
                email_templates et ON ess.template_id = et.id
            WHERE 
                es.id = ?
        ");
        $stmt->execute([$specific_schedule_id]);
        $schedule = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($schedule) {
            $scheduler->processSchedule($schedule);
        } else {
            $log_message = '[' . date('Y-m-d H:i:s') . '] Error: Schedule ID ' . $specific_schedule_id . ' not found.' . PHP_EOL;
            file_put_contents($logFile, $log_message, FILE_APPEND);
        }
    } else {
        $scheduler->processSchedules();
    }
    
    // Log completion
    $log_message = '[' . date('Y-m-d H:i:s') . '] Scheduled emails cron job completed successfully.' . PHP_EOL;
    file_put_contents($logFile, $log_message, FILE_APPEND);
    
    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
        'message' => 'Scheduled emails processed successfully',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    // Log error
    $errorLog = '[' . date('Y-m-d H:i:s') . '] Error: ' . $e->getMessage() . PHP_EOL;
    file_put_contents($logFile, $errorLog, FILE_APPEND);
    
    // Return error response
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Error processing scheduled emails: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Get the base URL of the application
 * 
 * @return string Base URL
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'freedomassemblydb.online';
    return "$protocol://$host";
}
