<?php
// Verification script to check if the raffle template is properly configured
require_once __DIR__ . '/../config.php';

// Get the template
$template_name = "The Big Raffle Winner";
$stmt = $pdo->prepare("SELECT id, template_name, subject, content FROM email_templates WHERE template_name = ?");
$stmt->execute([$template_name]);
$template = $stmt->fetch(PDO::FETCH_ASSOC);

echo "=== Raffle Template Verification ===\n\n";

if (!$template) {
    echo "ERROR: Template not found: $template_name\n";
    exit;
}

echo "Template found:\n";
echo "- ID: " . $template['id'] . "\n";
echo "- Name: " . $template['template_name'] . "\n";
echo "- Subject: " . $template['subject'] . "\n\n";

// Check for random number placeholders
$random_placeholders = [
    '{random_number_1}',
    '{random_number_2}',
    '{random_number_3}',
    '{random_number_4}',
    '{random_number_5}',
    '{random_powerball}'
];

$missing_placeholders = [];
foreach ($random_placeholders as $placeholder) {
    if (strpos($template['content'], $placeholder) === false) {
        $missing_placeholders[] = $placeholder;
    }
}

echo "Random number placeholders check: ";
if (empty($missing_placeholders)) {
    echo "PASSED - All random number placeholders present\n";
} else {
    echo "FAILED - Missing placeholders: " . implode(", ", $missing_placeholders) . "\n";
}

// Check for reply-to email placeholder
echo "Reply-to email placeholder check: ";
if (strpos($template['content'], '{reply_to_email}') !== false) {
    echo "PASSED - Using standard {reply_to_email} placeholder\n";
} else {
    echo "FAILED - Standard {reply_to_email} placeholder not found\n";
}

// Check for no hardcoded emails
echo "Hardcoded email check: ";
$hardcoded_emails = [];
preg_match_all('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', $template['content'], $matches);

// Filter out any email addresses that are part of placeholders or not actual emails
$actual_hardcoded_emails = [];
foreach ($matches[0] as $email) {
    // Skip if it's part of a placeholder or fallback value
    if (strpos($template['content'], '{' . $email . '}') !== false || 
        $email == '<EMAIL>' ||
        $email == 'example.com') {
        continue;
    }
    $actual_hardcoded_emails[] = $email;
}

if (empty($actual_hardcoded_emails)) {
    echo "PASSED - No hardcoded email addresses found\n";
} else {
    echo "FAILED - Hardcoded email addresses found: " . implode(", ", $actual_hardcoded_emails) . "\n";
}

// Check for unsubscribe link placeholder
echo "Unsubscribe link placeholder check: ";
if (strpos($template['content'], '{unsubscribe_link}') !== false) {
    echo "PASSED - Using standard {unsubscribe_link} placeholder\n";
} else {
    echo "FAILED - Standard {unsubscribe_link} placeholder not found\n";
}

// Get the reply-to email from settings
echo "\n=== Reply-To Email Configuration ===\n";
try {
    $stmt = $pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'reply_to_email' LIMIT 1");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result && !empty($result['setting_value'])) {
        echo "Current reply-to email: " . $result['setting_value'] . "\n";
    } else {
        echo "WARNING: reply_to_email setting not found in email_settings table\n";
        
        // Check if it's in the settings table under a different key
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'email_reply_to_email' LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result && !empty($result['setting_value'])) {
            echo "Found reply-to email in settings table: " . $result['setting_value'] . "\n";
            echo "RECOMMENDATION: Add this value to email_settings table for consistency\n";
        } else {
            echo "WARNING: No reply-to email configuration found in settings table either\n";
            echo "RECOMMENDATION: Configure a reply-to email in the email settings\n";
        }
    }
} catch (Exception $e) {
    echo "ERROR checking reply-to email settings: " . $e->getMessage() . "\n";
}

// Summary
echo "\n=== Summary ===\n";
$all_passed = empty($missing_placeholders) && 
              strpos($template['content'], '{reply_to_email}') !== false && 
              empty($actual_hardcoded_emails) && 
              strpos($template['content'], '{unsubscribe_link}') !== false;

if ($all_passed) {
    echo "✅ All checks PASSED! The template is properly configured.\n";
    echo "The template can now be used in the email system with proper reply-to functionality.\n";
} else {
    echo "❌ Some checks FAILED. Please review the issues above.\n";
}

echo "\nTemplate can be accessed from the admin panel at admin/email_templates.php\n"; 