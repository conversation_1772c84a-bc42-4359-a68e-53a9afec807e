<?php
/**
 * Create Missing Email Scheduling Tables
 * 
 * This script creates the missing email_schedules and related tables
 */

// Include database configuration
require_once '../config.php';

// Check if user is logged in as admin
session_start();
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$message = '';
$error = '';

// SQL to create the missing tables
$sql_statements = [
    // Main email schedules table
    "CREATE TABLE IF NOT EXISTS `email_schedules` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `description` text DEFAULT NULL,
        `frequency` enum('once','daily','weekly','monthly','yearly') NOT NULL DEFAULT 'once',
        `day_of_week` int(1) DEFAULT NULL COMMENT '0=Sunday, 1=Monday, etc.',
        `day_of_month` int(2) DEFAULT NULL COMMENT '1-31',
        `hour` int(2) NOT NULL DEFAULT 9 COMMENT '0-23',
        `minute` int(2) NOT NULL DEFAULT 0 COMMENT '0-59',
        `status` enum('active','paused','completed','cancelled') NOT NULL DEFAULT 'active',
        `custom_data` text DEFAULT NULL COMMENT 'JSON data for additional settings',
        `next_run` datetime DEFAULT NULL,
        `last_run` datetime DEFAULT NULL,
        `created_by` int(11) NOT NULL,
        `template_id` int(11) DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `idx_status` (`status`),
        KEY `idx_next_run` (`next_run`),
        KEY `idx_created_by` (`created_by`),
        KEY `idx_template_id` (`template_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

    // Email schedule settings table
    "CREATE TABLE IF NOT EXISTS `email_schedule_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `schedule_id` int(11) NOT NULL,
        `template_id` int(11) DEFAULT NULL,
        `custom_subject` varchar(255) DEFAULT NULL,
        `custom_content` text DEFAULT NULL,
        `track_opens` tinyint(1) NOT NULL DEFAULT 1,
        `track_clicks` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        UNIQUE KEY `schedule_id` (`schedule_id`),
        KEY `idx_template_id` (`template_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

    // Email schedule recipients table
    "CREATE TABLE IF NOT EXISTS `email_schedule_recipients` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `schedule_id` int(11) NOT NULL,
        `recipient_type` enum('member','contact','group','custom') NOT NULL,
        `recipient_id` int(11) DEFAULT NULL,
        `custom_email` varchar(100) DEFAULT NULL,
        `custom_name` varchar(100) DEFAULT NULL,
        `status` enum('pending','sent','failed','skipped') NOT NULL DEFAULT 'pending',
        `sent_at` datetime DEFAULT NULL,
        `error_message` text DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `idx_schedule_id` (`schedule_id`),
        KEY `idx_status` (`status`),
        KEY `idx_recipient` (`recipient_type`,`recipient_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

    // Email schedule logs table
    "CREATE TABLE IF NOT EXISTS `email_schedule_logs` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `schedule_id` int(11) NOT NULL,
        `log_type` enum('info','warning','error','success') NOT NULL DEFAULT 'info',
        `message` text NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `idx_schedule_id` (`schedule_id`),
        KEY `idx_log_type` (`log_type`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci"
];

// Execute the SQL statements
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_tables'])) {
    try {
        foreach ($sql_statements as $sql) {
            $pdo->exec($sql);
        }
        $message = "Email scheduling tables created successfully!";
    } catch (PDOException $e) {
        $error = "Error creating tables: " . $e->getMessage();
    }
}

// Check if tables exist
$tables_exist = [];
$table_names = ['email_schedules', 'email_schedule_settings', 'email_schedule_recipients', 'email_schedule_logs'];

foreach ($table_names as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        $tables_exist[$table] = $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        $tables_exist[$table] = false;
    }
}

$page_title = 'Create Missing Tables';
include 'includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Create Missing Email Scheduling Tables</h1>
    
    <?php if ($message): ?>
        <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-header">
            <h5>Table Status</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Table Name</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($tables_exist as $table => $exists): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($table); ?></td>
                            <td>
                                <?php if ($exists): ?>
                                    <span class="badge bg-success">Exists</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Missing</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <?php if (in_array(false, $tables_exist)): ?>
            <form method="post" class="mt-3">
                <button type="submit" name="create_tables" class="btn btn-primary">
                    Create Missing Tables
                </button>
            </form>
            <?php else: ?>
            <div class="alert alert-success mt-3">
                All email scheduling tables exist. You can now use the <a href="email_scheduler.php">Email Scheduler</a>.
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
