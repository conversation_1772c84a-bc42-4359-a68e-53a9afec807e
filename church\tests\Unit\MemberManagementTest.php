<?php
/**
 * Member Management Test
 * 
 * Tests create, read, update, and delete operations for church members
 */

class MemberManagementTest
{
    private $pdo;
    
    public function __construct()
    {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    /**
     * Test creating a new member
     */
    public function testCreateMember()
    {
        // Generate unique test data
        $timestamp = time();
        $testEmail = "test{$timestamp}@example.com";
        
        // Member data
        $memberData = [
            'first_name' => 'Test',
            'last_name' => 'Member',
            'full_name' => 'Test Member',
            'email' => $testEmail,
            'phone_number' => '1234567890',
            'birth_date' => date('Y-m-d'),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        // Insert the member
        $columns = implode(', ', array_keys($memberData));
        $placeholders = ':' . implode(', :', array_keys($memberData));
        
        $sql = "INSERT INTO members ($columns) VALUES ($placeholders)";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($memberData);
        
        $newMemberId = $this->pdo->lastInsertId();
        
        // Test if member was created successfully
        $success = ($newMemberId > 0);
        
        // Verify the member exists in the database
        $sql = "SELECT * FROM members WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$newMemberId]);
        $member = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $emailMatches = ($member['email'] === $testEmail);
        
        return [
            'name' => 'Create Member',
            'success' => $success && $emailMatches,
            'message' => $success && $emailMatches ? 
                "Successfully created member with ID: $newMemberId" : 
                "Failed to create member or data mismatch"
        ];
    }
    
    /**
     * Test reading a member
     */
    public function testReadMember()
    {
        // Create a test member to read
        $memberId = createTestMember([
            'first_name' => 'Read',
            'last_name' => 'Test',
            'email' => '<EMAIL>'
        ]);
        
        // Retrieve the member
        $sql = "SELECT * FROM members WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$memberId]);
        $member = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $success = ($member !== false);
        $dataCorrect = (
            $member && 
            $member['first_name'] === 'Read' && 
            $member['last_name'] === 'Test' && 
            $member['email'] === '<EMAIL>'
        );
        
        return [
            'name' => 'Read Member',
            'success' => $success && $dataCorrect,
            'message' => $success && $dataCorrect ? 
                "Successfully read member data" : 
                "Failed to read member data or data mismatch"
        ];
    }
    
    /**
     * Test updating a member
     */
    public function testUpdateMember()
    {
        // Create a test member to update
        $memberId = createTestMember([
            'first_name' => 'Update',
            'last_name' => 'Before',
            'email' => '<EMAIL>'
        ]);
        
        // New data for update
        $updateData = [
            'last_name' => 'After',
            'email' => '<EMAIL>',
            'phone_number' => '9876543210'
        ];
        
        // Build update query
        $updateSets = [];
        foreach ($updateData as $key => $value) {
            $updateSets[] = "$key = :$key";
        }
        $updateString = implode(', ', $updateSets);
        
        $sql = "UPDATE members SET $updateString WHERE id = :id";
        $updateData['id'] = $memberId;
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($updateData);
        
        $rowsAffected = $stmt->rowCount();
        
        // Verify the update
        $sql = "SELECT * FROM members WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$memberId]);
        $updatedMember = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $updateSuccess = ($rowsAffected > 0);
        $dataCorrect = (
            $updatedMember &&
            $updatedMember['first_name'] === 'Update' && // Unchanged
            $updatedMember['last_name'] === 'After' &&   // Changed
            $updatedMember['email'] === '<EMAIL>' && // Changed
            $updatedMember['phone_number'] === '9876543210' // Changed
        );
        
        return [
            'name' => 'Update Member',
            'success' => $updateSuccess && $dataCorrect,
            'message' => $updateSuccess && $dataCorrect ? 
                "Successfully updated member data" : 
                "Failed to update member data or verification failed"
        ];
    }
    
    /**
     * Test deleting a member
     */
    public function testDeleteMember()
    {
        // Create a test member to delete
        $memberId = createTestMember([
            'first_name' => 'Delete',
            'last_name' => 'Test',
            'email' => '<EMAIL>'
        ]);
        
        // Verify member exists first
        $sql = "SELECT COUNT(*) FROM members WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$memberId]);
        $count = $stmt->fetchColumn();
        $existsBefore = ($count > 0);
        
        // Delete the member
        $sql = "DELETE FROM members WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$memberId]);
        $rowsAffected = $stmt->rowCount();
        
        // Verify member no longer exists
        $sql = "SELECT COUNT(*) FROM members WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$memberId]);
        $count = $stmt->fetchColumn();
        $existsAfter = ($count > 0);
        
        $deleteSuccess = ($rowsAffected > 0);
        $verifySuccess = ($existsBefore && !$existsAfter);
        
        return [
            'name' => 'Delete Member',
            'success' => $deleteSuccess && $verifySuccess,
            'message' => $deleteSuccess && $verifySuccess ? 
                "Successfully deleted member" : 
                "Failed to delete member or verification failed"
        ];
    }
    
    /**
     * Run all tests
     */
    public function runAllTests()
    {
        $results = [];
        $results[] = $this->testCreateMember();
        $results[] = $this->testReadMember();
        $results[] = $this->testUpdateMember();
        $results[] = $this->testDeleteMember();
        
        return $results;
    }
} 