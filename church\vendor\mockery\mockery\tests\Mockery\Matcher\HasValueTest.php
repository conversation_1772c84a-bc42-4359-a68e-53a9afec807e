<?php

namespace test\Mockery\Matcher;

use <PERSON><PERSON><PERSON>\Matcher\HasValue;
use PHPUnit\Framework\TestCase;

class HasValueTest extends TestCase
{
    /** @test */
    public function it_can_handle_a_non_array()
    {
        $matcher = new HasValue(123);

        $actual = null;

        $this->assertFalse($matcher->match($actual));
    }

    /** @test */
    public function it_matches_an_array()
    {
        $matcher = new HasValue(123);

        $actual = [
            'foo' => 'bar',
            'dave' => 123,
            'bar' => 'baz',
        ];

        $this->assertTrue($matcher->match($actual));
    }

    /** @test */
    public function it_matches_an_array_like_object()
    {
        $matcher = new HasValue(123);

        $actual = new \ArrayObject([
            'foo' => 'bar',
            'dave' => 123,
            'bar' => 'baz',
        ]);

        $this->assertTrue($matcher->match($actual));
    }
}
