<?php
// Include the configuration file
require_once '../config.php';

// Set content type to plain text for easy reading
header('Content-Type: text/plain');

echo "=== BIRTHDAY DATA CHECK ===\n\n";

try {
    // Get the database connection
    $conn = $pdo;
    
    // Check if connection is successful
    echo "Database connection: SUCCESS\n\n";
    
    // Get all members with birthdays
    $stmt = $conn->prepare("SELECT id, full_name, birth_date, email FROM members WHERE birth_date IS NOT NULL ORDER BY birth_date");
    $stmt->execute();
    $allBirthdays = $stmt->fetchAll();
    
    echo "Total members with birthdays: " . count($allBirthdays) . "\n\n";
    
    // Get birthdays for current month
    $currentMonth = date('m');
    $stmt = $conn->prepare("SELECT id, full_name, birth_date, email FROM members WHERE MONTH(birth_date) = ? AND birth_date IS NOT NULL ORDER BY DAY(birth_date)");
    $stmt->execute([$currentMonth]);
    $currentMonthBirthdays = $stmt->fetchAll();
    
    echo "Birthdays in current month (" . date('F') . "): " . count($currentMonthBirthdays) . "\n\n";
    
    // Get birthdays for next 7 days
    $today = date('Y-m-d');
    $nextWeek = date('Y-m-d', strtotime('+7 days'));
    
    $stmt = $conn->prepare("
        SELECT id, full_name, birth_date, email,
        DATE_FORMAT(birth_date, '%m-%d') as birthday_md,
        DATE_FORMAT(NOW(), '%Y') as current_year
        FROM members 
        WHERE birth_date IS NOT NULL
        ORDER BY MONTH(birth_date), DAY(birth_date)
    ");
    $stmt->execute();
    $allBirthdaysFormatted = $stmt->fetchAll();
    
    echo "=== ALL BIRTHDAYS ===\n";
    foreach ($allBirthdaysFormatted as $member) {
        // Get month and day from birth_date
        $birthDate = new DateTime($member['birth_date']);
        $birthMonth = $birthDate->format('m');
        $birthDay = $birthDate->format('d');
        
        // Create this year's birthday date
        $thisYearBirthday = date('Y') . '-' . $birthMonth . '-' . $birthDay;
        $daysUntilBirthday = (strtotime($thisYearBirthday) - strtotime(date('Y-m-d'))) / (60 * 60 * 24);
        
        // If birthday has passed this year, calculate for next year
        if ($daysUntilBirthday < 0) {
            $nextYearBirthday = (date('Y') + 1) . '-' . $birthMonth . '-' . $birthDay;
            $daysUntilBirthday = (strtotime($nextYearBirthday) - strtotime(date('Y-m-d'))) / (60 * 60 * 24);
            $thisYearBirthday = $nextYearBirthday;
        }
        
        echo sprintf(
            "ID: %d | Name: %s | Birth Date: %s | This Year: %s | Days Until: %d\n",
            $member['id'],
            $member['full_name'],
            $member['birth_date'],
            $thisYearBirthday,
            $daysUntilBirthday
        );
    }
    
    echo "\n=== UPCOMING BIRTHDAYS (Next 7 days) ===\n";
    $upcomingBirthdays = [];
    foreach ($allBirthdaysFormatted as $member) {
        // Get month and day from birth_date
        $birthDate = new DateTime($member['birth_date']);
        $birthMonth = $birthDate->format('m');
        $birthDay = $birthDate->format('d');
        
        // Create this year's birthday date
        $thisYearBirthday = date('Y') . '-' . $birthMonth . '-' . $birthDay;
        $daysUntilBirthday = (strtotime($thisYearBirthday) - strtotime(date('Y-m-d'))) / (60 * 60 * 24);
        
        // If birthday has passed this year, calculate for next year
        if ($daysUntilBirthday < 0) {
            $nextYearBirthday = (date('Y') + 1) . '-' . $birthMonth . '-' . $birthDay;
            $daysUntilBirthday = (strtotime($nextYearBirthday) - strtotime(date('Y-m-d'))) / (60 * 60 * 24);
            $thisYearBirthday = $nextYearBirthday;
        }
        
        // Check if birthday is within the next 7 days
        if ($daysUntilBirthday >= 0 && $daysUntilBirthday <= 7) {
            $upcomingBirthdays[] = [
                'id' => $member['id'],
                'name' => $member['full_name'],
                'birthDate' => $member['birth_date'],
                'thisYearBirthday' => $thisYearBirthday,
                'daysUntil' => $daysUntilBirthday
            ];
        }
    }
    
    // Sort upcoming birthdays by days until
    usort($upcomingBirthdays, function($a, $b) {
        return $a['daysUntil'] - $b['daysUntil'];
    });
    
    foreach ($upcomingBirthdays as $member) {
        echo sprintf(
            "ID: %d | Name: %s | Birth Date: %s | This Year: %s | Days Until: %d\n",
            $member['id'],
            $member['name'],
            $member['birthDate'],
            $member['thisYearBirthday'],
            $member['daysUntil']
        );
    }
    
} catch (PDOException $e) {
    echo "ERROR: " . $e->getMessage();
} 