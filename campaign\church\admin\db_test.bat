@echo off
REM Database Connection Test Script

echo === Church Database Connection Test ===
echo.

echo 1. Testing connection to database...
C:\MAMP\bin\mysql\bin\mysql -u root -proot churchdb -e "SELECT 'Connection successful!' as Result;" || (
  echo Connection failed. Please check <PERSON><PERSON> is running and database exists.
  goto end
)
echo.

echo 2. Retrieving email templates...
C:\MAMP\bin\mysql\bin\mysql -u root -proot churchdb -e "SELECT id, template_name, subject, is_birthday_template FROM email_templates;" | findstr /v "template_name"
echo.

echo 3. Retrieving birthday templates only...
C:\MAMP\bin\mysql\bin\mysql -u root -proot churchdb -e "SELECT id, template_name, subject FROM email_templates WHERE is_birthday_template = 1" | findstr /v "template_name"
echo.

echo 4. Retrieving member birthdays for current month...
C:\MAMP\bin\mysql\bin\mysql -u root -proot churchdb -e "SELECT id, full_name, birth_date FROM members WHERE MONTH(birth_date) = MONTH(CURDATE()) ORDER BY DAY(birth_date);" | findstr /v "full_name"
echo.

echo 5. Retrieving birthday count by month...
C:\MAMP\bin\mysql\bin\mysql -u root -proot churchdb -e "SELECT MONTH(birth_date) as Month, COUNT(*) as BirthdayCount FROM members WHERE birth_date IS NOT NULL GROUP BY MONTH(birth_date) ORDER BY Month;" | findstr /v "Month"
echo.

echo Test completed successfully!

:end
pause 