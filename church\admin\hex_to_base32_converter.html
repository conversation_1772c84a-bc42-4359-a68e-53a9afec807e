<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secret Key Converter - Freedom Assembly Church</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 50px 0;
        }
        .converter-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .result {
            font-family: monospace;
            font-size: 1.2rem;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin-top: 20px;
            word-break: break-all;
        }
        .security-note {
            font-size: 0.9rem;
            margin-top: 30px;
            padding: 15px;
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="converter-container">
            <div class="logo">
                <h2>2FA Secret Key Converter</h2>
                <p>Freedom Assembly Church International</p>
            </div>
            
            <div class="alert alert-info">
                <p><strong>Instructions:</strong> Enter your hexadecimal secret key below to convert it to base32 format for use with Google Authenticator.</p>
            </div>
            
            <form id="converterForm">
                <div class="mb-3">
                    <label for="hexKey" class="form-label">Hexadecimal Secret Key</label>
                    <input type="text" class="form-control" id="hexKey" placeholder="Enter your hex secret key" required>
                    <div class="form-text">Example: 3132333435363738393031323334353637383930 (should be at least 32 characters long)</div>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">Convert to Base32</button>
                </div>
            </form>
            
            <div id="resultContainer" class="mt-4" style="display: none;">
                <h5>Base32 Secret Key:</h5>
                <div id="base32Result" class="result"></div>
                
                <div class="mt-3">
                    <button id="copyButton" class="btn btn-outline-secondary">
                        <i class="bi bi-clipboard"></i> Copy to Clipboard
                    </button>
                </div>
                
                <div class="mt-4">
                    <h5>How to use this key:</h5>
                    <ol>
                        <li>Open Google Authenticator app</li>
                        <li>Tap the "+" button</li>
                        <li>Select "Enter a setup key"</li>
                        <li>For "Account name", enter "Freedom Assembly Church"</li>
                        <li>For "Your key", enter the base32 key above</li>
                        <li>Make sure "Time-based" is selected</li>
                        <li>Tap "Add"</li>
                    </ol>
                </div>
                
                <div class="mt-4">
                    <h5>Troubleshooting:</h5>
                    <ul>
                        <li><strong>If you see "illegal character" error:</strong> Make sure you're copying only the characters shown above (no spaces or special characters).</li>
                        <li><strong>If you see "key value too short" error:</strong> The converter will automatically pad your key to meet the minimum length requirements.</li>
                        <li><strong>If the app still rejects the key:</strong> Try entering the key manually, character by character.</li>
                        <li><strong>Alternative apps:</strong> If Google Authenticator doesn't work, try Microsoft Authenticator or Authy, which may be more forgiving with key formats.</li>
                    </ul>
                </div>
                
                <div class="mt-4 alert alert-warning">
                    <h5><i class="bi bi-exclamation-triangle"></i> Alternative: Use a Backup Code</h5>
                    <p>If you're still having trouble setting up the authenticator app, you can use one of your backup codes instead:</p>
                    <ol>
                        <li>On the verification page, check the box that says "Use backup code instead"</li>
                        <li>Enter one of your backup codes in the "Verification Code" field</li>
                        <li>Click "Verify"</li>
                    </ol>
                    <p><strong>Note:</strong> Each backup code can only be used once. After logging in with a backup code, you should set up the authenticator app or generate new backup codes.</p>
                </div>
            </div>
            
            <div class="security-note">
                <p><strong>Security Note:</strong> This conversion happens entirely in your browser. Your secret key is never sent to any server.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Base32 alphabet (RFC 4648)
        const BASE32_ALPHABET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        
        // Convert hex to base32
        function hexToBase32(hexString) {
            // Remove any spaces or separators
            hexString = hexString.replace(/\s/g, '');
            
            // Check if input is valid hex
            if (!/^[0-9a-fA-F]+$/.test(hexString)) {
                throw new Error('Invalid hexadecimal string');
            }
            
            // Ensure the hex string is at least 32 characters (16 bytes) long
            // Google Authenticator requires at least 16 bytes of entropy
            while (hexString.length < 32) {
                hexString += '0'; // Pad with zeros if needed
            }
            
            // Convert hex to byte array
            const bytes = [];
            for (let i = 0; i < hexString.length; i += 2) {
                bytes.push(parseInt(hexString.substr(i, 2), 16));
            }
            
            // Convert bytes to base32
            let result = '';
            let buffer = 0;
            let bufferLength = 0;
            
            for (let i = 0; i < bytes.length; i++) {
                buffer = (buffer << 8) | bytes[i];
                bufferLength += 8;
                
                while (bufferLength >= 5) {
                    bufferLength -= 5;
                    result += BASE32_ALPHABET[(buffer >> bufferLength) & 31];
                }
            }
            
            // Handle remaining bits
            if (bufferLength > 0) {
                result += BASE32_ALPHABET[(buffer << (5 - bufferLength)) & 31];
            }
            
            // Remove padding characters (=) as Google Authenticator doesn't accept them
            return result.replace(/=/g, '');
        }
        
        // Form submission handler
        document.getElementById('converterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const hexKey = document.getElementById('hexKey').value.trim();
            const resultContainer = document.getElementById('resultContainer');
            const base32Result = document.getElementById('base32Result');
            
            try {
                const base32Key = hexToBase32(hexKey);
                base32Result.textContent = base32Key;
                resultContainer.style.display = 'block';
            } catch (error) {
                alert('Error: ' + error.message);
            }
        });
        
        // Copy to clipboard functionality
        document.getElementById('copyButton').addEventListener('click', function() {
            const base32Result = document.getElementById('base32Result');
            
            // Create a temporary textarea element
            const textarea = document.createElement('textarea');
            textarea.value = base32Result.textContent;
            document.body.appendChild(textarea);
            
            // Select and copy the text
            textarea.select();
            document.execCommand('copy');
            
            // Remove the temporary element
            document.body.removeChild(textarea);
            
            // Show feedback
            this.textContent = 'Copied!';
            setTimeout(() => {
                this.innerHTML = '<i class="bi bi-clipboard"></i> Copy to Clipboard';
            }, 2000);
        });
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 