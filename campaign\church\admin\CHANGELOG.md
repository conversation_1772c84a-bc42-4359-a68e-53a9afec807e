# Changelog

## [1.1.0] - 2023-07-15

### Added
- Email tracking functionality
- Email analytics dashboard
- Admin notifications for failed emails
- Tracking pixel for email open tracking

## [1.2.0] - 2023-07-16

### Added
- Common header and footer components
- Standardized sidebar across all admin pages
- Consistent styling with admin-style.css
- Modal for sending birthday messages
- Improved date range picker for analytics
- Documentation for adding new admin pages

### Changed
- Refactored admin pages to use common components
- Improved navigation with active link highlighting
- Enhanced mobile responsiveness
- Updated README with admin panel structure documentation

### Fixed
- Email analytics page now appears in sidebar on all pages
- Consistent menu items across all admin pages
- Standardized card and table styling
- Improved error and success message handling

## [1.2.1] - 2023-07-17

### Added
- Restored calendar to dashboard with birthday events
- Enhanced quick links section with visual improvements
- Improved email test form on dashboard
- Added CSS styling for calendar and events

### Changed
- Reorganized dashboard layout for better information hierarchy
- Enhanced visual styling of sidebar with rounded corners
- Improved responsive behavior for calendar on mobile devices

## [1.2.2] - 2023-07-18

### Added
- Send Message button to Birthday Celebrants section on dashboard
- Categorized sidebar menu with section headings
- Add Member link in sidebar for quick member creation

### Changed
- Reorganized sidebar menu items into logical groups
- Improved sidebar styling with category headings
- Enhanced Birthday Celebrants table with action buttons
- Moved View All button to card header for better visibility

## [1.2.3] - 2023-07-19

### Added
- Created send_birthday_message.php script to handle birthday message sending
- Added modal dialog for sending birthday messages directly from dashboard
- Implemented email template selection in the birthday message modal

### Fixed
- Fixed issue with send_birthday_message.php redirecting to index page
- Ensured consistent behavior between dashboard and birthday page
- Added proper error handling and success messages for email sending 