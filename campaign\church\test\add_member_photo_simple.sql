USE churchdb;

-- First get the current content
SET @current_content = (SELECT content FROM email_templates WHERE template_name = 'Member Birthday Notification');

-- Add the member photo div after the greeting div
SET @new_content = REPLACE(@current_content, 
'<div class="greeting">
                <p>Dear {recipient_full_name},</p>
                <p>We are excited to let you know that {birthday_member_full_name} will be celebrating their birthday {days_text}!</p>
            </div>', 
'<div class="greeting">
                <p>Dear {recipient_full_name},</p>
                <p>We are excited to let you know that {birthday_member_full_name} will be celebrating their birthday {days_text}!</p>
            </div>
            
            <div class="member-photo">
                <img src="{birthday_member_photo_url}" alt="{birthday_member_first_name}\'s photo" onerror="this.src=\'https://freedomassemblydb.online/assets/img/default-avatar.png\'; this.onerror=null;">
            </div>');

-- Update the template with the new content
UPDATE email_templates SET content = @new_content WHERE template_name = 'Member Birthday Notification'; 