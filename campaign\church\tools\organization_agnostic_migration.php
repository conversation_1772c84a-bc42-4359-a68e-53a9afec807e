<?php
/**
 * Organization-Agnostic Migration Script
 * 
 * This script updates existing email templates, WhatsApp templates, and other content
 * to replace hardcoded church-specific language with dynamic organization-agnostic placeholders.
 * 
 * Usage: php organization_agnostic_migration.php [--execute]
 * Without --execute flag, it runs in dry-run mode showing what would be changed.
 */

require_once '../config.php';

// Configuration
$dryRun = true;
$verboseOutput = true;

// Process command line arguments
if (isset($argv)) {
    foreach ($argv as $arg) {
        if ($arg === '--execute') {
            $dryRun = false;
        }
        if ($arg === '--quiet') {
            $verboseOutput = false;
        }
    }
}

echo "=== Organization-Agnostic Migration Script ===\n";
echo "Mode: " . ($dryRun ? "DRY RUN (use --execute to apply changes)" : "EXECUTING CHANGES") . "\n";
echo "Verbose: " . ($verboseOutput ? "ON" : "OFF") . "\n\n";

// Define replacement mappings for organization-agnostic content
$replacements = [
    // Church-specific terms to organization-agnostic terms
    'Church Team' => '{organization_name} Team',
    'church family' => '{organization_type} community',
    'Church Family' => '{organization_type} Community',
    'our church' => 'our {organization_type}',
    'Our church' => 'Our {organization_type}',
    'Our Church' => 'Our {organization_type}',
    'the church' => 'the {organization_type}',
    'The church' => 'The {organization_type}',
    'The Church' => 'The {organization_type}',
    'church community' => '{organization_type} community',
    'Church community' => '{organization_type} community',
    'Church Community' => '{organization_type} Community',
    
    // Religious-specific greetings to neutral ones
    'Blessings,' => 'Best regards,',
    'God bless,' => 'Best regards,',
    'In Christ,' => 'Sincerely,',
    'Grace and peace,' => 'Best regards,',
    
    // Pastor references to dynamic leader term
    'Pastor' => '{leader_term}',
    'pastor' => '{leader_term}',
    'our pastor' => 'our {leader_term}',
    'Our pastor' => 'Our {leader_term}',
    'Our Pastor' => 'Our {leader_term}',
    
    // Member references to dynamic member term
    'church member' => '{member_term}',
    'Church member' => '{member_term}',
    'Church Member' => '{member_term}',
    'congregation member' => '{member_term}',
    'Congregation member' => '{member_term}',
    'Congregation Member' => '{member_term}',
    
    // Service/Event references to dynamic event term
    'church service' => '{event_term}',
    'Church service' => '{event_term}',
    'Church Service' => '{event_term}',
    'worship service' => '{event_term}',
    'Worship service' => '{event_term}',
    'Worship Service' => '{event_term}',
    
    // Ministry/Group references to dynamic group term
    'ministry' => '{group_term}',
    'Ministry' => '{group_term}',
    'ministries' => '{group_term}s',
    'Ministries' => '{group_term}s',
    
    // Offering/Donation references to dynamic donation term
    'offering' => '{donation_term}',
    'Offering' => '{donation_term}',
    'offerings' => '{donation_term}s',
    'Offerings' => '{donation_term}s',
    'church offering' => '{donation_term}',
    'Church offering' => '{donation_term}',
    'Church Offering' => '{donation_term}',
    
    // Hardcoded organization names
    'Freedom Assembly Church International' => '{organization_name}',
    'Freedom Assembly Church' => '{organization_name}',
    'FACI' => '{organization_name}',
];

// Tables to update
$tables = [
    'email_templates' => ['content', 'subject'],
    'whatsapp_templates' => ['message_content'],
    'settings' => ['setting_value']
];

$totalChanges = 0;
$changesLog = [];

// Process each table
foreach ($tables as $tableName => $columns) {
    echo "\n=== Processing table: $tableName ===\n";
    
    try {
        // Get all records from the table
        $stmt = $pdo->prepare("SELECT * FROM $tableName");
        $stmt->execute();
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($records)) {
            echo "No records found in $tableName\n";
            continue;
        }
        
        echo "Found " . count($records) . " records in $tableName\n";
        
        foreach ($records as $record) {
            $recordChanges = [];
            $recordId = $record['id'] ?? 'unknown';
            
            foreach ($columns as $column) {
                if (!isset($record[$column]) || empty($record[$column])) {
                    continue;
                }
                
                $originalContent = $record[$column];
                $updatedContent = $originalContent;
                $columnChanges = [];
                
                // Apply all replacements
                foreach ($replacements as $search => $replace) {
                    if (strpos($updatedContent, $search) !== false) {
                        $updatedContent = str_replace($search, $replace, $updatedContent);
                        $columnChanges[] = "$search → $replace";
                    }
                }
                
                // If content was changed, prepare update
                if ($updatedContent !== $originalContent) {
                    $recordChanges[$column] = [
                        'original' => $originalContent,
                        'updated' => $updatedContent,
                        'changes' => $columnChanges
                    ];
                }
            }
            
            // If this record has changes, log and optionally execute
            if (!empty($recordChanges)) {
                $totalChanges++;
                $logEntry = [
                    'table' => $tableName,
                    'id' => $recordId,
                    'changes' => $recordChanges
                ];
                $changesLog[] = $logEntry;
                
                if ($verboseOutput) {
                    echo "\nRecord ID $recordId:\n";
                    foreach ($recordChanges as $column => $changeData) {
                        echo "  Column '$column':\n";
                        foreach ($changeData['changes'] as $change) {
                            echo "    - $change\n";
                        }
                    }
                }
                
                // Execute the update if not in dry run mode
                if (!$dryRun) {
                    try {
                        $updateColumns = [];
                        $updateValues = [];
                        
                        foreach ($recordChanges as $column => $changeData) {
                            $updateColumns[] = "$column = ?";
                            $updateValues[] = $changeData['updated'];
                        }
                        
                        $updateValues[] = $recordId; // For WHERE clause
                        
                        $updateSql = "UPDATE $tableName SET " . implode(', ', $updateColumns) . " WHERE id = ?";
                        $updateStmt = $pdo->prepare($updateSql);
                        $updateStmt->execute($updateValues);
                        
                        if ($verboseOutput) {
                            echo "  ✓ Updated successfully\n";
                        }
                    } catch (PDOException $e) {
                        echo "  ✗ Error updating record: " . $e->getMessage() . "\n";
                    }
                }
            }
        }
        
    } catch (PDOException $e) {
        echo "Error processing table $tableName: " . $e->getMessage() . "\n";
    }
}

// Summary
echo "\n=== Migration Summary ===\n";
echo "Total records with changes: $totalChanges\n";

if ($dryRun) {
    echo "\nThis was a DRY RUN. No changes were made to the database.\n";
    echo "To execute the changes, run: php organization_agnostic_migration.php --execute\n";
} else {
    echo "\nChanges have been applied to the database.\n";
}

// Save detailed log to file
$logFile = __DIR__ . '/migration_log_' . date('Y-m-d_H-i-s') . '.json';
file_put_contents($logFile, json_encode($changesLog, JSON_PRETTY_PRINT));
echo "\nDetailed log saved to: $logFile\n";

echo "\n=== Migration Complete ===\n";
?>
