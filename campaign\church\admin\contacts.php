<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['contact_file'])) {
    $file = $_FILES['contact_file'];
    $fileName = $file['name'];
    $fileType = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    
    // Check file type
    if ($fileType != "csv" && $fileType != "txt") {
        $error = "Only CSV and TXT files are allowed.";
    } else {
        try {
            $handle = fopen($file['tmp_name'], "r");
            $conn->beginTransaction();
            $success_count = 0;
            $error_count = 0;
            
            while (($data = fgetcsv($handle, 1000, "|")) !== FALSE) {
                if (count($data) >= 1) {
                    $email = trim($data[0]);
                    $name = isset($data[1]) ? trim($data[1]) : '';
                    
                    // Validate email
                    if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        // If name is empty, use email prefix
                        if (empty($name)) {
                            $name = ucfirst(explode('@', $email)[0]);
                        }
                        
                        // Try to insert the contact
                        try {
                            $stmt = $conn->prepare("INSERT INTO contacts (email, name) VALUES (?, ?) ON DUPLICATE KEY UPDATE name = VALUES(name)");
                            $stmt->execute([$email, $name]);
                            $success_count++;
                        } catch (PDOException $e) {
                            $error_count++;
                        }
                    } else {
                        $error_count++;
                    }
                }
            }
            
            fclose($handle);
            $conn->commit();
            
            $message = "Successfully imported $success_count contacts." . 
                      ($error_count > 0 ? " Failed to import $error_count entries." : "");
        } catch (PDOException $e) {
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            $error = "Error processing file: " . $e->getMessage();
        }
    }
}

// Get all contact groups
try {
    $stmt = $conn->query("SELECT * FROM contact_groups ORDER BY name");
    $groups = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = "Error fetching groups: " . $e->getMessage();
}

// Get statistics
try {
    // Total contacts count
    $total_contacts_stmt = $conn->query("SELECT COUNT(*) FROM contacts");
    $total_contacts = $total_contacts_stmt->fetchColumn();
    
    // Total groups count
    $total_groups_stmt = $conn->query("SELECT COUNT(*) FROM contact_groups");
    $total_groups = $total_groups_stmt->fetchColumn();
    
    // Contacts without groups
    $ungrouped_contacts_stmt = $conn->query("SELECT COUNT(DISTINCT c.id) FROM contacts c 
                                           LEFT JOIN contact_group_members m ON c.id = m.contact_id 
                                           WHERE m.group_id IS NULL");
    $ungrouped_contacts = $ungrouped_contacts_stmt->fetchColumn();
    
    // Recent contacts (last 30 days)
    $recent_contacts_stmt = $conn->query("SELECT COUNT(*) FROM contacts WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
    $recent_contacts = $recent_contacts_stmt->fetchColumn();
} catch (PDOException $e) {
    $error = "Error fetching statistics: " . $e->getMessage();
}

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Delete contact
if (isset($_GET['delete_id']) && !empty($_GET['delete_id'])) {
    if (!isset($_GET['csrf_token']) || $_GET['csrf_token'] !== $_SESSION['csrf_token']) {
        $error = "Invalid security token. Please try again.";
    } else {
        $delete_id = intval($_GET['delete_id']);

        try {
            $conn->beginTransaction();

            // Delete associated records first (to handle foreign key constraints)
            // Delete from contact_email_logs first (if table exists)
            try {
                $stmt = $conn->prepare("DELETE FROM contact_email_logs WHERE recipient_id = ?");
                $stmt->execute([$delete_id]);
            } catch (PDOException $e) {
                // Table might not exist, continue
            }

            // Delete group memberships
            $stmt = $conn->prepare("DELETE FROM contact_group_members WHERE contact_id = ?");
            $stmt->execute([$delete_id]);

            // Now delete the contact
            $stmt = $conn->prepare("DELETE FROM contacts WHERE id = ?");
            if ($stmt->execute([$delete_id])) {
                $conn->commit();
                $message = "Contact deleted successfully!";
            } else {
                $conn->rollBack();
                $error = "Failed to delete contact.";
            }
        } catch (PDOException $e) {
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            $error = "Error deleting contact: " . $e->getMessage();
        }
    }
}

// Sorting parameters
$sort_field = isset($_GET['sort']) ? $_GET['sort'] : 'created_at';
$sort_order = isset($_GET['order']) ? $_GET['order'] : 'DESC';
$allowed_sort_fields = ['name', 'email', 'created_at', 'group'];
if (!in_array($sort_field, $allowed_sort_fields)) {
    $sort_field = 'created_at';
}
$sort_order = strtoupper($sort_order) === 'ASC' ? 'ASC' : 'DESC';

// Get contacts with pagination and sorting
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 25;
if ($limit < 10) $limit = 10;
if ($limit > 100) $limit = 100;
$offset = ($page - 1) * $limit;

// Check if filtering by a specific group
$group_id = isset($_GET['group_id']) ? (int)$_GET['group_id'] : null;

try {
    // Base query for total count
    if ($group_id) {
        $total_stmt = $conn->prepare("SELECT COUNT(DISTINCT c.id) FROM contacts c 
                                     JOIN contact_group_members m ON c.id = m.contact_id 
                                     WHERE m.group_id = ?");
        $total_stmt->execute([$group_id]);
    } else {
        $total_stmt = $conn->query("SELECT COUNT(*) FROM contacts");
    }
    $total_contacts = $total_stmt->fetchColumn();
    $total_pages = ceil($total_contacts / $limit);
    
    // Ensure current page is within valid range
    if ($page < 1) $page = 1;
    if ($page > $total_pages) $page = $total_pages;
    
    // Modified query to support group-based sorting
    if ($sort_field === 'group') {
        if ($group_id) {
            // Sort contacts within a specific group
            $query = "SELECT c.* FROM contacts c 
                     JOIN contact_group_members m ON c.id = m.contact_id 
                     JOIN contact_groups g ON m.group_id = g.id 
                     WHERE m.group_id = ? 
                     ORDER BY c.name " . $sort_order . " 
                     LIMIT ? OFFSET ?";
            $stmt = $conn->prepare($query);
            $stmt->bindValue(1, $group_id, PDO::PARAM_INT);
            $stmt->bindValue(2, $limit, PDO::PARAM_INT);
            $stmt->bindValue(3, $offset, PDO::PARAM_INT);
        } else {
            // Sort all contacts by group names
            $query = "SELECT DISTINCT c.*, 
                     GROUP_CONCAT(g.name ORDER BY g.name ASC) as group_names
                     FROM contacts c 
                     LEFT JOIN contact_group_members m ON c.id = m.contact_id 
                     LEFT JOIN contact_groups g ON m.group_id = g.id 
                     GROUP BY c.id 
                     ORDER BY group_names " . $sort_order . " 
                     LIMIT ? OFFSET ?";
            $stmt = $conn->prepare($query);
            $stmt->bindValue(1, $limit, PDO::PARAM_INT);
            $stmt->bindValue(2, $offset, PDO::PARAM_INT);
        }
    } else {
        if ($group_id) {
            // Filter contacts by specific group
            $query = "SELECT c.* FROM contacts c 
                     JOIN contact_group_members m ON c.id = m.contact_id 
                     WHERE m.group_id = ? 
                     ORDER BY c.$sort_field $sort_order 
                     LIMIT ? OFFSET ?";
            $stmt = $conn->prepare($query);
            $stmt->bindValue(1, $group_id, PDO::PARAM_INT);
            $stmt->bindValue(2, $limit, PDO::PARAM_INT);
            $stmt->bindValue(3, $offset, PDO::PARAM_INT);
        } else {
            // No group filter
            $query = "SELECT * FROM contacts ORDER BY $sort_field $sort_order LIMIT ? OFFSET ?";
            $stmt = $conn->prepare($query);
            $stmt->bindValue(1, $limit, PDO::PARAM_INT);
            $stmt->bindValue(2, $offset, PDO::PARAM_INT);
        }
    }
    
    $stmt->execute();
    $contacts = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = "Error fetching contacts: " . $e->getMessage();
}

// Calculate pagination ranges
$start_number = $offset + 1;
$end_number = min($offset + $limit, $total_contacts);

// Set page variables
$page_title = 'Contact Management';
$page_header = 'Contact Management';
$page_description = 'Manage external contacts for bulk email communications.';

// Include header
include 'includes/header.php';

// Display messages
if (!empty($message)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($message) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

if (!empty($error)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($error) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

// Display session messages
if (isset($_SESSION['success'])) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($_SESSION['success']) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
    unset($_SESSION['success']);
}

if (isset($_SESSION['error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($_SESSION['error']) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
    unset($_SESSION['error']);
}
?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1">Total Contacts</h6>
                        <h2 class="mb-0"><?php echo number_format($total_contacts); ?></h2>
                    </div>
                    <div class="fs-1">
                        <i class="bi bi-person-lines-fill"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1">Total Groups</h6>
                        <h2 class="mb-0"><?php echo number_format($total_groups); ?></h2>
                    </div>
                    <div class="fs-1">
                        <i class="bi bi-folder"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1">Ungrouped Contacts</h6>
                        <h2 class="mb-0"><?php echo number_format($ungrouped_contacts); ?></h2>
                    </div>
                    <div class="fs-1">
                        <i class="bi bi-person-dash"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1">Recent Contacts</h6>
                        <h2 class="mb-0"><?php echo number_format($recent_contacts); ?></h2>
                        <small>Last 30 days</small>
                    </div>
                    <div class="fs-1">
                        <i class="bi bi-clock-history"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-upload me-2"></i>Import Contacts
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" class="mb-4">
                    <div class="mb-3">
                        <label for="contact_file" class="form-label">Upload Contact File</label>
                        <input type="file" class="form-control" id="contact_file" name="contact_file" accept=".csv,.txt" required>
                        <div class="form-text">
                            Upload a CSV or TXT file with contacts in the format: <code>email|name</code><br>
                            If name is not provided, it will be generated from the email address.
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-upload me-2"></i>Upload Contacts
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Contacts Table -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person-lines-fill me-2"></i>Contact List
                    </h5>
                    <small class="text-muted">
                        Showing <?php echo $start_number; ?> to <?php echo $end_number; ?> of <?php echo number_format($total_contacts); ?> contacts
                    </small>
                </div>
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <select class="form-select form-select-sm" id="limitSelect">
                            <option value="25" <?php echo $limit == 25 ? 'selected' : ''; ?>>25 per page</option>
                            <option value="50" <?php echo $limit == 50 ? 'selected' : ''; ?>>50 per page</option>
                            <option value="100" <?php echo $limit == 100 ? 'selected' : ''; ?>>100 per page</option>
                        </select>
                    </div>
                    <div class="me-3">
                        <select class="form-select form-select-sm" id="sortGroupSelect">
                            <option value="">Sort by Group</option>
                            <?php foreach ($groups as $group): ?>
                            <option value="<?php echo $group['id']; ?>"><?php echo htmlspecialchars($group['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <!-- Bulk Actions -->
                    <div class="me-3" id="bulkActionsContainer" style="display: none;">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-danger" id="bulkDeleteBtn">
                                <i class="bi bi-trash me-1"></i>Delete Selected (<span id="selectedCount">0</span>)
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="clearSelectionBtn">
                                <i class="bi bi-x-circle me-1"></i>Clear
                            </button>
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#importModal">
                        <i class="bi bi-upload me-1"></i>Import
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#groupModal">
                        <i class="bi bi-folder-plus me-1"></i>Create Group
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 50px;">
                                    <input type="checkbox" class="form-check-input" id="selectAllContacts" title="Select All">
                                </th>
                                <th style="width: 50px;">#</th>
                                <th>
                                    <a href="?sort=name&order=<?php echo $sort_field == 'name' && $sort_order == 'ASC' ? 'DESC' : 'ASC'; ?>"
                                       class="text-decoration-none text-dark">
                                        Name
                                        <?php if ($sort_field == 'name'): ?>
                                            <i class="bi bi-arrow-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                        <?php endif; ?>
                                    </a>
                                </th>
                                <th>
                                    <a href="?sort=email&order=<?php echo $sort_field == 'email' && $sort_order == 'ASC' ? 'DESC' : 'ASC'; ?>"
                                       class="text-decoration-none text-dark">
                                        Email
                                        <?php if ($sort_field == 'email'): ?>
                                            <i class="bi bi-arrow-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                        <?php endif; ?>
                                    </a>
                                </th>
                                <th>
                                    <a href="?sort=group&order=<?php echo $sort_field == 'group' && $sort_order == 'ASC' ? 'DESC' : 'ASC'; ?>"
                                       class="text-decoration-none text-dark">
                                        Groups
                                        <?php if ($sort_field == 'group'): ?>
                                            <i class="bi bi-arrow-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                        <?php endif; ?>
                                    </a>
                                </th>
                                <th>
                                    <a href="?sort=created_at&order=<?php echo $sort_field == 'created_at' && $sort_order == 'ASC' ? 'DESC' : 'ASC'; ?>"
                                       class="text-decoration-none text-dark">
                                        Added Date
                                        <?php if ($sort_field == 'created_at'): ?>
                                            <i class="bi bi-arrow-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                        <?php endif; ?>
                                    </a>
                                </th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($contacts as $index => $contact): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input contact-checkbox"
                                           value="<?php echo $contact['id']; ?>"
                                           data-name="<?php echo htmlspecialchars($contact['name']); ?>"
                                           data-email="<?php echo htmlspecialchars($contact['email']); ?>">
                                </td>
                                <td><?php echo $start_number + $index; ?></td>
                                <td><?php echo htmlspecialchars($contact['name']); ?></td>
                                <td><?php echo htmlspecialchars($contact['email']); ?></td>
                                <td>
                                    <?php
                                    $stmt = $conn->prepare("SELECT DISTINCT g.name FROM contact_groups g
                                                          JOIN contact_group_members m ON g.id = m.group_id
                                                          WHERE m.contact_id = ?
                                                          ORDER BY g.name");
                                    $stmt->execute([$contact['id']]);
                                    $contact_groups = $stmt->fetchAll(PDO::FETCH_COLUMN);

                                    foreach ($contact_groups as $group) {
                                        echo '<span class="badge bg-info me-1">' . htmlspecialchars($group) . '</span>';
                                    }
                                    if (empty($contact_groups)) {
                                        echo '<span class="badge bg-secondary">No Groups</span>';
                                    }
                                    ?>
                                </td>
                                <td><?php echo date('M j, Y', strtotime($contact['created_at'])); ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary edit-contact"
                                            data-id="<?php echo $contact['id']; ?>"
                                            data-name="<?php echo htmlspecialchars($contact['name']); ?>"
                                            data-email="<?php echo htmlspecialchars($contact['email']); ?>">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <a href="#" onclick="confirmDelete(<?php echo $contact['id']; ?>, '<?php echo addslashes($contact['name']); ?>')" class="btn btn-sm btn-danger">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Enhanced Pagination -->
                <?php if ($total_pages > 1): ?>
                <nav aria-label="Contact list pagination" class="mt-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="pagination-info">
                            Page <?php echo $page; ?> of <?php echo $total_pages; ?>
                        </div>
                        <ul class="pagination mb-0">
                            <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=1&sort=<?php echo $sort_field; ?>&order=<?php echo $sort_order; ?>&limit=<?php echo $limit; ?>">
                                    <i class="bi bi-chevron-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&sort=<?php echo $sort_field; ?>&order=<?php echo $sort_order; ?>&limit=<?php echo $limit; ?>">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                            
                            <?php
                            $start_page = max(1, min($page - 2, $total_pages - 4));
                            $end_page = min($total_pages, max(5, $page + 2));
                            
                            for ($i = $start_page; $i <= $end_page; $i++):
                            ?>
                            <li class="page-item <?php echo $page == $i ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&sort=<?php echo $sort_field; ?>&order=<?php echo $sort_order; ?>&limit=<?php echo $limit; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                            <?php endfor; ?>
                            
                            <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&sort=<?php echo $sort_field; ?>&order=<?php echo $sort_order; ?>&limit=<?php echo $limit; ?>">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $total_pages; ?>&sort=<?php echo $sort_field; ?>&order=<?php echo $sort_order; ?>&limit=<?php echo $limit; ?>">
                                    <i class="bi bi-chevron-double-right"></i>
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Import Contacts Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Contacts</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    Upload a CSV file with columns: Name, Email, Groups (optional)
                </div>
                <form id="importForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="import_file" class="form-label">CSV File</label>
                        <input type="file" class="form-control" id="import_file" name="import_file" accept=".csv" required>
                        <div class="form-text">Maximum file size: 2MB</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skip_duplicates" name="skip_duplicates" checked>
                            <label class="form-check-label" for="skip_duplicates">
                                Skip duplicate email addresses
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="startImport">
                    <i class="bi bi-upload me-2"></i>Import Contacts
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Create Group Modal -->
<div class="modal fade" id="groupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Contact Group</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="groupForm">
                    <div class="mb-3">
                        <label for="group_name" class="form-label">Group Name</label>
                        <input type="text" class="form-control" id="group_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="group_description" class="form-label">Description</label>
                        <textarea class="form-control" id="group_description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveGroup">Create Group</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Contact Modal -->
<div class="modal fade" id="editContactModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Contact</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editContactForm">
                    <input type="hidden" id="edit_contact_id">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="edit_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Groups</label>
                        <div id="groupCheckboxes">
                            <?php foreach ($groups as $group): ?>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox"
                                       name="groups[]"
                                       value="<?php echo $group['id']; ?>"
                                       id="edit_group_<?php echo $group['id']; ?>">
                                <label class="form-check-label" for="edit_group_<?php echo $group['id']; ?>">
                                    <?php echo htmlspecialchars($group['name']); ?>
                                </label>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveContact">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Individual Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete <strong><span id="contactNameToDelete"></span></strong>? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="confirmDeleteButton" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Confirmation Modal -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle me-2"></i>Confirm Bulk Deletion
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h6><i class="bi bi-shield-exclamation me-2"></i>⚠️ DANGER: This action cannot be undone!</h6>
                    <p class="mb-0">You are about to permanently delete the selected contacts and all their associated data.</p>
                </div>

                <div class="mb-3">
                    <h6>Selected Contacts (<span id="bulkDeleteCount">0</span>):</h6>
                    <div id="bulkDeletePreview" class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                        <!-- Contact list will be populated here -->
                    </div>
                </div>

                <div class="mb-3">
                    <h6>What will be deleted:</h6>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check text-danger me-2"></i>Contact records</li>
                        <li><i class="bi bi-check text-danger me-2"></i>Group memberships</li>
                        <li><i class="bi bi-check text-danger me-2"></i>Email logs and tracking data</li>
                        <li><i class="bi bi-check text-danger me-2"></i>All associated data</li>
                    </ul>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="confirmBulkDelete">
                    <label class="form-check-label text-danger fw-bold" for="confirmBulkDelete">
                        I understand this action is permanent and cannot be undone
                    </label>
                </div>

                <div class="mb-3">
                    <label for="bulkDeleteConfirmText" class="form-label">
                        Type <strong>DELETE</strong> to confirm:
                    </label>
                    <input type="text" class="form-control" id="bulkDeleteConfirmText" placeholder="Type DELETE here">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmBulkDeleteBtn" disabled>
                    <i class="bi bi-trash me-2"></i>Delete Selected Contacts
                </button>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<!-- Load jQuery from approved CDN -->
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/contacts.js"></script>
<script>
    function confirmDelete(contactId, contactName) {
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        document.getElementById('contactNameToDelete').textContent = contactName;
        document.getElementById('confirmDeleteButton').href = `contacts.php?delete_id=${contactId}&csrf_token=<?php echo $_SESSION['csrf_token']; ?>`;
        modal.show();
    }

    // Initialize functionality after page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize bulk delete if the function exists
        if (typeof initializeBulkDelete === 'function') {
            initializeBulkDelete();
        }

        // Initialize edit contact functionality if the function exists
        if (typeof initializeEditContactFunctionality === 'function') {
            initializeEditContactFunctionality();
        }
    });
</script>