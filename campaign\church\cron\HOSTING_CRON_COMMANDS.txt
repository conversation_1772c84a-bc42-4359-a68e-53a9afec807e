DYNAMIC CRON JOB COMMANDS FOR ANY HOSTING PROVIDER
==================================================

⚠️  IMPORTANT: Replace "YOUR_DOMAIN.COM" with your actual domain name
⚠️  Replace "YOUR_PATH" with your actual path structure

Copy and paste these commands into your hosting provider's cron job manager:

1. BIRTHDAY REMINDERS (Every 15 minutes at 1 AM)
   Frequency: */15 1 * * *
   Command: wget -q -O /dev/null "https://YOUR_DOMAIN.COM/YOUR_PATH/cron/birthday_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m"

2. EMAIL QUEUE PROCESSING (Every 5 minutes) - CRITICAL FIX
   Frequency: */5 * * * *
   Command: wget -q -O /dev/null "https://YOUR_DOMAIN.COM/YOUR_PATH/cron/process_email_queue.php?cron_key=fac_2024_secure_cron_8x9q2p5m"

3. SCHEDULED EMAILS (Every 5 minutes) - CRITICAL FIX
   Frequency: */5 * * * *
   Command: wget -q -O /dev/null "https://YOUR_DOMAIN.COM/YOUR_PATH/cron/process_scheduled_emails.php?cron_key=fac_2024_secure_cron_8x9q2p5m"

4. EVENT REMINDERS (Every 2 hours) - NEW FEATURE
   Frequency: 0 */2 * * *
   Command: wget -q -O /dev/null "https://YOUR_DOMAIN.COM/YOUR_PATH/cron/event_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m"

5. SYSTEM CLEANUP (Weekly - Sundays at 2 AM) - OPTIONAL
   Frequency: 0 2 * * 0
   Command: wget -q -O /dev/null "https://YOUR_DOMAIN.COM/YOUR_PATH/cron/system_cleanup.php?cron_key=fac_2024_secure_cron_8x9q2p5m"

EXAMPLE FOR FREEDOMASSEMBLYDB.ONLINE:
====================================

1. BIRTHDAY REMINDERS (Every 15 minutes at 1 AM)
   Frequency: */15 1 * * *
   Command: wget -q -O /dev/null "https://freedomassemblydb.online/campaign/church/cron/birthday_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m"

2. EMAIL QUEUE PROCESSING (Every 5 minutes) - CRITICAL FIX
   Frequency: */5 * * * *
   Command: wget -q -O /dev/null "https://freedomassemblydb.online/campaign/church/cron/process_email_queue.php?cron_key=fac_2024_secure_cron_8x9q2p5m"

3. SCHEDULED EMAILS (Every 5 minutes) - CRITICAL FIX
   Frequency: */5 * * * *
   Command: wget -q -O /dev/null "https://freedomassemblydb.online/campaign/church/cron/process_scheduled_emails.php?cron_key=fac_2024_secure_cron_8x9q2p5m"

4. EVENT REMINDERS (Every 2 hours) - NEW FEATURE
   Frequency: 0 */2 * * *
   Command: wget -q -O /dev/null "https://freedomassemblydb.online/campaign/church/cron/event_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m"

5. SYSTEM CLEANUP (Weekly - Sundays at 2 AM) - OPTIONAL
   Frequency: 0 2 * * 0
   Command: wget -q -O /dev/null "https://freedomassemblydb.online/campaign/church/cron/system_cleanup.php?cron_key=fac_2024_secure_cron_8x9q2p5m"

QUICK SETUP GUIDE:
==================

1. Log into your hosting control panel (cPanel, Plesk, etc.)
2. Find "Cron Jobs" or "Scheduled Tasks" section
3. Replace "YOUR_DOMAIN.COM" with your actual domain
4. Replace "YOUR_PATH" with your actual path (e.g., "campaign/church" or just "church")
5. Create 4 new cron jobs using the modified commands
6. Make sure to use the exact frequency patterns shown
7. Test by visiting: https://YOUR_DOMAIN.COM/YOUR_PATH/admin/cron_jobs.php

IMPORTANT NOTES:
===============
- The cron key "fac_2024_secure_cron_8x9q2p5m" is required for security
- Commands #2 and #3 are CRITICAL FIXES - they were broken before
- URLs are now dynamic and work with any domain/subdirectory structure
- Test email <NAME_EMAIL> after setup
- The system automatically detects the correct paths when running

TROUBLESHOOTING:
===============
If cron jobs don't work, try these alternative formats (replace YOUR_DOMAIN.COM/YOUR_PATH):

Alternative 1 (with /usr/bin/wget):
/usr/bin/wget -q -O /dev/null "https://YOUR_DOMAIN.COM/YOUR_PATH/cron/birthday_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m"

Alternative 2 (with curl):
curl -s "https://YOUR_DOMAIN.COM/YOUR_PATH/cron/birthday_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m" > /dev/null

Alternative 3 (with php - requires full server path):
php -q /full/server/path/to/your/site/YOUR_PATH/cron/birthday_reminders.php

TESTING COMMANDS:
================
Test manually by running these URLs in your browser (replace YOUR_DOMAIN.COM/YOUR_PATH):

https://YOUR_DOMAIN.COM/YOUR_PATH/cron/birthday_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m
https://YOUR_DOMAIN.COM/YOUR_PATH/cron/process_email_queue.php?cron_key=fac_2024_secure_cron_8x9q2p5m
https://YOUR_DOMAIN.COM/YOUR_PATH/cron/process_scheduled_emails.php?cron_key=fac_2024_secure_cron_8x9q2p5m

EXAMPLE TESTING URLS FOR FREEDOMASSEMBLYDB.ONLINE:
=================================================
https://freedomassemblydb.online/campaign/church/cron/birthday_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m
https://freedomassemblydb.online/campaign/church/cron/process_email_queue.php?cron_key=fac_2024_secure_cron_8x9q2p5m
https://freedomassemblydb.online/campaign/church/cron/process_scheduled_emails.php?cron_key=fac_2024_secure_cron_8x9q2p5m

STATUS: ✅ READY FOR DEPLOYMENT
==============================
