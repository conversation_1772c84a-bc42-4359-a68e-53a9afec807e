-- Fix for email recipient tracking
-- This script updates the email_tracking table to handle NULL member_id values

-- Check current structure of email_tracking
DESCRIBE email_tracking;

-- Reset all failed recipients to pending status
UPDATE email_schedule_recipients SET status = 'pending', error_message = NULL WHERE status = 'failed';

-- Make member_id nullable in email_tracking if not already
ALTER TABLE email_tracking MODIFY member_id int(11) NULL;

-- Check structure of email_logs table
DESCRIBE email_logs;

-- Show sample data for debugging
SELECT * FROM email_logs LIMIT 5;
SELECT * FROM email_schedule_recipients LIMIT 5;
SELECT * FROM email_tracking LIMIT 5;

-- Output count of pending recipients
SELECT COUNT(*) AS pending_count FROM email_schedule_recipients WHERE status = 'pending';
SELECT COUNT(*) as failed_count FROM email_schedule_recipients WHERE status = 'failed';
SELECT COUNT(*) as sent_count FROM email_schedule_recipients WHERE status = 'sent'; 