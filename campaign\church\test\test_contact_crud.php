<?php
/**
 * Test script for contact CRUD operations
 * This script creates, reads, updates, and deletes test contacts
 */

session_start();

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

echo "<h2>Contact CRUD Test</h2>";

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$action = $_GET['action'] ?? 'menu';

switch ($action) {
    case 'create':
        echo "<h3>Creating Test Contact</h3>";
        try {
            $test_email = 'test_delete_' . time() . '@example.com';
            $test_name = 'Test Contact ' . time();
            
            $stmt = $conn->prepare("INSERT INTO contacts (email, name, source) VALUES (?, ?, 'test')");
            $stmt->execute([$test_email, $test_name]);
            $contact_id = $conn->lastInsertId();
            
            echo "✅ Test contact created successfully!<br>";
            echo "ID: $contact_id<br>";
            echo "Name: $test_name<br>";
            echo "Email: $test_email<br>";
            echo "<p><a href='?action=delete&id=$contact_id'>Test Delete This Contact</a></p>";
            
        } catch (PDOException $e) {
            echo "❌ Error creating contact: " . $e->getMessage() . "<br>";
        }
        break;
        
    case 'delete':
        $contact_id = $_GET['id'] ?? 0;
        echo "<h3>Testing Individual Delete</h3>";
        
        if ($contact_id) {
            try {
                // First, get contact info
                $stmt = $conn->prepare("SELECT * FROM contacts WHERE id = ?");
                $stmt->execute([$contact_id]);
                $contact = $stmt->fetch();
                
                if ($contact) {
                    echo "Contact to delete:<br>";
                    echo "ID: " . $contact['id'] . "<br>";
                    echo "Name: " . htmlspecialchars($contact['name']) . "<br>";
                    echo "Email: " . htmlspecialchars($contact['email']) . "<br><br>";
                    
                    // Simulate the delete process from contacts.php
                    $conn->beginTransaction();
                    
                    // Delete associated records first
                    $stmt = $conn->prepare("DELETE FROM contact_email_logs WHERE recipient_id = ?");
                    $stmt->execute([$contact_id]);
                    echo "✅ Deleted email logs<br>";
                    
                    $stmt = $conn->prepare("DELETE FROM email_tracking WHERE contact_id = ?");
                    $stmt->execute([$contact_id]);
                    echo "✅ Deleted email tracking<br>";
                    
                    $stmt = $conn->prepare("DELETE FROM contact_group_members WHERE contact_id = ?");
                    $stmt->execute([$contact_id]);
                    echo "✅ Deleted group memberships<br>";
                    
                    // Delete the contact
                    $stmt = $conn->prepare("DELETE FROM contacts WHERE id = ?");
                    if ($stmt->execute([$contact_id])) {
                        $conn->commit();
                        echo "✅ Contact deleted successfully!<br>";
                    } else {
                        $conn->rollBack();
                        echo "❌ Failed to delete contact<br>";
                    }
                } else {
                    echo "❌ Contact not found<br>";
                }
            } catch (PDOException $e) {
                if ($conn->inTransaction()) {
                    $conn->rollBack();
                }
                echo "❌ Error deleting contact: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "❌ No contact ID provided<br>";
        }
        break;
        
    case 'test_ajax':
        echo "<h3>Testing AJAX Delete Handlers</h3>";
        
        // Test individual delete AJAX
        echo "<h4>Individual Delete AJAX Test</h4>";
        $delete_url = '../admin/ajax/delete_contact.php';
        if (file_exists($delete_url)) {
            echo "✅ delete_contact.php exists<br>";
            
            // Create a test contact first
            try {
                $test_email = 'ajax_test_' . time() . '@example.com';
                $test_name = 'AJAX Test Contact';
                
                $stmt = $conn->prepare("INSERT INTO contacts (email, name, source) VALUES (?, ?, 'test')");
                $stmt->execute([$test_email, $test_name]);
                $contact_id = $conn->lastInsertId();
                
                echo "Created test contact ID: $contact_id<br>";
                
                // Simulate AJAX call
                $_POST = json_decode(json_encode(['id' => $contact_id]), true);
                $_SESSION['admin_id'] = 1; // Simulate logged in admin
                
                ob_start();
                include $delete_url;
                $response = ob_get_clean();
                
                echo "AJAX Response: $response<br>";
                
                // Check if contact was deleted
                $stmt = $conn->prepare("SELECT COUNT(*) FROM contacts WHERE id = ?");
                $stmt->execute([$contact_id]);
                $count = $stmt->fetchColumn();
                
                if ($count == 0) {
                    echo "✅ AJAX delete successful<br>";
                } else {
                    echo "❌ AJAX delete failed - contact still exists<br>";
                }
                
            } catch (Exception $e) {
                echo "❌ AJAX test error: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "❌ delete_contact.php not found<br>";
        }
        break;
        
    default:
        echo "<h3>Test Menu</h3>";
        echo "<ul>";
        echo "<li><a href='?action=create'>Create Test Contact</a></li>";
        echo "<li><a href='?action=test_ajax'>Test AJAX Delete Handlers</a></li>";
        echo "<li><a href='../admin/contacts.php'>Go to Contacts Page</a></li>";
        echo "</ul>";
        break;
}

echo "<p><a href='?action=menu'>Back to Menu</a></p>";
?>
