<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Get the current month and set default filter to current month
$current_month = date('m');
$current_month_name = date('F');
$selected_month = isset($_GET['month']) ? $_GET['month'] : $current_month;
$selected_month_name = date('F', mktime(0, 0, 0, $selected_month, 1));

// Enhanced error handling for all database operations
try {
    // Get members with birthdays in the selected month
    $birthdays = [];
    $stmt = $conn->prepare("SELECT id, full_name, birth_date, email, phone_number, image_path FROM members WHERE MONTH(birth_date) = ? ORDER BY DAY(birth_date)");
    $stmt->execute([$selected_month]);
    $birthdays = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add day of the month to each member and filter those with phone numbers
    $processed_birthdays = [];
    foreach ($birthdays as $member) {
        if (!empty($member['phone_number'])) {
            $member_copy = $member;
            $member_copy['day'] = date('j', strtotime($member['birth_date']));
            $processed_birthdays[] = $member_copy;
        }
    }

    // Get WhatsApp templates
    $whatsapp_templates = [];
    $stmt = $conn->prepare("SHOW TABLES LIKE 'whatsapp_templates'");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $stmt = $conn->prepare("SELECT id, template_name, message_content, is_birthday FROM whatsapp_templates ORDER BY is_birthday DESC, template_name ASC");
        $stmt->execute();
        $whatsapp_templates = $stmt->fetchAll();
    } else {
        // Create the whatsapp_templates table if it doesn't exist
        $sql = "CREATE TABLE IF NOT EXISTS whatsapp_templates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            template_name VARCHAR(100) NOT NULL,
            message_content TEXT NOT NULL,
            is_birthday TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $conn->exec($sql);
        
        // Add sample birthday template
        $sample_template = "Hello {full_name},\n\nHappy Birthday! 🎂🎉\n\nWishing you a day filled with joy and blessings on your special day.\n\nBest regards,\n{sender_name}";
        $stmt = $conn->prepare("INSERT INTO whatsapp_templates (template_name, message_content, is_birthday) VALUES ('Birthday Wishes', ?, 1)");
        $stmt->execute([$sample_template]);
        
        // Try again to get templates
        $stmt = $conn->prepare("SELECT id, template_name, message_content, is_birthday FROM whatsapp_templates ORDER BY is_birthday DESC, template_name ASC");
        $stmt->execute();
        $whatsapp_templates = $stmt->fetchAll();
    }

    // Get WhatsApp settings
    $whatsapp_settings = [
        'sender_number' => '',
        'sender_name' => 'Freedom Assembly Church'
    ];

    // Check if whatsapp_settings table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'whatsapp_settings'");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $stmt = $conn->prepare("SELECT sender_number, sender_name FROM whatsapp_settings WHERE id = 1");
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $settings = $stmt->fetch();
            $whatsapp_settings['sender_number'] = $settings['sender_number'];
            $whatsapp_settings['sender_name'] = $settings['sender_name'];
        }
    } else {
        // Create the whatsapp_settings table if it doesn't exist
        $sql = "CREATE TABLE IF NOT EXISTS whatsapp_settings (
            id INT PRIMARY KEY DEFAULT 1,
            sender_number VARCHAR(20) NOT NULL,
            sender_name VARCHAR(100) NOT NULL
        )";
        
        $conn->exec($sql);
    }

    // Make sure WhatsApp logs table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'whatsapp_logs'");
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        // Create whatsapp_logs table
        $sql = "CREATE TABLE IF NOT EXISTS whatsapp_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            template_id INT NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'initiated',
            message TEXT NULL,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (member_id),
            INDEX (template_id)
        )";
        
        $conn->exec($sql);
        error_log("Created whatsapp_logs table");
    }

    // Get default WhatsApp template for quick sending
    $default_whatsapp_template_id = 0;
    $stmt = $conn->prepare("SELECT id FROM whatsapp_templates WHERE is_birthday = 1 ORDER BY id LIMIT 1");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        $default_whatsapp_template_id = $stmt->fetchColumn();
    }

    // Get recent WhatsApp logs
    $recent_logs = [];
    $stmt = $conn->prepare("SHOW TABLES LIKE 'whatsapp_logs'");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $query = "SELECT l.*, m.full_name, t.template_name 
                  FROM whatsapp_logs l
                  LEFT JOIN members m ON l.member_id = m.id
                  LEFT JOIN whatsapp_templates t ON l.template_id = t.id
                  ORDER BY l.sent_at DESC
                  LIMIT 10";
        
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $recent_logs = $stmt->fetchAll();
    }
} catch (PDOException $e) {
    // Log error and set session error message
    error_log("Error in whatsapp_messages.php: " . $e->getMessage());
    $_SESSION['error'] = "A database error occurred. Please try again or contact support.";
}

// Set page variables
$page_title = 'WhatsApp Birthday Messages';
$page_header = 'WhatsApp Birthday Messages';
$page_description = 'Send WhatsApp messages to birthday celebrants';

// Include header
include 'includes/header.php';

// Display success message if it exists in the session
if (isset($_SESSION['message']) && !empty($_SESSION['message'])) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($_SESSION['message']) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
    // Clear the message after displaying it
    unset($_SESSION['message']);
}

// Display error message if it exists in the session
if (isset($_SESSION['error']) && !empty($_SESSION['error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($_SESSION['error']) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
    // Clear the error after displaying it
    unset($_SESSION['error']);
}
?>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Send WhatsApp Messages</h5>
                <div>
                    <a href="whatsapp_templates.php" class="btn btn-success btn-sm">
                        <i class="bi bi-gear"></i> Manage Templates
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($whatsapp_settings['sender_number'])): ?>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>WhatsApp Sender Not Configured</strong>
                    <p class="mb-0">Please set up your WhatsApp sender number in <a href="whatsapp_templates.php" class="alert-link">WhatsApp Templates</a> before sending messages.</p>
                </div>
                <?php endif; ?>

                <div class="mb-4">
                    <form action="" method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="month" class="form-label">Select Month</label>
                            <select class="form-select" id="month" name="month" onchange="this.form.submit()">
                                <?php for ($i = 1; $i <= 12; $i++): ?>
                                <option value="<?php echo $i; ?>" <?php echo ($i == $selected_month) ? 'selected' : ''; ?>>
                                    <?php echo date('F', mktime(0, 0, 0, $i, 1)); ?>
                                </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </form>
                </div>

                <h5>Birthday Celebrants in <?php echo $selected_month_name; ?> with Phone Numbers</h5>
                
                <?php if (count($processed_birthdays) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Photo</th>
                                <th>Name</th>
                                <th>Birth Date</th>
                                <th>Phone</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($processed_birthdays as $member): ?>
                            <tr>
                                <td>
                                    <?php if (!empty($member['image_path'])): ?>
                                        <img src="../<?php echo htmlspecialchars($member['image_path']); ?>" 
                                             alt="<?php echo htmlspecialchars($member['full_name']); ?>" 
                                             class="rounded-circle member-photo"
                                             style="width: 40px; height: 40px; object-fit: cover;">
                                    <?php else: ?>
                                        <img src="../assets/img/default-profile.jpg" 
                                             alt="Default Profile" 
                                             class="rounded-circle member-photo"
                                             style="width: 40px; height: 40px; object-fit: cover;">
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($member['full_name']); ?></td>
                                <td><?php echo date('d M', strtotime($member['birth_date'])); ?></td>
                                <td><?php echo htmlspecialchars($member['phone_number']); ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-success send-whatsapp-btn" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#sendWhatsAppModal" 
                                                data-id="<?php echo $member['id']; ?>" 
                                                data-name="<?php echo htmlspecialchars($member['full_name']); ?>" 
                                                data-phone="<?php echo htmlspecialchars($member['phone_number']); ?>"
                                                data-birth-date="<?php echo date('d M', strtotime($member['birth_date'])); ?>">
                                            <i class="bi bi-whatsapp"></i> Compose Message
                                        </button>
                                        
                                        <?php if ($default_whatsapp_template_id > 0): ?>
                                        <a href="send_whatsapp_message.php?member_id=<?php echo $member['id']; ?>&template_id=<?php echo $default_whatsapp_template_id; ?>&recipient_name=<?php echo urlencode($member['full_name']); ?>&recipient_phone=<?php echo urlencode($member['phone_number']); ?>" 
                                           class="btn btn-outline-success" 
                                           title="Send quick WhatsApp using default template">
                                            <i class="bi bi-lightning"></i> Quick Send
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    No birthday celebrants with phone numbers found for <?php echo $selected_month_name; ?>.
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if (count($recent_logs) > 0): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Recent WhatsApp Messages</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm table-hover">
                <thead>
                    <tr>
                        <th>Date/Time</th>
                        <th>Member</th>
                        <th>Template</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_logs as $log): ?>
                    <tr>
                        <td><?php echo date('M d, Y g:i A', strtotime($log['sent_at'])); ?></td>
                        <td><?php echo htmlspecialchars($log['full_name'] ?? 'Unknown Member'); ?></td>
                        <td><?php echo htmlspecialchars($log['template_name'] ?? 'Unknown Template'); ?></td>
                        <td>
                            <span class="badge bg-<?php echo $log['status'] === 'initiated' ? 'info' : 'success'; ?>">
                                <?php echo ucfirst($log['status']); ?>
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <div class="mt-2 text-end">
            <a href="whatsapp_templates.php" class="btn btn-sm btn-outline-secondary">View All Message Logs</a>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Send WhatsApp Modal -->
<div class="modal fade" id="sendWhatsAppModal" tabindex="-1" aria-labelledby="sendWhatsAppModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="sendWhatsAppModalLabel"><i class="bi bi-whatsapp me-2"></i>Send WhatsApp Birthday Message</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php if (empty($whatsapp_settings['sender_number'])): ?>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>WhatsApp Sender Not Configured</strong>
                    <p class="mb-0">Please set up your WhatsApp sender number in <a href="whatsapp_templates.php" class="alert-link">WhatsApp Templates</a> before sending messages.</p>
                </div>
                <?php endif; ?>
                
                <form id="sendWhatsAppForm" action="send_whatsapp_message.php" method="post">
                    <input type="hidden" name="member_id" id="whatsapp_member_id">
                    <input type="hidden" name="recipient_phone" id="recipient_phone">
                    
                    <div class="mb-3">
                        <label for="whatsapp_recipient_name" class="form-label">Recipient Name</label>
                        <input type="text" class="form-control" id="whatsapp_recipient_name" name="recipient_name" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="whatsapp_template_id" class="form-label">WhatsApp Template</label>
                        <select class="form-select" id="whatsapp_template_id" name="template_id" required>
                            <option value="">Select a template</option>
                            <?php foreach ($whatsapp_templates as $template): ?>
                            <option value="<?php echo $template['id']; ?>" data-content="<?php echo htmlspecialchars($template['message_content']); ?>" <?php echo $template['is_birthday'] ? 'class="fw-bold"' : ''; ?>>
                                <?php echo htmlspecialchars($template['template_name']); ?> 
                                <?php echo $template['is_birthday'] ? '(Birthday)' : ''; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="whatsapp_message" class="form-label">Custom Message (Optional)</label>
                        <textarea class="form-control" id="whatsapp_message" name="custom_message" rows="5" placeholder="Enter any additional personal message to add to the template..."></textarea>
                        <small class="text-muted">This will be added to the template message. Leave empty to use just the template.</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="whatsapp-preview-container p-3 bg-light rounded">
                            <h6><i class="bi bi-eye me-2"></i>Message Preview</h6>
                            <div class="whatsapp-preview mt-2">
                                <div class="preview-content" id="whatsapp_preview_content">
                                    <p class="text-muted">Select a template to see preview</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" id="sendWhatsAppSubmitBtn" class="btn btn-success" <?php echo empty($whatsapp_settings['sender_number']) ? 'disabled' : ''; ?>>
                            <i class="bi bi-whatsapp me-1"></i> Send WhatsApp Message
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    /* Member photo styling */
    .member-photo {
        transition: transform 0.2s;
    }
    .member-photo:hover {
        transform: scale(1.2);
        cursor: pointer;
    }
    .table td {
        vertical-align: middle;
    }

    /* WhatsApp styling */
    .whatsapp-preview {
        background-color: #e5ddd5;
        border-radius: 8px;
        padding: 20px;
        max-height: 200px;
        overflow-y: auto;
    }
    
    .preview-content {
        background-color: #fff;
        border-radius: 7.5px;
        padding: 10px 15px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        position: relative;
        max-width: 80%;
        margin-left: auto;
    }
    
    .preview-content:after {
        content: "";
        position: absolute;
        top: 0;
        right: -10px;
        width: 0;
        height: 0;
        border-top: 10px solid #fff;
        border-right: 10px solid transparent;
    }
    
    .btn-whatsapp {
        background-color: #25D366;
        border-color: #25D366;
        color: white;
    }
    
    .btn-whatsapp:hover {
        background-color: #128C7E;
        border-color: #128C7E;
        color: white;
    }
</style>

<script>
document.addEventListener("DOMContentLoaded", function() {
    try {
        // Add error logging
        window.onerror = function(message, source, lineno, colno, error) {
            console.error("JavaScript Error:", message, "at", source, "line:", lineno, "column:", colno, "error:", error);
            return false;
        };
        
        // Store template data for easy access
        const whatsappTemplates = {};
        
        <?php foreach ($whatsapp_templates as $t): ?>
        whatsappTemplates["<?php echo $t['id']; ?>"] = {
            "name": <?php echo json_encode($t['template_name'] ?? ''); ?>,
            "content": <?php echo json_encode($t['message_content'] ?? ''); ?>
        };
        <?php endforeach; ?>
        
        // Handle WhatsApp button click
        const sendWhatsAppBtns = document.querySelectorAll(".send-whatsapp-btn");
        sendWhatsAppBtns.forEach(btn => {
            btn.addEventListener("click", function() {
                try {
                    const id = this.getAttribute("data-id");
                    const name = this.getAttribute("data-name");
                    const phone = this.getAttribute("data-phone");
                    const birthDate = this.getAttribute("data-birth-date");
                    
                    document.getElementById("whatsapp_member_id").value = id;
                    document.getElementById("whatsapp_recipient_name").value = name;
                    document.getElementById("recipient_phone").value = phone;
                    
                    // Clear the template selection and message
                    const templateSelect = document.getElementById("whatsapp_template_id");
                    templateSelect.value = "";
                    document.getElementById("whatsapp_message").value = "";
                    document.getElementById("whatsapp_preview_content").innerHTML = "<p class=\"text-muted\">Select a template to see preview</p>";
                    
                } catch (e) {
                    console.error("Error in WhatsApp button click handler:", e);
                    alert("Error: " + e.message);
                }
            });
        });
        
        // Handle template selection
        const whatsappTemplateSelect = document.getElementById("whatsapp_template_id");
        const whatsappMessageField = document.getElementById("whatsapp_message");
        const whatsappPreview = document.getElementById("whatsapp_preview_content");
        
        if (whatsappTemplateSelect && whatsappMessageField) {
            whatsappTemplateSelect.addEventListener("change", function() {
                try {
                    const templateId = this.value;
                    
                    if (templateId && whatsappTemplates[templateId]) {
                        // Get member name
                        const memberName = document.getElementById("whatsapp_recipient_name").value;
                        const memberFirstName = memberName.split(" ")[0];
                        const birthDate = document.querySelector(".send-whatsapp-btn[data-name='" + memberName + "']").getAttribute("data-birth-date");
                        
                        // Replace placeholders in the message for preview only
                        let messageContent = whatsappTemplates[templateId].content
                            .replace(/{full_name}/g, memberName)
                            .replace(/{first_name}/g, memberFirstName)
                            .replace(/{birth_date}/g, birthDate)
                            .replace(/{church_name}/g, "Freedom Assembly Church")
                            .replace(/{sender_name}/g, "<?php echo addslashes($whatsapp_settings['sender_name']); ?>");
                        
                        // Update preview
                        updateWhatsAppPreview(messageContent);
                    }
                } catch (e) {
                    console.error("Error in template change handler:", e);
                    document.getElementById("whatsapp_preview_content").innerHTML = "<p class=\"text-danger\">Error rendering preview: " + e.message + "</p>";
                }
            });
            
            // Update preview when custom message changes
            whatsappMessageField.addEventListener("input", function() {
                try {
                    // Get selected template id
                    const templateId = whatsappTemplateSelect.value;
                    
                    if (templateId && whatsappTemplates[templateId]) {
                        // Get member name for placeholders
                        const memberName = document.getElementById("whatsapp_recipient_name").value;
                        const memberFirstName = memberName.split(" ")[0];
                        const birthDate = document.querySelector(".send-whatsapp-btn[data-name='" + memberName + "']").getAttribute("data-birth-date");
                        
                        // Get template content with placeholders replaced
                        let messageContent = whatsappTemplates[templateId].content
                            .replace(/{full_name}/g, memberName)
                            .replace(/{first_name}/g, memberFirstName)
                            .replace(/{birth_date}/g, birthDate)
                            .replace(/{church_name}/g, "Freedom Assembly Church")
                            .replace(/{sender_name}/g, "<?php echo addslashes($whatsapp_settings['sender_name']); ?>");
                        
                        // Add custom message if any
                        const customMessage = this.value.trim();
                        if (customMessage) {
                            messageContent += "\n\n" + customMessage;
                        }
                        
                        // Update preview with combined content
                        updateWhatsAppPreview(messageContent);
                    }
                } catch (e) {
                    console.error("Error in message input handler:", e);
                    document.getElementById("whatsapp_preview_content").innerHTML = "<p class=\"text-danger\">Error updating preview: " + e.message + "</p>";
                }
            });
        }
        
        function updateWhatsAppPreview(content) {
            try {
                // Convert newlines to <br> for HTML display
                const previewHtml = content ? content.replace(/\n/g, "<br>") : "";
                
                // Update preview
                whatsappPreview.innerHTML = previewHtml || "<p class=\"text-muted\">No message content</p>";
                
                // Also store the raw content in a data attribute for form submission
                if (whatsappPreview && content) {
                    whatsappPreview.setAttribute('data-raw-content', content);
                }
            } catch (e) {
                console.error("Error in updateWhatsAppPreview:", e);
                whatsappPreview.innerHTML = "<p class=\"text-danger\">Error rendering preview</p>";
            }
        }
        
        // Handle form submission
        const whatsappForm = document.getElementById("sendWhatsAppForm");
        if (whatsappForm) {
            whatsappForm.addEventListener("submit", function(e) {
                try {
                    // Get phone number and template selection
                    const phone = document.getElementById("recipient_phone").value;
                    const templateId = document.getElementById("whatsapp_template_id").value;
                    
                    if (!phone || !templateId) {
                        e.preventDefault();
                        alert("Please select a template and ensure recipient has a phone number.");
                        return false;
                    }
                    
                    // Show sending indicator
                    const submitBtn = document.getElementById("sendWhatsAppSubmitBtn");
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Opening WhatsApp...';
                    submitBtn.disabled = true;
                    
                    // Let the form submit normally - it will redirect to WhatsApp
                    return true;
                } catch (e) {
                    console.error("Error in form submission:", e);
                    alert("Error: " + e.message);
                    e.preventDefault();
                    return false;
                }
            });
        }
        
    } catch (error) {
        console.error("Error in DOMContentLoaded:", error);
        alert("An error occurred while initializing the page. Please check the console for details.");
    }
});
</script>

<?php
// Include footer
include 'includes/footer.php';
?> 