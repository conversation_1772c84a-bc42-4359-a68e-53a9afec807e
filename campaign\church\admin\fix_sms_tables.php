<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

echo "<h2>Fix SMS Tables</h2>";
echo "<p>Fixing SMS database tables...</p>";

$errors = [];
$success = [];

try {
    // First, drop existing SMS tables if they exist (to start fresh)
    $tables_to_drop = ['sms_logs', 'sms_campaign_recipients', 'sms_campaigns', 'sms_templates'];
    
    echo "<h3>Dropping existing SMS tables...</h3>";
    foreach ($tables_to_drop as $table) {
        try {
            $pdo->exec("DROP TABLE IF EXISTS `$table`");
            echo "<span style='color: orange;'>⚠</span> Dropped table '$table'<br>";
        } catch (PDOException $e) {
            echo "<span style='color: red;'>✗</span> Error dropping '$table': " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h3>Creating SMS tables...</h3>";
    
    // Create SMS Templates table
    $sql = "CREATE TABLE `sms_templates` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `template_name` varchar(255) NOT NULL,
      `message_content` text NOT NULL,
      `category` varchar(100) DEFAULT NULL,
      `status` enum('active','inactive') NOT NULL DEFAULT 'active',
      `created_by` int(11) NOT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `idx_status` (`status`),
      KEY `idx_category` (`category`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $pdo->exec($sql);
    echo "<span style='color: green;'>✓</span> Created 'sms_templates' table<br>";
    
    // Create SMS Campaigns table
    $sql = "CREATE TABLE `sms_campaigns` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `campaign_name` varchar(255) NOT NULL,
      `message` text NOT NULL,
      `template_id` int(11) DEFAULT NULL,
      `total_recipients` int(11) NOT NULL DEFAULT 0,
      `sent_count` int(11) NOT NULL DEFAULT 0,
      `delivered_count` int(11) NOT NULL DEFAULT 0,
      `failed_count` int(11) NOT NULL DEFAULT 0,
      `status` enum('draft','scheduled','sending','completed','failed','cancelled') NOT NULL DEFAULT 'draft',
      `scheduled_at` timestamp NULL DEFAULT NULL,
      `sent_at` timestamp NULL DEFAULT NULL,
      `completed_at` timestamp NULL DEFAULT NULL,
      `created_by` int(11) NOT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `idx_status` (`status`),
      KEY `idx_scheduled_at` (`scheduled_at`),
      KEY `idx_created_by` (`created_by`),
      KEY `fk_sms_template` (`template_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $pdo->exec($sql);
    echo "<span style='color: green;'>✓</span> Created 'sms_campaigns' table<br>";
    
    // Create SMS Campaign Recipients table
    $sql = "CREATE TABLE `sms_campaign_recipients` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `campaign_id` int(11) NOT NULL,
      `member_id` int(11) DEFAULT NULL,
      `phone_number` varchar(20) NOT NULL,
      `status` enum('pending','sent','delivered','failed','cancelled') NOT NULL DEFAULT 'pending',
      `sent_at` timestamp NULL DEFAULT NULL,
      `delivered_at` timestamp NULL DEFAULT NULL,
      `error_message` text DEFAULT NULL,
      `provider_message_id` varchar(255) DEFAULT NULL,
      PRIMARY KEY (`id`),
      KEY `idx_campaign_id` (`campaign_id`),
      KEY `idx_member_id` (`member_id`),
      KEY `idx_status` (`status`),
      KEY `idx_phone_number` (`phone_number`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $pdo->exec($sql);
    echo "<span style='color: green;'>✓</span> Created 'sms_campaign_recipients' table<br>";
    
    // Create SMS Logs table
    $sql = "CREATE TABLE `sms_logs` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `campaign_id` int(11) DEFAULT NULL,
      `member_id` int(11) DEFAULT NULL,
      `phone_number` varchar(20) NOT NULL,
      `message` text NOT NULL,
      `provider` varchar(50) NOT NULL,
      `provider_message_id` varchar(255) DEFAULT NULL,
      `status` enum('sent','delivered','failed','pending') NOT NULL DEFAULT 'pending',
      `cost` decimal(10,4) DEFAULT NULL,
      `error_message` text DEFAULT NULL,
      `sent_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `idx_campaign_id` (`campaign_id`),
      KEY `idx_member_id` (`member_id`),
      KEY `idx_phone_number` (`phone_number`),
      KEY `idx_status` (`status`),
      KEY `idx_sent_at` (`sent_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $pdo->exec($sql);
    echo "<span style='color: green;'>✓</span> Created 'sms_logs' table<br>";
    
    echo "<h3>Adding foreign key constraints...</h3>";
    
    // Add foreign key constraints (after all tables are created)
    try {
        $pdo->exec("ALTER TABLE `sms_campaigns` ADD CONSTRAINT `fk_sms_campaigns_template` FOREIGN KEY (`template_id`) REFERENCES `sms_templates` (`id`) ON DELETE SET NULL");
        echo "<span style='color: green;'>✓</span> Added foreign key: sms_campaigns -> sms_templates<br>";
    } catch (PDOException $e) {
        echo "<span style='color: orange;'>⚠</span> Foreign key constraint already exists or failed: " . $e->getMessage() . "<br>";
    }
    
    try {
        $pdo->exec("ALTER TABLE `sms_campaign_recipients` ADD CONSTRAINT `fk_sms_campaign_recipients_campaign` FOREIGN KEY (`campaign_id`) REFERENCES `sms_campaigns` (`id`) ON DELETE CASCADE");
        echo "<span style='color: green;'>✓</span> Added foreign key: sms_campaign_recipients -> sms_campaigns<br>";
    } catch (PDOException $e) {
        echo "<span style='color: orange;'>⚠</span> Foreign key constraint already exists or failed: " . $e->getMessage() . "<br>";
    }
    
    try {
        $pdo->exec("ALTER TABLE `sms_logs` ADD CONSTRAINT `fk_sms_logs_campaign` FOREIGN KEY (`campaign_id`) REFERENCES `sms_campaigns` (`id`) ON DELETE SET NULL");
        echo "<span style='color: green;'>✓</span> Added foreign key: sms_logs -> sms_campaigns<br>";
    } catch (PDOException $e) {
        echo "<span style='color: orange;'>⚠</span> Foreign key constraint already exists or failed: " . $e->getMessage() . "<br>";
    }
    
    echo "<h3>Verifying SMS Tables:</h3>";
    $tables = ['sms_templates', 'sms_campaigns', 'sms_campaign_recipients', 'sms_logs'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<span style='color: green;'>✓</span> Table '$table' exists with " . count($columns) . " columns<br>";
        } catch (PDOException $e) {
            echo "<span style='color: red;'>✗</span> Table '$table' missing: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h3>Testing SMS Campaigns table columns:</h3>";
    try {
        $stmt = $pdo->query("SELECT delivered_count, failed_count, sent_count FROM sms_campaigns LIMIT 1");
        echo "<span style='color: green;'>✓</span> All required columns exist in sms_campaigns table<br>";
    } catch (PDOException $e) {
        echo "<span style='color: red;'>✗</span> Missing columns in sms_campaigns: " . $e->getMessage() . "<br>";
    }
    
    echo "<h3>✅ SMS Tables Setup Complete!</h3>";
    echo "<p>All SMS tables have been created successfully. You can now use the SMS features.</p>";
    
} catch (PDOException $e) {
    echo "<span style='color: red;'>✗</span> Critical Error: " . $e->getMessage() . "<br>";
}

echo "<br><a href='bulk_sms.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Go to Bulk SMS</a> ";
echo "<a href='sms_templates.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>Go to SMS Templates</a> ";
echo "<a href='sms_campaigns.php' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>Go to SMS Campaigns</a>";
?>
