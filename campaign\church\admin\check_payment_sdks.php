<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file with absolute path
$config_path = __DIR__ . '/../config.php';
require_once $config_path;

// Define the SDKs to check
$sdks = [
    'PayPal' => [
        'class' => 'PayPalCheckoutSdk\\Core\\SandboxEnvironment',
        'package' => 'paypal/paypal-checkout-sdk',
        'docs' => 'https://github.com/paypal/Checkout-PHP-SDK'
    ],
    'Stripe' => [
        'class' => 'Stripe\\Stripe',
        'package' => 'stripe/stripe-php',
        'docs' => 'https://github.com/stripe/stripe-php'
    ]
];

// Check if vendor/autoload.php exists
$autoloadExists = file_exists('../vendor/autoload.php');
$autoloadIncluded = false;

// Try to include autoload.php if it exists
if ($autoloadExists) {
    try {
        require_once '../vendor/autoload.php';
        $autoloadIncluded = true;
    } catch (Exception $e) {
        $error = "Error including autoload file: " . $e->getMessage();
    }
}

// Set page variables
$page_title = "Payment SDKs Check";
$page_header = "Payment SDKs Status";
$page_description = "Check the status of required payment SDKs and get installation instructions if needed.";

// Include header
include 'includes/header.php';
?>

            <!-- Instructions panel -->
            <div class="alert alert-info mb-4 instruction-panel">
                <div class="d-flex justify-content-between align-items-start">
                    <h5><i class="bi bi-info-circle-fill me-2"></i>Payment SDKs Check</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <p class="mb-2">This page helps you verify that all required payment SDKs are properly installed.</p>
                <ul class="mb-0">
                    <li><strong>Autoloader:</strong> Checks if Composer's autoloader is available</li>
                    <li><strong>PayPal SDK:</strong> Verifies PayPal Checkout SDK installation</li>
                    <li><strong>Stripe SDK:</strong> Verifies Stripe PHP SDK installation</li>
                </ul>
                <div class="mt-2">
                    <strong>Tip:</strong> If any SDK is missing, follow the installation instructions provided below.
                </div>
            </div>

            <!-- Autoloader Check -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Composer Autoloader Status</h5>
                </div>
                <div class="card-body">
                    <?php if ($autoloadExists): ?>
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            Composer autoload file found
                        </div>
                        <p>The file <code>vendor/autoload.php</code> exists and is accessible.</p>
                        <?php if ($autoloadIncluded): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                Successfully included autoload file
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="bi bi-x-circle-fill me-2"></i>
                            Composer autoload file not found
                        </div>
                        <p>The file <code>vendor/autoload.php</code> does not exist. Please run:</p>
                        <pre class="bg-light p-3 rounded"><code>composer install</code></pre>
                    <?php endif; ?>
                </div>
            </div>

            <!-- SDK Checks -->
            <?php foreach ($sdks as $name => $sdk): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><?php echo $name; ?> SDK Status</h5>
                </div>
                <div class="card-body">
                    <?php if ($autoloadIncluded): ?>
                        <?php if (class_exists($sdk['class'])): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <?php echo $name; ?> SDK is installed and available
                            </div>
                            <p>The class <code><?php echo htmlspecialchars($sdk['class']); ?></code> was found.</p>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <i class="bi bi-x-circle-fill me-2"></i>
                                <?php echo $name; ?> SDK is not installed
                            </div>
                            <p>To install the <?php echo $name; ?> SDK, run:</p>
                            <pre class="bg-light p-3 rounded"><code>composer require <?php echo $sdk['package']; ?></code></pre>
                            <p class="mt-3">
                                <a href="<?php echo $sdk['docs']; ?>" target="_blank" class="btn btn-outline-primary">
                                    <i class="bi bi-book me-2"></i>View <?php echo $name; ?> Documentation
                                </a>
                            </p>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            Cannot check <?php echo $name; ?> SDK without autoloader
                        </div>
                        <p>Please fix the autoloader issues first.</p>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>

            <!-- Installation Instructions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Installation Instructions</h5>
                </div>
                <div class="card-body">
                    <h6 class="mb-3">To install all required payment SDKs:</h6>
                    <ol>
                        <li class="mb-2">Ensure Composer is installed on your system
                            <a href="https://getcomposer.org/download/" target="_blank" class="ms-2 btn btn-sm btn-outline-primary">
                                <i class="bi bi-download me-1"></i>Get Composer
                            </a>
                        </li>
                        <li class="mb-2">Open a terminal in your project directory</li>
                        <li class="mb-2">Run the following command:</li>
                    </ol>
                    <pre class="bg-light p-3 rounded"><code>composer install</code></pre>
                    
                    <div class="mt-4">
                        <a href="payment_integration.php" class="btn btn-primary">
                            <i class="bi bi-gear-fill me-2"></i>Go to Payment Integration
                        </a>
                        <a href="payment_tables.php" class="btn btn-secondary ms-2">
                            <i class="bi bi-table me-2"></i>Check Payment Tables
                        </a>
                    </div>
                </div>
            </div>


<?php include 'includes/footer.php'; ?> 