<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Test template retrieval
try {
    // Get a sample template
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE is_birthday_template = 1 LIMIT 1");
    $stmt->execute();
    $template = $stmt->fetch();

    echo "<h2>Template Test</h2>";
    echo "<pre>";
    print_r($template);
    echo "</pre>";

    if ($template) {
        echo "<h3>Content Key Test</h3>";
        echo "<p>Template ID: " . $template['id'] . "</p>";
        echo "<p>Template Name: " . $template['template_name'] . "</p>";
        
        // Test if content key exists
        if (isset($template['content'])) {
            echo "<p style='color: green;'>✓ Content key exists</p>";
            echo "<p>Content length: " . strlen($template['content']) . " characters</p>";
        } else {
            echo "<p style='color: red;'>✗ Content key does NOT exist</p>";
        }
        
        // Test if template_content key exists (it shouldn't)
        if (isset($template['template_content'])) {
            echo "<p style='color: red;'>✗ template_content key exists (unexpected)</p>";
        } else {
            echo "<p style='color: green;'>✓ template_content key does NOT exist (as expected)</p>";
        }
        
        // Show all keys in the template array
        echo "<h3>All Template Keys</h3>";
        echo "<ul>";
        foreach (array_keys($template) as $key) {
            echo "<li>" . htmlspecialchars($key) . "</li>";
        }
        echo "</ul>";
    }
} catch (Exception $e) {
    echo "<h2>Error</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?> 