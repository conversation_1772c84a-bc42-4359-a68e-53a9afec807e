<?php
require_once '../config.php';
header('Content-Type: application/json');

try {
    // Get parameters
    $type = $_GET['type'] ?? '';
    $id = $_GET['id'] ?? '';

    // Validate parameters
    if (empty($type) || empty($id)) {
        throw new Exception('Missing required parameters');
    }

    // Determine table and fields based on type
    $table = $type === 'members' ? 'members' : 'contacts';
    $nameField = $type === 'members' ? 'full_name' : 'name';

    // Get recipient details
    $stmt = $pdo->prepare("SELECT id, $nameField as full_name, email FROM $table WHERE id = ?");
    $stmt->execute([$id]);
    $recipient = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$recipient) {
        throw new Exception('Recipient not found');
    }

    // Return success response
    echo json_encode([
        'success' => true,
        'data' => $recipient
    ]);

} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} 