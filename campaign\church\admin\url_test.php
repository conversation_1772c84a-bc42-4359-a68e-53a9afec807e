<?php
// Include environment configuration
require_once '../environment.php';

// Output URL constants
echo "<h2>URL Constants</h2>";
echo "<p>SITE_URL: " . SITE_URL . "</p>";
echo "<p>ADMIN_URL: " . ADMIN_URL . "</p>";

// Test URL generation
echo "<h2>URL Generation Tests</h2>";
echo "<p>ADMIN_URL . '/calendar_integration.php': " . ADMIN_URL . '/calendar_integration.php' . "</p>";
echo "<p>ADMIN_URL . 'calendar_integration.php': " . ADMIN_URL . 'calendar_integration.php' . "</p>";

// Check if files exist
echo "<h2>File Existence Tests</h2>";
$files = [
    'calendar_integration.php',
    'social_media_integration.php',
    'sms_integration.php'
];

foreach ($files as $file) {
    $path = __DIR__ . '/' . $file;
    echo "<p>File: $file - Exists: " . (file_exists($path) ? 'Yes' : 'No') . "</p>";
}
?> 