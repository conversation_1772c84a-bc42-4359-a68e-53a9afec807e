<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Get parameters
$memberId = isset($_GET['member_id']) ? intval($_GET['member_id']) : 0;
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Get member details if member_id is provided
$memberDetails = null;
if ($memberId > 0) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$memberId]);
        $memberDetails = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $error = "Error fetching member details: " . $e->getMessage();
    }
}

// Get recipient engagement statistics
$recipientStats = [];
try {
    $query = "SELECT 
                m.id,
                m.full_name,
                m.email,
                COUNT(DISTINCT el.id) as total_emails_received,
                COUNT(DISTINCT CASE WHEN et.opened_at IS NOT NULL THEN et.id END) as emails_opened,
                COUNT(DISTINCT CASE WHEN et.clicked_at IS NOT NULL THEN et.id END) as emails_clicked,
                MAX(et.opened_at) as last_opened,
                MAX(el.sent_at) as last_email_sent,
                ROUND(
                    COUNT(DISTINCT CASE WHEN et.opened_at IS NOT NULL THEN et.id END) / 
                    NULLIF(COUNT(DISTINCT el.id), 0) * 100, 
                    2
                ) as open_rate,
                ROUND(
                    COUNT(DISTINCT CASE WHEN et.clicked_at IS NOT NULL THEN et.id END) / 
                    NULLIF(COUNT(DISTINCT CASE WHEN et.opened_at IS NOT NULL THEN et.id END), 0) * 100, 
                    2
                ) as click_rate
              FROM 
                members m
              LEFT JOIN 
                email_logs el ON m.id = el.member_id
              LEFT JOIN 
                email_tracking et ON el.member_id = et.member_id AND DATE(el.sent_at) = DATE(et.sent_at)
              WHERE 
                m.status = 'active'
              GROUP BY 
                m.id, m.full_name, m.email
              HAVING 
                total_emails_received > 0
              ORDER BY 
                open_rate DESC, total_emails_received DESC
              LIMIT $offset, $limit";
              
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $recipientStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total count for pagination
    $countQuery = "SELECT COUNT(DISTINCT m.id) as total
                   FROM members m
                   LEFT JOIN email_logs el ON m.id = el.member_id
                   WHERE m.status = 'active' AND el.id IS NOT NULL";
    $countStmt = $pdo->prepare($countQuery);
    $countStmt->execute();
    $totalRecords = $countStmt->fetch()['total'];
    $totalPages = ceil($totalRecords / $limit);
    
} catch (PDOException $e) {
    $error = "Error fetching recipient statistics: " . $e->getMessage();
}

// Get individual member email history if member is selected
$memberEmailHistory = [];
if ($memberId > 0) {
    try {
        $query = "SELECT 
                    el.id,
                    el.email_type,
                    el.subject,
                    el.sent_at,
                    el.status,
                    et.opened_at,
                    et.opened_count,
                    et.clicked_at,
                    et.clicked_count,
                    et.user_agent,
                    et.ip_address,
                    ett.template_name
                  FROM 
                    email_logs el
                  LEFT JOIN 
                    email_tracking et ON el.member_id = et.member_id AND DATE(el.sent_at) = DATE(et.sent_at)
                  LEFT JOIN 
                    email_templates ett ON el.template_id = ett.id
                  WHERE 
                    el.member_id = ?
                  ORDER BY 
                    el.sent_at DESC
                  LIMIT 50";
                  
        $stmt = $pdo->prepare($query);
        $stmt->execute([$memberId]);
        $memberEmailHistory = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (PDOException $e) {
        $error = "Error fetching member email history: " . $e->getMessage();
    }
}

// Set page variables
$page_title = 'Recipient Analytics';
$page_header = 'Recipient Analytics';
$page_description = 'Detailed engagement analytics for email recipients.';

// Include header
include 'includes/header.php';
?>

<style>
.engagement-score {
    font-weight: bold;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}
.engagement-high { background-color: #d1e7dd; color: #0f5132; }
.engagement-medium { background-color: #fff3cd; color: #664d03; }
.engagement-low { background-color: #f8d7da; color: #721c24; }
.engagement-none { background-color: #e2e3e5; color: #41464b; }

.member-card {
    transition: all 0.3s ease;
    border-left: 4px solid #dee2e6;
}
.member-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.member-card.selected {
    border-left-color: #0d6efd;
    background-color: #f8f9fa;
}
</style>

<!-- Page Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="bi bi-person-lines-fill me-2"></i><?php echo $page_header; ?>
    </h1>
    <div>
        <a href="email_analytics.php" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left me-2"></i>Back to Email Analytics
        </a>
        <a href="bulk_email.php" class="btn btn-primary">
            <i class="bi bi-envelope-plus me-2"></i>Send Email
        </a>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
<?php endif; ?>

<div class="row">
    <!-- Recipient List -->
    <div class="col-lg-<?php echo $memberId > 0 ? '6' : '12'; ?>">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-people me-2"></i>Recipient Engagement Rankings
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($recipientStats)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <p class="text-muted mt-2">No email recipients found.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Recipient</th>
                                    <th>Emails</th>
                                    <th>Opens</th>
                                    <th>Clicks</th>
                                    <th>Engagement</th>
                                    <th>Last Activity</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recipientStats as $recipient): ?>
                                    <tr class="member-card <?php echo $memberId == $recipient['id'] ? 'selected' : ''; ?>" 
                                        style="cursor: pointer;" 
                                        onclick="window.location.href='?member_id=<?php echo $recipient['id']; ?>'">
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($recipient['full_name']); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo htmlspecialchars($recipient['email']); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $recipient['total_emails_received']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo $recipient['emails_opened']; ?></span>
                                            <br>
                                            <small class="text-muted"><?php echo $recipient['open_rate']; ?>%</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?php echo $recipient['emails_clicked']; ?></span>
                                            <br>
                                            <small class="text-muted"><?php echo $recipient['click_rate'] ?: '0'; ?>%</small>
                                        </td>
                                        <td>
                                            <?php 
                                            $openRate = $recipient['open_rate'];
                                            if ($openRate >= 50) {
                                                $engagementClass = 'engagement-high';
                                                $engagementText = 'High';
                                            } elseif ($openRate >= 25) {
                                                $engagementClass = 'engagement-medium';
                                                $engagementText = 'Medium';
                                            } elseif ($openRate > 0) {
                                                $engagementClass = 'engagement-low';
                                                $engagementText = 'Low';
                                            } else {
                                                $engagementClass = 'engagement-none';
                                                $engagementText = 'None';
                                            }
                                            ?>
                                            <span class="engagement-score <?php echo $engagementClass; ?>">
                                                <?php echo $engagementText; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php if ($recipient['last_opened']): ?>
                                                    Opened: <?php echo date('M j, Y', strtotime($recipient['last_opened'])); ?>
                                                <?php elseif ($recipient['last_email_sent']): ?>
                                                    Sent: <?php echo date('M j, Y', strtotime($recipient['last_email_sent'])); ?>
                                                <?php else: ?>
                                                    No activity
                                                <?php endif; ?>
                                            </small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Recipient pagination">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo ($page - 1); ?>&member_id=<?php echo $memberId; ?>">&laquo;</a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&member_id=<?php echo $memberId; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo ($page + 1); ?>&member_id=<?php echo $memberId; ?>">&raquo;</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Individual Member Details -->
    <?php if ($memberId > 0 && $memberDetails): ?>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-person-circle me-2"></i><?php echo htmlspecialchars($memberDetails['full_name']); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Email:</strong></div>
                        <div class="col-sm-8"><?php echo htmlspecialchars($memberDetails['email']); ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Phone:</strong></div>
                        <div class="col-sm-8"><?php echo htmlspecialchars($memberDetails['phone'] ?? 'N/A'); ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Status:</strong></div>
                        <div class="col-sm-8">
                            <span class="badge bg-<?php echo $memberDetails['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                <?php echo ucfirst($memberDetails['status']); ?>
                            </span>
                        </div>
                    </div>
                    
                    <h6 class="mt-4 mb-3">Email History</h6>
                    <?php if (empty($memberEmailHistory)): ?>
                        <p class="text-muted">No email history found.</p>
                    <?php else: ?>
                        <div class="timeline" style="max-height: 400px; overflow-y: auto;">
                            <?php foreach ($memberEmailHistory as $email): ?>
                                <div class="timeline-item mb-3 p-3 border rounded">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($email['subject'] ?: 'No Subject'); ?></h6>
                                            <small class="text-muted">
                                                <?php echo ucfirst($email['email_type']); ?> • 
                                                <?php echo date('M j, Y g:i A', strtotime($email['sent_at'])); ?>
                                            </small>
                                        </div>
                                        <span class="badge bg-<?php echo $email['status'] === 'success' ? 'success' : 'danger'; ?>">
                                            <?php echo ucfirst($email['status']); ?>
                                        </span>
                                    </div>
                                    
                                    <?php if ($email['opened_at']): ?>
                                        <div class="mt-2">
                                            <small class="text-success">
                                                <i class="bi bi-envelope-open me-1"></i>
                                                Opened <?php echo date('M j, Y g:i A', strtotime($email['opened_at'])); ?>
                                                <?php if ($email['opened_count'] > 1): ?>
                                                    (<?php echo $email['opened_count']; ?> times)
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($email['clicked_at']): ?>
                                        <div class="mt-1">
                                            <small class="text-warning">
                                                <i class="bi bi-cursor me-1"></i>
                                                Clicked <?php echo date('M j, Y g:i A', strtotime($email['clicked_at'])); ?>
                                                <?php if ($email['clicked_count'] > 1): ?>
                                                    (<?php echo $email['clicked_count']; ?> times)
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
