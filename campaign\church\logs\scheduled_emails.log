Test log entry: 2025-03-29 09:22:20
[2025-03-29 09:22:52] Scheduled emails cron job started.
[2025-03-29 09:22:52] [INFO] Starting to process scheduled emails
[2025-03-29 09:22:52] [INFO] Found 4 active schedules
[2025-03-29 09:22:52] [INFO] No pending recipients for schedule #14 (<PERSON><PERSON>).
[2025-03-29 09:22:52] [INFO] One-time schedule #14 (<PERSON><PERSON>) completed.
[2025-03-29 09:22:52] [INFO] No pending recipients for schedule #19 (<PERSON>).
[2025-03-29 09:22:52] [INFO] No pending recipients for schedule #20 (<PERSON>).
[2025-03-29 09:22:52] [INFO] One-time schedule #20 (<PERSON>) completed.
[2025-03-29 09:22:52] [INFO] No pending recipients for schedule #21 (<PERSON><PERSON>).
[2025-03-29 09:22:52] [INFO] One-time schedule #21 (<PERSON><PERSON>) completed.
[2025-03-29 09:22:52] [INFO] Finished processing scheduled emails
[2025-03-29 09:22:52] Scheduled emails cron job completed successfully.
[2025-03-29 09:27:03] Scheduled emails cron job started.
[2025-03-29 09:27:03] [INFO] Starting to process scheduled emails
[2025-03-29 09:27:03] [INFO] No active schedules found
[2025-03-29 09:27:03] Scheduled emails cron job completed successfully.
[2025-03-29 09:32:03] Scheduled emails cron job started.
[2025-03-29 09:32:03] [INFO] Starting to process scheduled emails
[2025-03-29 09:32:03] [INFO] No active schedules found
[2025-03-29 09:32:03] Scheduled emails cron job completed successfully.
[2025-03-29 09:37:03] Scheduled emails cron job started.
[2025-03-29 09:37:03] [INFO] Starting to process scheduled emails
[2025-03-29 09:37:03] [INFO] No active schedules found
[2025-03-29 09:37:03] Scheduled emails cron job completed successfully.
[2025-03-29 09:42:02] Scheduled emails cron job started.
[2025-03-29 09:42:02] [INFO] Starting to process scheduled emails
[2025-03-29 09:42:02] [INFO] No active schedules found
[2025-03-29 09:42:02] Scheduled emails cron job completed successfully.
[2025-03-29 09:47:03] Scheduled emails cron job started.
[2025-03-29 09:47:03] [INFO] Starting to process scheduled emails
[2025-03-29 09:47:03] [INFO] No active schedules found
[2025-03-29 09:47:03] Scheduled emails cron job completed successfully.
[2025-03-29 09:52:03] Scheduled emails cron job started.
[2025-03-29 09:52:03] [INFO] Starting to process scheduled emails
[2025-03-29 09:52:03] [INFO] Found 1 active schedules
[2025-03-29 09:52:03] [INFO] No pending recipients for schedule #22 (Ndi).
[2025-03-29 09:52:03] [INFO] One-time schedule #22 (Ndi) completed.
[2025-03-29 09:52:03] [INFO] Finished processing scheduled emails
[2025-03-29 09:52:03] Scheduled emails cron job completed successfully.
[2025-03-29 09:57:02] Scheduled emails cron job started.
[2025-03-29 09:57:02] [INFO] Starting to process scheduled emails
[2025-03-29 09:57:02] [INFO] Found 1 active schedules
[2025-03-29 09:57:02] [INFO] No pending recipients for schedule #23 (Ndibbb).
[2025-03-29 09:57:02] [INFO] Finished processing scheduled emails
[2025-03-29 09:57:02] Scheduled emails cron job completed successfully.
[2025-03-29 10:02:02] Scheduled emails cron job started.
[2025-03-29 10:02:02] [INFO] Starting to process scheduled emails
[2025-03-29 10:02:02] [INFO] No active schedules found
[2025-03-29 10:02:02] Scheduled emails cron job completed successfully.
[2025-03-29 10:05:21] Scheduled emails cron job started.
[2025-03-29 10:05:21] Processing specific schedule ID: 1
[2025-03-29 10:05:21] Error: Schedule ID 1 not found.
[2025-03-29 10:05:21] Scheduled emails cron job completed successfully.
[2025-03-29 10:07:02] Scheduled emails cron job started.
[2025-03-29 10:07:02] [INFO] Starting to process scheduled emails
[2025-03-29 10:07:02] [INFO] No active schedules found
[2025-03-29 10:07:02] Scheduled emails cron job completed successfully.
[2025-03-29 10:12:03] Scheduled emails cron job started.
[2025-03-29 10:12:03] [INFO] Starting to process scheduled emails
[2025-03-29 10:12:03] [INFO] Found 1 active schedules
[2025-03-29 10:12:03] [INFO] No pending recipients for schedule #24 (Ndibbbnnnn).
[2025-03-29 10:12:03] [INFO] One-time schedule #24 (Ndibbbnnnn) completed.
[2025-03-29 10:12:03] [INFO] Finished processing scheduled emails
[2025-03-29 10:12:03] Scheduled emails cron job completed successfully.
[2025-03-29 10:26:23] Scheduled emails cron job started.
[2025-03-29 10:26:23] [INFO] Starting to process scheduled emails
[2025-03-29 10:26:23] [INFO] Found 3 active schedules
[2025-03-29 10:26:23] [INFO] No pending recipients for schedule #19 (Mike Rhonda).
[2025-03-29 10:26:23] [INFO] No pending recipients for schedule #25 (rhondadd).
[2025-03-29 10:26:23] [INFO] Processing 26 (Test Placeholder Fix): sending to 1 recipients.
[2025-03-29 10:26:26] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:26:26] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:26:31] [INFO] Completed batch for schedule #26 (Test Placeholder Fix).
[2025-03-29 10:26:31] [INFO] Finished processing scheduled emails
[2025-03-29 10:26:31] Scheduled emails cron job completed successfully.
[2025-03-29 10:26:31] Test scheduled email placeholder script completed.
[2025-03-29 10:34:15] Scheduled emails cron job started.
[2025-03-29 10:34:15] [INFO] Starting to process scheduled emails
[2025-03-29 10:34:15] [INFO] Found 6 active schedules
[2025-03-29 10:34:15] [INFO] No pending recipients for schedule #26 (Test Placeholder Fix).
[2025-03-29 10:34:15] [INFO] One-time schedule #26 (Test Placeholder Fix) completed.
[2025-03-29 10:34:15] [INFO] No pending recipients for schedule #27 (Godwin Bointadddd).
[2025-03-29 10:34:15] [INFO] One-time schedule #27 (Godwin Bointadddd) completed.
[2025-03-29 10:34:15] [INFO] Processing 28 (rhondadddd): sending to 10 recipients.
[2025-03-29 10:34:18] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:34:18] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 10:34:18] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 10:34:18] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:34:26] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:34:26] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 10:34:26] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 10:34:26] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:34:34] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:34:34] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 10:34:34] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 10:34:34] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:34:41] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:34:41] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 10:34:41] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 10:34:41] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:34:49] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:34:49] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 10:34:49] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 10:34:49] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:34:57] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:34:57] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 10:34:57] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 10:34:57] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:35:06] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:35:06] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 10:35:06] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 10:35:06] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:35:13] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:35:13] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 10:35:13] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 10:35:13] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:35:21] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:35:21] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 10:35:21] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 10:35:21] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:35:29] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:35:29] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 10:35:29] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 10:35:29] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:35:34] [INFO] Completed batch for schedule #28 (rhondadddd).
[2025-03-29 10:35:34] [INFO] Processing 29 (Test Birthday Email Fix): sending to 1 recipients.
[2025-03-29 10:35:37] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:35:37] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 10:35:37] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 10:35:37] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:35:42] [INFO] Completed batch for schedule #29 (Test Birthday Email Fix).
[2025-03-29 10:35:42] [INFO] Processing 30 (Test Birthday Email Fix): sending to 1 recipients.
[2025-03-29 10:35:45] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:35:45] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 10:35:45] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 10:35:45] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:35:50] [INFO] Completed batch for schedule #30 (Test Birthday Email Fix).
[2025-03-29 10:35:50] [INFO] Processing 31 (Test Birthday Email Fix): sending to 1 recipients.
[2025-03-29 10:35:53] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 10:35:53] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 10:35:53] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 10:35:53] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 10:35:58] [INFO] Completed batch for schedule #31 (Test Birthday Email Fix).
[2025-03-29 10:35:58] [INFO] Finished processing scheduled emails
[2025-03-29 10:35:58] Scheduled emails cron job completed successfully.
[2025-03-29 10:35:58] Test birthday scheduled email script completed.
[2025-03-29 10:45:05] Scheduled emails cron job started.
[2025-03-29 10:45:05] [INFO] Starting to process scheduled emails
[2025-03-29 10:45:05] [INFO] Found 6 active schedules
[2025-03-29 10:45:05] [INFO] No pending recipients for schedule #28 (rhondadddd).
[2025-03-29 10:45:05] [INFO] One-time schedule #28 (rhondadddd) completed.
[2025-03-29 10:45:05] [INFO] No pending recipients for schedule #29 (Test Birthday Email Fix).
[2025-03-29 10:45:05] [INFO] One-time schedule #29 (Test Birthday Email Fix) completed.
[2025-03-29 10:45:05] [INFO] No pending recipients for schedule #30 (Test Birthday Email Fix).
[2025-03-29 10:45:05] [INFO] One-time schedule #30 (Test Birthday Email Fix) completed.
[2025-03-29 10:45:05] [INFO] No pending recipients for schedule #31 (Test Birthday Email Fix).
[2025-03-29 10:45:05] [INFO] One-time schedule #31 (Test Birthday Email Fix) completed.
[2025-03-29 10:45:05] [INFO] Processing 35 (Test Birthday Email Names): sending to 1 recipients.
[2025-03-29 10:45:05] [INFO] Error fetching recipient data: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'phone' in 'field list'
[2025-03-29 10:45:05] [INFO] Could not find recipient data for ID: 27, Type: member
[2025-03-29 10:45:10] [INFO] Completed batch for schedule #35 (Test Birthday Email Names).
[2025-03-29 10:45:10] [INFO] Processing 36 (Test Birthday Email Names): sending to 1 recipients.
[2025-03-29 10:45:10] [INFO] Error fetching recipient data: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'phone' in 'field list'
[2025-03-29 10:45:10] [INFO] Could not find recipient data for ID: 28, Type: member
[2025-03-29 10:45:15] [INFO] Completed batch for schedule #36 (Test Birthday Email Names).
[2025-03-29 10:45:15] [INFO] Finished processing scheduled emails
[2025-03-29 10:45:15] Scheduled emails cron job completed successfully.
[2025-03-29 10:45:15] Test birthday email names script completed.
[2025-03-29 10:49:45] Scheduled emails cron job started.
[2025-03-29 10:49:45] [INFO] Starting to process scheduled emails
[2025-03-29 10:49:45] [INFO] Found 3 active schedules
[2025-03-29 10:49:45] [INFO] No pending recipients for schedule #34 (ddddd).
[2025-03-29 10:49:45] [INFO] One-time schedule #34 (ddddd) completed.
[2025-03-29 10:49:45] [INFO] Processing 37 (Test Birthday Email Names): sending to 1 recipients.
[2025-03-29 10:49:45] [INFO] Error fetching recipient data: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'phone' in 'field list'
[2025-03-29 10:49:45] [INFO] Could not find recipient data for ID: 27, Type: member
[2025-03-29 10:49:50] [INFO] Completed batch for schedule #37 (Test Birthday Email Names).
[2025-03-29 10:49:50] [INFO] Processing 38 (Test Birthday Email Names): sending to 1 recipients.
[2025-03-29 10:49:50] [INFO] Error fetching recipient data: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'phone' in 'field list'
[2025-03-29 10:49:50] [INFO] Could not find recipient data for ID: 28, Type: member
[2025-03-29 10:49:55] [INFO] Completed batch for schedule #38 (Test Birthday Email Names).
[2025-03-29 10:49:56] [INFO] Finished processing scheduled emails
[2025-03-29 10:49:56] Scheduled emails cron job completed successfully.
[2025-03-29 10:49:56] Test birthday email names script completed.
[2025-03-29 10:54:13] Scheduled emails cron job started.
[2025-03-29 10:54:13] [INFO] Starting to process scheduled emails
[2025-03-29 10:54:13] [INFO] Found 5 active schedules
[2025-03-29 10:54:13] [INFO] No pending recipients for schedule #32 (tttt).
[2025-03-29 10:54:13] [INFO] One-time schedule #32 (tttt) completed.
[2025-03-29 10:54:13] [INFO] Processing 37 (Test Birthday Email Names): sending to 1 recipients.
[2025-03-29 10:54:13] [INFO] Error fetching recipient data: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'phone' in 'field list'
[2025-03-29 10:54:13] [INFO] Could not find recipient data for ID: 27, Type: member
[2025-03-29 10:54:18] [INFO] Completed batch for schedule #37 (Test Birthday Email Names).
[2025-03-29 10:54:18] [INFO] Processing 38 (Test Birthday Email Names): sending to 1 recipients.
[2025-03-29 10:54:18] [INFO] Error fetching recipient data: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'phone' in 'field list'
[2025-03-29 10:54:18] [INFO] Could not find recipient data for ID: 28, Type: member
[2025-03-29 10:54:23] [INFO] Completed batch for schedule #38 (Test Birthday Email Names).
[2025-03-29 10:54:23] [INFO] Processing 39 (Test Birthday Email Names): sending to 1 recipients.
[2025-03-29 10:54:23] [INFO] Error fetching recipient data: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'phone' in 'field list'
[2025-03-29 10:54:23] [INFO] Could not find recipient data for ID: 27, Type: member
[2025-03-29 10:54:28] [INFO] Completed batch for schedule #39 (Test Birthday Email Names).
[2025-03-29 10:54:28] [INFO] Processing 40 (Test Birthday Email Names): sending to 1 recipients.
[2025-03-29 10:54:28] [INFO] Error fetching recipient data: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'phone' in 'field list'
[2025-03-29 10:54:28] [INFO] Could not find recipient data for ID: 28, Type: member
[2025-03-29 10:54:33] [INFO] Completed batch for schedule #40 (Test Birthday Email Names).
[2025-03-29 10:54:33] [INFO] Finished processing scheduled emails
[2025-03-29 10:54:33] Scheduled emails cron job completed successfully.
[2025-03-29 10:54:33] Test birthday email names script completed.
[2025-03-29 11:11:24] Scheduled emails cron job started.
[2025-03-29 11:11:24] [INFO] Starting to process scheduled emails
[2025-03-29 11:11:24] [INFO] Found 9 active schedules
[2025-03-29 11:11:24] [INFO] No pending recipients for schedule #23 (Ndibbb).
[2025-03-29 11:11:24] [INFO] No pending recipients for schedule #28 (rhondadddd).
[2025-03-29 11:11:24] [INFO] One-time schedule #28 (rhondadddd) completed.
[2025-03-29 11:11:24] [INFO] No pending recipients for schedule #33 (ffffff).
[2025-03-29 11:11:24] [INFO] One-time schedule #33 (ffffff) completed.
[2025-03-29 11:11:24] [INFO] No pending recipients for schedule #34 (ddddd).
[2025-03-29 11:11:24] [INFO] One-time schedule #34 (ddddd) completed.
[2025-03-29 11:11:24] [INFO] Processing 37 (Test Birthday Email Names): sending to 1 recipients.
[2025-03-29 11:11:27] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:11:27] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:11:27] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:11:27] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:11:32] [INFO] Completed batch for schedule #37 (Test Birthday Email Names).
[2025-03-29 11:11:32] [INFO] Processing 38 (Test Birthday Email Names): sending to 1 recipients.
[2025-03-29 11:11:35] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:11:35] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:11:35] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:11:35] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:11:40] [INFO] Completed batch for schedule #38 (Test Birthday Email Names).
[2025-03-29 11:11:40] [INFO] Processing 39 (Test Birthday Email Names): sending to 1 recipients.
[2025-03-29 11:11:43] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:11:43] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:11:43] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:11:43] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:11:48] [INFO] Completed batch for schedule #39 (Test Birthday Email Names).
[2025-03-29 11:11:48] [INFO] Processing 40 (Test Birthday Email Names): sending to 1 recipients.
[2025-03-29 11:11:50] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:11:50] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:11:50] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:11:50] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:11:55] [INFO] Completed batch for schedule #40 (Test Birthday Email Names).
[2025-03-29 11:11:55] [INFO] Processing 41 (Test Birthday Name Fix): sending to 1 recipients.
[2025-03-29 11:11:59] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:11:59] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:11:59] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:11:59] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:12:04] [INFO] Completed batch for schedule #41 (Test Birthday Name Fix).
[2025-03-29 11:12:04] [INFO] Finished processing scheduled emails
[2025-03-29 11:12:04] Scheduled emails cron job completed successfully.
[2025-03-29 11:12:04] Test scheduled birthday email fix script completed.
[2025-03-29 11:48:50] Scheduled emails cron job started.
[2025-03-29 11:48:50] [INFO] Starting to process scheduled emails
[2025-03-29 11:48:50] [INFO] Found 7 active schedules
[2025-03-29 11:48:50] [INFO] No pending recipients for schedule #25 (rhondadd).
[2025-03-29 11:48:50] [INFO] No pending recipients for schedule #28 (rhondadddd).
[2025-03-29 11:48:50] [INFO] One-time schedule #28 (rhondadddd) completed.
[2025-03-29 11:48:50] [INFO] No pending recipients for schedule #33 (ffffff).
[2025-03-29 11:48:50] [INFO] One-time schedule #33 (ffffff) completed.
[2025-03-29 11:48:50] [INFO] No pending recipients for schedule #34 (ddddd).
[2025-03-29 11:48:50] [INFO] One-time schedule #34 (ddddd) completed.
[2025-03-29 11:48:50] [INFO] No pending recipients for schedule #42 (rhondarrrrrcc).
[2025-03-29 11:48:50] [INFO] One-time schedule #42 (rhondarrrrrcc) completed.
[2025-03-29 11:48:50] [INFO] No pending recipients for schedule #43 (rhondassssss).
[2025-03-29 11:48:50] [INFO] One-time schedule #43 (rhondassssss) completed.
[2025-03-29 11:48:50] [INFO] No pending recipients for schedule #44 (rhondarrttt).
[2025-03-29 11:48:50] [INFO] Finished processing scheduled emails
[2025-03-29 11:48:50] Scheduled emails cron job completed successfully.
[2025-03-29 11:52:05] Scheduled emails cron job started.
[2025-03-29 11:52:05] [INFO] Starting to process scheduled emails
[2025-03-29 11:52:05] [INFO] Found 1 active schedules
[2025-03-29 11:52:05] [INFO] No pending recipients for schedule #45 (fgfgfggf).
[2025-03-29 11:52:05] [INFO] One-time schedule #45 (fgfgfggf) completed.
[2025-03-29 11:52:05] [INFO] Finished processing scheduled emails
[2025-03-29 11:52:05] Scheduled emails cron job completed successfully.
[2025-03-29 11:57:02] Scheduled emails cron job started.
[2025-03-29 11:57:02] [INFO] Starting to process scheduled emails
[2025-03-29 11:57:02] [INFO] Found 1 active schedules
[2025-03-29 11:57:02] [INFO] Processing 46 (Mike Johnneeee): sending to 10 recipients.
[2025-03-29 11:57:05] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:57:05] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:57:05] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:57:05] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:57:33] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:57:33] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:57:33] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:57:33] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:57:41] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:57:41] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:57:41] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:57:41] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:57:49] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:57:49] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:57:49] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:57:49] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:57:56] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:57:56] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:57:56] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:57:56] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:58:05] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:58:05] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:58:05] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:58:05] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:58:13] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:58:13] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:58:13] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:58:13] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:58:21] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:58:21] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:58:21] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:58:21] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:58:29] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:58:29] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:58:29] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:58:29] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:58:37] [INFO] [SUCCESS] Email sent <NAME_EMAIL>
[2025-03-29 11:58:37] [INFO] [ERROR] Error logging email: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchtest.sent_emails' doesn't exist
[2025-03-29 11:58:37] [ERROR] Failed to send <NAME_EMAIL>: 
[2025-03-29 11:58:37] [ERROR] Error logging email: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'email_schedule_id' in 'field list'
[2025-03-29 11:58:42] [INFO] Completed batch for schedule #46 (Mike Johnneeee).
[2025-03-29 11:58:42] [INFO] Finished processing scheduled emails
[2025-03-29 11:58:42] Scheduled emails cron job completed successfully.
[2025-03-29 12:02:01] Scheduled emails cron job started.
[2025-03-29 12:02:01] [INFO] Starting to process scheduled emails
[2025-03-29 12:02:01] [INFO] Found 2 active schedules
[2025-03-29 12:02:01] [INFO] No pending recipients for schedule #44 (rhondarrttt).
[2025-03-29 12:02:01] [INFO] No pending recipients for schedule #46 (Mike Johnneeee).
[2025-03-29 12:02:01] [INFO] One-time schedule #46 (Mike Johnneeee) completed.
[2025-03-29 12:02:01] [INFO] Finished processing scheduled emails
[2025-03-29 12:02:01] Scheduled emails cron job completed successfully.
[2025-03-29 12:07:02] Scheduled emails cron job started.
[2025-03-29 12:07:02] [INFO] Starting to process scheduled emails
[2025-03-29 12:07:02] [INFO] Found 2 active schedules
[2025-03-29 12:07:02] [INFO] No pending recipients for schedule #45 (fgfgfggf).
[2025-03-29 12:07:02] [INFO] One-time schedule #45 (fgfgfggf) completed.
[2025-03-29 12:07:02] [INFO] No pending recipients for schedule #47 (Godwin Bointa3333).
[2025-03-29 12:07:02] [INFO] Finished processing scheduled emails
[2025-03-29 12:07:02] Scheduled emails cron job completed successfully.
[2025-03-29 12:12:03] Scheduled emails cron job started.
[2025-03-29 12:12:03] [INFO] Starting to process scheduled emails
[2025-03-29 12:12:03] [INFO] Found 1 active schedules
[2025-03-29 12:12:03] [INFO] No pending recipients for schedule #23 (Ndibbb).
[2025-03-29 12:12:03] [INFO] Finished processing scheduled emails
[2025-03-29 12:12:03] Scheduled emails cron job completed successfully.
[2025-03-29 12:22:01] Scheduled emails cron job started.
[2025-03-29 12:22:01] [INFO] Starting to process scheduled emails
[2025-03-29 12:22:01] [INFO] No active schedules found
[2025-03-29 12:22:01] Scheduled emails cron job completed successfully.
[2025-03-29 12:27:01] Scheduled emails cron job started.
[2025-03-29 12:27:01] [INFO] Starting to process scheduled emails
[2025-03-29 12:27:01] [INFO] No active schedules found
[2025-03-29 12:27:01] Scheduled emails cron job completed successfully.
[2025-03-29 12:32:02] Scheduled emails cron job started.
[2025-03-29 12:32:02] [INFO] Starting to process scheduled emails
[2025-03-29 12:32:02] [INFO] No active schedules found
[2025-03-29 12:32:02] Scheduled emails cron job completed successfully.
[2025-03-29 12:37:02] Scheduled emails cron job started.
[2025-03-29 12:37:02] [INFO] Starting to process scheduled emails
[2025-03-29 12:37:02] [INFO] No active schedules found
[2025-03-29 12:37:02] Scheduled emails cron job completed successfully.
[2025-03-29 12:42:02] Scheduled emails cron job started.
[2025-03-29 12:42:02] [INFO] Starting to process scheduled emails
[2025-03-29 12:42:02] [INFO] No active schedules found
[2025-03-29 12:42:02] Scheduled emails cron job completed successfully.
[2025-03-29 12:47:01] Scheduled emails cron job started.
[2025-03-29 12:47:01] [INFO] Starting to process scheduled emails
[2025-03-29 12:47:01] [INFO] Found 1 active schedules
[2025-03-29 12:47:01] [INFO] No pending recipients for schedule #45 (fgfgfggf).
[2025-03-29 12:47:01] [INFO] One-time schedule #45 (fgfgfggf) completed.
[2025-03-29 12:47:01] [INFO] Finished processing scheduled emails
[2025-03-29 12:47:01] Scheduled emails cron job completed successfully.
[2025-06-28 10:56:17] Scheduled emails cron job started.
[2025-06-28 10:56:17] [INFO] Starting to process scheduled emails
[2025-06-28 10:56:17] [INFO] No active schedules found
[2025-06-28 10:56:17] Scheduled emails cron job completed successfully.
