<?php
/**
 * Test Email Script
 * 
 * This script verifies the email sending functionality and can be used to test
 * the fixed email sending code.
 */

// Include necessary files
require_once '../config.php';
require_once '../includes/email_functions.php';

echo "Starting email test...\n";

// Set up some test data
$to = "<EMAIL>";
$toName = "Test User";
$subject = "Test Email - " . date('Y-m-d H:i:s');
$body = "<h1>Test Email</h1><p>This is a test email sent on " . date('Y-m-d H:i:s') . "</p>";

// Try to send the email
echo "Attempting to send email to $toName <$to>...\n";
$result = sendScheduledEmail($to, $toName, $subject, $body);

// Output the result
echo "Result: " . ($result['success'] ? 'Success!' : 'Failed!') . "\n";
echo "Message: " . $result['message'] . "\n";

echo "Test completed.\n"; 