<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['file_id']) || !is_numeric($input['file_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid file ID']);
    exit();
}

$file_id = (int)$input['file_id'];

try {
    // Get file info first
    $stmt = $conn->prepare("SELECT file_path FROM event_files WHERE id = ?");
    $stmt->execute([$file_id]);
    $file = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$file) {
        echo json_encode(['success' => false, 'message' => 'File not found']);
        exit();
    }
    
    // Delete from database
    $stmt = $conn->prepare("DELETE FROM event_files WHERE id = ?");
    $stmt->execute([$file_id]);
    
    // Delete physical file
    if (file_exists($file['file_path'])) {
        unlink($file['file_path']);
    }
    
    echo json_encode(['success' => true, 'message' => 'File deleted successfully']);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
