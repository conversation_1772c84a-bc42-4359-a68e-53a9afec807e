<?php
/**
 * Email Schedule Detail - Admin Interface
 * 
 * This file provides detailed information about a specific email schedule,
 * including recipient list, logs, and status information.
 */

// Include necessary files
require_once '../config.php';
require_once '../includes/auth_check.php';
require_once '../includes/email_functions.php';

// Check if schedule ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: email_scheduler.php');
    exit;
}

$schedule_id = intval($_GET['id']);

// Process form submissions
$message = '';
$messageType = '';

// Handle recipient status updates
if (isset($_POST['action']) && $_POST['action'] == 'update_recipient_status') {
    try {
        $recipient_id = intval($_POST['recipient_id']);
        $new_status = $_POST['new_status'];
        $admin_id = $_SESSION['admin_id'];
        
        // Validate status
        $valid_statuses = ['pending', 'sent', 'failed', 'skipped'];
        if (!in_array($new_status, $valid_statuses)) {
            throw new Exception("Invalid status");
        }
        
        // Update recipient status
        $stmt = $pdo->prepare("
            UPDATE email_schedule_recipients 
            SET status = ?, updated_at = NOW() 
            WHERE id = ? AND schedule_id = ?
        ");
        $stmt->execute([$new_status, $recipient_id, $schedule_id]);
        
        // Log the status change
        $stmt = $pdo->prepare("
            INSERT INTO email_schedule_logs 
            (schedule_id, log_type, message) 
            VALUES (?, 'info', ?)
        ");
        $stmt->execute([
            $schedule_id, 
            "Recipient ID $recipient_id status changed to '$new_status' by Admin ID: $admin_id"
        ]);
        
        $message = "Recipient status updated successfully!";
        $messageType = "success";
        
    } catch (Exception $e) {
        $message = "Error updating status: " . $e->getMessage();
        $messageType = "danger";
    }
}

// Fetch schedule details
$stmt = $pdo->prepare("
    SELECT 
        es.*, 
        ess.template_id, ess.custom_subject, ess.custom_content, ess.track_opens, ess.track_clicks,
        et.template_name as template_name, et.subject as template_subject,
        'System' as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'pending'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'pending'
        ) as pending_count,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'failed'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'failed'
        ) as failed_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    WHERE 
        es.id = ?
");
$stmt->execute([$schedule_id]);
$schedule = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$schedule) {
    header('Location: email_scheduler.php');
    exit;
}

// Extract additional schedule information from custom_data
$custom_data = json_decode($schedule['custom_data'] ?? '{}', true);
$schedule_type = $custom_data['schedule_type'] ?? 'one_time';
$start_datetime = $custom_data['start_datetime'] ?? $schedule['next_run'];
$end_datetime = $custom_data['end_datetime'] ?? null;
$emails_per_hour = $custom_data['emails_per_hour'] ?? 100;
$min_interval_seconds = $custom_data['min_interval_seconds'] ?? 5;

// Fetch recipients
$stmt = $pdo->prepare("
    SELECT 
        esr.id, esr.schedule_id, esr.recipient_id, esr.recipient_type, 
        esr.status, esr.error_message, esr.created_at, esr.updated_at,
        CASE 
            WHEN esr.recipient_type = 'member' THEN m.full_name
            WHEN esr.recipient_type = 'contact' THEN c.name
            WHEN esr.recipient_type = 'group' THEN 'Group'
            ELSE 'Unknown'
        END as recipient_name,
        CASE 
            WHEN esr.recipient_type = 'member' THEN m.email
            WHEN esr.recipient_type = 'contact' THEN c.email
            WHEN esr.recipient_type = 'group' THEN 'Group'
            ELSE 'Unknown'
        END as recipient_email
    FROM 
        email_schedule_recipients esr
    LEFT JOIN 
        members m ON esr.recipient_type = 'member' AND esr.recipient_id = m.id
    LEFT JOIN 
        contacts c ON esr.recipient_type = 'contact' AND esr.recipient_id = c.id
    WHERE 
        esr.schedule_id = ?
    ORDER BY 
        esr.status, esr.created_at
");
$stmt->execute([$schedule_id]);
$recipients = $stmt->fetchAll(PDO::FETCH_ASSOC);

// We don't need to calculate counts manually anymore since they come from the query
// But we'll still calculate skipped_count as it's not part of the query
$skipped_count = 0;
foreach ($recipients as $recipient) {
    if ($recipient['status'] == 'skipped') {
        if ($recipient['recipient_type'] == 'group') {
            try {
                $groupStmt = $pdo->prepare("SELECT COUNT(*) as member_count FROM contact_group_members WHERE group_id = ?");
                $groupStmt->execute([$recipient['recipient_id']]);
                $groupCount = $groupStmt->fetch(PDO::FETCH_ASSOC);
                $groupMemberCount = $groupCount ? (int)$groupCount['member_count'] : 0;
                
                // If group is empty, count as at least 1
                $groupMemberCount = max(1, $groupMemberCount);
                
                $skipped_count += $groupMemberCount;
            } catch (Exception $e) {
                // If there's an error, count the group as one recipient
                $skipped_count++;
            }
        } else {
            $skipped_count++;
        }
    }
}

// Add skipped count to the schedule array
$schedule['skipped_count'] = $skipped_count;

// Fetch logs
$stmt = $pdo->prepare("
    SELECT * FROM email_schedule_logs 
    WHERE schedule_id = ? 
    ORDER BY created_at DESC
");
$stmt->execute([$schedule_id]);
$logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Include header
$pageTitle = "Email Schedule Detail";
include 'includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Email Schedule Detail</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="email_scheduler.php">Email Scheduler</a></li>
        <li class="breadcrumb-item active">Schedule Detail</li>
    </ol>
    
    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="row">
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle me-1"></i>
                    Campaign Details
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>Campaign Name:</th>
                            <td><?php echo htmlspecialchars($schedule['name']); ?></td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                <span class="badge bg-<?php 
                                    echo match($schedule['status']) {
                                        'pending' => 'secondary',
                                        'active' => 'success',
                                        'paused' => 'warning',
                                        'completed' => 'info',
                                        'failed' => 'danger',
                                        default => 'secondary'
                                    };
                                ?> fs-6 p-2">
                                    <?php echo ucfirst($schedule['status']); ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>Schedule Type:</th>
                            <td><?php echo ucfirst(str_replace('_', ' ', $schedule_type)); ?></td>
                        </tr>
                        <tr>
                            <th>Start Time:</th>
                            <td><?php echo date('Y-m-d H:i', strtotime($start_datetime)); ?></td>
                        </tr>
                        <?php if ($end_datetime): ?>
                        <tr>
                            <th>End Time:</th>
                            <td><?php echo date('Y-m-d H:i', strtotime($end_datetime)); ?></td>
                        </tr>
                        <?php endif; ?>
                        <tr>
                            <th>Emails Per Hour:</th>
                            <td><?php echo $emails_per_hour; ?></td>
                        </tr>
                        <tr>
                            <th>Min Interval:</th>
                            <td><?php echo $min_interval_seconds; ?> seconds</td>
                        </tr>
                        <tr>
                            <th>Template:</th>
                            <td><?php echo htmlspecialchars($schedule['template_name'] ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <th>Subject:</th>
                            <td><?php echo htmlspecialchars($schedule['custom_subject'] ?? $schedule['template_subject'] ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <th>Tracking:</th>
                            <td>
                                <?php if ($schedule['track_opens']): ?>
                                    <span class="badge bg-success">Opens</span>
                                <?php endif; ?>
                                <?php if ($schedule['track_clicks']): ?>
                                    <span class="badge bg-success">Clicks</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Created By:</th>
                            <td><?php echo htmlspecialchars($schedule['created_by_name'] ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td><?php echo date('Y-m-d H:i', strtotime($schedule['created_at'])); ?></td>
                        </tr>
                    </table>
                    
                    <div class="mt-3">
                        <a href="email_scheduler.php" class="btn btn-secondary">Back to Scheduler</a>
                        
                        <?php if ($schedule['status'] == 'pending'): ?>
                            <form method="post" action="email_scheduler.php" class="d-inline">
                                <input type="hidden" name="action" value="update_status">
                                <input type="hidden" name="schedule_id" value="<?php echo $schedule_id; ?>">
                                <input type="hidden" name="new_status" value="active">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-play me-1"></i> Activate
                                </button>
                            </form>
                        <?php endif; ?>
                        
                        <?php if ($schedule['status'] == 'active'): ?>
                            <form method="post" action="email_scheduler.php" class="d-inline">
                                <input type="hidden" name="action" value="update_status">
                                <input type="hidden" name="schedule_id" value="<?php echo $schedule_id; ?>">
                                <input type="hidden" name="new_status" value="paused">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-pause me-1"></i> Pause
                                </button>
                            </form>
                        <?php endif; ?>
                        
                        <?php if ($schedule['status'] == 'paused'): ?>
                            <form method="post" action="email_scheduler.php" class="d-inline">
                                <input type="hidden" name="action" value="update_status">
                                <input type="hidden" name="schedule_id" value="<?php echo $schedule_id; ?>">
                                <input type="hidden" name="new_status" value="active">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-play me-1"></i> Resume
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    Progress Summary
                </div>
                <div class="card-body">
                    <div class="row text-center mb-4">
                        <div class="col">
                            <div class="h1"><?php echo $schedule['total_recipients']; ?></div>
                            <div>Total Recipients</div>
                        </div>
                        <div class="col">
                            <div class="h1 text-success"><?php echo $schedule['sent_count']; ?></div>
                            <div>Sent</div>
                        </div>
                        <div class="col">
                            <div class="h1 text-warning"><?php echo $schedule['pending_count']; ?></div>
                            <div>Pending</div>
                        </div>
                        <div class="col">
                            <div class="h1 text-danger"><?php echo $schedule['failed_count']; ?></div>
                            <div>Failed</div>
                        </div>
                    </div>
                    
                    <?php 
                        $total = (int)$schedule['total_recipients'];
                        $sent = (int)$schedule['sent_count'];
                        $percent = $total > 0 ? round(($sent / $total) * 100) : 0;
                    ?>
                    <h5>Overall Progress: <?php echo $percent; ?>%</h5>
                    <div class="progress mb-4" style="height: 25px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $percent; ?>%;" 
                             aria-valuenow="<?php echo $percent; ?>" aria-valuemin="0" aria-valuemax="100">
                            <?php echo $percent; ?>%
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <?php if ($schedule['status'] == 'active'): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-1"></i>
                                This campaign is currently active and sending emails.
                            </div>
                        <?php elseif ($schedule['status'] == 'pending'): ?>
                            <div class="alert alert-secondary">
                                <i class="fas fa-clock me-1"></i>
                                This campaign is scheduled to start at <?php echo date('Y-m-d H:i', strtotime($start_datetime)); ?>.
                            </div>
                        <?php elseif ($schedule['status'] == 'paused'): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-pause-circle me-1"></i>
                                <strong>This campaign is currently paused.</strong> Use the Resume button below to continue sending emails.
                                <div class="mt-2">
                                    <form method="post" action="email_scheduler.php" class="d-inline">
                                        <input type="hidden" name="action" value="update_status">
                                        <input type="hidden" name="schedule_id" value="<?php echo $schedule_id; ?>">
                                        <input type="hidden" name="new_status" value="active">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-play me-1"></i> Resume Campaign
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php elseif ($schedule['status'] == 'completed'): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-1"></i>
                                This campaign has been completed.
                            </div>
                        <?php elseif ($schedule['status'] == 'failed'): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-1"></i>
                                This campaign has failed. Check the logs for details.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="detailTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="recipients-tab" data-bs-toggle="tab" data-bs-target="#recipients" type="button" role="tab" aria-controls="recipients" aria-selected="true">
                        <i class="fas fa-users me-1"></i> Recipients
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab" aria-controls="logs" aria-selected="false">
                        <i class="fas fa-list me-1"></i> Logs
                    </button>
                </li>
                <?php if (!empty($schedule['custom_content'])): ?>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab" aria-controls="content" aria-selected="false">
                        <i class="fas fa-file-alt me-1"></i> Email Content
                    </button>
                </li>
                <?php endif; ?>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="detailTabsContent">
                <div class="tab-pane fade show active" id="recipients" role="tabpanel" aria-labelledby="recipients-tab">
                    <table id="recipientsTable" class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Type</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Sent At</th>
                                <th>Error</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recipients as $recipient): 
                                // Get more descriptive names based on recipient type
                                $recipientTypeName = match($recipient['recipient_type']) {
                                    'member' => 'Church Member',
                                    'contact' => 'Contact',
                                    'group' => 'Contact Group',
                                    default => ucfirst($recipient['recipient_type'])
                                };
                                
                                // Try to get a better name for the recipient
                                $recipientName = $recipient['recipient_name'];
                                if ($recipient['recipient_type'] == 'group') {
                                    // For groups, try to get the group name from the database
                                    try {
                                        $groupStmt = $pdo->prepare("SELECT name FROM contact_groups WHERE id = ?");
                                        $groupStmt->execute([$recipient['recipient_id']]);
                                        $group = $groupStmt->fetch(PDO::FETCH_ASSOC);
                                        if ($group) {
                                            $recipientName = $group['name'];
                                        }
                                    } catch (Exception $e) {
                                        // Silently fail and use the default name
                                    }
                                }
                            ?>
                                <tr>
                                    <td><?php echo $recipient['id']; ?></td>
                                    <td><?php echo $recipientTypeName; ?></td>
                                    <td><?php echo htmlspecialchars($recipientName); ?></td>
                                    <td>
                                        <?php if ($recipient['recipient_type'] == 'group'): ?>
                                            <span class="badge bg-info">Group Recipients</span>
                                        <?php else: ?>
                                            <?php echo htmlspecialchars($recipient['recipient_email']); ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo match($recipient['status']) {
                                                'pending' => 'secondary',
                                                'sent' => 'success',
                                                'failed' => 'danger',
                                                'skipped' => 'warning',
                                                default => 'secondary'
                                            };
                                        ?>">
                                            <?php echo ucfirst($recipient['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php 
                                        // Use updated_at as sent_at if status is 'sent'
                                        if ($recipient['status'] == 'sent' && isset($recipient['updated_at'])) {
                                            echo date('Y-m-d H:i:s', strtotime($recipient['updated_at']));
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </td>
                                    <td><?php echo !empty($recipient['error_message']) ? '<span class="text-danger">' . htmlspecialchars(substr($recipient['error_message'], 0, 50)) . '...</span>' : 'N/A'; ?></td>
                                    <td>
                                        <?php if (in_array($schedule['status'], ['active', 'paused', 'pending'])): ?>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton<?php echo $recipient['id']; ?>" data-bs-toggle="dropdown" aria-expanded="false">
                                                    Actions
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton<?php echo $recipient['id']; ?>">
                                                    <?php if ($recipient['status'] != 'pending'): ?>
                                                        <li>
                                                            <form method="post" action="">
                                                                <input type="hidden" name="action" value="update_recipient_status">
                                                                <input type="hidden" name="recipient_id" value="<?php echo $recipient['id']; ?>">
                                                                <input type="hidden" name="new_status" value="pending">
                                                                <button type="submit" class="dropdown-item">Mark as Pending</button>
                                                            </form>
                                                        </li>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($recipient['status'] != 'sent'): ?>
                                                        <li>
                                                            <form method="post" action="">
                                                                <input type="hidden" name="action" value="update_recipient_status">
                                                                <input type="hidden" name="recipient_id" value="<?php echo $recipient['id']; ?>">
                                                                <input type="hidden" name="new_status" value="sent">
                                                                <button type="submit" class="dropdown-item">Mark as Sent</button>
                                                            </form>
                                                        </li>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($recipient['status'] != 'failed'): ?>
                                                        <li>
                                                            <form method="post" action="">
                                                                <input type="hidden" name="action" value="update_recipient_status">
                                                                <input type="hidden" name="recipient_id" value="<?php echo $recipient['id']; ?>">
                                                                <input type="hidden" name="new_status" value="failed">
                                                                <button type="submit" class="dropdown-item">Mark as Failed</button>
                                                            </form>
                                                        </li>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($recipient['status'] != 'skipped'): ?>
                                                        <li>
                                                            <form method="post" action="">
                                                                <input type="hidden" name="action" value="update_recipient_status">
                                                                <input type="hidden" name="recipient_id" value="<?php echo $recipient['id']; ?>">
                                                                <input type="hidden" name="new_status" value="skipped">
                                                                <button type="submit" class="dropdown-item">Skip</button>
                                                            </form>
                                                        </li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="tab-pane fade" id="logs" role="tabpanel" aria-labelledby="logs-tab">
                    <table id="logsTable" class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Type</th>
                                <th>Message</th>
                                <th>Timestamp</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($logs as $log): ?>
                                <tr>
                                    <td><?php echo $log['id']; ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo match($log['log_type']) {
                                                'info' => 'info',
                                                'warning' => 'warning',
                                                'error' => 'danger',
                                                'success' => 'success',
                                                default => 'secondary'
                                            };
                                        ?>">
                                            <?php echo ucfirst($log['log_type']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($log['message']); ?></td>
                                    <td><?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <?php if (!empty($schedule['custom_content'])): ?>
                <div class="tab-pane fade" id="content" role="tabpanel" aria-labelledby="content-tab">
                    <div class="card">
                        <div class="card-header">
                            <h5>Email Content Preview</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Subject:</strong> <?php echo htmlspecialchars($schedule['custom_subject'] ?? $schedule['template_subject'] ?? 'N/A'); ?>
                            </div>
                            <div class="border p-3">
                                <?php echo $schedule['custom_content']; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTables
        $('#recipientsTable').DataTable({
            order: [[4, 'asc'], [5, 'desc']] // Sort by status, then sent_at
        });
        
        $('#logsTable').DataTable({
            order: [[3, 'desc']] // Sort by timestamp
        });
    });
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
