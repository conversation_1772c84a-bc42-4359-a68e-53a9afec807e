<?php
// <PERSON>ript to verify the raffle template functionality
require_once __DIR__ . '/../config.php';

// Database connection
$conn = $pdo;

// Specify the template name to check
$template_name = "The Big Raffle Winner";

try {
    // Get the template details
    $stmt = $conn->prepare("SELECT id, template_name, subject, content FROM email_templates WHERE template_name = ?");
    $stmt->execute([$template_name]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "Template not found: " . htmlspecialchars($template_name) . "\n";
        exit;
    }
    
    echo "=== Template Details ===\n";
    echo "ID: " . $template['id'] . "\n";
    echo "Name: " . $template['template_name'] . "\n";
    echo "Subject: " . $template['subject'] . "\n\n";
    
    // Check for PHP code that generates random numbers
    $has_random_number_generation = (
        strpos($template['content'], 'rand(') !== false || 
        strpos($template['content'], 'random') !== false
    );
    echo "Has random number generation: " . ($has_random_number_generation ? "Yes" : "No") . "\n";
    
    // Check for the reply-to email PHP code
    $has_dynamic_reply_to = (
        strpos($template['content'], 'reply_to_email') !== false && 
        strpos($template['content'], 'SELECT') !== false
    );
    echo "Has dynamic reply-to email: " . ($has_dynamic_reply_to ? "Yes" : "No") . "\n";
    
    // Check for unsubscribe link placeholder
    $has_unsubscribe_link = (
        strpos($template['content'], '{unsubscribe_link}') !== false
    );
    echo "Has unsubscribe link placeholder: " . ($has_unsubscribe_link ? "Yes" : "No") . "\n";
    
    // Check for hardcoded email addresses
    $hardcoded_emails = [];
    preg_match_all('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', $template['content'], $matches);
    
    foreach ($matches[0] as $email) {
        // Exclude default fallback email as that's expected
        if ($email != '' . CHURCH_EMAIL . '') {
            $hardcoded_emails[] = $email;
        }
    }
    
    echo "Has hardcoded emails: " . (!empty($hardcoded_emails) ? "Yes - " . implode(", ", $hardcoded_emails) : "No") . "\n\n";
    
    // Extract relevant parts for display
    echo "=== Template Content Preview ===\n";
    
    // Display PHP code for random numbers
    if (preg_match('/\/\/ Random powerball numbers.*?rand\(.*?rand\(.*?rand\(.*?rand\(.*?rand\(.*?rand\(/s', $template['content'], $matches)) {
        echo "Random Number Generation:\n";
        echo substr($matches[0], 0, 500) . "\n...\n\n";
    }
    
    // Display PHP code for reply-to email
    if (preg_match('/\/\/ Get the reply_to_email.*?try \{.*?SELECT.*?}/s', $template['content'], $matches)) {
        echo "Reply-To Email Logic:\n";
        echo substr($matches[0], 0, 500) . "\n...\n\n";
    }
    
    // Display the mailto link
    if (preg_match('/<a href="mailto:.*?class="claim-button">CLAIM YOUR PRIZE NOW<\/a>/s', $template['content'], $matches)) {
        echo "Mailto Link:\n";
        echo htmlspecialchars(substr($matches[0], 0, 500)) . "\n...\n\n";
    }
    
    // Display the unsubscribe section
    if (preg_match('/<td class="footer">.*?<\/td>/s', $template['content'], $matches)) {
        echo "Footer with Unsubscribe Link:\n";
        echo htmlspecialchars(substr($matches[0], 0, 500)) . "\n...\n\n";
    }
    
    // Check if template is usable in the system
    echo "=== System Integration ===\n";
    
    // Get the current reply-to email setting
    $stmt = $conn->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'reply_to_email' LIMIT 1");
    $stmt->execute();
    $reply_to_setting = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Current reply-to email setting: " . ($reply_to_setting ? $reply_to_setting['setting_value'] : "Not set") . "\n";
    
    // Overall assessment
    echo "\n=== Assessment ===\n";
    if ($has_random_number_generation && $has_dynamic_reply_to && $has_unsubscribe_link && empty($hardcoded_emails)) {
        echo "✅ Template implementation looks good! All key features are present and properly implemented.\n";
    } else {
        echo "⚠️ Template may have issues:\n";
        if (!$has_random_number_generation) echo "  - Missing random number generation\n";
        if (!$has_dynamic_reply_to) echo "  - Missing dynamic reply-to email functionality\n";
        if (!$has_unsubscribe_link) echo "  - Missing unsubscribe link placeholder\n";
        if (!empty($hardcoded_emails)) echo "  - Contains hardcoded email addresses\n";
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
} 