-- Add indexes for frequently queried fields
-- Note: Only add if they don't exist to prevent duplicates

-- Optimize members table for birthday queries
CREATE INDEX IF NOT EXISTS idx_birth_date ON members(birth_date);
CREATE INDEX IF NOT EXISTS idx_email ON members(email);
CREATE INDEX IF NOT EXISTS idx_status ON members(status);

-- Optimize email_templates table
CREATE INDEX IF NOT EXISTS idx_template_type ON email_templates(template_name, is_birthday_template);

-- Optimize email_tracking table
CREATE INDEX IF NOT EXISTS idx_tracking_dates ON email_tracking(sent_at, opened_at);
CREATE INDEX IF NOT EXISTS idx_email_type ON email_tracking(email_type);

-- Optimize settings table for faster lookups
CREATE INDEX IF NOT EXISTS idx_setting_name ON settings(setting_name);

-- Add composite indexes for commonly joined queries
CREATE INDEX IF NOT EXISTS idx_birthday_tracking ON email_tracking(email_type, sent_at);

-- Note: Run ANALYZE after adding indexes
ANALYZE TABLE members, email_templates, email_tracking, settings; 