[2025-03-10 13:43:12] PHP Error [2]: Undefined array key "HTTP_HOST" in C:\xampp\htdocs\church_test\church\config.php on line 157
[2025-03-10 13:43:12] PHP Error [2]: Constant SECURE_CRON_KEY already defined in C:\xampp\htdocs\church_test\church\birthday_reminders.php on line 73
[2025-03-10 13:43:12] Request received: format=, cron_key=not set
[2025-03-10 13:43:12] Request details: {"ACSetupSvcPort":"23210","ACSvcPort":"17532","ALLUSERSPROFILE":"C:\\ProgramData","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133687095186645209","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_24020_WSFNNIVNQXJZDEAK","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"USER","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","CURSOR_TRACE_ID":"d2e1a7259cd848a2bccb3f75cd611b54","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EFC_15912":"1","EnableLog":"INFO","Fluter":"C:\\src\\flutter\\bin","FPS_BROWSER_APP_PROFILE_STRING":"Internet Explorer","FPS_BROWSER_USER_PROFILE_STRING":"Default","HOMEDRIVE":"C:","HOMEPATH":"\\Users\\offiv","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\USER","NUMBER_OF_PROCESSORS":"8","OneDrive":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PSModulePath":"C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"USER","USERDOMAIN_ROAMINGPROFILE":"USER","USERNAME":"offiv","USERPROFILE":"C:\\Users\\<USER>\\WINDOWS","ZES_ENABLE_SYSMAN":"1","__PSLockDownPolicy":"0","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"0.46.8","LANG":"en_US.UTF-8","COLORTERM":"truecolor","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe","VSCODE_GIT_ASKPASS_EXTRA_ARGS":"","VSCODE_GIT_ASKPASS_MAIN":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-907b6e2e2a-sock","VSCODE_INJECTION":"1","PHP_SELF":"church\/birthday_reminders.php","SCRIPT_NAME":"church\/birthday_reminders.php","SCRIPT_FILENAME":"church\/birthday_reminders.php","PATH_TRANSLATED":"church\/birthday_reminders.php","DOCUMENT_ROOT":"","REQUEST_TIME_FLOAT":1741610592.145653,"REQUEST_TIME":1741610592,"argv":["church\/birthday_reminders.php","format=json","check=1","cron_key=fac_2024_secure_cron_8x9q2p5m"],"argc":4}
[2025-03-10 13:43:12] Access granted: via CLI
[2025-03-10 13:43:12] Starting birthday reminders cron job
[2025-03-10 13:43:12] Database connection successful. Found 5 total members.
[2025-03-10 13:43:12] Uncaught Exception: Too few arguments to function BirthdayReminder::__construct(), 0 passed in C:\xampp\htdocs\church_test\church\birthday_reminders.php on line 191 and at least 1 expected in C:\xampp\htdocs\church_test\church\send_birthday_reminders.php on line 16
[2025-03-10 13:44:19] PHP Error [2]: Undefined array key "HTTP_HOST" in C:\xampp\htdocs\church_test\church\config.php on line 157
[2025-03-10 13:44:19] PHP Error [2]: Constant SECURE_CRON_KEY already defined in C:\xampp\htdocs\church_test\church\birthday_reminders.php on line 73
[2025-03-10 13:44:19] Request received: format=, cron_key=not set
[2025-03-10 13:44:19] Request details: {"ACSetupSvcPort":"23210","ACSvcPort":"17532","ALLUSERSPROFILE":"C:\\ProgramData","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133687095186645209","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_24020_WSFNNIVNQXJZDEAK","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"USER","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","CURSOR_TRACE_ID":"d2e1a7259cd848a2bccb3f75cd611b54","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EFC_15912":"1","EnableLog":"INFO","Fluter":"C:\\src\\flutter\\bin","FPS_BROWSER_APP_PROFILE_STRING":"Internet Explorer","FPS_BROWSER_USER_PROFILE_STRING":"Default","HOMEDRIVE":"C:","HOMEPATH":"\\Users\\offiv","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\USER","NUMBER_OF_PROCESSORS":"8","OneDrive":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PSModulePath":"C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"USER","USERDOMAIN_ROAMINGPROFILE":"USER","USERNAME":"offiv","USERPROFILE":"C:\\Users\\<USER>\\WINDOWS","ZES_ENABLE_SYSMAN":"1","__PSLockDownPolicy":"0","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"0.46.8","LANG":"en_US.UTF-8","COLORTERM":"truecolor","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe","VSCODE_GIT_ASKPASS_EXTRA_ARGS":"","VSCODE_GIT_ASKPASS_MAIN":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-907b6e2e2a-sock","VSCODE_INJECTION":"1","PHP_SELF":"church\/birthday_reminders.php","SCRIPT_NAME":"church\/birthday_reminders.php","SCRIPT_FILENAME":"church\/birthday_reminders.php","PATH_TRANSLATED":"church\/birthday_reminders.php","DOCUMENT_ROOT":"","REQUEST_TIME_FLOAT":1741610659.937436,"REQUEST_TIME":1741610659,"argv":["church\/birthday_reminders.php","format=json","check=1","cron_key=fac_2024_secure_cron_8x9q2p5m"],"argc":4}
[2025-03-10 13:44:19] Access granted: via CLI
[2025-03-10 13:44:19] Starting birthday reminders cron job
[2025-03-10 13:44:19] Database connection successful. Found 5 total members.
[2025-03-10 13:44:19] Birthday reminder results: {"sent":[],"failed":[],"total_sent":0,"total_failed":0}
[2025-03-10 13:44:19] Cron job completed successfully
[2025-03-10 13:46:57] PHP Error [2]: Undefined array key "HTTP_HOST" in C:\xampp\htdocs\church_test\church\config.php on line 162
[2025-03-10 13:46:57] PHP Error [2]: Constant SECURE_CRON_KEY already defined in C:\xampp\htdocs\church_test\church\birthday_reminders.php on line 73
[2025-03-10 13:46:57] Request received: format=, cron_key=not set
[2025-03-10 13:46:57] Request details: {"ACSetupSvcPort":"23210","ACSvcPort":"17532","ALLUSERSPROFILE":"C:\\ProgramData","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133687095186645209","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_24020_WSFNNIVNQXJZDEAK","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"USER","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","CURSOR_TRACE_ID":"d2e1a7259cd848a2bccb3f75cd611b54","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EFC_15912":"1","EnableLog":"INFO","Fluter":"C:\\src\\flutter\\bin","FPS_BROWSER_APP_PROFILE_STRING":"Internet Explorer","FPS_BROWSER_USER_PROFILE_STRING":"Default","HOMEDRIVE":"C:","HOMEPATH":"\\Users\\offiv","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\USER","NUMBER_OF_PROCESSORS":"8","OneDrive":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PSModulePath":"C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"USER","USERDOMAIN_ROAMINGPROFILE":"USER","USERNAME":"offiv","USERPROFILE":"C:\\Users\\<USER>\\WINDOWS","ZES_ENABLE_SYSMAN":"1","__PSLockDownPolicy":"0","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"0.46.8","LANG":"en_US.UTF-8","COLORTERM":"truecolor","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe","VSCODE_GIT_ASKPASS_EXTRA_ARGS":"","VSCODE_GIT_ASKPASS_MAIN":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-907b6e2e2a-sock","VSCODE_INJECTION":"1","PHP_SELF":"church\/birthday_reminders.php","SCRIPT_NAME":"church\/birthday_reminders.php","SCRIPT_FILENAME":"church\/birthday_reminders.php","PATH_TRANSLATED":"church\/birthday_reminders.php","DOCUMENT_ROOT":"","REQUEST_TIME_FLOAT":1741610817.391592,"REQUEST_TIME":1741610817,"argv":["church\/birthday_reminders.php","format=json","check=1","cron_key=fac_2024_secure_cron_8x9q2p5m"],"argc":4}
[2025-03-10 13:46:57] Access granted: via CLI
[2025-03-10 13:46:57] Starting birthday reminders cron job
[2025-03-10 13:46:57] Database connection successful. Found 5 total members.
[2025-03-10 13:46:57] Birthday reminder results: {"sent":[],"failed":[],"total_sent":0,"total_failed":0}
[2025-03-10 13:46:57] Cron job completed successfully
[2025-03-10 13:48:51] PHP Error [2]: Undefined array key "HTTP_HOST" in C:\xampp\htdocs\church_test\church\config.php on line 162
[2025-03-10 13:48:51] PHP Error [2]: Constant SECURE_CRON_KEY already defined in C:\xampp\htdocs\church_test\church\birthday_reminders.php on line 73
[2025-03-10 13:48:51] Request received: format=, cron_key=not set
[2025-03-10 13:48:51] Request details: {"ACSetupSvcPort":"23210","ACSvcPort":"17532","ALLUSERSPROFILE":"C:\\ProgramData","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133687095186645209","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_24020_WSFNNIVNQXJZDEAK","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"USER","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","CURSOR_TRACE_ID":"d2e1a7259cd848a2bccb3f75cd611b54","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EFC_15912":"1","EnableLog":"INFO","Fluter":"C:\\src\\flutter\\bin","FPS_BROWSER_APP_PROFILE_STRING":"Internet Explorer","FPS_BROWSER_USER_PROFILE_STRING":"Default","HOMEDRIVE":"C:","HOMEPATH":"\\Users\\offiv","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\USER","NUMBER_OF_PROCESSORS":"8","OneDrive":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PSModulePath":"C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"USER","USERDOMAIN_ROAMINGPROFILE":"USER","USERNAME":"offiv","USERPROFILE":"C:\\Users\\<USER>\\WINDOWS","ZES_ENABLE_SYSMAN":"1","__PSLockDownPolicy":"0","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"0.46.8","LANG":"en_US.UTF-8","COLORTERM":"truecolor","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe","VSCODE_GIT_ASKPASS_EXTRA_ARGS":"","VSCODE_GIT_ASKPASS_MAIN":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-907b6e2e2a-sock","VSCODE_INJECTION":"1","PHP_SELF":"church\/birthday_reminders.php","SCRIPT_NAME":"church\/birthday_reminders.php","SCRIPT_FILENAME":"church\/birthday_reminders.php","PATH_TRANSLATED":"church\/birthday_reminders.php","DOCUMENT_ROOT":"","REQUEST_TIME_FLOAT":1741610931.420399,"REQUEST_TIME":1741610931,"argv":["church\/birthday_reminders.php","format=json","check=1","cron_key=fac_2024_secure_cron_8x9q2p5m"],"argc":4}
[2025-03-10 13:48:51] Access granted: via CLI
[2025-03-10 13:48:51] Starting birthday reminders cron job
[2025-03-10 13:48:51] Database connection successful. Found 5 total members.
[2025-03-10 13:48:51] Birthday reminder results: {"sent":[],"failed":[],"total_sent":0,"total_failed":0}
[2025-03-10 13:48:51] Cron job completed successfully
[2025-03-10 13:53:49] PHP Error [2]: Undefined array key "HTTP_HOST" in C:\xampp\htdocs\church_test\church\config.php on line 162
[2025-03-10 13:53:49] PHP Error [2]: Constant SECURE_CRON_KEY already defined in C:\xampp\htdocs\church_test\church\birthday_reminders.php on line 73
[2025-03-10 13:53:49] Request received: format=, cron_key=not set
[2025-03-10 13:53:49] Request details: {"ACSetupSvcPort":"23210","ACSvcPort":"17532","ALLUSERSPROFILE":"C:\\ProgramData","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133687095186645209","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_24020_WSFNNIVNQXJZDEAK","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"USER","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","CURSOR_TRACE_ID":"d2e1a7259cd848a2bccb3f75cd611b54","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EFC_15912":"1","EnableLog":"INFO","Fluter":"C:\\src\\flutter\\bin","FPS_BROWSER_APP_PROFILE_STRING":"Internet Explorer","FPS_BROWSER_USER_PROFILE_STRING":"Default","HOMEDRIVE":"C:","HOMEPATH":"\\Users\\offiv","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\USER","NUMBER_OF_PROCESSORS":"8","OneDrive":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PSModulePath":"C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"USER","USERDOMAIN_ROAMINGPROFILE":"USER","USERNAME":"offiv","USERPROFILE":"C:\\Users\\<USER>\\WINDOWS","ZES_ENABLE_SYSMAN":"1","__PSLockDownPolicy":"0","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"0.46.8","LANG":"en_US.UTF-8","COLORTERM":"truecolor","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe","VSCODE_GIT_ASKPASS_EXTRA_ARGS":"","VSCODE_GIT_ASKPASS_MAIN":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-907b6e2e2a-sock","VSCODE_INJECTION":"1","PHP_SELF":"church\/birthday_reminders.php","SCRIPT_NAME":"church\/birthday_reminders.php","SCRIPT_FILENAME":"church\/birthday_reminders.php","PATH_TRANSLATED":"church\/birthday_reminders.php","DOCUMENT_ROOT":"","REQUEST_TIME_FLOAT":1741611229.938086,"REQUEST_TIME":1741611229,"argv":["church\/birthday_reminders.php","format=json","check=1","cron_key=fac_2024_secure_cron_8x9q2p5m","debug=1"],"argc":5}
[2025-03-10 13:53:49] Access granted: via CLI
[2025-03-10 13:53:49] Starting birthday reminders cron job
[2025-03-10 13:53:49] Database connection successful. Found 5 total members.
[2025-03-10 13:53:49] Birthday reminder results: {"sent":[],"failed":[],"total_sent":0,"total_failed":0}
[2025-03-10 13:53:49] Cron job completed successfully
[2025-03-10 13:54:53] PHP Error [2]: Undefined array key "HTTP_HOST" in C:\xampp\htdocs\church_test\church\config.php on line 162
[2025-03-10 13:54:53] PHP Error [2]: Constant SECURE_CRON_KEY already defined in C:\xampp\htdocs\church_test\church\birthday_reminders.php on line 73
[2025-03-10 13:54:53] Request received: format=, cron_key=not set
[2025-03-10 13:54:53] Request details: {"ACSetupSvcPort":"23210","ACSvcPort":"17532","ALLUSERSPROFILE":"C:\\ProgramData","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133687095186645209","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_24020_WSFNNIVNQXJZDEAK","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"USER","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","CURSOR_TRACE_ID":"d2e1a7259cd848a2bccb3f75cd611b54","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EFC_15912":"1","EnableLog":"INFO","Fluter":"C:\\src\\flutter\\bin","FPS_BROWSER_APP_PROFILE_STRING":"Internet Explorer","FPS_BROWSER_USER_PROFILE_STRING":"Default","HOMEDRIVE":"C:","HOMEPATH":"\\Users\\offiv","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\USER","NUMBER_OF_PROCESSORS":"8","OneDrive":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PSModulePath":"C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"USER","USERDOMAIN_ROAMINGPROFILE":"USER","USERNAME":"offiv","USERPROFILE":"C:\\Users\\<USER>\\WINDOWS","ZES_ENABLE_SYSMAN":"1","__PSLockDownPolicy":"0","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"0.46.8","LANG":"en_US.UTF-8","COLORTERM":"truecolor","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe","VSCODE_GIT_ASKPASS_EXTRA_ARGS":"","VSCODE_GIT_ASKPASS_MAIN":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-907b6e2e2a-sock","VSCODE_INJECTION":"1","PHP_SELF":"church\/birthday_reminders.php","SCRIPT_NAME":"church\/birthday_reminders.php","SCRIPT_FILENAME":"church\/birthday_reminders.php","PATH_TRANSLATED":"church\/birthday_reminders.php","DOCUMENT_ROOT":"","REQUEST_TIME_FLOAT":1741611293.093517,"REQUEST_TIME":1741611293,"argv":["church\/birthday_reminders.php","format=json","check=1","cron_key=fac_2024_secure_cron_8x9q2p5m"],"argc":4}
[2025-03-10 13:54:53] Access granted: via CLI
[2025-03-10 13:54:53] Starting birthday reminders cron job
[2025-03-10 13:54:53] Database connection successful. Found 5 total members.
[2025-03-10 13:55:08] Birthday reminder results: {"sent":[{"member":"John Doe","email":"<EMAIL>","template":"Birthday Template 3: Modern Green Theme","type":"birthday"}],"failed":[],"total_sent":1,"total_failed":0}
[2025-03-10 13:55:08] Cron job completed successfully
