<?php
// Direct email sending script that bypasses the bulk email system
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../classes/PHPMailer/PHPMailer.php';
require_once __DIR__ . '/../classes/PHPMailer/SMTP.php';
require_once __DIR__ . '/../classes/PHPMailer/Exception.php';

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Get email settings from database
$stmt = $pdo->query("SELECT setting_name, setting_value FROM email_settings");
$email_settings = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $email_settings[$row['setting_name']] = $row['setting_value'];
}

if (isset($_POST['send_email'])) {
    $recipient_email = filter_input(INPUT_POST, 'recipient_email', FILTER_VALIDATE_EMAIL);
    $template_id = filter_input(INPUT_POST, 'template_id', FILTER_VALIDATE_INT);
    
    if (!$recipient_email) {
        echo "<p>❌ Please enter a valid email address.</p>";
    } elseif (!$template_id) {
        echo "<p>❌ Invalid template ID.</p>";
    } else {
        // Get the template
        $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$template_id]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            echo "<p>❌ Template not found.</p>";
        } else {
            // Setup email
            $mail = new PHPMailer(true);
            
            try {
                // Server settings
                $mail->isSMTP();
                $mail->Host       = $email_settings['smtp_host'] ?? 'localhost';
                $mail->SMTPAuth   = !empty($email_settings['smtp_auth']) && $email_settings['smtp_auth'] === 'yes';
                $mail->Username   = $email_settings['smtp_username'] ?? '';
                $mail->Password   = $email_settings['smtp_password'] ?? '';
                $mail->SMTPSecure = $email_settings['smtp_secure'] ?? '';
                $mail->Port       = $email_settings['smtp_port'] ?? 25;
                
                // Recipients
                $mail->setFrom($email_settings['from_email'] ?? '<EMAIL>', $email_settings['from_name'] ?? 'Church Admin');
                $mail->addAddress($recipient_email);
                $mail->addReplyTo($email_settings['raffle_reply_email'] ?? '<EMAIL>');
                
                // Content
                $mail->isHTML(true);
                $mail->Subject = $template['subject'];
                $mail->Body    = $template['content'];
                
                $mail->send();
                echo "<p>✅ Email has been sent successfully to " . htmlspecialchars($recipient_email) . "</p>";
                echo "<p>Please check your email to verify the lottery numbers are displayed correctly.</p>";
            } catch (Exception $e) {
                echo "<p>❌ Message could not be sent. Mailer Error: {$mail->ErrorInfo}</p>";
            }
        }
    }
}

// Template dropdown
$stmt = $pdo->query("SELECT id, template_name FROM email_templates ORDER BY template_name");
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<h2>Send Direct Test Email</h2>
<p>This form lets you send an email directly using PHPMailer without going through the bulk email system.</p>

<form method="post" style="margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px;">
    <div style="margin-bottom: 15px;">
        <label for="recipient_email" style="display: block; margin-bottom: 5px; font-weight: bold;">Recipient Email:</label>
        <input type="email" name="recipient_email" id="recipient_email" required 
               style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="template_id" style="display: block; margin-bottom: 5px; font-weight: bold;">Email Template:</label>
        <select name="template_id" id="template_id" required
                style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
            <?php foreach ($templates as $template): ?>
                <option value="<?= $template['id'] ?>">
                    <?= htmlspecialchars($template['template_name']) ?> (ID: <?= $template['id'] ?>)
                </option>
            <?php endforeach; ?>
        </select>
    </div>
    
    <button type="submit" name="send_email" 
            style="background-color: #8A2BE2; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer;">
        Send Test Email
    </button>
</form>

<h3>Recommended Template</h3>
<p>For best results with lottery numbers, use "The Big Raffle Winner (Fixed Numbers)" template.</p>

<p><a href="../admin/email_templates.php" style="color: #8A2BE2;">Go to Email Templates</a></p>