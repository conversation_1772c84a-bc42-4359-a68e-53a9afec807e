-- Security-related tables for the church application

-- Table for tracking admin login attempts
CREATE TABLE IF NOT EXISTS `admin_login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL,
  `failed_attempts` int(11) NOT NULL DEFAULT 0,
  `last_failed_attempt` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table for security logs
CREATE TABLE IF NOT EXISTS `security_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_type` varchar(255) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `event_data` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table for admin activity logs
CREATE TABLE IF NOT EXISTS `admin_activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL,
  `action` varchar(255) NOT NULL,
  `entity_type` varchar(50) DEFAULT NULL,
  `entity_id` int(11) DEFAULT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_action` (`action`),
  KEY `idx_entity` (`entity_type`, `entity_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table for two-factor authentication
CREATE TABLE IF NOT EXISTS `admin_2fa` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL,
  `secret_key` varchar(255) NOT NULL,
  `backup_codes` text DEFAULT NULL,
  `is_enabled` tinyint(1) NOT NULL DEFAULT 0,
  `last_used` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add security-related columns to admins table if they don't exist
ALTER TABLE `admins` 
ADD COLUMN IF NOT EXISTS `password_reset_token` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `password_reset_expires` datetime DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `password_changed_at` datetime DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `last_login_at` datetime DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `last_login_ip` varchar(45) DEFAULT NULL;

-- Create index on password_reset_token
CREATE INDEX IF NOT EXISTS `idx_password_reset_token` ON `admins` (`password_reset_token`);

-- Add encrypted_data column to members table for sensitive information
ALTER TABLE `members` 
ADD COLUMN IF NOT EXISTS `encrypted_data` text DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `data_retention_date` date DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `anonymized` tinyint(1) NOT NULL DEFAULT 0; 