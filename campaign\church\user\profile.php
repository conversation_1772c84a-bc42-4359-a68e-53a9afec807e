<?php
/**
 * User Profile Management
 * 
 * Allows authenticated users to view and edit their profile information
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Check if user must change password (redirect to change password page)
if ($userData['must_change_password']) {
    header("Location: change_password.php");
    exit();
}

// Process profile update form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        $profileData = [
            'full_name' => trim($_POST['full_name'] ?? ''),
            'first_name' => trim($_POST['first_name'] ?? ''),
            'last_name' => trim($_POST['last_name'] ?? ''),
            'phone_number' => trim($_POST['phone_number'] ?? ''),
            'birth_date' => trim($_POST['birth_date'] ?? ''),
            'home_address' => trim($_POST['home_address'] ?? ''),
            'occupation' => trim($_POST['occupation'] ?? ''),
        ];
        
        // Validate required fields
        if (empty($profileData['full_name'])) {
            $error = "Full name is required.";
        } elseif (!empty($profileData['phone_number']) && !$security->validateInput($profileData['phone_number'], 'phone')) {
            $error = "Please enter a valid phone number.";
        } else {
            // Handle profile image upload
            $imageUpdated = false;
            $currentImagePath = $userData['image_path'];
            
            // Check if user wants to remove current image
            if (isset($_POST['remove_image']) && $_POST['remove_image'] == '1') {
                // Delete old image if it exists and is not default
                if (!empty($currentImagePath) && $currentImagePath != 'assets/img/default-profile.jpg') {
                    $oldImagePath = '../' . $currentImagePath;
                    if (file_exists($oldImagePath)) {
                        unlink($oldImagePath);
                    }
                }
                $profileData['image_path'] = null;
                $imageUpdated = true;
            } elseif (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
                try {
                    $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
                    $fileExtension = strtolower(pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION));
                    
                    if (!in_array($fileExtension, $allowedTypes)) {
                        throw new Exception('Invalid file type. Please upload JPG, PNG, or GIF files only.');
                    }
                    
                    // Check file size (max 5MB)
                    if ($_FILES['profile_image']['size'] > 5 * 1024 * 1024) {
                        throw new Exception('File size too large. Please upload an image smaller than 5MB.');
                    }
                    
                    $uploadDir = '../uploads/profiles/';

                    // Create upload directory if it doesn't exist
                    if (!is_dir($uploadDir)) {
                        if (!mkdir($uploadDir, 0755, true)) {
                            throw new Exception('Failed to create upload directory.');
                        }
                    }

                    // Check if directory is writable
                    if (!is_writable($uploadDir)) {
                        throw new Exception('Upload directory is not writable. Please check permissions.');
                    }

                    // Generate unique filename
                    $filename = uniqid() . '.' . $fileExtension;
                    $uploadPath = $uploadDir . $filename;
                    
                    if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $uploadPath)) {
                        // Delete old image if it exists and is not default
                        if (!empty($currentImagePath) && $currentImagePath != 'assets/img/default-profile.jpg') {
                            $oldImagePath = '../' . $currentImagePath;
                            if (file_exists($oldImagePath)) {
                                unlink($oldImagePath);
                            }
                        }
                        
                        $profileData['image_path'] = 'uploads/profiles/' . $filename;
                        $imageUpdated = true;
                    } else {
                        throw new Exception('Failed to upload image.');
                    }
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
            }
            
            if (empty($error)) {
                // Update profile
                $result = $userAuth->updateProfile($userId, $profileData);
                
                if ($result['success']) {
                    $success = "Profile updated successfully!";
                    
                    // Update session data
                    $_SESSION['user_name'] = $profileData['full_name'];
                    
                    // Refresh user data
                    $userData = $userAuth->getUserById($userId);
                } else {
                    $error = $result['message'];
                }
            }
        }
    }
}

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }
        
        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            color: white !important;
        }
        
        .profile-container {
            margin-top: 2rem;
        }
        
        .profile-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
        }
        
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid rgba(255,255,255,0.3);
            margin-bottom: 1rem;
        }
        
        .profile-avatar-placeholder {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.2);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            border: 4px solid rgba(255,255,255,0.3);
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .btn-outline-danger {
            border-radius: 10px;
            padding: 0.5rem 1rem;
            font-weight: 500;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .alert-danger {
            background-color: #fee;
            color: #c33;
        }
        
        .alert-success {
            background-color: #efe;
            color: #363;
        }
        
        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .required-field::after {
            content: " *";
            color: #dc3545;
        }
        
        .image-upload-section {
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .image-upload-section:hover {
            border-color: #667eea;
            background-color: #f0f4ff;
        }
        
        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
            margin-top: 1rem;
        }
        
        .profile-stats {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 0.5rem;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($sitename); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Empty left side to push everything to the right -->
                <div class="navbar-nav me-auto"></div>

                <!-- Main navigation items on the right -->
                <ul class="navbar-nav me-3">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="bi bi-calendar-event"></i> Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="birthday_templates.php">
                            <i class="bi bi-gift"></i> Birthdays
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                </ul>

                <!-- User dropdown -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($userData['first_name'] ?: $userData['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php"><i class="bi bi-shield-lock"></i> Change Password</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="bi bi-gear"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container profile-container">
        <!-- Profile Header -->
        <div class="profile-header">
            <?php if (!empty($userData['image_path'])): ?>
                <img src="../<?php echo htmlspecialchars($userData['image_path']); ?>" alt="Profile" class="profile-avatar">
            <?php else: ?>
                <div class="profile-avatar-placeholder">
                    <i class="bi bi-person-fill" style="font-size: 3rem;"></i>
                </div>
            <?php endif; ?>
            <h2><?php echo htmlspecialchars($userData['full_name']); ?></h2>
            <p class="mb-0"><?php echo htmlspecialchars($userData['email']); ?></p>
            <?php if (!empty($userData['occupation'])): ?>
                <p class="mb-0 opacity-75"><?php echo htmlspecialchars($userData['occupation']); ?></p>
            <?php endif; ?>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Profile Form -->
            <div class="col-lg-8">
                <div class="profile-card">
                    <h4 class="mb-4"><i class="bi bi-person-gear"></i> Edit Profile Information</h4>
                    
                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" enctype="multipart/form-data">
                        <?php echo $security->generateCSRFInput(); ?>
                        
                        <!-- Profile Image Section -->
                        <div class="mb-4">
                            <label class="form-label">Profile Photo</label>
                            <div class="image-upload-section">
                                <?php if (!empty($userData['image_path'])): ?>
                                    <div class="current-image mb-3">
                                        <img src="../<?php echo htmlspecialchars($userData['image_path']); ?>" alt="Current Profile" class="image-preview">
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeCurrentImage()">
                                                <i class="bi bi-trash"></i> Remove Current Photo
                                            </button>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="upload-area">
                                    <i class="bi bi-cloud-upload" style="font-size: 2rem; color: #667eea;"></i>
                                    <p class="mb-2">Upload a new profile photo</p>
                                    <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*" onchange="previewImage(this)">
                                    <small class="text-muted">JPG, PNG, or GIF. Max size: 5MB</small>
                                </div>
                                
                                <div id="image_preview_container" class="d-none">
                                    <img id="image_preview" class="image-preview">
                                </div>
                                
                                <input type="hidden" id="remove_image" name="remove_image" value="0">
                            </div>
                        </div>
                        
                        <!-- Personal Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label required-field">Full Name</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($userData['full_name']); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($userData['email']); ?>" readonly>
                                    <small class="text-muted">Email cannot be changed. Contact an administrator if needed.</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">First Name</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" 
                                           value="<?php echo htmlspecialchars($userData['first_name'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">Last Name</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" 
                                           value="<?php echo htmlspecialchars($userData['last_name'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone_number" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone_number" name="phone_number" 
                                           value="<?php echo htmlspecialchars($userData['phone_number'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="birth_date" class="form-label">Birth Date</label>
                                    <input type="date" class="form-control" id="birth_date" name="birth_date" 
                                           value="<?php echo htmlspecialchars($userData['birth_date'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="occupation" class="form-label">Occupation</label>
                            <input type="text" class="form-control" id="occupation" name="occupation" 
                                   value="<?php echo htmlspecialchars($userData['occupation'] ?? ''); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="home_address" class="form-label">Home Address</label>
                            <textarea class="form-control" id="home_address" name="home_address" rows="3"><?php echo htmlspecialchars($userData['home_address'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Profile
                            </button>
                            <a href="dashboard.php" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Profile Stats & Info -->
            <div class="col-lg-4">
                <div class="profile-card">
                    <h5 class="mb-3"><i class="bi bi-info-circle"></i> Account Information</h5>
                    
                    <div class="profile-stats">
                        <div class="row">
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number">0</div>
                                    <div class="stat-label">Events</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number">0</div>
                                    <div class="stat-label">Messages</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Member Since:</strong><br>
                        <span class="text-muted"><?php echo date('F j, Y', strtotime($userData['created_at'])); ?></span>
                    </div>
                    
                    <?php if (!empty($userData['last_login_at'])): ?>
                        <div class="mb-3">
                            <strong>Last Login:</strong><br>
                            <span class="text-muted"><?php echo date('M j, Y g:i A', strtotime($userData['last_login_at'])); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <strong>Account Status:</strong><br>
                        <span class="badge bg-success">Active</span>
                    </div>
                </div>
                
                <div class="profile-card">
                    <h5 class="mb-3"><i class="bi bi-gear"></i> Quick Actions</h5>
                    <div class="d-grid gap-2">
                        <a href="change_password.php" class="btn btn-outline-primary">
                            <i class="bi bi-shield-lock"></i> Change Password
                        </a>
                        <a href="settings.php" class="btn btn-outline-primary">
                            <i class="bi bi-gear"></i> Account Settings
                        </a>
                        <a href="events.php" class="btn btn-outline-primary">
                            <i class="bi bi-calendar-event"></i> View Events
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewImage(input) {
            const previewContainer = document.getElementById('image_preview_container');
            const preview = document.getElementById('image_preview');
            const removeImageField = document.getElementById('remove_image');
            
            // Reset remove image flag when new image is selected
            removeImageField.value = '0';
            
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    previewContainer.classList.remove('d-none');
                }
                reader.readAsDataURL(input.files[0]);
            } else {
                previewContainer.classList.add('d-none');
            }
        }
        
        function removeCurrentImage() {
            if (confirm('Are you sure you want to remove your current profile photo?')) {
                document.getElementById('remove_image').value = '1';
                
                // Hide current image
                const currentImage = document.querySelector('.current-image');
                if (currentImage) {
                    currentImage.style.display = 'none';
                }
                
                // Show confirmation
                const uploadArea = document.querySelector('.upload-area');
                uploadArea.innerHTML = '<div class="alert alert-info"><i class="bi bi-info-circle"></i> Current photo will be removed when you save changes.</div>' + uploadArea.innerHTML;
            }
        }
        
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert-dismissible');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
