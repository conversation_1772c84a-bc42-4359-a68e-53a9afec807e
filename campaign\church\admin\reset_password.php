<?php
// Include the configuration file
require_once '../config.php';

// Include session manager which will handle starting the session
require_once 'includes/session-manager.php';

// If already logged in, redirect to admin dashboard
if (isset($_SESSION['admin_id'])) {
    header("Location: dashboard.php");
    exit();
}

// Include the SecurityManager class
require_once '../classes/SecurityManager.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize SecurityManager
$security = new SecurityManager($conn);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';
$token = '';
$validToken = false;
$admin = null;

// Check if token is provided
if (isset($_GET['token']) && !empty($_GET['token'])) {
    $token = $security->sanitizeInput($_GET['token'], 'text');
    
    // For debugging
    error_log("Received token: " . $token);
    
    // First check if the token exists at all, regardless of expiration
    $stmt = $conn->prepare("
        SELECT id, username, full_name, password_reset_expires
        FROM admins 
        WHERE password_reset_token = ?
    ");
    $stmt->execute([$token]);
    $admin = $stmt->fetch();
    
    if ($admin) {
        // Token exists, now check if it's expired
        $expiryTime = strtotime($admin['password_reset_expires']);
        $currentTime = time();
        
        error_log("Token found for user: " . $admin['username'] . 
                 ", Expires at: " . $admin['password_reset_expires'] . 
                 ", Current server time: " . date('Y-m-d H:i:s', $currentTime));
        
        // Give a 30-minute grace period beyond the official expiry time
        if ($currentTime <= ($expiryTime + 1800)) {
            $validToken = true;
            error_log("Valid token for user: " . $admin['username']);
        } else {
            error_log("Token expired for user: " . $admin['username'] . 
                     ", Expired at: " . $admin['password_reset_expires'] . 
                     ", Current time: " . date('Y-m-d H:i:s', $currentTime));
            $error = "Your password reset link has expired. Please request a new password reset.";
        }
    } else {
        error_log("Invalid token, not found in database");
        $error = "Invalid password reset token. Please request a new password reset.";
    }
} else {
    $error = "No password reset token provided. Please request a password reset.";
}

// Process password reset
if ($_SERVER["REQUEST_METHOD"] == "POST" && $validToken) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        $password = $_POST['password'];
        $confirmPassword = $_POST['confirm_password'];
        
        // Validate password
        if ($password !== $confirmPassword) {
            $error = "Passwords do not match.";
        } elseif (!$security->validateInput($password, 'password')) {
            $error = "Password does not meet the requirements. Please use a stronger password.";
        } else {
            // Hash password
            $hashedPassword = $security->hashPassword($password);
            
            // Update password
            $stmt = $conn->prepare("
                UPDATE admins 
                SET password = ?, 
                    password_reset_token = NULL, 
                    password_reset_expires = NULL,
                    password_changed_at = NOW()
                WHERE id = ?
            ");
            
            if ($stmt->execute([$hashedPassword, $admin['id']])) {
                $success = "Your password has been reset successfully. You can now login with your new password.";
                
                // Log password reset
                $security->logSecurityEvent('Password reset completed', [
                    'admin_id' => $admin['id'],
                    'username' => $admin['username']
                ]);
                
                // Clear token from URL to prevent reuse
                $token = '';
                $validToken = false;
            } else {
                $error = "Failed to reset password. Please try again or contact an administrator.";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Freedom Assembly Church</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 50px;
        }
        .reset-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
        }
        .reset-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .reset-logo h1 {
            color: #343a40;
        }
        .error-message {
            color: #dc3545;
            margin-bottom: 20px;
        }
        .success-message {
            color: #28a745;
            margin-bottom: 20px;
        }
        .password-requirements {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }
        .password-strength {
            height: 5px;
            margin-top: 5px;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="reset-container">
            <div class="reset-logo">
                <h1>Reset Password</h1>
                <p>Freedom Assembly Church International</p>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="error-message text-center">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="success-message text-center">
                    <?php echo $success; ?>
                </div>
                <div class="text-center mt-3">
                    <a href="login.php" class="btn btn-primary">Go to Login</a>
                </div>
            <?php elseif ($validToken): ?>
                <div class="mb-4">
                    <p>Please enter your new password below.</p>
                </div>
                
                <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?token=' . urlencode($token)); ?>">
                    <?php echo $security->generateCSRFInput(); ?>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="password-strength"></div>
                        <div class="password-requirements">
                            Password must be at least 8 characters long and include uppercase letters, 
                            lowercase letters, numbers, and special characters.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Reset Password</button>
                    </div>
                </form>
            <?php else: ?>
                <div class="text-center mt-3">
                    <a href="forgot_password.php" class="btn btn-primary">Request New Password Reset</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password strength meter
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password');
            const strengthMeter = document.querySelector('.password-strength');
            
            if (passwordInput && strengthMeter) {
                passwordInput.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;
                    
                    // Length check
                    if (password.length >= 8) strength += 25;
                    
                    // Character type checks
                    if (/[A-Z]/.test(password)) strength += 25;
                    if (/[a-z]/.test(password)) strength += 25;
                    if (/[0-9]/.test(password)) strength += 12.5;
                    if (/[^A-Za-z0-9]/.test(password)) strength += 12.5;
                    
                    // Update strength meter
                    strengthMeter.style.width = strength + '%';
                    
                    // Set color based on strength
                    if (strength < 50) {
                        strengthMeter.style.backgroundColor = '#dc3545'; // red
                    } else if (strength < 75) {
                        strengthMeter.style.backgroundColor = '#ffc107'; // yellow
                    } else {
                        strengthMeter.style.backgroundColor = '#28a745'; // green
                    }
                });
            }
        });
    </script>
</body>
</html> 