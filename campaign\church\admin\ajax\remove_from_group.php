<?php
session_start();
require_once '../../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

// Get JSON data
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['group_id']) || !isset($data['contact_id'])) {
    echo json_encode(['success' => false, 'error' => 'Group ID and contact ID are required']);
    exit();
}

try {
    $stmt = $pdo->prepare("DELETE FROM contact_group_members WHERE contact_id = ? AND group_id = ?");
    $stmt->execute([$data['contact_id'], $data['group_id']]);
    
    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'error' => 'Error removing contact from group']);
}
?> 