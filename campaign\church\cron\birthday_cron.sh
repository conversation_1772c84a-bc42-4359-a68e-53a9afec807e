#!/bin/sh
# Dynamic Birthday Reminder Script
# Path should be updated when deploying to match the server environment
# Works with any domain/path structure

# Define the base directory (UPDATE THIS PATH FOR YOUR SERVER)
BASE_DIR="/path/to/your/site/YOUR_PATH"

# Examples for different hosting environments:
# For shared hosting: BASE_DIR="/home/<USER>/public_html/YOUR_PATH"
# For VPS/dedicated: BASE_DIR="/var/www/html/YOUR_PATH"
# For freedomassemblydb.online: BASE_DIR="/home/<USER>/domains/freedomassemblydb.online/public_html/campaign/church"

# Run the script with the PHP interpreter
/usr/bin/php $BASE_DIR/cron/birthday_reminders.php > /dev/null 2>&1

# Alternative: Use wget/curl for web-based execution (REPLACE YOUR_DOMAIN.COM/YOUR_PATH)
# wget -q -O /dev/null "https://YOUR_DOMAIN.COM/YOUR_PATH/cron/birthday_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m"