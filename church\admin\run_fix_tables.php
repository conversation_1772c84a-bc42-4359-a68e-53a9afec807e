<?php
// Include the configuration file with absolute path
$config_path = __DIR__ . '/../config.php';
require_once $config_path;

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';
$logs = [];

try {
    // Start transaction
    $conn->beginTransaction();
    
    // Drop existing tables if they exist (in reverse order of dependencies)
    $tables_to_drop = ['donation_notifications', 'payment_transactions', 'donations'];
    
    foreach ($tables_to_drop as $table) {
        $sql = "DROP TABLE IF EXISTS $table";
        $conn->exec($sql);
        $logs[] = "Dropped table $table if it existed";
    }
    
    // Create donations table with correct data types
    $sql = "CREATE TABLE IF NOT EXISTS donations (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        donor_name VARCHAR(255) NOT NULL,
        donor_email VARCHAR(255) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) NOT NULL,
        payment_method ENUM('paypal', 'stripe') NOT NULL,
        payment_status ENUM('pending', 'completed', 'failed', 'refunded') NOT NULL DEFAULT 'pending',
        transaction_id VARCHAR(255) NOT NULL,
        donation_type ENUM('general', 'birthday_gift') NOT NULL DEFAULT 'general',
        recipient_id INT(11) DEFAULT NULL,
        message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (recipient_id) REFERENCES members(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    $conn->exec($sql);
    $logs[] = "Created donations table with correct data types";

    // Create payment_transactions table with correct data types
    $sql = "CREATE TABLE IF NOT EXISTS payment_transactions (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        donation_id INT(11) NOT NULL,
        payment_provider ENUM('paypal', 'stripe') NOT NULL,
        provider_transaction_id VARCHAR(255) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) NOT NULL,
        payment_status VARCHAR(50) NOT NULL,
        payment_method_details TEXT,
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (donation_id) REFERENCES donations(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    $conn->exec($sql);
    $logs[] = "Created payment_transactions table with correct data types";

    // Create donation_notifications table with correct data types
    $sql = "CREATE TABLE IF NOT EXISTS donation_notifications (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        donation_id INT(11) NOT NULL,
        notification_type ENUM('donor_receipt', 'admin_notification', 'recipient_notification') NOT NULL,
        recipient_email VARCHAR(255) NOT NULL,
        sent_at TIMESTAMP NULL DEFAULT NULL,
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (donation_id) REFERENCES donations(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    $conn->exec($sql);
    $logs[] = "Created donation_notifications table with correct data types";

    // Commit transaction
    $conn->commit();
    $message = "Payment tables fixed successfully!";

} catch (PDOException $e) {
    // Rollback transaction on error
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    $error = "Database error: " . $e->getMessage();
    error_log("Error fixing payment tables: " . $e->getMessage());
}

// Output results
echo "<h1>Fix Payment Tables</h1>";

if ($message) {
    echo "<div style='color: green; padding: 10px; border: 1px solid green;'>{$message}</div>";
}

if ($error) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red;'>{$error}</div>";
}

echo "<h2>Operation Log</h2>";
echo "<ul>";
foreach ($logs as $log) {
    echo "<li>{$log}</li>";
}
echo "</ul>";

// Verify tables
try {
    $tables = ['donations', 'payment_transactions', 'donation_notifications'];
    echo "<h2>Table Verification</h2>";
    echo "<ul>";
    
    foreach ($tables as $table) {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = $stmt->rowCount() > 0;
        
        if ($exists) {
            echo "<li style='color: green;'>{$table}: Created successfully</li>";
        } else {
            echo "<li style='color: red;'>{$table}: Failed to create</li>";
        }
    }
    
    echo "</ul>";
} catch (PDOException $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red;'>Error verifying tables: " . $e->getMessage() . "</div>";
}
?> 