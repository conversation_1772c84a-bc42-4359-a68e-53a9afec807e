# PowerShell script for testing database connections
Write-Host "=== Church Database Connection Test ===" -ForegroundColor Cyan
Write-Host ""

try {
    Write-Host "1. Testing connection to database..." -ForegroundColor Yellow
    $result = & C:\MAMP\bin\mysql\bin\mysql -u root -proot churchdb -e "SELECT 'Connection successful!' as Result;"
    Write-Host $result -ForegroundColor Green
    Write-Host ""
    
    Write-Host "2. Retrieving email templates..." -ForegroundColor Yellow
    $templates = & C:\MAMP\bin\mysql\bin\mysql -u root -proot churchdb -e "SELECT id, template_name, subject, is_birthday_template FROM email_templates;"
    Write-Host $templates
    Write-Host ""
    
    Write-Host "3. Retrieving birthday templates only..." -ForegroundColor Yellow
    $birthdayTemplates = & C:\MAMP\bin\mysql\bin\mysql -u root -proot churchdb -e "SELECT id, template_name, subject FROM email_templates WHERE is_birthday_template = 1"
    Write-Host $birthdayTemplates
    Write-Host ""
    
    Write-Host "4. Retrieving member birthdays for current month..." -ForegroundColor Yellow
    $currentMonthBirthdays = & C:\MAMP\bin\mysql\bin\mysql -u root -proot churchdb -e "SELECT id, full_name, birth_date FROM members WHERE MONTH(birth_date) = MONTH(CURDATE()) ORDER BY DAY(birth_date);"
    Write-Host $currentMonthBirthdays
    Write-Host ""
    
    Write-Host "5. Retrieving birthday count by month..." -ForegroundColor Yellow
    $birthdayCounts = & C:\MAMP\bin\mysql\bin\mysql -u root -proot churchdb -e "SELECT MONTH(birth_date) as Month, COUNT(*) as BirthdayCount FROM members WHERE birth_date IS NOT NULL GROUP BY MONTH(birth_date) ORDER BY Month;"
    Write-Host $birthdayCounts
    Write-Host ""
    
    Write-Host "6. Checking for members with NULL birth dates..." -ForegroundColor Yellow
    $nullBirthdays = & C:\MAMP\bin\mysql\bin\mysql -u root -proot churchdb -e "SELECT COUNT(*) as NullBirthdays FROM members WHERE birth_date IS NULL;"
    Write-Host $nullBirthdays
    Write-Host ""
    
    Write-Host "Test completed successfully!" -ForegroundColor Green
}
catch {
    Write-Host "An error occurred: $_" -ForegroundColor Red
}

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 