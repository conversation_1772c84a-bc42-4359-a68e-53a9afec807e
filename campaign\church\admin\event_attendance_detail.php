<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

$message = '';
$error = '';

// Get event ID from URL
$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header("Location: event_attendance.php");
    exit();
}

// Handle attendance updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_attendance') {
    try {
        $attendance_data = $_POST['attendance'] ?? [];
        
        $conn->beginTransaction();
        
        // Update attendance for each user
        foreach ($attendance_data as $rsvp_id => $attended) {
            $actually_attended = ($attended === '1') ? 1 : 0;
            
            $stmt = $conn->prepare("
                UPDATE event_rsvps 
                SET actually_attended = ? 
                WHERE id = ? AND event_id = ?
            ");
            $stmt->execute([$actually_attended, $rsvp_id, $event_id]);
        }
        
        $conn->commit();
        $message = "Attendance records updated successfully!";
        
    } catch (Exception $e) {
        $conn->rollBack();
        $error = "Error updating attendance: " . $e->getMessage();
    }
}

// Get event details
$event_stmt = $conn->prepare("
    SELECT id, title, event_date, location, description, max_attendees
    FROM events 
    WHERE id = ?
");
$event_stmt->execute([$event_id]);
$event = $event_stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header("Location: event_attendance.php");
    exit();
}

// Get all users who RSVP'd as "attending" for this event
$attendees_query = "
    SELECT 
        er.id as rsvp_id,
        er.user_id,
        er.status,
        er.notes,
        er.actually_attended,
        er.created_at as rsvp_date,
        m.full_name,
        m.email,
        m.phone_number
    FROM event_rsvps er
    JOIN members m ON er.user_id = m.id
    WHERE er.event_id = ? AND er.status = 'attending'
    ORDER BY m.full_name ASC
";

$attendees_stmt = $conn->prepare($attendees_query);
$attendees_stmt->execute([$event_id]);
$attendees = $attendees_stmt->fetchAll(PDO::FETCH_ASSOC);

// Set page variables
$page_title = "Event Attendance - " . $event['title'];
$page_header = "Mark Attendance";
$page_description = "Mark actual attendance for " . $event['title'];

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Event Details Card -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-event"></i> Event Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h4><?php echo htmlspecialchars($event['title']); ?></h4>
                            <p class="text-muted mb-2">
                                <i class="bi bi-calendar"></i> 
                                <?php 
                                $event_date = new DateTime($event['event_date']);
                                echo $event_date->format('l, F j, Y \a\t g:i A');
                                ?>
                            </p>
                            <p class="text-muted mb-2">
                                <i class="bi bi-geo-alt"></i> 
                                <?php echo htmlspecialchars($event['location'] ?? 'Location TBD'); ?>
                            </p>
                            <?php if ($event['description']): ?>
                                <p class="mb-0"><?php echo nl2br(htmlspecialchars($event['description'])); ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="d-flex flex-column gap-2">
                                <a href="event_attendance.php" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left"></i> Back to Events
                                </a>
                                <a href="events.php" class="btn btn-outline-primary">
                                    <i class="bi bi-pencil"></i> Edit Event
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Tracking -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-people-fill"></i> Attendees (<?php echo count($attendees); ?> RSVP'd as Attending)
                    </h5>
                    <div>
                        <?php 
                        $marked_count = array_filter($attendees, function($a) { return $a['actually_attended'] !== null; });
                        $attended_count = array_filter($attendees, function($a) { return $a['actually_attended'] == 1; });
                        ?>
                        <span class="badge bg-info">
                            <?php echo count($marked_count); ?>/<?php echo count($attendees); ?> marked
                        </span>
                        <span class="badge bg-success">
                            <?php echo count($attended_count); ?> attended
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($attendees)): ?>
                        <div class="text-center text-muted py-5">
                            <i class="bi bi-person-x fs-1 d-block mb-3"></i>
                            <h5>No Attendees</h5>
                            <p>No one has RSVP'd as "attending" for this event yet.</p>
                        </div>
                    <?php else: ?>
                        <form method="POST" action="">
                            <input type="hidden" name="action" value="update_attendance">
                            <input type="hidden" name="event_id" value="<?php echo $event_id; ?>">
                            
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>RSVP Date</th>
                                            <th>Notes</th>
                                            <th class="text-center">Actually Attended</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($attendees as $attendee): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($attendee['full_name']); ?></strong>
                                                </td>
                                                <td>
                                                    <a href="mailto:<?php echo htmlspecialchars($attendee['email']); ?>">
                                                        <?php echo htmlspecialchars($attendee['email']); ?>
                                                    </a>
                                                </td>
                                                <td>
                                                    <?php if ($attendee['phone_number']): ?>
                                                        <a href="tel:<?php echo htmlspecialchars($attendee['phone_number']); ?>">
                                                            <?php echo htmlspecialchars($attendee['phone_number']); ?>
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php 
                                                    $rsvp_date = new DateTime($attendee['rsvp_date']);
                                                    echo $rsvp_date->format('M j, Y');
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php if ($attendee['notes']): ?>
                                                        <span class="text-truncate" style="max-width: 150px;" 
                                                              title="<?php echo htmlspecialchars($attendee['notes']); ?>">
                                                            <?php echo htmlspecialchars($attendee['notes']); ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-center">
                                                    <div class="form-check form-switch d-inline-block">
                                                        <input class="form-check-input" 
                                                               type="checkbox" 
                                                               name="attendance[<?php echo $attendee['rsvp_id']; ?>]" 
                                                               value="1"
                                                               id="attendance_<?php echo $attendee['rsvp_id']; ?>"
                                                               <?php echo ($attendee['actually_attended'] == 1) ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" 
                                                               for="attendance_<?php echo $attendee['rsvp_id']; ?>">
                                                            <?php 
                                                            if ($attendee['actually_attended'] === null) {
                                                                echo '<span class="text-muted">Not marked</span>';
                                                            } elseif ($attendee['actually_attended'] == 1) {
                                                                echo '<span class="text-success">Attended</span>';
                                                            } else {
                                                                echo '<span class="text-danger">Did not attend</span>';
                                                            }
                                                            ?>
                                                        </label>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mt-4">
                                <div>
                                    <button type="button" class="btn btn-outline-success" onclick="checkAll()">
                                        <i class="bi bi-check-all"></i> Mark All as Attended
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" onclick="uncheckAll()">
                                        <i class="bi bi-x-circle"></i> Mark All as Not Attended
                                    </button>
                                </div>
                                <div>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="bi bi-save"></i> Save Attendance
                                    </button>
                                </div>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function checkAll() {
    document.querySelectorAll('input[type="checkbox"][name^="attendance"]').forEach(function(checkbox) {
        checkbox.checked = true;
    });
}

function uncheckAll() {
    document.querySelectorAll('input[type="checkbox"][name^="attendance"]').forEach(function(checkbox) {
        checkbox.checked = false;
    });
}
</script>

<?php include 'includes/footer.php'; ?>
