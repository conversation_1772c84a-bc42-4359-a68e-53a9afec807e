<?php
// Verification script for the final raffle template
require_once __DIR__ . '/../config.php';

echo "=== Final Raffle Template Verification ===\n\n";

// Get the template
$template_name = "The Big Raffle Winner";
$stmt = $pdo->prepare("SELECT id, template_name, content FROM email_templates WHERE template_name = ?");
$stmt->execute([$template_name]);
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    echo "ERROR: Template not found: $template_name\n";
    exit;
}

echo "Template found:\n";
echo "- ID: " . $template['id'] . "\n";
echo "- Name: " . $template['template_name'] . "\n\n";

// Check for fixed numbers
echo "Lottery numbers: ";
preg_match_all('/<div class="lotto-ball [^"]+">(\d+)<\/div>/', $template['content'], $matches);
if (!empty($matches[1])) {
    echo implode(", ", $matches[1]) . "\n";
} else {
    echo "No fixed numbers found\n";
}

// Check for placeholders
echo "\nPlaceholder check:\n";

$check_placeholders = [
    '{{replyToEmail}}' => 'replyToEmail placeholder',
    '{{unsubscribeLink}}' => 'unsubscribeLink placeholder'
];

foreach ($check_placeholders as $placeholder => $description) {
    echo "- " . $description . ": ";
    if (strpos($template['content'], $placeholder) !== false) {
        echo "FOUND\n";
    } else {
        echo "NOT FOUND\n";
    }
}

// Check for any PHP code (which would be problematic)
echo "\nPHP code check: ";
if (preg_match('/<\?php|\$[a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*/', $template['content'])) {
    echo "WARNING - PHP code detected in template\n";
} else {
    echo "CLEAN - No PHP code in template\n";
}

// Check for hardcoded email addresses
echo "\nHardcoded email check: ";
preg_match_all('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', $template['content'], $matches);

// Filter out emails that are part of placeholders
$actual_hardcoded_emails = [];
foreach ($matches[0] as $email) {
    if (strpos($template['content'], '{{' . $email . '}}') === false &&
        strpos($template['content'], '{' . $email . '}') === false &&
        $email != 'example.com') {
        $actual_hardcoded_emails[] = $email;
    }
}

if (empty($actual_hardcoded_emails)) {
    echo "CLEAN - No hardcoded emails found\n";
} else {
    echo "WARNING - Hardcoded emails found: " . implode(", ", $actual_hardcoded_emails) . "\n";
}

// Get the replyToEmail setting
echo "\nreplyToEmail configuration:\n";
try {
    $stmt = $pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'replyToEmail' LIMIT 1");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "Value: " . $result['setting_value'] . "\n";
    } else {
        echo "Not configured\n";
    }
} catch (Exception $e) {
    echo "Error checking setting: " . $e->getMessage() . "\n";
}

// Final assessment
echo "\n=== Summary ===\n";
$has_issues = !preg_match('/{{replyToEmail}}/', $template['content']) ||
              !preg_match('/{{unsubscribeLink}}/', $template['content']) ||
              !empty($actual_hardcoded_emails) ||
              preg_match('/<\?php|\$[a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*/', $template['content']);

if (!$has_issues) {
    echo "✅ PASSED - The template is correctly configured and should work properly.\n";
} else {
    echo "❌ ISSUES FOUND - Please review the details above.\n";
}

echo "\nTemplate can be accessed from the admin panel at admin/email_templates.php\n"; 