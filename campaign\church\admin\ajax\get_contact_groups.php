<?php
session_start();
require_once '../../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

if (!isset($_GET['id'])) {
    echo json_encode(['success' => false, 'error' => 'Contact ID is required']);
    exit();
}

try {
    $stmt = $pdo->prepare("SELECT group_id FROM contact_group_members WHERE contact_id = ?");
    $stmt->execute([$_GET['id']]);
    $groups = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo json_encode($groups);
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'error' => 'Error fetching contact groups']);
}
?> 