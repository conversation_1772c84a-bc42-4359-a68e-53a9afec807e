-- Clean view definition without DEFINER clause 
-- Drop existing view first 
DROP VIEW IF EXISTS `email_tracking_stats`; 
 
-- Create clean view 
CREATE VIEW `email_tracking_stats` AS 
SELECT 
  tr.email_type AS email_type, 
  et.template_name AS template_name, 
  COUNT(DISTINCT tr.id) AS emails_sent, 
  COUNT(DISTINCT (CASE WHEN (tr.opened_at IS NOT NULL) THEN tr.id END)) AS emails_opened, 
  ROUND(((COUNT(DISTINCT (CASE WHEN (tr.opened_at IS NOT NULL) THEN tr.id END)) / COUNT(DISTINCT tr.id)) * 100),2) AS open_rate, 
  AVG(tr.opened_count) AS avg_opens_per_email 
FROM 
  (email_tracking tr LEFT JOIN email_templates et ON((et.template_name LIKE CONCAT('%',tr.email_type,'%')))) 
GROUP BY 
  tr.email_type, et.template_name; 
