<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Church Email Management System - User Guide</title>
    <style>
        :root {
            --primary-color: #333;
            --secondary-color: #666;
            --border-color: #ddd;
            --highlight-color: #f5f5f5;
            --notebook-line: #e5e5e5;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--primary-color);
            background: #fff;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            background-image: linear-gradient(#fff 1.1rem, var(--notebook-line) 1.2rem);
            background-size: 100% 1.2rem;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 2rem;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        h1, h2, h3, h4 {
            color: var(--primary-color);
            margin: 1.5rem 0 1rem;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 0.5rem;
        }

        h1 {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        h2 {
            font-size: 1.8rem;
            margin-top: 3rem;
        }

        h3 {
            font-size: 1.4rem;
            color: var(--secondary-color);
        }

        p {
            margin-bottom: 1rem;
        }

        ul, ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }

        li {
            margin-bottom: 0.5rem;
        }

        code {
            background: var(--highlight-color);
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .note {
            background: var(--highlight-color);
            border-left: 4px solid var(--secondary-color);
            padding: 1rem;
            margin: 1rem 0;
        }

        .warning {
            background: #fff8dc;
            border-left: 4px solid #ffd700;
            padding: 1rem;
            margin: 1rem 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        th, td {
            border: 1px solid var(--border-color);
            padding: 0.5rem;
            text-align: left;
        }

        th {
            background: var(--highlight-color);
        }

        img {
            max-width: 100%;
            height: auto;
            margin: 1rem 0;
            border: 1px solid var(--border-color);
        }

        .toc {
            background: var(--highlight-color);
            padding: 1rem;
            border-radius: 4px;
            margin: 2rem 0;
        }

        .toc ul {
            list-style-type: none;
        }

        .toc li {
            margin: 0.5rem 0;
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }

            .container {
                padding: 1rem;
            }

            h1 {
                font-size: 2rem;
            }

            h2 {
                font-size: 1.5rem;
            }

            h3 {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Church Email Management System</h1>
        <p style="text-align: center;">Comprehensive User Guide</p>

        <div class="toc">
            <h2>Table of Contents</h2>
            <ul>
                <li><a href="#introduction">1. Introduction</a></li>
                <li><a href="#getting-started">2. Getting Started</a></li>
                <li><a href="#member-management">3. Member Management</a></li>
                <li><a href="#email-system">4. Email System</a></li>
                <li><a href="#birthday-automation">5. Birthday Automation</a></li>
                <li><a href="#analytics">6. Analytics and Reporting</a></li>
                <li><a href="#settings">7. System Settings</a></li>
                <li><a href="#troubleshooting">8. Troubleshooting</a></li>
            </ul>
        </div>

        <section id="introduction">
            <h2>1. Introduction</h2>
            <p>Welcome to the Church Email Management System, a comprehensive solution designed to streamline your church's communication and member management processes.</p>

            <h3>Key Features</h3>
            <ul>
                <li>Member Database Management</li>
                <li>Automated Birthday Emails</li>
                <li>Email Template System</li>
                <li>Contact Group Organization</li>
                <li>Email Analytics and Tracking</li>
                <li>WhatsApp Integration</li>
            </ul>

            <div class="note">
                <strong>Note:</strong> This system is designed to be user-friendly while providing powerful features for church administration.
            </div>
        </section>

        <section id="getting-started">
            <h2>2. Getting Started</h2>
            
            <h3>System Requirements</h3>
            <ul>
                <li>Web server with PHP 7.4 or higher</li>
                <li>MySQL 5.7 or higher</li>
                <li>Modern web browser (Chrome, Firefox, Safari, Edge)</li>
            </ul>

            <h3>First-Time Setup</h3>
            <ol>
                <li>Access your admin panel at <code>your-domain.com/church/admin</code></li>
                <li>Login with your provided credentials</li>
                <li>Navigate to Settings to configure your church information</li>
                <li>Set up your email templates</li>
            </ol>

            <div class="warning">
                <strong>Important:</strong> Change your default password immediately after first login.
            </div>
        </section>

        <section id="member-management">
            <h2>3. Member Management</h2>

            <h3>Adding New Members</h3>
            <ol>
                <li>Click on "Members" in the sidebar</li>
                <li>Click "Add New Member"</li>
                <li>Fill in the required information:
                    <ul>
                        <li>Full Name</li>
                        <li>Email Address</li>
                        <li>Phone Number</li>
                        <li>Birth Date</li>
                    </ul>
                </li>
                <li>Click "Save" to add the member</li>
            </ol>

            <h3>Managing Member Groups</h3>
            <p>Organize members into groups for targeted communication:</p>
            <ul>
                <li>Create custom groups</li>
                <li>Assign members to multiple groups</li>
                <li>Send group-specific emails</li>
            </ul>
        </section>

        <section id="email-system">
            <h2>4. Email System</h2>

            <h3>Email Templates</h3>
            <p>Create and manage email templates for various purposes:</p>
            <ul>
                <li>Birthday wishes</li>
                <li>Event announcements</li>
                <li>Newsletter templates</li>
                <li>Custom notifications</li>
            </ul>

            <h3>Template Variables</h3>
            <table>
                <tr>
                    <th>Variable</th>
                    <th>Description</th>
                </tr>
                <tr>
                    <td>{full_name}</td>
                    <td>Member's full name</td>
                </tr>
                <tr>
                    <td>{first_name}</td>
                    <td>Member's first name</td>
                </tr>
                <tr>
                    <td>{birth_date}</td>
                    <td>Member's birth date</td>
                </tr>
            </table>
        </section>

        <section id="birthday-automation">
            <h2>5. Birthday Automation</h2>

            <h3>Setting Up Birthday Reminders</h3>
            <ol>
                <li>Navigate to "Birthday Settings"</li>
                <li>Configure reminder preferences:
                    <ul>
                        <li>Days before birthday to send reminder</li>
                        <li>Select template for birthday emails</li>
                        <li>Enable/disable automatic sending</li>
                    </ul>
                </li>
            </ol>

            <div class="note">
                <strong>Tip:</strong> Test your birthday automation system using the test feature before enabling automatic sending.
            </div>
        </section>

        <section id="analytics">
            <h2>6. Analytics and Reporting</h2>

            <h3>Email Analytics</h3>
            <p>Track the performance of your email campaigns:</p>
            <ul>
                <li>Open rates</li>
                <li>Click-through rates</li>
                <li>Delivery status</li>
                <li>Bounce rates</li>
            </ul>

            <h3>Generating Reports</h3>
            <p>Access detailed reports for:</p>
            <ul>
                <li>Monthly email statistics</li>
                <li>Member engagement</li>
                <li>Birthday email performance</li>
                <li>Group activity</li>
            </ul>
        </section>

        <section id="settings">
            <h2>7. System Settings</h2>

            <h3>General Settings</h3>
            <ul>
                <li>Church Information
                    <ul>
                        <li>Church name</li>
                        <li>Contact details</li>
                        <li>Logo upload</li>
                    </ul>
                </li>
                <li>Email Settings
                    <ul>
                        <li>SMTP configuration</li>
                        <li>Sender email address</li>
                        <li>Reply-to address</li>
                    </ul>
                </li>
            </ul>

            <h3>Customization</h3>
            <p>Customize your system's appearance:</p>
            <ul>
                <li>Admin panel title</li>
                <li>Color scheme</li>
                <li>Email template styles</li>
            </ul>
        </section>

        <section id="troubleshooting">
            <h2>8. Troubleshooting</h2>

            <h3>Common Issues</h3>
            <table>
                <tr>
                    <th>Issue</th>
                    <th>Solution</th>
                </tr>
                <tr>
                    <td>Emails not sending</td>
                    <td>Check SMTP settings and email credentials</td>
                </tr>
                <tr>
                    <td>Birthday reminders not working</td>
                    <td>Verify cron job setup and automation settings</td>
                </tr>
                <tr>
                    <td>Analytics not updating</td>
                    <td>Clear browser cache and check tracking pixel settings</td>
                </tr>
            </table>

            <div class="warning">
                <strong>Need Help?</strong> Contact our support <NAME_EMAIL> for assistance.
            </div>
        </section>
    </div>
</body>
</html> 