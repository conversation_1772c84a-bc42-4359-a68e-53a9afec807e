<?php
// Improved Raffle Email Template with Random Numbers - Version 3
// This version uses standard placeholders that get replaced by the email system
require_once __DIR__ . '/../config.php';

// Database connection
$conn = $pdo;

try {
    // Template details
    $template_name = "The Big Raffle Winner";
    $subject = "The Big Raffle - You're a Winner!";
    
    // Create the content with standardized placeholders
    $content = <<<'HTML'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Big Raffle - You're a Winner!</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: Arial, Helvetica, sans-serif;
            background-color: #f4f4f4;
        }
        
        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .flyer {
            background: linear-gradient(135deg, #8A2BE2, #5a1ca5);
            border-radius: 10px 10px 0 0;
            text-align: center;
            padding: 30px;
        }
        
        .winner-banner {
            background-color: #FFD700;
            color: #8A2BE2;
            font-size: 22px;
            font-weight: bold;
            padding: 10px 20px;
            border-radius: 8px;
            display: inline-block;
        }
        
        .title {
            font-size: 40px;
            font-weight: bold;
            color: #fff;
            margin: 15px 0;
        }
        
        .lotto-ball {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            font-size: 22px;
            font-weight: bold;
            color: white;
            margin: 5px;
        }
        
        .ball-1 { background-color: #FF69B4; }
        .ball-2 { background-color: #FFD700; }
        .ball-3 { background-color: #00CED1; }
        .ball-4 { background-color: #32CD32; }
        .ball-5 { background-color: #FF6347; }
        
        .powerball {
            background-color: #FF0000;
            width: 70px;
            height: 70px;
            font-size: 24px;
        }
        
        .claim-section {
            text-align: center;
            padding: 20px;
        }
        
        .claim-button {
            display: inline-block;
            padding: 15px 30px;
            background-color: #FFD700;
            color: #8A2BE2;
            font-weight: bold;
            text-decoration: none;
            border-radius: 6px;
            font-size: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .instructions {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 20px;
            margin: 20px;
        }
        
        .footer {
            background-color: #8A2BE2;
            color: white;
            padding: 15px;
            font-size: 12px;
            text-align: center;
            border-radius: 0 0 10px 10px;
        }
    </style>
</head>
<body>
    <table class="container" cellpadding="0" cellspacing="0" border="0" width="100%">
        <tr>
            <td class="flyer">
                <div class="winner-banner">CONGRATULATIONS!</div>
                <div class="title">THE BIG RAFFLE</div>
                <p style="color: white; font-size: 24px;">You're a WINNER!</p>
            </td>
        </tr>
        <tr>
            <td align="center" style="padding: 20px;">
                <div class="lotto-ball ball-1">{random_number_1}</div>
                <div class="lotto-ball ball-2">{random_number_2}</div>
                <div class="lotto-ball ball-3">{random_number_3}</div>
                <div class="lotto-ball ball-4">{random_number_4}</div>
                <div class="lotto-ball ball-5">{random_number_5}</div>
                <div class="lotto-ball powerball">{random_powerball}</div>
            </td>
        </tr>
        <tr>
            <td class="claim-section">
                <p style="font-size: 18px;">Congratulations! Your Powerball numbers have been drawn, and you're a winner! A life-changing prize awaits you.</p>
                
                <!-- Direct mailto link with reply-to placeholder -->
                <a href="mailto:{reply_to_email}?subject=I%20Won%20The%20Big%20Raffle&body=Hello,%0A%0AI%20received%20your%20email%20notification%20and%20I%20am%20confirming%20that%20I%20am%20the%20Powerball%20winner!%0A%0AMy%20contact%20details:%0AName:%20%0APhone:%20%0AAddress:%20%0A%0AI'm%20excited%20to%20claim%20my%20prize!%0A%0AThank%20you,%0A" class="claim-button">CLAIM YOUR PRIZE NOW</a>
            </td>
        </tr>
        <tr>
            <td class="instructions">
                <h3 style="color: #8A2BE2;">How to Claim Your Prize:</h3>
                <ol>
                    <li>Click the claim button above</li>
                    <li>Verify your identity</li>
                    <li>Choose your payment method</li>
                    <li>Receive your prize within 24 hours</li>
                </ol>
            </td>
        </tr>
        <tr>
            <td class="footer">
                <p>© 2025 The Big Raffle. All rights reserved.</p>
                <p>If you did not register for this raffle, please disregard this email.</p>
                <p><a href="{unsubscribe_link}" style="color: #FFD700; text-decoration: none;">Unsubscribe</a></p>
            </td>
        </tr>
    </table>
</body>
</html>
HTML;

    // Check if a template with this name already exists
    $stmt = $conn->prepare("SELECT id FROM email_templates WHERE template_name = ?");
    $stmt->execute([$template_name]);
    $existing = $stmt->fetch();
    
    $is_birthday_template = 0;
    $template_category = 'bulk';
    
    if ($existing) {
        // Update the existing template
        echo "<h3>Updating existing raffle template</h3>";
        $stmt = $conn->prepare("UPDATE email_templates SET subject = ?, content = ?, is_birthday_template = ?, template_category = ? WHERE id = ?");
        $stmt->execute([$subject, $content, $is_birthday_template, $template_category, $existing['id']]);
        
        echo "<p>Successfully updated template with ID: " . $existing['id'] . "</p>";
    } else {
        // Create a new template
        echo "<h3>Creating new raffle template</h3>";
        $stmt = $conn->prepare("INSERT INTO email_templates (template_name, subject, content, is_birthday_template, template_category) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$template_name, $subject, $content, $is_birthday_template, $template_category]);
        
        echo "<p>Successfully created template with ID: " . $conn->lastInsertId() . "</p>";
    }
    
    echo "<p>The template has been improved with:</p>";
    echo "<ul>";
    echo "<li>Standardized random number placeholders that can be replaced at sending time</li>";
    echo "<li>Standard {reply_to_email} placeholder that works with the email system</li>";
    echo "<li>Improved HTML structure for better email client compatibility</li>";
    echo "<li>Standard {unsubscribe_link} format used by the system</li>";
    echo "</ul>";
    
    // Add placeholder initialization script for testing
    echo "<h3>Adding placeholder initialization script</h3>";
    
    // Create a file to initialize the placeholders in settings table
    $init_script_path = __DIR__ . '/init_email_placeholders.php';
    $init_script = <<<'PHP'
<?php
// Script to initialize email placeholders in the system
require_once __DIR__ . '/../config.php';

try {
    // Initialize reply_to_email if not exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM email_settings WHERE setting_key = 'reply_to_email'");
    $stmt->execute();
    $exists = $stmt->fetchColumn();
    
    if (!$exists) {
        $stmt = $pdo->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES ('reply_to_email', '' . CHURCH_EMAIL . '')");
        $stmt->execute();
        echo "<p>Added reply_to_email setting to email_settings table</p>";
    } else {
        echo "<p>reply_to_email setting already exists in email_settings table</p>";
    }
    
    // Register the random number placeholders in the system
    function add_placeholder_function($placeholder_key, $function_code) {
        global $pdo;
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM email_placeholder_functions WHERE placeholder_key = ?");
        $stmt->execute([$placeholder_key]);
        $exists = $stmt->fetchColumn();
        
        if (!$exists) {
            $stmt = $pdo->prepare("INSERT INTO email_placeholder_functions (placeholder_key, function_code) VALUES (?, ?)");
            $stmt->execute([$placeholder_key, $function_code]);
            return true;
        }
        return false;
    }
    
    // Try to create the email_placeholder_functions table if it doesn't exist
    $pdo->exec("CREATE TABLE IF NOT EXISTS email_placeholder_functions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        placeholder_key VARCHAR(255) NOT NULL,
        function_code TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // Add random number generator placeholders
    $random_functions = [
        'random_number_1' => 'return rand(1, 20);',
        'random_number_2' => 'return rand(21, 40);',
        'random_number_3' => 'return rand(41, 60);',
        'random_number_4' => 'return rand(61, 80);',
        'random_number_5' => 'return rand(81, 99);',
        'random_powerball' => 'return rand(1, 15);'
    ];
    
    $added_count = 0;
    foreach ($random_functions as $key => $code) {
        if (add_placeholder_function($key, $code)) {
            $added_count++;
        }
    }
    
    echo "<p>Added $added_count new placeholder functions for random numbers</p>";
    
    // Verify the reply_to_email value
    $stmt = $pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'reply_to_email'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "<p>Current reply_to_email value: " . htmlspecialchars($result['setting_value']) . "</p>";
    }
    
    echo "<p>Placeholder initialization completed</p>";
    
} catch (PDOException $e) {
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
}
PHP;

    file_put_contents($init_script_path, $init_script);
    echo "<p>Created initialization script: <a href='init_email_placeholders.php'>init_email_placeholders.php</a></p>";
    
    echo "<p><a href='../admin/email_templates.php'>View all email templates</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
} 