-- Create A/B Testing table for email templates
CREATE TABLE IF NOT EXISTS `ab_tests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `test_name` varchar(255) NOT NULL,
  `template_a_id` int(11) NOT NULL,
  `template_b_id` int(11) NOT NULL,
  `split_percentage` int(11) NOT NULL DEFAULT 50 COMMENT 'Percentage for Template A (Template B gets remainder)',
  `description` text DEFAULT NULL,
  `status` enum('active','completed','paused') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `ended_at` timestamp NULL DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `fk_template_a` (`template_a_id`),
  KEY `fk_template_b` (`template_b_id`),
  CONSTRAINT `fk_ab_tests_template_a` FOREIGN KEY (`template_a_id`) REFERENCES `email_templates` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_ab_tests_template_b` FOREIGN KEY (`template_b_id`) REFERENCES `email_templates` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add click tracking to email_tracking table if not exists
ALTER TABLE `email_tracking` 
ADD COLUMN IF NOT EXISTS `clicked_at` timestamp NULL DEFAULT NULL AFTER `opened_count`,
ADD COLUMN IF NOT EXISTS `clicked_count` int(11) DEFAULT 0 AFTER `clicked_at`,
ADD INDEX IF NOT EXISTS `idx_clicked_at` (`clicked_at`);

-- Create email_clicks table for detailed click tracking
CREATE TABLE IF NOT EXISTS `email_clicks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tracking_id` varchar(50) NOT NULL,
  `member_id` int(11) NOT NULL,
  `url` text NOT NULL,
  `clicked_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `user_agent` varchar(255) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_tracking_id` (`tracking_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_clicked_at` (`clicked_at`),
  CONSTRAINT `fk_email_clicks_member` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create view for A/B test performance summary
CREATE OR REPLACE VIEW `ab_test_performance` AS
SELECT 
    at.id as test_id,
    at.test_name,
    at.status,
    at.created_at,
    at.ended_at,
    ta.template_name as template_a_name,
    tb.template_name as template_b_name,
    at.split_percentage,
    
    -- Template A metrics
    COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id THEN el.id END) as emails_sent_a,
    COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id AND el.status = 'success' THEN el.id END) as emails_delivered_a,
    COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id AND et.opened_at IS NOT NULL THEN et.id END) as emails_opened_a,
    COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id AND et.clicked_at IS NOT NULL THEN et.id END) as emails_clicked_a,
    
    -- Template B metrics
    COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id THEN el.id END) as emails_sent_b,
    COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id AND el.status = 'success' THEN el.id END) as emails_delivered_b,
    COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id AND et.opened_at IS NOT NULL THEN et.id END) as emails_opened_b,
    COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id AND et.clicked_at IS NOT NULL THEN et.id END) as emails_clicked_b,
    
    -- Calculated rates for Template A
    ROUND(
        COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id AND el.status = 'success' THEN el.id END) / 
        NULLIF(COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id THEN el.id END), 0) * 100, 
        2
    ) as delivery_rate_a,
    ROUND(
        COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id AND et.opened_at IS NOT NULL THEN et.id END) / 
        NULLIF(COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id AND el.status = 'success' THEN el.id END), 0) * 100, 
        2
    ) as open_rate_a,
    ROUND(
        COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id AND et.clicked_at IS NOT NULL THEN et.id END) / 
        NULLIF(COUNT(DISTINCT CASE WHEN el.template_id = at.template_a_id AND et.opened_at IS NOT NULL THEN et.id END), 0) * 100, 
        2
    ) as click_rate_a,
    
    -- Calculated rates for Template B
    ROUND(
        COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id AND el.status = 'success' THEN el.id END) / 
        NULLIF(COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id THEN el.id END), 0) * 100, 
        2
    ) as delivery_rate_b,
    ROUND(
        COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id AND et.opened_at IS NOT NULL THEN et.id END) / 
        NULLIF(COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id AND el.status = 'success' THEN el.id END), 0) * 100, 
        2
    ) as open_rate_b,
    ROUND(
        COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id AND et.clicked_at IS NOT NULL THEN et.id END) / 
        NULLIF(COUNT(DISTINCT CASE WHEN el.template_id = at.template_b_id AND et.opened_at IS NOT NULL THEN et.id END), 0) * 100, 
        2
    ) as click_rate_b

FROM 
    ab_tests at
LEFT JOIN 
    email_templates ta ON at.template_a_id = ta.id
LEFT JOIN 
    email_templates tb ON at.template_b_id = tb.id
LEFT JOIN 
    email_logs el ON (el.template_id = at.template_a_id OR el.template_id = at.template_b_id) 
                    AND el.sent_at >= at.created_at 
                    AND (at.ended_at IS NULL OR el.sent_at <= at.ended_at)
LEFT JOIN 
    email_tracking et ON el.member_id = et.member_id AND DATE(el.sent_at) = DATE(et.sent_at)
GROUP BY
    at.id, at.test_name, at.status, at.created_at, at.ended_at,
    ta.template_name, tb.template_name, at.split_percentage;

-- SMS Campaign Management Tables

-- SMS Templates table
CREATE TABLE IF NOT EXISTS `sms_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) NOT NULL,
  `message_content` text NOT NULL,
  `category` varchar(100) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- SMS Campaigns table
CREATE TABLE IF NOT EXISTS `sms_campaigns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `campaign_name` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `total_recipients` int(11) NOT NULL DEFAULT 0,
  `sent_count` int(11) NOT NULL DEFAULT 0,
  `delivered_count` int(11) NOT NULL DEFAULT 0,
  `failed_count` int(11) NOT NULL DEFAULT 0,
  `status` enum('draft','scheduled','sending','completed','failed','cancelled') NOT NULL DEFAULT 'draft',
  `scheduled_at` timestamp NULL DEFAULT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_scheduled_at` (`scheduled_at`),
  KEY `idx_created_by` (`created_by`),
  KEY `fk_sms_template` (`template_id`),
  CONSTRAINT `fk_sms_campaigns_template` FOREIGN KEY (`template_id`) REFERENCES `sms_templates` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- SMS Campaign Recipients table
CREATE TABLE IF NOT EXISTS `sms_campaign_recipients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `campaign_id` int(11) NOT NULL,
  `member_id` int(11) DEFAULT NULL,
  `phone_number` varchar(20) NOT NULL,
  `status` enum('pending','sent','delivered','failed','cancelled') NOT NULL DEFAULT 'pending',
  `sent_at` timestamp NULL DEFAULT NULL,
  `delivered_at` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `provider_message_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_status` (`status`),
  KEY `idx_phone_number` (`phone_number`),
  CONSTRAINT `fk_sms_campaign_recipients_campaign` FOREIGN KEY (`campaign_id`) REFERENCES `sms_campaigns` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sms_campaign_recipients_member` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- SMS Logs table for detailed tracking
CREATE TABLE IF NOT EXISTS `sms_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `campaign_id` int(11) DEFAULT NULL,
  `member_id` int(11) DEFAULT NULL,
  `phone_number` varchar(20) NOT NULL,
  `message` text NOT NULL,
  `provider` varchar(50) NOT NULL,
  `provider_message_id` varchar(255) DEFAULT NULL,
  `status` enum('sent','delivered','failed','pending') NOT NULL DEFAULT 'pending',
  `cost` decimal(10,4) DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `sent_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `delivered_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_status` (`status`),
  KEY `idx_sent_at` (`sent_at`),
  CONSTRAINT `fk_sms_logs_campaign` FOREIGN KEY (`campaign_id`) REFERENCES `sms_campaigns` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_sms_logs_member` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
