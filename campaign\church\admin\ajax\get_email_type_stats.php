<?php
session_start();
require_once '../../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

try {
    // Get email type statistics
    $query = "SELECT 
                el.email_type,
                COUNT(*) as total_sent,
                SUM(CASE WHEN el.status = 'success' THEN 1 ELSE 0 END) as successful_sent,
                SUM(CASE WHEN tr.opened_at IS NOT NULL THEN 1 ELSE 0 END) as total_opened,
                ROUND(
                    SUM(CASE WHEN tr.opened_at IS NOT NULL THEN 1 ELSE 0 END) / 
                    NULLIF(COUNT(*), 0) * 100, 
                    2
                ) as open_rate
              FROM 
                email_logs el
              LEFT JOIN 
                email_tracking tr ON el.member_id = tr.member_id AND DATE(el.sent_at) = DATE(tr.sent_at)
              WHERE 
                el.sent_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
              GROUP BY 
                el.email_type
              ORDER BY 
                total_sent DESC";
              
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format the results
    $formattedResults = [];
    foreach ($results as $row) {
        $formattedResults[] = [
            'email_type' => ucfirst($row['email_type']),
            'total_sent' => (int)$row['total_sent'],
            'successful_sent' => (int)$row['successful_sent'],
            'total_opened' => (int)$row['total_opened'],
            'open_rate' => (float)$row['open_rate']
        ];
    }
    
    header('Content-Type: application/json');
    echo json_encode($formattedResults);
    
} catch (PDOException $e) {
    error_log("Error fetching email type stats: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Database error']);
}
?>
