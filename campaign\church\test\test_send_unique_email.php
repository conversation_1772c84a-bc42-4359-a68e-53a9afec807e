<?php
// <PERSON><PERSON><PERSON> to test sending an email with unique lottery numbers
require_once __DIR__ . '/../config.php';

// Check if the form was submitted
if (!isset($_POST['test_email']) || empty($_POST['test_email'])) {
    echo "<h2>Error: No email address provided</h2>";
    echo "<p><a href='unique_numbers_template.php'>Go back and try again</a></p>";
    exit;
}

$test_email = filter_var($_POST['test_email'], FILTER_VALIDATE_EMAIL);
if (!$test_email) {
    echo "<h2>Error: Invalid email address</h2>";
    echo "<p><a href='unique_numbers_template.php'>Go back and try again</a></p>";
    exit;
}

$template_id = isset($_POST['template_id']) ? intval($_POST['template_id']) : null;
if (!$template_id) {
    echo "<h2>Error: No template ID provided</h2>";
    echo "<p><a href='unique_numbers_template.php'>Go back and try again</a></p>";
    exit;
}

try {
    // Get the template
    $stmt = $pdo->prepare("SELECT id, template_name, subject, content FROM email_templates WHERE id = ?");
    $stmt->execute([$template_id]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "<h2>Error: Template not found</h2>";
        echo "<p><a href='unique_numbers_template.php'>Go back and try again</a></p>";
        exit;
    }
    
    // Generate unique lottery numbers for this email
    $ball1 = rand(1, 20);
    $ball2 = rand(21, 40);
    $ball3 = rand(41, 60);
    $ball4 = rand(61, 80);
    $ball5 = rand(81, 99);
    $powerball = rand(1, 15);
    
    // Replace placeholders with actual numbers
    $content = str_replace(
        ['{BALL1}', '{BALL2}', '{BALL3}', '{BALL4}', '{BALL5}', '{POWERBALL}'],
        [$ball1, $ball2, $ball3, $ball4, $ball5, $powerball],
        $template['content']
    );
    
    // Get email settings
    $email_settings = [];
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'email_%'");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $email_settings[$row['setting_key']] = $row['setting_value'];
    }
    
    $sender_email = $email_settings['email_sender_email'] ?? '<EMAIL>';
    $sender_name = $email_settings['email_sender_name'] ?? 'Powerball Lotto';
    $reply_to = '<EMAIL>';
    
    // Send the email
    require_once __DIR__ . '/../lib/PHPMailer/PHPMailerAutoload.php';
    $mail = new PHPMailer;
    
    $mail->isSMTP();
    $mail->Host = $email_settings['email_smtp_host'] ?? 'smtp.hostinger.com';
    $mail->SMTPAuth = true;
    $mail->Username = $email_settings['email_smtp_username'] ?? '<EMAIL>';
    $mail->Password = $email_settings['email_smtp_password'] ?? '';
    $mail->SMTPSecure = $email_settings['email_smtp_secure'] ?? 'ssl';
    $mail->Port = $email_settings['email_smtp_port'] ?? 465;
    
    $mail->setFrom($sender_email, $sender_name);
    $mail->addReplyTo($reply_to, $sender_name);
    $mail->addAddress($test_email);
    
    $mail->isHTML(true);
    $mail->Subject = $template['subject'];
    $mail->Body = $content;
    $mail->AltBody = strip_tags(str_replace(['<br>', '</p>'], ["\r\n", "\r\n\r\n"], $content));
    
    $success = false;
    $error_message = '';
    
    try {
        $success = $mail->send();
        if (!$success) {
            $error_message = $mail->ErrorInfo;
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
    
    echo "<h2>Test Email Result</h2>";
    
    if ($success) {
        echo "<div style='padding: 20px; background-color: #d4edda; color: #155724; border-radius: 5px; margin-bottom: 20px;'>";
        echo "<h3>Email Sent Successfully!</h3>";
        echo "<p>A unique lottery numbers email has been sent to: " . htmlspecialchars($test_email) . "</p>";
        echo "</div>";
        
        echo "<h3>Email Details:</h3>";
        echo "<ul>";
        echo "<li><strong>Template:</strong> " . htmlspecialchars($template['template_name']) . "</li>";
        echo "<li><strong>Subject:</strong> " . htmlspecialchars($template['subject']) . "</li>";
        echo "<li><strong>From:</strong> " . htmlspecialchars($sender_name) . " &lt;" . htmlspecialchars($sender_email) . "&gt;</li>";
        echo "<li><strong>Reply-To:</strong> " . htmlspecialchars($reply_to) . "</li>";
        echo "</ul>";
        
        echo "<h3>Unique Lottery Numbers Generated:</h3>";
        echo "<div style='display: flex; justify-content: center; margin: 20px 0;'>";
        
        $balls = [
            ['number' => $ball1, 'color' => '#FF69B4'],
            ['number' => $ball2, 'color' => '#FFD700'],
            ['number' => $ball3, 'color' => '#00CED1'],
            ['number' => $ball4, 'color' => '#32CD32'],
            ['number' => $ball5, 'color' => '#FF6347'],
            ['number' => $powerball, 'color' => '#FF0000']
        ];
        
        foreach ($balls as $ball) {
            echo "<div style='display: flex; justify-content: center; align-items: center; width: 60px; height: 60px; border-radius: 50%; background-color: " . $ball['color'] . "; color: white; font-size: 22px; font-weight: bold; margin: 0 5px;'>";
            echo $ball['number'];
            echo "</div>";
        }
        
        echo "</div>";
        
    } else {
        echo "<div style='padding: 20px; background-color: #f8d7da; color: #721c24; border-radius: 5px; margin-bottom: 20px;'>";
        echo "<h3>Email Sending Failed</h3>";
        echo "<p>Error: " . htmlspecialchars($error_message) . "</p>";
        echo "</div>";
        
        echo "<p>Please check your SMTP settings and try again.</p>";
    }
    
    echo "<p><a href='unique_numbers_template.php' style='display: inline-block; padding: 10px 20px; background-color: #8A2BE2; color: white; text-decoration: none; border-radius: 4px;'>Go Back</a></p>";
    
} catch (Exception $e) {
    echo "<h2>Error:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p><a href='unique_numbers_template.php'>Go back and try again</a></p>";
} 