<?php

class AssetOptimizer {
    private $cacheManager;
    private $assetsDir;
    private $cacheDir;
    
    public function __construct($assetsDir = null, CacheManager $cacheManager = null) {
        $this->assetsDir = $assetsDir ?? dirname(__DIR__) . '/assets';
        $this->cacheDir = dirname(__DIR__) . '/cache/assets';
        $this->cacheManager = $cacheManager ?? new CacheManager();
        $this->ensureDirectories();
    }
    
    private function ensureDirectories() {
        if (!file_exists($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    public function optimizeCSS($files, $outputFile = null) {
        $outputFile = $outputFile ?? 'combined.min.css';
        $cacheKey = 'css_' . md5(implode('', $files));
        
        // Check if cached version exists
        if ($cached = $this->cacheManager->getCachedAsset($cacheKey)) {
            return $cached;
        }
        
        $combined = '';
        foreach ($files as $file) {
            $content = file_get_contents($this->assetsDir . '/css/' . $file);
            // Remove comments
            $content = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $content);
            // Remove whitespace
            $content = preg_replace('/\s+/', ' ', $content);
            $combined .= $content;
        }
        
        // Cache the result
        $this->cacheManager->cacheAsset($cacheKey, $combined);
        
        // Save to file if requested
        if ($outputFile) {
            file_put_contents($this->cacheDir . '/' . $outputFile, $combined);
        }
        
        return $combined;
    }
    
    public function optimizeJS($files, $outputFile = null) {
        $outputFile = $outputFile ?? 'combined.min.js';
        $cacheKey = 'js_' . md5(implode('', $files));
        
        // Check if cached version exists
        if ($cached = $this->cacheManager->getCachedAsset($cacheKey)) {
            return $cached;
        }
        
        $combined = '';
        foreach ($files as $file) {
            $content = file_get_contents($this->assetsDir . '/js/' . $file);
            // Remove comments
            $content = preg_replace('/\/\*.*?\*\//s', '', $content);
            $content = preg_replace('/\/\/.*$/m', '', $content);
            // Remove whitespace
            $content = preg_replace('/\s+/', ' ', $content);
            $combined .= $content . ";\n";
        }
        
        // Cache the result
        $this->cacheManager->cacheAsset($cacheKey, $combined);
        
        // Save to file if requested
        if ($outputFile) {
            file_put_contents($this->cacheDir . '/' . $outputFile, $combined);
        }
        
        return $combined;
    }
    
    public function optimizeImage($imagePath, $quality = 85) {
        $cacheKey = 'img_' . md5($imagePath . $quality);
        
        // Check if cached version exists
        if ($cached = $this->cacheManager->getCachedAsset($cacheKey)) {
            return $cached;
        }
        
        $info = getimagesize($imagePath);
        if (!$info) {
            return false;
        }
        
        switch ($info[2]) {
            case IMAGETYPE_JPEG:
                $image = imagecreatefromjpeg($imagePath);
                break;
            case IMAGETYPE_PNG:
                $image = imagecreatefrompng($imagePath);
                break;
            default:
                return false;
        }
        
        // Start output buffering
        ob_start();
        
        // Output the optimized image
        if ($info[2] == IMAGETYPE_JPEG) {
            imagejpeg($image, null, $quality);
        } else {
            imagepng($image, null, round(9 - (($quality / 100) * 9)));
        }
        
        $optimized = ob_get_clean();
        imagedestroy($image);
        
        // Cache the optimized image
        $this->cacheManager->cacheAsset($cacheKey, $optimized);
        
        return $optimized;
    }
    
    public function setCacheHeaders($type = 'text/css', $maxAge = 604800) { // 1 week default
        header('Cache-Control: public, max-age=' . $maxAge);
        header('Pragma: cache');
        header('Content-Type: ' . $type);
        header('Expires: ' . gmdate('D, d M Y H:i:s \G\M\T', time() + $maxAge));
    }
    
    public function getAssetUrl($file, $type = 'css') {
        $hash = substr(md5_file($this->assetsDir . '/' . $type . '/' . $file), 0, 8);
        return "/assets/$type/$file?v=$hash";
    }
} 