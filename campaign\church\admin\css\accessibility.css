/* WCAG 2.1 Accessibility Compliance Styles */

/* Screen Reader Only Content */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.sr-only-focusable:focus,
.sr-only-focusable:active {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: inherit !important;
    margin: inherit !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: inherit !important;
}

/* Skip Links */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    z-index: 9999;
    border-radius: 0 0 4px 4px;
    font-weight: bold;
}

.skip-link:focus {
    top: 0;
}

/* Focus Management */
*:focus {
    outline: 2px solid #0066cc;
    outline-offset: 2px;
}

/* High contrast focus for dark mode */
[data-theme="dark"] *:focus {
    outline-color: #66b3ff;
}

/* Button Focus States */
.btn:focus,
button:focus {
    outline: 2px solid #0066cc;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.3);
}

[data-theme="dark"] .btn:focus,
[data-theme="dark"] button:focus {
    outline-color: #66b3ff;
    box-shadow: 0 0 0 3px rgba(102, 179, 255, 0.3);
}

/* Form Control Focus */
.form-control:focus,
.form-select:focus,
textarea:focus,
input:focus {
    outline: 2px solid #0066cc;
    outline-offset: 1px;
    border-color: #0066cc;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.2);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] input:focus {
    outline-color: #66b3ff;
    border-color: #66b3ff;
    box-shadow: 0 0 0 3px rgba(102, 179, 255, 0.2);
}

/* Link Focus */
a:focus {
    outline: 2px solid #0066cc;
    outline-offset: 2px;
    text-decoration: underline;
}

[data-theme="dark"] a:focus {
    outline-color: #66b3ff;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --bs-primary: #0000ff;
        --bs-secondary: #000000;
        --bs-success: #008000;
        --bs-danger: #ff0000;
        --bs-warning: #ffff00;
        --bs-info: #00ffff;
    }
    
    .btn {
        border-width: 2px;
        font-weight: bold;
    }
    
    .form-control,
    .form-select {
        border-width: 2px;
    }
    
    .card {
        border-width: 2px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .collapse {
        transition: none !important;
    }
    
    .fade {
        transition: none !important;
    }
}

/* Font Size Scaling */
.font-size-small {
    font-size: 0.875rem;
}

.font-size-normal {
    font-size: 1rem;
}

.font-size-large {
    font-size: 1.125rem;
}

.font-size-extra-large {
    font-size: 1.25rem;
}

/* Color Contrast Helpers */
.high-contrast {
    filter: contrast(150%);
}

.high-contrast .text-muted {
    color: #000 !important;
}

[data-theme="dark"] .high-contrast .text-muted {
    color: #fff !important;
}

/* Keyboard Navigation Indicators */
.keyboard-navigation .nav-link:focus,
.keyboard-navigation .dropdown-item:focus,
.keyboard-navigation .btn:focus {
    background-color: rgba(0, 102, 204, 0.1);
    border-radius: 4px;
}

[data-theme="dark"] .keyboard-navigation .nav-link:focus,
[data-theme="dark"] .keyboard-navigation .dropdown-item:focus,
[data-theme="dark"] .keyboard-navigation .btn:focus {
    background-color: rgba(102, 179, 255, 0.1);
}

/* Table Accessibility */
.table th {
    position: relative;
}

.table th[aria-sort]::after {
    content: '';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border: 4px solid transparent;
}

.table th[aria-sort="ascending"]::after {
    border-bottom-color: currentColor;
    border-top: 0;
}

.table th[aria-sort="descending"]::after {
    border-top-color: currentColor;
    border-bottom: 0;
}

.table th[aria-sort="none"]::after {
    border-top-color: rgba(0, 0, 0, 0.3);
    border-bottom-color: rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .table th[aria-sort="none"]::after {
    border-top-color: rgba(255, 255, 255, 0.3);
    border-bottom-color: rgba(255, 255, 255, 0.3);
}

/* Form Validation Accessibility */
.form-control.is-invalid {
    border-color: #dc3545;
    background-image: none;
}

.form-control.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.valid-feedback {
    display: block;
    color: #198754;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Modal Accessibility */
.modal {
    --bs-modal-zindex: 1055;
}

.modal-backdrop {
    --bs-backdrop-zindex: 1050;
}

/* Ensure modals are properly announced */
.modal[role="dialog"] {
    outline: none;
}

.modal-header .btn-close {
    margin: -0.5rem -0.5rem -0.5rem auto;
    padding: 0.5rem;
}

.modal-header .btn-close:focus {
    outline: 2px solid #0066cc;
    outline-offset: 2px;
}

/* Dropdown Accessibility */
.dropdown-menu {
    border: 1px solid rgba(0, 0, 0, 0.15);
}

.dropdown-item:focus {
    background-color: rgba(0, 102, 204, 0.1);
    outline: 2px solid #0066cc;
    outline-offset: -2px;
}

[data-theme="dark"] .dropdown-item:focus {
    background-color: rgba(102, 179, 255, 0.1);
    outline-color: #66b3ff;
}

/* Pagination Accessibility */
.page-link:focus {
    z-index: 3;
    outline: 2px solid #0066cc;
    outline-offset: 2px;
}

[data-theme="dark"] .page-link:focus {
    outline-color: #66b3ff;
}

/* Alert Accessibility */
.alert {
    border-left: 4px solid;
}

.alert-primary {
    border-left-color: var(--bs-primary);
}

.alert-success {
    border-left-color: var(--bs-success);
}

.alert-warning {
    border-left-color: var(--bs-warning);
}

.alert-danger {
    border-left-color: var(--bs-danger);
}

.alert-info {
    border-left-color: var(--bs-info);
}

/* Progress Bar Accessibility */
.progress {
    overflow: visible;
}

.progress-bar {
    position: relative;
}

.progress-bar::after {
    content: attr(aria-valuenow) '%';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: bold;
    color: #000;
    text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.8);
}

/* Tooltip Accessibility */
.tooltip {
    font-size: 0.875rem;
}

/* Card Accessibility */
.card {
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card:focus-within {
    outline: 2px solid #0066cc;
    outline-offset: 2px;
}

[data-theme="dark"] .card:focus-within {
    outline-color: #66b3ff;
}

/* Breadcrumb Accessibility */
.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
    font-weight: bold;
}

/* List Group Accessibility */
.list-group-item:focus {
    outline: 2px solid #0066cc;
    outline-offset: -2px;
    z-index: 1;
}

[data-theme="dark"] .list-group-item:focus {
    outline-color: #66b3ff;
}

/* Badge Accessibility */
.badge {
    font-weight: 600;
    letter-spacing: 0.025em;
}

/* Spinner Accessibility */
.spinner-border,
.spinner-grow {
    vertical-align: -0.125em;
}

/* Language Switcher */
.language-switcher {
    position: relative;
}

.language-toggle {
    background: transparent;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 6px 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.language-toggle:hover {
    background: rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .language-toggle {
    border-color: rgba(255, 255, 255, 0.2);
    color: #e9ecef;
}

[data-theme="dark"] .language-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

.language-toggle .flag {
    font-size: 1rem;
    line-height: 1;
}

.language-toggle .lang-code {
    font-weight: 500;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.language-toggle.loading {
    opacity: 0.6;
    cursor: not-allowed;
}

.language-menu {
    min-width: 200px;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .language-menu {
    background: var(--bs-dark);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.language-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    text-decoration: none;
    color: inherit;
    transition: background-color 0.2s ease;
}

.language-option:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: inherit;
}

[data-theme="dark"] .language-option:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.language-option.active {
    background-color: var(--bs-primary);
    color: white;
}

.language-option .flag {
    font-size: 1.1rem;
    line-height: 1;
}

.language-option .name {
    flex: 1;
    font-weight: 500;
}

.language-option .code {
    font-size: 0.75rem;
    opacity: 0.7;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.language-option.active .code {
    opacity: 0.9;
}

/* Floating language switcher */
.floating-language-switcher {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1040;
}

/* Sidebar language switcher */
.sidebar .language-switcher-item {
    margin-top: auto;
    padding: 0.5rem 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar .language-toggle {
    width: 100%;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
}

.sidebar .language-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Language error message */
.language-error {
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* RTL Support */
[dir="rtl"] .language-toggle {
    flex-direction: row-reverse;
}

[dir="rtl"] .language-option {
    flex-direction: row-reverse;
    text-align: right;
}

[dir="rtl"] .floating-language-switcher {
    right: auto;
    left: 20px;
}

/* Print Styles */
@media print {
    .no-print,
    .language-switcher {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }

    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #666;
    }

    .btn {
        border: 1px solid #000;
        background: transparent !important;
        color: #000 !important;
    }
}
