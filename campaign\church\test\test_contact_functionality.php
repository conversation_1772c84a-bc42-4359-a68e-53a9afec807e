<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Contact Functionality</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Contact Functionality Test</h1>
        
        <?php
        session_start();
        require_once '../config.php';
        $conn = $pdo;
        $_SESSION['admin_id'] = 1;
        
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        $action = $_GET['action'] ?? 'menu';
        
        switch ($action) {
            case 'create_test_data':
                echo "<h2>Creating Test Data</h2>";
                
                // Create test group
                try {
                    $stmt = $conn->prepare("INSERT INTO contact_groups (name, description) VALUES (?, ?)");
                    $stmt->execute(['Test Group', 'Group for testing functionality']);
                    $group_id = $conn->lastInsertId();
                    echo "<div class='alert alert-success'>✅ Created test group (ID: $group_id)</div>";
                } catch (PDOException $e) {
                    echo "<div class='alert alert-warning'>⚠️ Test group might already exist</div>";
                    // Get existing group
                    $stmt = $conn->prepare("SELECT id FROM contact_groups WHERE name = 'Test Group' LIMIT 1");
                    $stmt->execute();
                    $group_id = $stmt->fetchColumn();
                }
                
                // Create test contacts
                $test_contacts = [];
                for ($i = 1; $i <= 3; $i++) {
                    try {
                        $email = "test_contact_{$i}_" . time() . "@example.com";
                        $name = "Test Contact $i";
                        
                        $stmt = $conn->prepare("INSERT INTO contacts (email, name, source) VALUES (?, ?, 'test')");
                        $stmt->execute([$email, $name]);
                        $contact_id = $conn->lastInsertId();
                        
                        // Add to group (for testing edit functionality)
                        if ($i <= 2) {
                            $stmt = $conn->prepare("INSERT INTO contact_group_members (contact_id, group_id) VALUES (?, ?)");
                            $stmt->execute([$contact_id, $group_id]);
                        }
                        
                        $test_contacts[] = [
                            'id' => $contact_id,
                            'name' => $name,
                            'email' => $email,
                            'in_group' => $i <= 2
                        ];
                        
                        echo "<div class='alert alert-success'>✅ Created: $name (ID: $contact_id)" . ($i <= 2 ? " - Added to group" : "") . "</div>";
                    } catch (PDOException $e) {
                        echo "<div class='alert alert-danger'>❌ Error creating contact $i: " . $e->getMessage() . "</div>";
                    }
                }
                
                if (!empty($test_contacts)) {
                    echo "<h3>Test Individual Delete</h3>";
                    $contact = $test_contacts[0];
                    $delete_url = "../admin/contacts.php?delete_id={$contact['id']}&csrf_token=" . $_SESSION['csrf_token'];
                    echo "<p><a href='$delete_url' class='btn btn-danger' onclick='return confirm(\"Delete {$contact['name']}?\")'>Delete {$contact['name']}</a></p>";
                    
                    echo "<h3>Test Edit & Bulk Delete</h3>";
                    echo "<p><a href='../admin/contacts.php' class='btn btn-primary' target='_blank'>Go to Contacts Page</a></p>";
                    echo "<p>On the contacts page, you can:</p>";
                    echo "<ul>";
                    echo "<li>Click the edit button (pencil icon) to test edit functionality</li>";
                    echo "<li>Select multiple contacts and use bulk delete</li>";
                    echo "<li>Individual delete should work with the trash icon</li>";
                    echo "</ul>";
                }
                break;
                
            case 'test_ajax_handlers':
                echo "<h2>Testing AJAX Handlers</h2>";
                
                // Test update_contact.php
                echo "<h3>Testing update_contact.php</h3>";
                
                // Create a test contact first
                try {
                    $test_email = 'ajax_test_' . time() . '@example.com';
                    $test_name = 'AJAX Test Contact';
                    
                    $stmt = $conn->prepare("INSERT INTO contacts (email, name, source) VALUES (?, ?, 'test')");
                    $stmt->execute([$test_email, $test_name]);
                    $contact_id = $conn->lastInsertId();
                    
                    echo "<p>✅ Created test contact (ID: $contact_id)</p>";
                    
                    // Test the update functionality
                    $update_data = [
                        'id' => $contact_id,
                        'name' => 'Updated AJAX Test Contact',
                        'email' => 'updated_ajax_test_' . time() . '@example.com',
                        'groups' => []
                    ];
                    
                    // Simulate AJAX call
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, 'http://localhost/campaign/church/admin/ajax/update_contact.php');
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($update_data));
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Content-Type: application/json',
                        'Content-Length: ' . strlen(json_encode($update_data))
                    ]);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
                    
                    $response = curl_exec($ch);
                    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    echo "<p><strong>HTTP Code:</strong> $http_code</p>";
                    echo "<p><strong>Response:</strong> " . htmlspecialchars($response) . "</p>";
                    
                    $result = json_decode($response, true);
                    if ($result && $result['success']) {
                        echo "<div class='alert alert-success'>✅ Update AJAX handler works!</div>";
                        
                        // Verify the update
                        $stmt = $conn->prepare("SELECT * FROM contacts WHERE id = ?");
                        $stmt->execute([$contact_id]);
                        $updated_contact = $stmt->fetch();
                        
                        if ($updated_contact && $updated_contact['name'] === 'Updated AJAX Test Contact') {
                            echo "<div class='alert alert-success'>✅ Contact was actually updated in database</div>";
                        } else {
                            echo "<div class='alert alert-warning'>⚠️ Contact update not reflected in database</div>";
                        }
                    } else {
                        echo "<div class='alert alert-danger'>❌ Update AJAX handler failed</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>❌ Error testing AJAX: " . $e->getMessage() . "</div>";
                }
                
                // Test get_contact_groups.php
                echo "<h3>Testing get_contact_groups.php</h3>";
                
                if (isset($contact_id)) {
                    $url = "http://localhost/campaign/church/admin/ajax/get_contact_groups.php?id=$contact_id";
                    
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
                    
                    $response = curl_exec($ch);
                    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    echo "<p><strong>HTTP Code:</strong> $http_code</p>";
                    echo "<p><strong>Response:</strong> " . htmlspecialchars($response) . "</p>";
                    
                    if ($http_code === 200) {
                        echo "<div class='alert alert-success'>✅ Get contact groups AJAX handler works!</div>";
                    } else {
                        echo "<div class='alert alert-danger'>❌ Get contact groups AJAX handler failed</div>";
                    }
                }
                break;
                
            default:
                echo "<h2>Test Menu</h2>";
                echo "<div class='list-group'>";
                echo "<a href='?action=create_test_data' class='list-group-item list-group-item-action'>";
                echo "<h5>Create Test Data</h5>";
                echo "<p>Creates test contacts and groups for testing functionality</p>";
                echo "</a>";
                echo "<a href='?action=test_ajax_handlers' class='list-group-item list-group-item-action'>";
                echo "<h5>Test AJAX Handlers</h5>";
                echo "<p>Tests the update_contact.php and get_contact_groups.php handlers</p>";
                echo "</a>";
                echo "<a href='../admin/contacts.php' class='list-group-item list-group-item-action' target='_blank'>";
                echo "<h5>Go to Contacts Page</h5>";
                echo "<p>Test the actual functionality on the contacts page</p>";
                echo "</a>";
                echo "</div>";
                break;
        }
        ?>
        
        <div class="mt-4">
            <a href="?" class="btn btn-secondary">Back to Menu</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
