<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

echo "<h2>Database Structure Check</h2>";

// Get all tables
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>Existing Tables:</h3>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    // Check SMS-related tables specifically
    $sms_tables = ['sms_templates', 'sms_campaigns', 'sms_campaign_recipients', 'sms_logs'];
    
    echo "<h3>SMS Tables Status:</h3>";
    foreach ($sms_tables as $table) {
        if (in_array($table, $tables)) {
            echo "<span style='color: green;'>✓</span> $table exists<br>";
            
            // Show table structure
            try {
                $stmt = $pdo->query("DESCRIBE $table");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo "<details style='margin-left: 20px;'>";
                echo "<summary>Show columns</summary>";
                echo "<table border='1' style='margin: 10px; border-collapse: collapse;'>";
                echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
                foreach ($columns as $column) {
                    echo "<tr>";
                    echo "<td>" . $column['Field'] . "</td>";
                    echo "<td>" . $column['Type'] . "</td>";
                    echo "<td>" . $column['Null'] . "</td>";
                    echo "<td>" . $column['Key'] . "</td>";
                    echo "<td>" . $column['Default'] . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                echo "</details>";
            } catch (PDOException $e) {
                echo "<span style='color: red;'>Error describing table: " . $e->getMessage() . "</span><br>";
            }
        } else {
            echo "<span style='color: red;'>✗</span> $table missing<br>";
        }
    }
    
} catch (PDOException $e) {
    echo "<span style='color: red;'>Error: " . $e->getMessage() . "</span>";
}

echo "<br><br><a href='run_sms_migration.php'>Run SMS Migration</a> | <a href='bulk_sms.php'>Go to Bulk SMS</a>";
?>
