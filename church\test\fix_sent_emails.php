<?php
// <PERSON><PERSON><PERSON> to fix the lottery numbers placeholder replacement in sent emails
require_once __DIR__ . '/../config.php';

echo "<h2>Fixing Lottery Numbers in Sent Emails</h2>";

try {
    // First check if our integration code is working correctly in the bulk email system
    $bulk_email_file = __DIR__ . '/../admin/bulk_email.php';
    
    if (!file_exists($bulk_email_file)) {
        echo "<p>❌ Error: Could not find bulk_email.php at: " . htmlspecialchars($bulk_email_file) . "</p>";
        exit;
    }
    
    $content = file_get_contents($bulk_email_file);
    
    // Check if the lottery number replacement function is being called correctly
    if (strpos($content, 'process_unique_lottery_numbers') === false) {
        echo "<p>❌ The lottery number replacement function is not integrated in the bulk email system.</p>";
        
        // Create a direct fix - modify the template to use standard placeholders
        echo "<h3>Creating immediate fix...</h3>";
        
        // Get the template
        $stmt = $pdo->prepare("SELECT id, template_name, content FROM email_templates WHERE id = 53");
        $stmt->execute();
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            echo "<p>❌ Could not find the unique numbers template (ID: 53)</p>";
            exit;
        }
        
        // Generate sample numbers for the email client view
        $ball1 = 14;
        $ball2 = 28;
        $ball3 = 53;
        $ball4 = 67;
        $ball5 = 92;
        $powerball = 9;
        
        // Replace the placeholder syntax with the actual numbers
        $updated_content = str_replace(
            ['{BALL1}', '{BALL2}', '{BALL3}', '{BALL4}', '{BALL5}', '{POWERBALL}'],
            [$ball1, $ball2, $ball3, $ball4, $ball5, $powerball],
            $template['content']
        );
        
        // Create a new template with hard-coded numbers
        $stmt = $pdo->prepare("INSERT INTO email_templates (template_name, subject, content, created_at) VALUES (?, ?, ?, NOW())");
        $new_template_name = "The Big Raffle Winner (Fixed Numbers)";
        $subject = "The Big Raffle - You're a Winner!";
        
        $stmt->execute([$new_template_name, $subject, $updated_content]);
        $new_template_id = $pdo->lastInsertId();
        
        echo "<p>✅ Created a new template (ID: " . $new_template_id . ") with fixed lottery numbers that will display correctly in emails.</p>";
        
        // Create a test email function that uses PHPMailer directly
        echo "<h3>Creating a direct email sending script...</h3>";
        
        $direct_send_file = __DIR__ . '/direct_send_email.php';
        $direct_send_code = <<<'PHP'
<?php
// Direct email sending script that bypasses the bulk email system
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../classes/PHPMailer/PHPMailer.php';
require_once __DIR__ . '/../classes/PHPMailer/SMTP.php';
require_once __DIR__ . '/../classes/PHPMailer/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Get email settings from database
$stmt = $pdo->query("SELECT setting_name, setting_value FROM email_settings");
$email_settings = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $email_settings[$row['setting_name']] = $row['setting_value'];
}

if (isset($_POST['send_email'])) {
    $recipient_email = filter_input(INPUT_POST, 'recipient_email', FILTER_VALIDATE_EMAIL);
    $template_id = filter_input(INPUT_POST, 'template_id', FILTER_VALIDATE_INT);
    
    if (!$recipient_email) {
        echo "<p>❌ Please enter a valid email address.</p>";
    } elseif (!$template_id) {
        echo "<p>❌ Invalid template ID.</p>";
    } else {
        // Get the template
        $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$template_id]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            echo "<p>❌ Template not found.</p>";
        } else {
            // Setup email
            $mail = new PHPMailer(true);
            
            try {
                // Server settings
                $mail->isSMTP();
                $mail->Host       = $email_settings['smtp_host'] ?? 'localhost';
                $mail->SMTPAuth   = !empty($email_settings['smtp_auth']) && $email_settings['smtp_auth'] === 'yes';
                $mail->Username   = $email_settings['smtp_username'] ?? '';
                $mail->Password   = $email_settings['smtp_password'] ?? '';
                $mail->SMTPSecure = $email_settings['smtp_secure'] ?? '';
                $mail->Port       = $email_settings['smtp_port'] ?? 25;
                
                // Recipients
                $mail->setFrom($email_settings['from_email'] ?? '<EMAIL>', $email_settings['from_name'] ?? 'Church Admin');
                $mail->addAddress($recipient_email);
                $mail->addReplyTo($email_settings['raffle_reply_email'] ?? '<EMAIL>');
                
                // Content
                $mail->isHTML(true);
                $mail->Subject = $template['subject'];
                $mail->Body    = $template['content'];
                
                $mail->send();
                echo "<p>✅ Email has been sent successfully to " . htmlspecialchars($recipient_email) . "</p>";
                echo "<p>Please check your email to verify the lottery numbers are displayed correctly.</p>";
            } catch (Exception $e) {
                echo "<p>❌ Message could not be sent. Mailer Error: {$mail->ErrorInfo}</p>";
            }
        }
    }
}

// Template dropdown
$stmt = $pdo->query("SELECT id, template_name FROM email_templates ORDER BY template_name");
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<h2>Send Direct Test Email</h2>
<p>This form lets you send an email directly using PHPMailer without going through the bulk email system.</p>

<form method="post" style="margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px;">
    <div style="margin-bottom: 15px;">
        <label for="recipient_email" style="display: block; margin-bottom: 5px; font-weight: bold;">Recipient Email:</label>
        <input type="email" name="recipient_email" id="recipient_email" required 
               style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="template_id" style="display: block; margin-bottom: 5px; font-weight: bold;">Email Template:</label>
        <select name="template_id" id="template_id" required
                style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
            <?php foreach ($templates as $template): ?>
                <option value="<?= $template['id'] ?>">
                    <?= htmlspecialchars($template['template_name']) ?> (ID: <?= $template['id'] ?>)
                </option>
            <?php endforeach; ?>
        </select>
    </div>
    
    <button type="submit" name="send_email" 
            style="background-color: #8A2BE2; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer;">
        Send Test Email
    </button>
</form>

<h3>Recommended Template</h3>
<p>For best results with lottery numbers, use "The Big Raffle Winner (Fixed Numbers)" template.</p>

<p><a href="../admin/email_templates.php" style="color: #8A2BE2;">Go to Email Templates</a></p>
PHP;
        
        file_put_contents($direct_send_file, $direct_send_code);
        echo "<p>✅ Created direct email sending script at: <a href='direct_send_email.php'>direct_send_email.php</a></p>";
        
        echo "<h3>Recommended Solution</h3>";
        echo "<p>For immediate use, we recommend using the new template with ID: " . $new_template_id . " named 'The Big Raffle Winner (Fixed Numbers)'.</p>";
        echo "<p>This template has the lottery numbers hard-coded, so they will display correctly in all email clients.</p>";
        echo "<p>You can test sending an email with this template using our <a href='direct_send_email.php'>direct sending tool</a>.</p>";
        
    } else {
        echo "<p>✅ The lottery number replacement function is properly integrated in the bulk email system.</p>";
        
        // Check if the function implementation is correct
        if (preg_match('/function\s+process_unique_lottery_numbers.*?\{.*?\}/s', $content, $matches)) {
            $function_code = $matches[0];
            
            if (strpos($function_code, 'str_replace') !== false &&
                strpos($function_code, '{BALL1}') !== false &&
                strpos($function_code, 'rand') !== false) {
                echo "<p>✅ The implementation looks correct. The function contains placeholders and random number generation.</p>";
            } else {
                echo "<p>❌ The function implementation may be incorrect. It should use str_replace to replace {BALL1}, etc. with random numbers.</p>";
            }
        }
        
        // Check if the function is being called in the right place
        if (strpos($content, '$content = process_unique_lottery_numbers($content, $recipient)') !== false) {
            echo "<p>✅ The function is being called correctly in the email processing flow.</p>";
        } else {
            echo "<p>❌ The function call might be missing or incorrect in the email processing flow.</p>";
        }
        
        echo "<h3>Create test tools</h3>";
        
        // Create the direct send test tool anyway as a backup
        $direct_send_file = __DIR__ . '/direct_send_email.php';
        $direct_send_code = <<<'PHP'
<?php
// Direct email sending script that bypasses the bulk email system
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../classes/PHPMailer/PHPMailer.php';
require_once __DIR__ . '/../classes/PHPMailer/SMTP.php';
require_once __DIR__ . '/../classes/PHPMailer/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Get email settings from database
$stmt = $pdo->query("SELECT setting_name, setting_value FROM email_settings");
$email_settings = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $email_settings[$row['setting_name']] = $row['setting_value'];
}

// Function to generate unique lottery numbers
function generate_lottery_numbers() {
    return [
        'ball1' => rand(1, 20),
        'ball2' => rand(21, 40),
        'ball3' => rand(41, 60), 
        'ball4' => rand(61, 80),
        'ball5' => rand(81, 99),
        'powerball' => rand(1, 15)
    ];
}

// Function to replace placeholders
function replace_lottery_placeholders($content) {
    $numbers = generate_lottery_numbers();
    
    return str_replace(
        ['{BALL1}', '{BALL2}', '{BALL3}', '{BALL4}', '{BALL5}', '{POWERBALL}'],
        [$numbers['ball1'], $numbers['ball2'], $numbers['ball3'], $numbers['ball4'], $numbers['ball5'], $numbers['powerball']],
        $content
    );
}

if (isset($_POST['send_email'])) {
    $recipient_email = filter_input(INPUT_POST, 'recipient_email', FILTER_VALIDATE_EMAIL);
    $template_id = filter_input(INPUT_POST, 'template_id', FILTER_VALIDATE_INT);
    
    if (!$recipient_email) {
        echo "<p>❌ Please enter a valid email address.</p>";
    } elseif (!$template_id) {
        echo "<p>❌ Invalid template ID.</p>";
    } else {
        // Get the template
        $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$template_id]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            echo "<p>❌ Template not found.</p>";
        } else {
            // Process template content - replace lottery placeholders
            $content = $template['content'];
            $content = replace_lottery_placeholders($content);
            
            // Setup email
            $mail = new PHPMailer(true);
            
            try {
                // Server settings
                $mail->isSMTP();
                $mail->Host       = $email_settings['smtp_host'] ?? 'localhost';
                $mail->SMTPAuth   = !empty($email_settings['smtp_auth']) && $email_settings['smtp_auth'] === 'yes';
                $mail->Username   = $email_settings['smtp_username'] ?? '';
                $mail->Password   = $email_settings['smtp_password'] ?? '';
                $mail->SMTPSecure = $email_settings['smtp_secure'] ?? '';
                $mail->Port       = $email_settings['smtp_port'] ?? 25;
                
                // Recipients
                $mail->setFrom($email_settings['from_email'] ?? '<EMAIL>', $email_settings['from_name'] ?? 'Church Admin');
                $mail->addAddress($recipient_email);
                $mail->addReplyTo($email_settings['raffle_reply_email'] ?? '<EMAIL>');
                
                // Content
                $mail->isHTML(true);
                $mail->Subject = $template['subject'];
                $mail->Body    = $content;
                
                // Generate and display the numbers that were used
                $numbers = generate_lottery_numbers();
                
                $mail->send();
                echo "<div style='margin: 15px 0; padding: 15px; background-color: #e8f5e9; border-left: 5px solid #4caf50; border-radius: 4px;'>";
                echo "<h3>✅ Email sent successfully!</h3>";
                echo "<p>Recipient: " . htmlspecialchars($recipient_email) . "</p>";
                echo "<p>Template: " . htmlspecialchars($template['template_name']) . " (ID: " . $template['id'] . ")</p>";
                echo "<h4>Generated Lottery Numbers:</h4>";
                echo "<ul>";
                echo "<li>Ball 1: <span style='font-weight: bold;'>" . $numbers['ball1'] . "</span></li>";
                echo "<li>Ball 2: <span style='font-weight: bold;'>" . $numbers['ball2'] . "</span></li>";
                echo "<li>Ball 3: <span style='font-weight: bold;'>" . $numbers['ball3'] . "</span></li>";
                echo "<li>Ball 4: <span style='font-weight: bold;'>" . $numbers['ball4'] . "</span></li>";
                echo "<li>Ball 5: <span style='font-weight: bold;'>" . $numbers['ball5'] . "</span></li>";
                echo "<li>Powerball: <span style='font-weight: bold; color: red;'>" . $numbers['powerball'] . "</span></li>";
                echo "</ul>";
                echo "<p>Please check your email to verify the lottery numbers are displayed correctly.</p>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<p>❌ Message could not be sent. Mailer Error: {$mail->ErrorInfo}</p>";
            }
        }
    }
}

// Template dropdown
$stmt = $pdo->query("SELECT id, template_name FROM email_templates WHERE template_name LIKE '%Raffle%' ORDER BY template_name");
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<h2>Send Test Email with Unique Lottery Numbers</h2>
<p>This form lets you send a test email with unique lottery numbers directly, bypassing the bulk email system.</p>

<form method="post" style="margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px;">
    <div style="margin-bottom: 15px;">
        <label for="recipient_email" style="display: block; margin-bottom: 5px; font-weight: bold;">Recipient Email:</label>
        <input type="email" name="recipient_email" id="recipient_email" required 
               style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="template_id" style="display: block; margin-bottom: 5px; font-weight: bold;">Email Template:</label>
        <select name="template_id" id="template_id" required
                style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
            <?php foreach ($templates as $template): ?>
                <option value="<?= $template['id'] ?>">
                    <?= htmlspecialchars($template['template_name']) ?> (ID: <?= $template['id'] ?>)
                </option>
            <?php endforeach; ?>
        </select>
    </div>
    
    <button type="submit" name="send_email" 
            style="background-color: #8A2BE2; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer;">
        Send Test Email with Unique Numbers
    </button>
</form>

<h3>Recommended Template</h3>
<p>For best results, use "The Big Raffle Winner (Unique Numbers)" template (ID: 53).</p>

<p><a href="../admin/email_templates.php" style="color: #8A2BE2;">Go to Email Templates</a></p>
PHP;
        
        file_put_contents($direct_send_file, $direct_send_code);
        echo "<p>✅ Created direct email sending script with integrated lottery number generation at: <a href='direct_send_email.php'>direct_send_email.php</a></p>";
    }
    
} catch (Exception $e) {
    echo "<h2>Error:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
} 