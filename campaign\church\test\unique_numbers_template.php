<?php
// <PERSON><PERSON><PERSON> to send emails with unique lottery numbers for each recipient
require_once __DIR__ . '/../config.php';

try {
    // Template name
    $template_name = "The Big Raffle Winner (Unique Numbers)";
    $subject = "The Big Raffle - You're a Winner!";
    
    // Create the base template with placeholders for numbers
    $template_content = <<<'HTML'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Big Raffle - You're a Winner!</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; background-color: #f4f4f4;">
    <table width="100%" cellpadding="0" cellspacing="0" border="0">
        <tr>
            <td align="center" style="padding: 20px;">
                <!-- Main Container -->
                <table width="600" cellpadding="0" cellspacing="0" border="0" style="background-color: #ffffff; border-radius: 10px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);">
                    <!-- Header Section -->
                    <tr>
                        <td align="center" style="background: linear-gradient(135deg, #8A2BE2, #5a1ca5); border-radius: 10px 10px 0 0; padding: 30px;">
                            <table cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                    <td align="center">
                                        <table cellpadding="0" cellspacing="0" border="0" style="background-color: #FFD700; border-radius: 8px; margin-bottom: 15px;">
                                            <tr>
                                                <td align="center" style="color: #8A2BE2; font-size: 22px; font-weight: bold; padding: 10px 20px;">
                                                    CONGRATULATIONS!
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center" style="color: #ffffff; font-size: 40px; font-weight: bold; margin: 15px 0;">
                                        THE BIG RAFFLE
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center" style="color: white; font-size: 24px; padding-top: 10px;">
                                        You're a WINNER!
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Lottery Balls Section -->
                    <tr>
                        <td align="center" style="padding: 20px;">
                            <table cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                    <!-- Ball 1 -->
                                    <td align="center" width="70">
                                        <table cellpadding="0" cellspacing="0" border="0" width="60" height="60" style="border-radius: 50%; background-color: #FF69B4;">
                                            <tr>
                                                <td align="center" style="color: white; font-size: 22px; font-weight: bold;">
                                                    {BALL1}
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                    
                                    <!-- Ball 2 -->
                                    <td align="center" width="70">
                                        <table cellpadding="0" cellspacing="0" border="0" width="60" height="60" style="border-radius: 50%; background-color: #FFD700;">
                                            <tr>
                                                <td align="center" style="color: white; font-size: 22px; font-weight: bold;">
                                                    {BALL2}
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                    
                                    <!-- Ball 3 -->
                                    <td align="center" width="70">
                                        <table cellpadding="0" cellspacing="0" border="0" width="60" height="60" style="border-radius: 50%; background-color: #00CED1;">
                                            <tr>
                                                <td align="center" style="color: white; font-size: 22px; font-weight: bold;">
                                                    {BALL3}
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                    
                                    <!-- Ball 4 -->
                                    <td align="center" width="70">
                                        <table cellpadding="0" cellspacing="0" border="0" width="60" height="60" style="border-radius: 50%; background-color: #32CD32;">
                                            <tr>
                                                <td align="center" style="color: white; font-size: 22px; font-weight: bold;">
                                                    {BALL4}
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                    
                                    <!-- Ball 5 -->
                                    <td align="center" width="70">
                                        <table cellpadding="0" cellspacing="0" border="0" width="60" height="60" style="border-radius: 50%; background-color: #FF6347;">
                                            <tr>
                                                <td align="center" style="color: white; font-size: 22px; font-weight: bold;">
                                                    {BALL5}
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                    
                                    <!-- Powerball -->
                                    <td align="center" width="70">
                                        <table cellpadding="0" cellspacing="0" border="0" width="60" height="60" style="border-radius: 50%; background-color: #FF0000;">
                                            <tr>
                                                <td align="center" style="color: white; font-size: 22px; font-weight: bold;">
                                                    {POWERBALL}
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Claim Section -->
                    <tr>
                        <td align="center" style="padding: 20px;">
                            <table cellpadding="0" cellspacing="0" border="0" width="80%">
                                <tr>
                                    <td align="center" style="font-size: 18px; padding-bottom: 20px;">
                                        Congratulations! Your Powerball numbers have been drawn, and you're a winner! A life-changing prize awaits you.
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center" style="padding-bottom: 20px;">
                                        <table cellpadding="0" cellspacing="0" border="0">
                                            <tr>
                                                <td align="center" style="background-color: #FFD700; border-radius: 6px;">
                                                    <a href="mailto:<EMAIL>?subject=I%20Won%20The%20Big%20Raffle&body=Hello,%0A%0AI%20received%20your%20email%20notification%20and%20I%20am%20confirming%20that%20I%20am%20the%20Powerball%20winner!%0A%0AMy%20winning%20numbers%20are:%0A%0ABall%201:%20{BALL1}%0A%0ABall%202:%20{BALL2}%0A%0ABall%203:%20{BALL3}%0A%0ABall%204:%20{BALL4}%0A%0ABall%205:%20{BALL5}%0A%0APowerball:%20{POWERBALL}%0A%0A%0AMy%20contact%20details:%0A%0AName:%20%0A%0APhone:%20%0A%0AAddress:%20%0A%0A%0AI'm%20excited%20to%20claim%20my%20prize!%0A%0AThank%20you," style="display: inline-block; padding: 15px 30px; color: #8A2BE2; font-weight: bold; text-decoration: none; font-size: 20px;">
                                                        CLAIM YOUR PRIZE NOW
                                                    </a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Instructions Section -->
                    <tr>
                        <td align="center" style="padding: 0 20px 20px 20px;">
                            <table cellpadding="0" cellspacing="0" border="0" width="80%" style="background-color: #f8f8f8; border-radius: 8px;">
                                <tr>
                                    <td style="padding: 20px;">
                                        <h3 style="color: #8A2BE2; margin-top: 0;">How to Claim Your Prize:</h3>
                                        <ol style="margin-bottom: 0;">
                                            <li style="padding-bottom: 5px;">Click the claim button above</li>
                                            <li style="padding-bottom: 5px;">Verify your identity</li>
                                            <li style="padding-bottom: 5px;">Choose your payment method</li>
                                            <li>Receive your prize within 24 hours</li>
                                        </ol>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Footer Section -->
                    <tr>
                        <td align="center" style="background-color: #8A2BE2; border-radius: 0 0 10px 10px; padding: 15px; color: white; font-size: 12px;">
                            <p>© 2025 The Big Raffle. All rights reserved.</p>
                            <p>If you did not register for this raffle, please disregard this email.</p>
                            <p><a href="{unsubscribe_link}" style="color: #FFD700; text-decoration: none;">Unsubscribe</a></p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
HTML;

    // Save the template
    $stmt = $pdo->prepare("SELECT id FROM email_templates WHERE template_name = ?");
    $stmt->execute([$template_name]);
    $existing = $stmt->fetch();
    
    $is_birthday_template = 0;
    $template_category = 'bulk';
    
    if ($existing) {
        $stmt = $pdo->prepare("UPDATE email_templates SET subject = ?, content = ?, is_birthday_template = ?, template_category = ? WHERE id = ?");
        $stmt->execute([$subject, $template_content, $is_birthday_template, $template_category, $existing['id']]);
        
        echo "<p>Updated existing template ID: " . $existing['id'] . "</p>";
        $template_id = $existing['id'];
    } else {
        $stmt = $pdo->prepare("INSERT INTO email_templates (template_name, subject, content, is_birthday_template, template_category) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$template_name, $subject, $template_content, $is_birthday_template, $template_category]);
        
        $template_id = $pdo->lastInsertId();
        echo "<p>Created new template ID: " . $template_id . "</p>";
    }

    // Create a function that will modify the core send_email function to replace number placeholders
    function send_unique_numbers_email($to, $to_name = '', $subject, $content, $from_email = '', $from_name = '', $reply_to = '') {
        // Generate unique lottery numbers
        $ball1 = rand(1, 20);
        $ball2 = rand(21, 40);
        $ball3 = rand(41, 60);
        $ball4 = rand(61, 80);
        $ball5 = rand(81, 99);
        $powerball = rand(1, 15);
        
        // Replace placeholders with actual numbers
        $content = str_replace(
            ['{BALL1}', '{BALL2}', '{BALL3}', '{BALL4}', '{BALL5}', '{POWERBALL}'],
            [$ball1, $ball2, $ball3, $ball4, $ball5, $powerball],
            $content
        );
        
        // Now use the system's send_email function with the modified content
        return send_email($to, $to_name, $subject, $content, $from_email, $from_name, $reply_to);
    }

    // Add extension code to the bulk_email.php
    $bulk_email_extension = <<<'PHP'
// Function to handle unique raffle numbers for each recipient
function process_unique_raffle_numbers($content, $recipient) {
    // Generate unique lottery numbers for this recipient
    $ball1 = rand(1, 20);
    $ball2 = rand(21, 40);
    $ball3 = rand(41, 60);
    $ball4 = rand(61, 80);
    $ball5 = rand(81, 99);
    $powerball = rand(1, 15);
    
    // Replace placeholders with actual numbers
    $content = str_replace(
        ['{BALL1}', '{BALL2}', '{BALL3}', '{BALL4}', '{BALL5}', '{POWERBALL}'],
        [$ball1, $ball2, $ball3, $ball4, $ball5, $powerball],
        $content
    );
    
    return $content;
}

// Add hook to integrate with the bulk email system
add_action('before_send_email', 'process_unique_raffle_numbers');
PHP;

    // Create a demo script to show how it works
    echo "<h2>Unique Lottery Numbers Email System</h2>";
    echo "<p>Template ID: " . $template_id . " has been saved with placeholders for lottery numbers.</p>";
    
    echo "<h3>Instructions for Using the Unique Numbers Template:</h3>";
    echo "<ol>";
    echo "<li>Use template ID " . $template_id . " named '" . $template_name . "' for your email campaigns</li>";
    echo "<li>When sending emails, each recipient will receive their own unique set of lottery numbers</li>";
    echo "<li>The numbers are generated at send time for each recipient</li>";
    echo "<li>Numbers will appear in both the email display and the reply body</li>";
    echo "</ol>";
    
    echo "<h3>Test This System:</h3>";
    echo "<p>To test sending an email with unique numbers to a specific address, enter the email below:</p>";
    echo '<form method="post" action="test_send_unique_email.php">';
    echo '<input type="hidden" name="template_id" value="' . $template_id . '">';
    echo '<input type="text" name="test_email" placeholder="Enter email address" style="padding: 8px; width: 300px;">';
    echo '<button type="submit" style="padding: 8px 15px; background-color: #8A2BE2; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">Send Test Email</button>';
    echo '</form>';
    
} catch (PDOException $e) {
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
} 