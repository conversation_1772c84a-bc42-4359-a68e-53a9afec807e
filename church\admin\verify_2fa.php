<?php
// Include the configuration file
require_once '../config.php';

// Include session manager which will handle starting the session
require_once 'includes/session-manager.php';

// Check if 2FA verification is pending
if (!isset($_SESSION['2fa_pending']) || !isset($_SESSION['2fa_admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the SecurityManager class
require_once '../classes/SecurityManager.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize SecurityManager
$security = new SecurityManager($conn);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';

// Process 2FA verification
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        $code = $security->sanitizeInput($_POST['verification_code'], 'text');
        $adminId = $_SESSION['2fa_admin_id'];
        
        // Check if using backup code
        if (isset($_POST['use_backup_code']) && $_POST['use_backup_code'] == '1') {
            // Verify backup code
            $stmt = $conn->prepare("SELECT backup_codes FROM admin_2fa WHERE admin_id = ? AND is_enabled = 1");
            $stmt->execute([$adminId]);
            $result = $stmt->fetch();
            
            if ($result && !empty($result['backup_codes'])) {
                $backupCodes = json_decode($result['backup_codes'], true);
                
                if (is_array($backupCodes) && in_array($code, $backupCodes)) {
                    // Valid backup code, remove it from the list
                    $backupCodes = array_diff($backupCodes, [$code]);
                    
                    // Update backup codes
                    $stmt = $conn->prepare("UPDATE admin_2fa SET backup_codes = ?, last_used = NOW() WHERE admin_id = ?");
                    $stmt->execute([json_encode($backupCodes), $adminId]);
                    
                    // Complete login
                    completeLogin($adminId, $security, $conn);
                } else {
                    $error = "Invalid backup code. Please try again.";
                }
            } else {
                $error = "No backup codes available. Please contact an administrator.";
            }
        } else {
            // Verify TOTP code
            $stmt = $conn->prepare("SELECT secret_key FROM admin_2fa WHERE admin_id = ? AND is_enabled = 1");
            $stmt->execute([$adminId]);
            $result = $stmt->fetch();
            
            if ($result && !empty($result['secret_key'])) {
                // Get the secret key
                $secretKey = $result['secret_key'];
                
                // Implement proper TOTP validation
                $valid = false;
                
                // For security, we check a window of codes (current, previous, and next)
                // to account for time synchronization issues
                $timeWindow = 2; // Check current code, 1 before, and 1 after
                $timestamp = floor(time() / 30); // Current 30-second window
                
                for ($i = -$timeWindow; $i <= $timeWindow; $i++) {
                    $checkTime = $timestamp + $i;
                    $expectedCode = generateTOTPCode($secretKey, $checkTime);
                    
                    if (hash_equals($expectedCode, $code)) {
                        $valid = true;
                        break;
                    }
                }
                
                if ($valid) {
                    // Update last used timestamp
                    $stmt = $conn->prepare("UPDATE admin_2fa SET last_used = NOW() WHERE admin_id = ?");
                    $stmt->execute([$adminId]);
                    
                    // Complete login
                    completeLogin($adminId, $security, $conn);
                } else {
                    $security->logSecurityEvent('Failed 2FA attempt', [
                        'admin_id' => $adminId,
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ]);
                    $error = "Invalid verification code. Please try again.";
                }
            } else {
                $error = "2FA is not properly set up for this account. Please contact an administrator.";
            }
        }
    }
}

/**
 * Generate a TOTP code based on the secret key and timestamp
 * 
 * @param string $secretKey The secret key
 * @param int $timestamp The timestamp (in 30-second intervals)
 * @return string The 6-digit TOTP code
 */
function generateTOTPCode($secretKey, $timestamp) {
    // Convert timestamp to binary (64-bit)
    $timeBytes = pack('N*', 0, $timestamp);
    
    // Convert secret key to binary
    $secretBytes = hex2bin($secretKey);
    
    // Generate HMAC-SHA1 hash
    $hash = hash_hmac('sha1', $timeBytes, $secretBytes, true);
    
    // Get offset (last 4 bits of the hash)
    $offset = ord($hash[19]) & 0x0F;
    
    // Get 4 bytes from the hash starting at the offset
    $hashPart = substr($hash, $offset, 4);
    
    // Convert to 32-bit integer
    $value = unpack('N', $hashPart)[1];
    
    // Remove the most significant bit (RFC 4226)
    $value = $value & 0x7FFFFFFF;
    
    // Modulo 1000000 to get 6 digits
    $modulo = pow(10, 6);
    $value = $value % $modulo;
    
    // Pad with leading zeros if needed
    return str_pad($value, 6, '0', STR_PAD_LEFT);
}

/**
 * Complete the login process after successful 2FA verification
 */
function completeLogin($adminId, $security, $conn) {
    // Get admin details
    $stmt = $conn->prepare("SELECT username, full_name FROM admins WHERE id = ?");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch();
    
    // Clear 2FA pending flags
    unset($_SESSION['2fa_pending']);
    unset($_SESSION['2fa_admin_id']);
    
    // Set regular session variables if not already set
    $_SESSION['admin_id'] = $adminId;
    $_SESSION['admin_name'] = $admin['full_name'];
    $_SESSION['admin_username'] = $admin['username'];
    
    // Log successful 2FA verification
    $security->logSecurityEvent('Successful 2FA verification', [
        'admin_id' => $adminId,
        'username' => $admin['username']
    ]);
    
    // Redirect to dashboard
    header("Location: index.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Two-Factor Authentication - Freedom Assembly Church</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 50px;
        }
        .verification-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
        }
        .verification-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .verification-logo h1 {
            color: #343a40;
        }
        .error-message {
            color: #dc3545;
            margin-bottom: 20px;
        }
        .success-message {
            color: #28a745;
            margin-bottom: 20px;
        }
        .backup-code-toggle {
            margin-top: 15px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="verification-container">
            <div class="verification-logo">
                <h1>Two-Factor Authentication</h1>
                <p>Freedom Assembly Church International</p>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="error-message text-center">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="success-message text-center">
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <div class="mb-4 text-center">
                <p>Please enter the verification code from your authenticator app to complete the login process.</p>
            </div>
            
            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="verification-form">
                <?php echo $security->generateCSRFInput(); ?>
                
                <div class="mb-3">
                    <label for="verification_code" class="form-label">Verification Code</label>
                    <input type="text" class="form-control" id="verification_code" name="verification_code" 
                           inputmode="numeric" pattern="[0-9]*" maxlength="6" autocomplete="one-time-code" required>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">Verify</button>
                </div>
                
                <div class="backup-code-toggle text-center">
                    <div class="form-check form-switch d-inline-block">
                        <input class="form-check-input" type="checkbox" id="use_backup_code" name="use_backup_code" value="1">
                        <label class="form-check-label" for="use_backup_code">Use backup code instead</label>
                    </div>
                </div>
                
                <div class="mt-3 text-center">
                    <a href="logout.php">Cancel and return to login</a>
                </div>
            </form>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 