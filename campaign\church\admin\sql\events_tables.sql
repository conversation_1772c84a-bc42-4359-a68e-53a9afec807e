-- Events Management System Database Tables
-- Created for Freedom Assembly Church Management System

-- Event Categories Table
CREATE TABLE IF NOT EXISTS event_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color_code VARCHAR(7) DEFAULT '#6f42c1',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Events Table
CREATE TABLE IF NOT EXISTS events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    start_datetime DATETIME NOT NULL,
    end_datetime DATETIME NOT NULL,
    location VARCHAR(255),
    capacity INT DEFAULT NULL,
    category_id INT,
    created_by INT NOT NULL,
    status ENUM('draft', 'published', 'cancelled', 'completed') DEFAULT 'draft',
    requirements TEXT,
    image_path VARCHAR(500),
    is_public BOOLEAN DEFAULT TRUE,
    registration_required BOOLEAN DEFAULT FALSE,
    registration_deadline DATETIME DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES event_categories(id) ON DELETE SET NULL,
    INDEX idx_start_datetime (start_datetime),
    INDEX idx_status (status),
    INDEX idx_category (category_id),
    INDEX idx_public (is_public)
);

-- Event RSVPs Table
CREATE TABLE IF NOT EXISTS event_rsvps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    user_id INT DEFAULT NULL,
    guest_name VARCHAR(255) DEFAULT NULL,
    guest_email VARCHAR(255) DEFAULT NULL,
    guest_phone VARCHAR(20) DEFAULT NULL,
    response_type ENUM('attending', 'maybe', 'not_attending', 'waitlist') NOT NULL,
    party_size INT DEFAULT 1,
    special_requirements TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_event (event_id, user_id),
    UNIQUE KEY unique_guest_event (event_id, guest_email),
    INDEX idx_event_response (event_id, response_type),
    INDEX idx_user_rsvp (user_id)
);

-- Event Files Table
CREATE TABLE IF NOT EXISTS event_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INT NOT NULL,
    uploaded_by INT NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_public BOOLEAN DEFAULT TRUE,
    description TEXT,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    INDEX idx_event_files (event_id),
    INDEX idx_file_type (file_type)
);

-- Event Comments Table
CREATE TABLE IF NOT EXISTS event_comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    user_id INT DEFAULT NULL,
    guest_name VARCHAR(255) DEFAULT NULL,
    guest_email VARCHAR(255) DEFAULT NULL,
    comment TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    INDEX idx_event_comments (event_id),
    INDEX idx_approved (is_approved),
    INDEX idx_user_comments (user_id)
);

-- Event Notifications Table
CREATE TABLE IF NOT EXISTS event_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    notification_type ENUM('reminder', 'update', 'cancellation', 'new_event') NOT NULL,
    recipient_type ENUM('all_rsvp', 'attending_only', 'specific_users', 'all_members') NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    send_datetime DATETIME NOT NULL,
    sent_at TIMESTAMP NULL DEFAULT NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    INDEX idx_send_datetime (send_datetime),
    INDEX idx_sent_status (sent_at)
);

-- Insert Default Event Categories
INSERT IGNORE INTO event_categories (name, description, color_code) VALUES
('Worship Service', 'Regular worship services and special services', '#6f42c1'),
('Bible Study', 'Bible study sessions and small group meetings', '#28a745'),
('Fellowship', 'Fellowship events and social gatherings', '#fd7e14'),
('Outreach', 'Community outreach and evangelism events', '#dc3545'),
('Youth', 'Youth ministry events and activities', '#20c997'),
('Children', 'Children ministry events and programs', '#ffc107'),
('Prayer', 'Prayer meetings and prayer events', '#6610f2'),
('Conference', 'Conferences, seminars, and special meetings', '#0d6efd'),
('Holiday', 'Holiday celebrations and special occasions', '#e83e8c'),
('Training', 'Training sessions and workshops', '#6c757d');
