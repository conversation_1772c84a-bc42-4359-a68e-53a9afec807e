<?php
// This file serves the font data using <PERSON><PERSON> to bypass CSP restrictions

// Font data (base64 encoded)
$fontData = 'AAEAAAALAIAAAwAwT1MvMg8SBfAAAAC8AAAAYGNtYXAXVtKNAAABHAAAAFRnYXNwAAAAEAAAAXAAAAAIZ2x5ZgYydxIAAAF4AAAFNGhlYWQUJ7cIAAAGrAAAADZoaGVhB20DzAAABuQAAAAkaG10eCIABhQAAAcIAAAALGxvY2ED4AU6AAAHNAAAABhtYXhwAA8AjAAAB0wAAAAgbmFtZXsr690AAAdsAAABhnBvc3QAAwAAAAAI9AAAACAAAwPAAZAABQAAApkCzAAAAI8CmQLMAAAB6wAzAQkAAAAAAAAAAAAAAAAAAAABEAAAAAAAAAAAAAAAAAAAAABAAADpBgPA/8AAQAPAAEAAAAABAAAAAAAAAAAAAAAgAAAAAAADAAAAAwAAABwAAQADAAAAHAADAAEAAAAcAAQAOAAAAAoACAACAAIAAQAg6Qb//f//AAAAAAAg6QD//f//AAH/4xcEAAMAAQAAAAAAAAAAAAAAAQAB//8ADwABAAAAAAAAAAAAAgAANzkBAAAAAAEAAAAAAAAAAAACAAA3OQEAAAAAAQAAAAAAAAAAAAIAADc5AQAAAAACAAD/wQP+A78ABAAJAAATJwkBBwEXCQE35QIDAgMC/NnlAgP9/QIRB/3pAhcH/XgHAhf96QcAAAABAAD/wQQCA78ABgAAAScBJwkBNwQCB/13BwIX/ekHAgkH/XgHAhcCFwcAAgAA/8ED/wO/AAYACwAAARcJAQcBNxcnCQEXA/4H/ekCFwf9eAfl5f39AgMFA78H/XgCFwf9eAfcBwIX/ekHAAABAAAAAQAAFeXLDl8PPPUACwQAAAAAANPLcFIAAAAA08twUgAA/8EEAQO/AAAAAAAIAAIAAAAAAAAAAQAAA8D/wAAABAAAAAAABAEAAQAAAAAAAAAAAAAAAAAAAAsAAAAAAAAAAAAAAAACAAAABAAAAARFAAED/wAAAAAACgAUAB4AOABIAF4AcACGAJYAAQAAAAsADAACAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAAAA';

// Set headers
header('Content-Type: application/octet-stream');
header('Content-Transfer-Encoding: binary');
header('Content-Disposition: attachment; filename="fcicons.ttf"');
header('Cache-Control: public, max-age=31536000'); // Cache for a year

// Output the font data
echo base64_decode($fontData);
?> 