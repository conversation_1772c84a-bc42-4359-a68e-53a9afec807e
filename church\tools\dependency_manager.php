<?php
/**
 * Dependency Manager
 * 
 * This script helps manage external library dependencies by downloading
 * CDN-hosted libraries for local use. This ensures the application works
 * even when internet access is limited or CDNs are blocked.
 */

// Configuration
$baseDir = dirname(__DIR__);
$vendorAssetDir = $baseDir . '/vendor/assets';
$libraries = [
    'bootstrap' => [
        'css' => 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css',
        'js' => 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js'
    ],
    'bootstrap-icons' => [
        'css' => 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css',
        'font' => 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff2'
    ],
    'fullcalendar' => [
        'js' => 'https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js',
        'css' => 'https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css'
    ],
    'chartjs' => [
        'js' => 'https://cdn.jsdelivr.net/npm/chart.js'
    ],
    'moment' => [
        'js' => 'https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js'
    ]
];

// Create directories
function createDirectories($path) {
    if (!file_exists($path)) {
        if (mkdir($path, 0755, true)) {
            echo "[INFO] Created directory: $path\n";
        } else {
            echo "[ERROR] Failed to create directory: $path\n";
            return false;
        }
    }
    return true;
}

// Download a file
function downloadFile($url, $destination) {
    if (file_exists($destination)) {
        echo "[INFO] File already exists: $destination\n";
        return true;
    }
    
    echo "[INFO] Downloading: $url\n";
    $content = @file_get_contents($url);
    
    if ($content === false) {
        echo "[ERROR] Failed to download: $url\n";
        return false;
    }
    
    if (file_put_contents($destination, $content)) {
        echo "[INFO] Saved to: $destination\n";
        return true;
    } else {
        echo "[ERROR] Failed to save: $destination\n";
        return false;
    }
}

// Process a library
function processLibrary($libraryName, $files, $baseDir) {
    $libraryDir = $baseDir . '/vendor/assets/' . $libraryName;
    
    if (!createDirectories($libraryDir)) {
        return false;
    }
    
    $success = true;
    
    foreach ($files as $type => $url) {
        $extension = pathinfo($url, PATHINFO_EXTENSION);
        if (empty($extension)) {
            $extension = $type;
        }
        
        $filename = $libraryName . '.' . $extension;
        
        // Special case for fonts
        if ($type === 'font') {
            $fontDir = $libraryDir . '/fonts';
            if (!createDirectories($fontDir)) {
                $success = false;
                continue;
            }
            $destination = $fontDir . '/' . basename($url);
        } else {
            $destination = $libraryDir . '/' . $filename;
        }
        
        if (!downloadFile($url, $destination)) {
            $success = false;
        }
    }
    
    return $success;
}

// Generate a reference file
function generateReferenceFile($libraries, $baseDir) {
    $referenceFile = $baseDir . '/vendor/assets/references.php';
    $content = "<?php\n/**\n * Auto-generated library references\n * This file maps CDN URLs to local file paths\n */\n\nreturn [\n";
    
    foreach ($libraries as $libraryName => $files) {
        foreach ($files as $type => $url) {
            $extension = pathinfo($url, PATHINFO_EXTENSION);
            if (empty($extension)) {
                $extension = $type;
            }
            
            $filename = $libraryName . '.' . $extension;
            
            // Special case for fonts
            if ($type === 'font') {
                $localPath = "assets/{$libraryName}/fonts/" . basename($url);
            } else {
                $localPath = "assets/{$libraryName}/{$filename}";
            }
            
            $content .= "    '{$url}' => '{$localPath}',\n";
        }
    }
    
    $content .= "];\n";
    
    if (file_put_contents($referenceFile, $content)) {
        echo "[INFO] Generated reference file: $referenceFile\n";
        return true;
    } else {
        echo "[ERROR] Failed to generate reference file\n";
        return false;
    }
}

// Generate helper functions
function generateHelperFile($baseDir) {
    $helperFile = $baseDir . '/includes/dependency_helpers.php';
    $content = <<<'EOD'
<?php
/**
 * Dependency Helper Functions
 * 
 * This file provides helper functions for managing external dependencies.
 */

/**
 * Get the URL for a library asset (CSS, JS, etc.)
 * If local assets are available, it returns the local path, otherwise the CDN URL
 * 
 * @param string $cdnUrl The original CDN URL
 * @param bool $forceLocal Whether to force using local assets
 * @return string The URL to use
 */
function get_asset_url($cdnUrl, $forceLocal = false) {
    static $references = null;
    static $baseUrl = null;
    
    // Initialize references if not already loaded
    if ($references === null) {
        $referencePath = dirname(__DIR__) . '/vendor/assets/references.php';
        if (file_exists($referencePath)) {
            $references = require_once $referencePath;
        } else {
            $references = [];
        }
    }
    
    // Initialize base URL if not already set
    if ($baseUrl === null) {
        $baseUrl = defined('SITE_URL') ? SITE_URL : '';
    }
    
    // If we're using local assets and we have a reference for this URL
    if (($forceLocal || defined('USE_LOCAL_ASSETS') && USE_LOCAL_ASSETS) && isset($references[$cdnUrl])) {
        return $baseUrl . '/vendor/' . $references[$cdnUrl];
    }
    
    // Otherwise, return the CDN URL
    return $cdnUrl;
}

/**
 * Output a CSS link tag for an asset
 * 
 * @param string $cdnUrl The original CDN URL
 * @param bool $forceLocal Whether to force using local assets
 */
function css_asset($cdnUrl, $forceLocal = false) {
    $url = get_asset_url($cdnUrl, $forceLocal);
    echo '<link rel="stylesheet" href="' . htmlspecialchars($url) . '">' . PHP_EOL;
}

/**
 * Output a JavaScript script tag for an asset
 * 
 * @param string $cdnUrl The original CDN URL
 * @param bool $forceLocal Whether to force using local assets
 */
function js_asset($cdnUrl, $forceLocal = false) {
    $url = get_asset_url($cdnUrl, $forceLocal);
    echo '<script src="' . htmlspecialchars($url) . '"></script>' . PHP_EOL;
}
EOD;
    
    if (!file_exists(dirname($helperFile))) {
        createDirectories(dirname($helperFile));
    }
    
    if (file_put_contents($helperFile, $content)) {
        echo "[INFO] Generated helper file: $helperFile\n";
        return true;
    } else {
        echo "[ERROR] Failed to generate helper file\n";
        return false;
    }
}

// Main execution
echo "[INFO] Starting dependency manager...\n";

// Create vendor assets directory
if (!createDirectories($vendorAssetDir)) {
    echo "[ERROR] Failed to create vendor assets directory. Aborting.\n";
    exit(1);
}

// Process each library
$allSuccess = true;
foreach ($libraries as $libraryName => $files) {
    echo "[INFO] Processing library: $libraryName\n";
    if (!processLibrary($libraryName, $files, $baseDir)) {
        $allSuccess = false;
        echo "[ERROR] Failed to process library: $libraryName\n";
    }
}

// Generate reference file
if (!generateReferenceFile($libraries, $baseDir)) {
    $allSuccess = false;
}

// Generate helper file
if (!generateHelperFile($baseDir)) {
    $allSuccess = false;
}

if ($allSuccess) {
    echo "[INFO] All dependencies processed successfully.\n";
    echo "[INFO] To use local assets, set USE_LOCAL_ASSETS constant to true in environment.php.\n";
} else {
    echo "[WARNING] Some dependencies could not be processed. Check the errors above.\n";
}

echo "[INFO] Dependency manager completed.\n"; 