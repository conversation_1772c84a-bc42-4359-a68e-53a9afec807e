<?php
/**
 * API endpoint to get public events for homepage
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Include configuration
    require_once '../config.php';
    
    // Get upcoming events (next 6 events)
    $stmt = $pdo->prepare("
        SELECT e.id, e.title, e.description, e.event_date, e.location, e.max_attendees, e.is_active,
               COUNT(er.id) as rsvp_count,
               COUNT(CASE WHEN er.status = 'attending' THEN 1 END) as attending_count
        FROM events e
        LEFT JOIN event_rsvps er ON e.id = er.event_id
        WHERE e.is_active = 1 
        AND e.event_date > NOW()
        GROUP BY e.id
        ORDER BY e.event_date ASC
        LIMIT 6
    ");
    
    $stmt->execute();
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format events for frontend
    $formatted_events = [];
    foreach ($events as $event) {
        $formatted_events[] = [
            'id' => (int)$event['id'],
            'title' => $event['title'],
            'description' => $event['description'],
            'event_date' => $event['event_date'],
            'location' => $event['location'],
            'max_attendees' => $event['max_attendees'] ? (int)$event['max_attendees'] : null,
            'rsvp_count' => (int)$event['rsvp_count'],
            'attending_count' => (int)$event['attending_count'],
            'is_active' => (bool)$event['is_active']
        ];
    }
    
    $response = [
        'success' => true,
        'events' => $formatted_events,
        'total_count' => count($formatted_events)
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to load events',
        'message' => $e->getMessage()
    ]);
}
?>
