/**
 * Church Admin - Contact Management JavaScript
 * 
 * This file contains functions for handling contact data in the admin panel,
 * including pagination, selection, and group management.
 */

// Global variables for state management
let selectedContactIds = [];
let allAvailableContacts = [];
let currentPage = 1;
let itemsPerPage = 50;
let currentSort = 'name';
let currentOrder = 'ASC';
let currentGroupId = '';

// Process contact groups to ensure uniqueness
window.renderGroups = function(groups) {
    if (!groups || groups.length === 0) {
        return '<span class="badge bg-secondary">No Groups</span>';
    }
    
    const uniqueGroups = [...new Set(groups)];
    const maxDisplay = 3;
    
    let html = '<div class="group-badges-container">';
    const displayGroups = uniqueGroups.slice(0, maxDisplay);
    
    displayGroups.forEach(group => {
        html += `<span class="badge bg-info me-1">${escapeHtml(group)}</span>`;
    });
    
    if (uniqueGroups.length > maxDisplay) {
        html += `<span class="badge bg-secondary">+${uniqueGroups.length - maxDisplay} more</span>`;
    }
    
    html += '</div>';
    return html;
}

// Helper function to escape HTML
window.escapeHtml = function(text) {
    if (!text) return '';
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, m => map[m]);
}

// Function to load contacts via AJAX
function loadContacts(page = currentPage) {
    console.log('Loading contacts:', { page, itemsPerPage, currentSort, currentOrder, currentGroupId });
    
    const url = `ajax/get_contacts.php?page=${page}&sort=${currentSort}&order=${currentOrder}&limit=${itemsPerPage}&get_all=true${currentGroupId ? '&group_id=' + currentGroupId : ''}`;
    
    // Show loading state
    const tbody = document.querySelector('#contactsTable tbody');
    if (tbody) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></td></tr>';
    }
    
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Store all contacts for bulk operations
                allAvailableContacts = data.all_contacts || [];
                
                // Update table content
                updateContactsTable(data.contacts);
                
                // Update pagination
                updatePagination(data.pagination);
                
                // Update selection info
                updateSelectionInfo();
                
                // Reinitialize event handlers
                initializeContactActions();
            } else {
                throw new Error(data.error || 'Failed to load contacts');
            }
        })
        .catch(error => {
            console.error('Error loading contacts:', error);
            showNotification('Error loading contacts: ' + error.message, 'error');
            
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center text-danger">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            ${error.message}
                        </td>
                    </tr>
                `;
            }
        });
}

// Update contacts table with data
function updateContactsTable(contacts) {
    const tbody = document.querySelector('#contactsTable tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    contacts.forEach((contact, index) => {
        const isSelected = selectedContactIds.includes(contact.id.toString());
        
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input contact-checkbox" 
                       name="selected_contacts[]" value="${contact.id}" 
                       ${isSelected ? 'checked' : ''}>
            </td>
            <td>${escapeHtml(contact.name)}</td>
            <td>${escapeHtml(contact.email)}</td>
            <td>${renderGroups(contact.groups)}</td>
            <td>${new Date(contact.created_at).toLocaleDateString()}</td>
        `;
        tbody.appendChild(tr);
    });
    
    // Update "Select All" checkbox state
    updateSelectAllCheckbox();
}

// Update pagination controls
function updatePagination(pagination) {
    const paginationContainer = document.querySelector('#contactPagination');
    if (!paginationContainer) return;
    
    const totalPages = Math.ceil(pagination.total / itemsPerPage);
    let html = '';
    
    // Previous button
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage - 1}">&laquo;</a>
        </li>
    `;
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (
            i === 1 || 
            i === totalPages || 
            (i >= currentPage - 2 && i <= currentPage + 2)
        ) {
            html += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        } else if (
            i === currentPage - 3 || 
            i === currentPage + 3
        ) {
            html += `
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            `;
        }
    }
    
    // Next button
    html += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage + 1}">&raquo;</a>
        </li>
    `;
    
    paginationContainer.innerHTML = html;
    
    // Add click handlers to pagination links
    paginationContainer.querySelectorAll('.page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = parseInt(this.dataset.page);
            if (page && !isNaN(page)) {
                currentPage = page;
                loadContacts(page);
            }
        });
    });
}

// Update selection info display
function updateSelectionInfo() {
    const infoElement = document.getElementById('contactSelectionInfo');
    if (!infoElement) return;
    
    infoElement.innerHTML = `
        <span class="badge bg-primary">${selectedContactIds.length} contacts selected</span>
        <small class="text-muted ms-2">out of ${allAvailableContacts.length} total</small>
    `;
}

// Update "Select All" checkbox state
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('selectAllContactsCheckbox');
    if (!selectAllCheckbox) return;
    
    const checkboxes = document.querySelectorAll('.contact-checkbox');
    const checkedCount = document.querySelectorAll('.contact-checkbox:checked').length;
    
    selectAllCheckbox.checked = checkboxes.length > 0 && checkedCount === checkboxes.length;
    selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize contact table functionality
    const contactsTable = document.getElementById('contactsTable');
    if (!contactsTable) return;
    
    // Initialize pagination
    loadContacts();
    
    // Handle "Select All" checkbox
    const selectAllCheckbox = document.getElementById('selectAllContactsCheckbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.contact-checkbox');
            checkboxes.forEach(cb => {
                cb.checked = this.checked;
                const contactId = cb.value;
                
                if (this.checked) {
                    if (!selectedContactIds.includes(contactId)) {
                        selectedContactIds.push(contactId);
                    }
                } else {
                    const index = selectedContactIds.indexOf(contactId);
                    if (index > -1) {
                        selectedContactIds.splice(index, 1);
                    }
                }
            });
            
            updateSelectionInfo();
        });
    }
    
    // Handle individual contact checkboxes
    contactsTable.addEventListener('change', function(e) {
        if (e.target.classList.contains('contact-checkbox')) {
            const contactId = e.target.value;
            
            if (e.target.checked) {
                if (!selectedContactIds.includes(contactId)) {
                    selectedContactIds.push(contactId);
                }
            } else {
                const index = selectedContactIds.indexOf(contactId);
                if (index > -1) {
                    selectedContactIds.splice(index, 1);
                }
            }
            
            updateSelectAllCheckbox();
            updateSelectionInfo();
        }
    });
    
    // Handle "Select All Contacts" button
    const selectAllBtn = document.getElementById('selectAllContacts');
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', function() {
            selectedContactIds = allAvailableContacts.map(contact => contact.id.toString());
            loadContacts(); // Reload to update checkboxes
            showNotification(`Selected all ${selectedContactIds.length} contacts`, 'info');
        });
    }
    
    // Handle "Select First N" buttons
    document.querySelectorAll('.select-first-n-contacts').forEach(button => {
        button.addEventListener('click', function() {
            const count = parseInt(this.dataset.count);
            if (!count || count <= 0) return;
            
            selectedContactIds = allAvailableContacts
                .slice(0, Math.min(count, allAvailableContacts.length))
                .map(contact => contact.id.toString());
            
            loadContacts();
            showNotification(`Selected first ${selectedContactIds.length} contacts`, 'info');
        });
    });
    
    // Handle "Deselect All" button
    const deselectAllBtn = document.getElementById('deselectAllContacts');
    if (deselectAllBtn) {
        deselectAllBtn.addEventListener('click', function() {
            selectedContactIds = [];
            loadContacts();
            showNotification('Cleared all selections', 'info');
        });
    }
    
    // Handle items per page change
    const limitSelect = document.getElementById('contactLimitSelect');
    if (limitSelect) {
        limitSelect.addEventListener('change', function() {
            itemsPerPage = parseInt(this.value);
            currentPage = 1;
            loadContacts();
        });
    }
    
    // Handle group filter change
    const groupSelect = document.getElementById('contactGroupSelect');
    if (groupSelect) {
        groupSelect.addEventListener('change', function() {
            currentGroupId = this.value;
            currentPage = 1;
            loadContacts();
        });
    }
}); 