<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Birthday Celebration</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f9f9f9;
            color: #333333;
            line-height: 1.5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            padding: 30px;
            text-align: center;
            color: #ffffff;
            border-radius: 10px 10px 0 0;
        }
        .content {
            background: #ffffff;
            padding: 30px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .greeting {
            font-size: 18px;
            margin-bottom: 25px;
            color: #333333;
        }
        .member-photo {
            text-align: center;
            margin: 20px 0;
        }
        .member-photo img {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #6a11cb;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .birthday-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .birthday-date {
            font-size: 18px;
            color: #2575fc;
            margin-bottom: 10px;
        }
        .birthday-age {
            display: inline-block;
            background: #6a11cb;
            color: #ffffff;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 16px;
        }
        .suggestions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .suggestions h3 {
            color: #2575fc;
            margin-top: 0;
        }
        .suggestions ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        .suggestions li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .suggestions li:last-child {
            border-bottom: none;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            color: #ffffff;
            text-decoration: none;
            padding: 12px 30px;
            border-radius: 25px;
            margin: 20px 0;
            text-align: center;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666666;
            font-size: 14px;
        }
        .church-name {
            color: #2575fc;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Birthday Celebration!</h1>
            <p>Join us in celebrating a special member of our church family</p>
        </div>
        
        <div class="content">
            <div class="greeting">
                <p>Dear {recipient_full_name},</p>
                <p>We are excited to let you know that {birthday_member_full_name} will be celebrating their birthday {days_text}!</p>
            </div>
            
            <div class="member-photo">
                <img src="{birthday_member_photo_url}" alt="{birthday_member_first_name}'s photo" onerror="this.src='https://freedomassemblydb.online/assets/img/default-avatar.png'; this.onerror=null;">
            </div>
            
            <div class="birthday-details">
                <div class="birthday-date">Birthday: {upcoming_birthday_formatted}</div>
                <div class="birthday-age">Turning {birthday_member_age} years</div>
            </div>
            
            <div class="suggestions">
                <h3>Ways to Bless {birthday_member_first_name}:</h3>
                <ul>
                    <li>Send a heartfelt birthday message</li>
                    <li>Pray for their growth and blessings in the coming year</li>
                    <li>Share a scripture that encourages them</li>
                    <li>Consider gifting something meaningful</li>
                </ul>
            </div>
            
            <div style="text-align: center;">
                <a href="mailto:{birthday_member_email}?subject=Happy%20Birthday%20{birthday_member_first_name}!&body=Dear%20{birthday_member_first_name},%0D%0A%0D%0AWishing%20you%20a%20blessed%20birthday!%0D%0A%0D%0ABlessings,%0D%0A{recipient_full_name}" class="button">
                    Send Birthday Wishes
                </a>
            </div>
            
            <div class="footer">
                <p>With blessings from <span class="church-name">Freedom Assembly Church</span></p>
                <p><small><a href="{unsubscribe_link}" style="color: #666666;">Unsubscribe from these updates</a></small></p>
            </div>
        </div>
    </div>
    {tracking_pixel}
</body>
</html> 