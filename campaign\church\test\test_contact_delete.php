<?php
/**
 * Test script for contact delete functionality
 * This script tests both individual and bulk delete operations
 */

session_start();

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

echo "<h2>Contact Delete Functionality Test</h2>";

// Test 1: Check if delete_contact.php exists and is accessible
echo "<h3>Test 1: Individual Delete Handler</h3>";
$delete_contact_file = '../admin/ajax/delete_contact.php';
if (file_exists($delete_contact_file)) {
    echo "✅ delete_contact.php exists<br>";
} else {
    echo "❌ delete_contact.php not found<br>";
}

// Test 2: Check if bulk_delete_contacts.php exists and is accessible
echo "<h3>Test 2: Bulk Delete Handler</h3>";
$bulk_delete_file = '../admin/ajax/bulk_delete_contacts.php';
if (file_exists($bulk_delete_file)) {
    echo "✅ bulk_delete_contacts.php exists<br>";
} else {
    echo "❌ bulk_delete_contacts.php not found<br>";
}

// Test 3: Check database structure
echo "<h3>Test 3: Database Structure</h3>";
try {
    // Check if contacts table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'contacts'");
    if ($stmt->rowCount() > 0) {
        echo "✅ contacts table exists<br>";
        
        // Check table structure
        $stmt = $conn->query("DESCRIBE contacts");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "Columns: " . implode(', ', $columns) . "<br>";
    } else {
        echo "❌ contacts table not found<br>";
    }
    
    // Check related tables
    $related_tables = ['contact_group_members', 'contact_email_logs', 'email_tracking'];
    foreach ($related_tables as $table) {
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ $table table exists<br>";
        } else {
            echo "⚠️ $table table not found (may not be critical)<br>";
        }
    }
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 4: Check current contacts
echo "<h3>Test 4: Current Contacts</h3>";
try {
    $stmt = $conn->query("SELECT COUNT(*) FROM contacts");
    $count = $stmt->fetchColumn();
    echo "Total contacts: $count<br>";
    
    if ($count > 0) {
        $stmt = $conn->query("SELECT id, name, email FROM contacts LIMIT 5");
        $contacts = $stmt->fetchAll();
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th></tr>";
        foreach ($contacts as $contact) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($contact['id']) . "</td>";
            echo "<td>" . htmlspecialchars($contact['name']) . "</td>";
            echo "<td>" . htmlspecialchars($contact['email']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (PDOException $e) {
    echo "❌ Error fetching contacts: " . $e->getMessage() . "<br>";
}

// Test 5: Test CSRF token generation
echo "<h3>Test 5: CSRF Token</h3>";
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
echo "✅ CSRF token generated: " . substr($_SESSION['csrf_token'], 0, 10) . "...<br>";

echo "<h3>Test Complete</h3>";
echo "<p><a href='../admin/contacts.php'>Go to Contacts Page</a></p>";
?>
