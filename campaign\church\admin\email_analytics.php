<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Get filters and pagination parameters
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$emailType = isset($_GET['email_type']) ? $_GET['email_type'] : 'all';
$templateId = isset($_GET['template_id']) ? intval($_GET['template_id']) : 0;
$status = isset($_GET['status']) ? $_GET['status'] : 'all';
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'date_desc';
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$tag = isset($_GET['tag']) ? trim($_GET['tag']) : '';

// Validate the limit to be one of the allowed values
$allowedLimits = [10, 20, 50, 100];
if (!in_array($limit, $allowedLimits)) {
    $limit = 20; // Default to 20 if an invalid limit is provided
}

// Set offset for pagination
$offset = ($page - 1) * $limit;

// Build the WHERE clause based on filters
$whereClause = "WHERE 1=1";
$params = [];

if (!empty($startDate) && !empty($endDate)) {
    $whereClause .= " AND el.sent_at BETWEEN ? AND DATE_ADD(?, INTERVAL 1 DAY)";
    $params[] = $startDate;
    $params[] = $endDate;
}
    
    if ($emailType !== 'all') {
    $whereClause .= " AND el.email_type = ?";
        $params[] = $emailType;
    }
    
if ($templateId > 0) {
    $whereClause .= " AND el.template_id = ?";
    $params[] = $templateId;
}

if ($status !== 'all') {
    $whereClause .= " AND el.status = ?";
    $params[] = $status;
}

if (!empty($search)) {
    $whereClause .= " AND (m.full_name LIKE ? OR m.email LIKE ? OR el.subject LIKE ? OR et.template_name LIKE ?)";
    $searchTerm = "%$search%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if (!empty($tag)) {
    if ($tag === 'birthday') {
        $whereClause .= " AND (el.email_type = 'birthday' OR et.is_birthday_template = 1)";
    } else if ($tag === 'problem') {
        $whereClause .= " AND (m.email IS NULL OR m.email = '' OR el.status = 'failed')";
    } else {
        $whereClause .= " AND el.email_type = ?";
        $params[] = $tag;
    }
}

// Build the ORDER BY clause based on sort parameter
$orderClause = "ORDER BY el.sent_at DESC";
switch ($sort) {
    case 'date_asc':
        $orderClause = "ORDER BY el.sent_at ASC";
        break;
    case 'date_desc':
        $orderClause = "ORDER BY el.sent_at DESC";
        break;
    case 'name_asc':
        $orderClause = "ORDER BY m.full_name ASC";
        break;
    case 'name_desc':
        $orderClause = "ORDER BY m.full_name DESC";
        break;
    case 'subject_asc':
        $orderClause = "ORDER BY el.subject ASC";
        break;
    case 'subject_desc':
        $orderClause = "ORDER BY el.subject DESC";
        break;
    case 'status_asc':
        $orderClause = "ORDER BY el.status ASC";
        break;
    case 'status_desc':
        $orderClause = "ORDER BY el.status DESC";
        break;
    case 'type_asc':
        $orderClause = "ORDER BY el.email_type ASC";
        break;
    case 'type_desc':
        $orderClause = "ORDER BY el.email_type DESC";
        break;
}

// Get total count for pagination
$totalRecords = 0;
try {
    $countQuery = "SELECT COUNT(*) FROM email_logs el 
                   LEFT JOIN members m ON el.member_id = m.id
                   LEFT JOIN email_templates et ON el.template_id = et.id
                   $whereClause";
    $stmt = $pdo->prepare($countQuery);
    $stmt->execute($params);
    $totalRecords = $stmt->fetchColumn();
} catch (PDOException $e) {
    $error = "Error counting records: " . $e->getMessage();
}

// Calculate total pages
$totalPages = ceil($totalRecords / $limit);

// Get detailed email logs with pagination
$emailLogs = [];
try {
    $query = "SELECT 
                el.id,
                el.member_id,
                el.template_id,
                el.email_type,
                el.subject,
                el.sent_at,
                el.status,
                el.error_message,
                m.full_name, 
                m.email,
                et.template_name,
                et.template_category,
                et.is_birthday_template,
                CASE WHEN tr.opened_at IS NOT NULL THEN 1 ELSE 0 END as is_opened,
                tr.opened_at,
                tr.opened_count
              FROM 
                email_logs el
              LEFT JOIN 
                members m ON el.member_id = m.id
              LEFT JOIN 
                email_templates et ON el.template_id = et.id
              LEFT JOIN 
                email_tracking tr ON el.member_id = tr.member_id AND DATE(el.sent_at) = DATE(tr.sent_at)
              $whereClause
              $orderClause
              LIMIT $offset, $limit";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $emailLogs = $stmt->fetchAll();
    
    // Ensure that full_name and email are always set
    foreach ($emailLogs as $key => $log) {
        if (!isset($log['full_name'])) {
            $emailLogs[$key]['full_name'] = 'Unknown Member (ID: ' . $log['member_id'] . ')';
        }
        if (!isset($log['email'])) {
            $emailLogs[$key]['email'] = '';
        }
    }
} catch (PDOException $e) {
    $error = "Error fetching email logs: " . $e->getMessage();
}

// Get available email types for filter
$availableEmailTypes = [];
try {
    $query = "SELECT DISTINCT email_type FROM email_logs WHERE email_type IS NOT NULL ORDER BY email_type";
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $availableEmailTypes = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    // Ignore error
}

// Get available templates for filter
$availableTemplates = [];
try {
    $query = "SELECT id, template_name, template_category FROM email_templates ORDER BY template_name";
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $availableTemplates = $stmt->fetchAll();
} catch (PDOException $e) {
    // Ignore error
}

// Get available statuses for filter
$availableStatuses = [];
try {
    $query = "SELECT DISTINCT status FROM email_logs WHERE status IS NOT NULL ORDER BY status";
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $availableStatuses = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    // Ignore error
}

// Get summary statistics
$summaryStats = [];
try {
    $query = "SELECT 
                COUNT(*) as total_emails,
                SUM(CASE WHEN el.status = 'success' THEN 1 ELSE 0 END) as successful_emails,
                SUM(CASE WHEN el.status = 'failed' THEN 1 ELSE 0 END) as failed_emails,
                COUNT(DISTINCT el.member_id) as unique_recipients,
                COUNT(DISTINCT el.template_id) as templates_used,
                SUM(CASE WHEN tr.opened_at IS NOT NULL THEN 1 ELSE 0 END) as emails_opened,
                ROUND(
                    SUM(CASE WHEN tr.opened_at IS NOT NULL THEN 1 ELSE 0 END) / 
                    NULLIF(COUNT(*), 0) * 100, 
                    2
                ) as open_rate
              FROM 
                email_logs el
              LEFT JOIN 
                email_tracking tr ON el.member_id = tr.member_id AND DATE(el.sent_at) = DATE(tr.sent_at)";
    
    // We need to add the WHERE clauses without the member table joins for this particular query
    $whereClauseSummary = "WHERE 1=1";
    $paramsSummary = [];
    
    if (!empty($startDate) && !empty($endDate)) {
        $whereClauseSummary .= " AND el.sent_at BETWEEN ? AND DATE_ADD(?, INTERVAL 1 DAY)";
        $paramsSummary[] = $startDate;
        $paramsSummary[] = $endDate;
    }
    
    if ($emailType !== 'all') {
        $whereClauseSummary .= " AND el.email_type = ?";
        $paramsSummary[] = $emailType;
    }
    
    if ($templateId > 0) {
        $whereClauseSummary .= " AND el.template_id = ?";
        $paramsSummary[] = $templateId;
    }
    
    if ($status !== 'all') {
        $whereClauseSummary .= " AND el.status = ?";
        $paramsSummary[] = $status;
    }
    
    // Append the WHERE clause to the query
    $query .= ' ' . $whereClauseSummary;
              
    $stmt = $pdo->prepare($query);
    $stmt->execute($paramsSummary);
    $summaryStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Ensure all expected keys exist with default values if null
    $expectedKeys = ['total_emails', 'successful_emails', 'failed_emails', 'unique_recipients', 'templates_used', 'emails_opened', 'open_rate'];
    foreach ($expectedKeys as $key) {
        if (!isset($summaryStats[$key]) || is_null($summaryStats[$key])) {
            $summaryStats[$key] = 0;
        }
    }
    
} catch (PDOException $e) {
    $error = "Error fetching summary statistics: " . $e->getMessage();
    // Initialize default values for summary stats to prevent undefined index errors
    $summaryStats = [
        'total_emails' => 0,
        'successful_emails' => 0,
        'failed_emails' => 0,
        'unique_recipients' => 0,
        'templates_used' => 0,
        'emails_opened' => 0,
        'open_rate' => 0
    ];
}

// Get tag statistics
$tagStats = [];
try {
    $query = "SELECT 
                el.email_type as tag_name,
                COUNT(*) as count,
                SUM(CASE WHEN tr.opened_at IS NOT NULL THEN 1 ELSE 0 END) as opened,
                ROUND(SUM(CASE WHEN tr.opened_at IS NOT NULL THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) as open_rate
              FROM 
                email_logs el
              LEFT JOIN 
                email_tracking tr ON el.member_id = tr.member_id AND DATE(el.sent_at) = DATE(tr.sent_at)
              WHERE
                el.email_type IS NOT NULL
              GROUP BY 
                el.email_type
              ORDER BY 
                count DESC";
              
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $tagStats = $stmt->fetchAll();
    
    // Add a special "problem" tag that counts emails with issues
    $query = "SELECT 
                'problem' as tag_name,
                COUNT(*) as count,
                0 as opened,
                0 as open_rate
              FROM 
                email_logs el
              LEFT JOIN 
                members m ON el.member_id = m.id
              WHERE
                m.email IS NULL OR m.email = '' OR el.status = 'failed'";
              
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $problemStats = $stmt->fetch();
    
    if ($problemStats && $problemStats['count'] > 0) {
        $tagStats[] = $problemStats;
    }
} catch (PDOException $e) {
    $error = "Error fetching tag statistics: " . $e->getMessage();
}

// Get time-based analytics (emails per day)
$timeStats = [];
try {
    $query = "SELECT 
                DATE(el.sent_at) as date,
                COUNT(*) AS emails_sent,
                SUM(CASE WHEN el.status = 'success' THEN 1 ELSE 0 END) as successful,
                SUM(CASE WHEN el.status = 'failed' THEN 1 ELSE 0 END) as failed,
                SUM(CASE WHEN tr.opened_at IS NOT NULL THEN 1 ELSE 0 END) as opened,
                GROUP_CONCAT(DISTINCT el.email_type) as email_types
              FROM 
                email_logs el
              LEFT JOIN 
                email_tracking tr ON el.member_id = tr.member_id AND DATE(el.sent_at) = DATE(tr.sent_at)
              GROUP BY 
                DATE(el.sent_at)
              ORDER BY 
                DATE(el.sent_at) DESC
              LIMIT 14";
              
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $timeStats = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = "Error fetching time-based statistics: " . $e->getMessage();
}

// Close the database connection
$conn = null;

// Set page variables
$page_title = 'Email Analytics';
$page_header = 'Email Analytics Dashboard';
$page_description = 'Track and analyze email performance metrics';

// Include header
include 'includes/header.php';

// Handle export requests
if (isset($_GET['export']) && $_GET['export'] === 'csv') {
    // Set headers for CSV download
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="email_analytics_' . date('Y-m-d') . '.csv"');

    // Create CSV output
    $output = fopen('php://output', 'w');

    // CSV headers
    fputcsv($output, [
        'Date', 'Email Type', 'Template', 'Recipient', 'Email', 'Status',
        'Sent At', 'Opened', 'Opened At', 'Open Count'
    ]);

    // Export data
    foreach ($emailLogs as $log) {
        fputcsv($output, [
            date('Y-m-d', strtotime($log['sent_at'])),
            $log['email_type'],
            $log['template_name'] ?? 'N/A',
            $log['full_name'] ?? 'N/A',
            $log['email'] ?? 'N/A',
            ucfirst($log['status']),
            $log['sent_at'],
            $log['is_opened'] ? 'Yes' : 'No',
            $log['opened_at'] ?? 'N/A',
            $log['opened_count'] ?? 0
        ]);
    }

    fclose($output);
    exit;
}
?>

<style>
    .analytics-card {
        transition: all 0.3s ease;
        border-radius: 10px;
    }
    .analytics-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .tag-badge {
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .tag-badge:hover {
        opacity: 0.8;
    }
    .text-success-light {
        color: #28a745;
        opacity: 0.8;
    }
    .text-danger-light {
        color: #dc3545;
        opacity: 0.8;
    }
    .bg-light-hover:hover {
        background-color: rgba(0,0,0,0.03);
    }
    .stats-icon {
        font-size: 2rem;
        opacity: 0.7;
    }
    .chart-container {
        height: 400px; /* Taller for more dramatic charts */
        position: relative;
    }
    .pagination-info {
        color: #6c757d;
        font-size: 0.9rem;
    }
    .filter-badge {
        margin-right: 5px;
        font-size: 0.8rem;
    }
    /* Notebook-style table */
    .table-notebook {
        background: #fff;
        border-collapse: collapse;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .table-notebook thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        position: sticky;
        top: 0;
        z-index: 10;
        white-space: nowrap;
    }
    .table-notebook tbody tr {
        border-bottom: 1px solid #f2f2f2;
        background-image: linear-gradient(transparent 23px, #e5e5f7 24px, transparent 24px);
        background-size: 100% 24px;
    }
    .table-notebook tbody tr:hover {
        background-color: rgba(0,0,0,0.03);
        background-image: linear-gradient(transparent 23px, #d4d4e7 24px, transparent 24px);
    }
    .table-notebook tbody td {
        padding: 0.75rem 0.5rem;
        vertical-align: middle;
        border-right: 1px solid #f2f2f2;
    }
    .table-notebook tbody td:last-child {
        border-right: none;
    }
    .table-notebook tbody tr:nth-child(odd) {
        background-color: rgba(0,0,0,0.01);
    }
    .table-responsive {
        overflow-x: auto;
        max-width: 100%;
        margin-bottom: 1rem;
        -webkit-overflow-scrolling: touch;
    }
    .empty-email {
        font-style: italic;
        color: #dc3545;
    }
    .table-container {
        position: relative;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    }
    .recipient-name {
        color: #3498db;
        font-weight: 500;
    }
    .template-name {
        color: #9b59b6;
        font-weight: 500;
    }
    .date-compact {
        white-space: nowrap;
        color: #7f8c8d;
        font-size: 0.85rem;
    }
    .tab-content {
        padding: 20px 0;
    }
    .nav-tabs .nav-link {
        color: #495057;
        font-weight: 500;
        padding: 1rem 1.5rem;
        border: none;
        border-bottom: 3px solid transparent;
        transition: all 0.2s ease;
    }
    .nav-tabs .nav-link:hover {
        border-color: #e9ecef;
        isolation: isolate;
    }
    .nav-tabs .nav-link.active {
        color: #007bff;
        border-bottom-color: #007bff;
        background: transparent;
    }
    .nav-tabs .nav-link:hover:not(.active) {
        border-bottom: 3px solid #dee2e6;
    }
    .card-header-tabs {
        margin-right: 0;
        margin-bottom: -1rem;
        margin-left: 0;
        border-bottom: 0;
    }
    .chart-dramatic {
        background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    .chart-dramatic:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    }
    @media (max-width: 1400px) {
        /* 14 inch screens */
        .chart-container {
            height: 300px;
        }
        .table-notebook tbody td {
            padding: 0.6rem 0.4rem;
        }
    }
    @media (max-width: 1200px) {
        /* 13 inch screens */
        .chart-container {
            height: 250px;
        }
        .table-notebook tbody td {
            padding: 0.5rem 0.3rem;
            font-size: 0.9rem;
        }
    }
    @media (max-width: 992px) {
        /* Tablet */
        .table-notebook tbody td {
            padding: 0.5rem;
        }
        .analytics-card {
            margin-bottom: 1rem;
        }
        .chart-container {
            height: 220px;
        }
    }
    @media (max-width: 768px) {
        /* Mobile */
        .table-responsive {
            border: 0;
        }
        .table-notebook {
            width: 100%;
        }
        .table-notebook thead {
            display: none;
        }
        .table-notebook tbody tr {
            display: block;
            margin-bottom: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            background-image: none;
        }
        .table-notebook tbody td {
            display: block;
            text-align: right;
            padding: 0.75rem;
            border-right: none;
            border-bottom: 1px solid #f2f2f2;
            position: relative;
            padding-left: 50%;
        }
        .table-notebook tbody td:before {
            content: attr(data-label);
            position: absolute;
            left: 0.75rem;
            font-weight: bold;
            text-align: left;
            width: 45%;
        }
        .table-notebook tbody td:last-child {
            border-bottom: none;
        }
        .chart-container {
            height: 300px;
        }
        .nav-tabs .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
        }
    }
    .pagination .page-link {
        background-color: #fff;
        border-color: #dee2e6;
    }
    .pagination .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
    }
    .pagination .page-item.disabled .page-link {
        color: #6c757d;
    }
</style>

<!-- Dashboard Header with Filter -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-bar-chart-line me-2"></i> Email Analytics Dashboard
                </h5>
            </div>
    <div class="card-body">
                <form method="get" id="filterForm">
                    <div class="row g-3 mb-3">
                        <div class="col-md-3 col-sm-6">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo htmlspecialchars($startDate); ?>">
                    </div>
                        <div class="col-md-3 col-sm-6">
                        <label for="end_date" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo htmlspecialchars($endDate); ?>">
                </div>
                        <div class="col-md-3 col-sm-6">
                        <label for="email_type" class="form-label">Email Type</label>
                        <select class="form-select" id="email_type" name="email_type">
                                <option value="all">All Types</option>
                                <?php foreach ($availableEmailTypes as $type): ?>
                                    <option value="<?php echo htmlspecialchars($type); ?>" <?php echo $emailType === $type ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars(ucfirst($type)); ?>
                                    </option>
                                <?php endforeach; ?>
                        </select>
                    </div>
                        <div class="col-md-3 col-sm-6">
                            <label for="template_id" class="form-label">Email Template</label>
                            <select class="form-select" id="template_id" name="template_id">
                                <option value="0">All Templates</option>
                                <?php foreach ($availableTemplates as $template): ?>
                                    <option value="<?php echo $template['id']; ?>" <?php echo $templateId == $template['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($template['template_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
            </div>
            </div>
                    <div class="row g-3 mb-3">
                        <div class="col-md-3 col-sm-6">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all">All Statuses</option>
                                <?php foreach ($availableStatuses as $stat): ?>
                                    <option value="<?php echo htmlspecialchars($stat); ?>" <?php echo $status === $stat ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars(ucfirst($stat)); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
        </div>
                        <div class="col-md-3 col-sm-6">
                            <label for="sort" class="form-label">Sort By</label>
                            <select class="form-select" id="sort" name="sort">
                                <option value="date_desc" <?php echo $sort === 'date_desc' ? 'selected' : ''; ?>>Date (Newest First)</option>
                                <option value="date_asc" <?php echo $sort === 'date_asc' ? 'selected' : ''; ?>>Date (Oldest First)</option>
                                <option value="name_asc" <?php echo $sort === 'name_asc' ? 'selected' : ''; ?>>Recipient Name (A-Z)</option>
                                <option value="name_desc" <?php echo $sort === 'name_desc' ? 'selected' : ''; ?>>Recipient Name (Z-A)</option>
                                <option value="subject_asc" <?php echo $sort === 'subject_asc' ? 'selected' : ''; ?>>Subject (A-Z)</option>
                                <option value="subject_desc" <?php echo $sort === 'subject_desc' ? 'selected' : ''; ?>>Subject (Z-A)</option>
                                <option value="status_asc" <?php echo $sort === 'status_asc' ? 'selected' : ''; ?>>Status (A-Z)</option>
                                <option value="status_desc" <?php echo $sort === 'status_desc' ? 'selected' : ''; ?>>Status (Z-A)</option>
                                <option value="type_asc" <?php echo $sort === 'type_asc' ? 'selected' : ''; ?>>Email Type (A-Z)</option>
                                <option value="type_desc" <?php echo $sort === 'type_desc' ? 'selected' : ''; ?>>Email Type (Z-A)</option>
                            </select>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <label for="limit" class="form-label">Records Per Page</label>
                            <select class="form-select" id="limit" name="limit">
                                <option value="10" <?php echo $limit === 10 ? 'selected' : ''; ?>>10</option>
                                <option value="20" <?php echo $limit === 20 ? 'selected' : ''; ?>>20</option>
                                <option value="50" <?php echo $limit === 50 ? 'selected' : ''; ?>>50</option>
                                <option value="100" <?php echo $limit === 100 ? 'selected' : ''; ?>>100</option>
                        </select>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <label for="search" class="form-label">Search</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="search" name="search" placeholder="Search emails..." value="<?php echo htmlspecialchars($search); ?>">
                                <button class="btn btn-primary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
    </div>
</div>

                    <!-- Tag Selection -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <label class="form-label">Quick Tags</label><br>
                            <a href="?tag=&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>" class="badge bg-secondary tag-badge me-1 <?php echo empty($tag) ? 'bg-dark' : ''; ?>">All</a>
                            <?php foreach ($tagStats as $tagStat): ?>
                                <?php if ($tagStat['tag_name'] === 'problem'): ?>
                                    <a href="?tag=problem&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>" 
                                       class="badge bg-danger tag-badge me-1 <?php echo $tag === 'problem' ? 'bg-dark' : ''; ?>">
                                        Issues <i class="bi bi-exclamation-triangle-fill"></i>
                                        <span class="badge bg-light text-dark"><?php echo $tagStat['count']; ?></span>
                                    </a>
                                <?php else: ?>
                                    <a href="?tag=<?php echo urlencode($tagStat['tag_name']); ?>&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>" 
                                       class="badge bg-primary tag-badge me-1 <?php echo $tag === $tagStat['tag_name'] ? 'bg-dark' : ''; ?>">
                                        <?php echo htmlspecialchars(ucfirst($tagStat['tag_name'])); ?> 
                                        <span class="badge bg-light text-dark"><?php echo $tagStat['count']; ?></span>
                                    </a>
                                <?php endif; ?>
                            <?php endforeach; ?>
                            <a href="?tag=birthday&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>" class="badge bg-warning text-dark tag-badge me-1 <?php echo $tag === 'birthday' ? 'bg-dark text-white' : ''; ?>">
                                Birthday <i class="bi bi-cake2"></i>
                            </a>
            </div>
                    </div>
                    
                    <!-- Active Filters Summary -->
                    <?php if (!empty($search) || $emailType !== 'all' || $templateId > 0 || $status !== 'all' || !empty($tag)): ?>
                    <div class="alert alert-info mb-3">
                        <span class="fw-bold">Active Filters:</span>
                        <?php if (!empty($search)): ?>
                            <span class="badge bg-secondary filter-badge">Search: <?php echo htmlspecialchars($search); ?> <a href="#" class="text-white ms-1" onclick="clearFilter('search'); return false;"><i class="bi bi-x-circle"></i></a></span>
                        <?php endif; ?>
                        <?php if ($emailType !== 'all'): ?>
                            <span class="badge bg-secondary filter-badge">Type: <?php echo htmlspecialchars(ucfirst($emailType)); ?> <a href="#" class="text-white ms-1" onclick="clearFilter('email_type'); return false;"><i class="bi bi-x-circle"></i></a></span>
                        <?php endif; ?>
                        <?php if ($templateId > 0): ?>
                            <?php foreach ($availableTemplates as $template): ?>
                                <?php if ($template['id'] == $templateId): ?>
                                    <span class="badge bg-secondary filter-badge">Template: <?php echo htmlspecialchars($template['template_name']); ?> <a href="#" class="text-white ms-1" onclick="clearFilter('template_id'); return false;"><i class="bi bi-x-circle"></i></a></span>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <?php if ($status !== 'all'): ?>
                            <span class="badge bg-secondary filter-badge">Status: <?php echo htmlspecialchars(ucfirst($status)); ?> <a href="#" class="text-white ms-1" onclick="clearFilter('status'); return false;"><i class="bi bi-x-circle"></i></a></span>
                        <?php endif; ?>
                        <?php if (!empty($tag)): ?>
                            <span class="badge bg-secondary filter-badge">Tag: <?php echo htmlspecialchars(ucfirst($tag)); ?> <a href="#" class="text-white ms-1" onclick="clearFilter('tag'); return false;"><i class="bi bi-x-circle"></i></a></span>
                        <?php endif; ?>
                        <a href="email_analytics.php" class="btn btn-sm btn-outline-secondary ms-2">Clear All Filters</a>
                        </div>
                    <?php endif; ?>
        </form>
                    </div>
                        </div>
                    </div>
                        </div>

<!-- Summary Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card analytics-card border-left-primary h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Emails</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($summaryStats['total_emails']); ?></div>
                        <div class="text-muted small mt-2">
                            <span class="text-success me-2"><i class="bi bi-check-circle"></i> <?php echo number_format($summaryStats['successful_emails']); ?> successful</span>
                            <span class="text-danger"><i class="bi bi-x-circle"></i> <?php echo number_format($summaryStats['failed_emails']); ?> failed</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-envelope stats-icon text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card analytics-card border-left-success h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Success Rate</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php 
                            $successRate = $summaryStats['total_emails'] > 0 
                                ? round(($summaryStats['successful_emails'] / $summaryStats['total_emails']) * 100, 1)
                                : 0;
                            echo $successRate . '%';
                            ?>
                        </div>
                        <div class="text-muted small mt-2">
                            <span class="text-primary"><?php echo number_format($summaryStats['successful_emails']); ?> successful out of <?php echo number_format($summaryStats['total_emails']); ?></span>
                    </div>
                        </div>
                    <div class="col-auto">
                        <i class="bi bi-check-circle stats-icon text-success"></i>
                    </div>
                        </div>
                        </div>
                    </div>
                </div>
                
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card analytics-card border-left-info h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Recipients</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($summaryStats['unique_recipients']); ?></div>
                        <div class="text-muted small mt-2">
                            <span class="text-primary">Unique members reached</span>
                </div>
            </div>
                    <div class="col-auto">
                        <i class="bi bi-people stats-icon text-info"></i>
        </div>
    </div>
</div>
            </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card analytics-card border-left-warning h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Templates</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($summaryStats['templates_used']); ?></div>
                        <div class="text-muted small mt-2">
                            <span class="text-primary">Different templates used</span>
                </div>
            </div>
                    <div class="col-auto">
                        <i class="bi bi-file-richtext stats-icon text-warning"></i>
        </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Interactive Charts Section -->
<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <div class="card chart-dramatic">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i> Email Performance Over Time
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="timeChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card chart-dramatic">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-pie-chart me-2"></i> Status Distribution
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="card chart-dramatic">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-bar-chart me-2"></i> Performance by Email Type
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="typeChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card chart-dramatic">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="bi bi-speedometer2 me-2"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="?export=csv&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>&email_type=<?php echo $emailType; ?>&template_id=<?php echo $templateId; ?>&status=<?php echo $status; ?>"
                       class="btn btn-outline-primary">
                        <i class="bi bi-download me-2"></i> Export to CSV
                    </a>
                    <button type="button" class="btn btn-outline-success" onclick="refreshCharts()">
                        <i class="bi bi-arrow-clockwise me-2"></i> Refresh Charts
                    </button>
                    <a href="bulk_email.php" class="btn btn-outline-info">
                        <i class="bi bi-envelope-plus me-2"></i> Send New Email
                    </a>
                    <a href="email_templates.php" class="btn btn-outline-warning">
                        <i class="bi bi-file-richtext me-2"></i> Manage Templates
                    </a>
                    <a href="recipient_analytics.php" class="btn btn-outline-secondary">
                        <i class="bi bi-person-lines-fill me-2"></i> Recipient Analytics
                    </a>
                    <a href="ab_testing.php" class="btn btn-outline-dark">
                        <i class="bi bi-graph-up-arrow me-2"></i> A/B Testing
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Logs Table -->
<div class="card mb-4">
    <div class="card-header bg-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="bi bi-envelope-paper me-2"></i> Email Logs
            </h5>
            <div class="pagination-info">
                Showing <?php echo min($offset + 1, $totalRecords); ?> to <?php echo min($offset + $limit, $totalRecords); ?> of <?php echo number_format($totalRecords); ?> entries
                                                            </div>
                                                        </div>
                                                    </div>
    <div class="card-body">
                                                    <?php 
        // Check for potential issues in the data
        $missingEmails = 0;
        $failedEmails = 0;
        foreach ($emailLogs as $log) {
            if (empty($log['email'])) {
                $missingEmails++;
            }
            if ($log['status'] === 'failed') {
                $failedEmails++;
            }
        }
        
        // Display notifications if issues are found
        if ($missingEmails > 0 || $failedEmails > 0):
        ?>
        <div class="alert alert-warning alert-dismissible fade show mb-3" role="alert">
            <h5 class="alert-heading"><i class="bi bi-exclamation-triangle-fill me-2"></i>Attention Required</h5>
            <?php if ($missingEmails > 0): ?>
                <p class="mb-1"><strong><?php echo $missingEmails; ?> <?php echo $missingEmails === 1 ? 'email' : 'emails'; ?></strong> in this view <?php echo $missingEmails === 1 ? 'has' : 'have'; ?> missing recipient email addresses. These emails cannot be delivered.</p>
                                    <?php endif; ?>
            <?php if ($failedEmails > 0): ?>
                <p class="mb-1"><strong><?php echo $failedEmails; ?> <?php echo $failedEmails === 1 ? 'email' : 'emails'; ?></strong> in this view <?php echo $failedEmails === 1 ? 'has' : 'have'; ?> failed to send. Hover over the "Failed" badge to see the error message.</p>
            <?php endif; ?>
            <p class="mb-0 mt-2">
                <a href="members.php" class="btn btn-sm btn-outline-primary me-2">
                    <i class="bi bi-people"></i> Manage Members
                </a>
                <a href="email_templates.php" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-envelope"></i> Manage Email Templates
                </a>
            </p>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
        <?php endif; ?>
        
        <?php if (count($emailLogs) > 0): ?>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-notebook">
                        <thead>
                            <tr>
                                <th width="5%">ID</th>
                                <th width="15%">Date</th>
                                <th width="25%">Recipient</th>
                                <th width="10%">Type</th>
                                <th width="30%">Subject/Template</th>
                                <th width="15%">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                            <?php foreach ($emailLogs as $log): ?>
                                <tr>
                                    <td data-label="ID"><?php echo $log['id']; ?></td>
                                    <td data-label="Date">
                                        <span class="date-compact"><?php echo date('M d, g:i a', strtotime($log['sent_at'])); ?></span>
                                    </td>
                                    <td data-label="Recipient">
                                        <a href="view_member.php?id=<?php echo $log['member_id']; ?>" class="text-decoration-none recipient-name">
                                            <?php echo htmlspecialchars($log['full_name']); ?>
                                        </a>
                                        <div class="small <?php echo empty($log['email']) ? 'empty-email' : 'text-muted'; ?>">
                                                    <?php 
                                            if (empty($log['email'])) {
                                                echo '<i class="bi bi-exclamation-triangle-fill"></i> No email address';
                                            } else {
                                                echo htmlspecialchars($log['email']);
                                            }
                                            ?>
                                        </div>
                                                </td>
                                    <td data-label="Type">
                                                    <?php 
                                                            $badgeClass = 'bg-secondary';
                                        switch ($log['email_type']) {
                                            case 'birthday': $badgeClass = 'bg-warning text-dark'; break;
                                            case 'welcome': $badgeClass = 'bg-success'; break;
                                            case 'reminder': $badgeClass = 'bg-info'; break;
                                            case 'bulk': $badgeClass = 'bg-primary'; break;
                                            case 'b_notification': $badgeClass = 'bg-info'; // Treat as reminder
                                            default: $badgeClass = 'bg-secondary';
                                        }
                                        ?>
                                        <span class="badge <?php echo $badgeClass; ?>"><?php echo htmlspecialchars(ucfirst($log['email_type'] ?? 'Unknown')); ?></span>
                                                </td>
                                    <td data-label="Subject/Template">
                                        <div class="d-flex flex-column">
                                            <div><?php echo htmlspecialchars($log['subject'] ?? 'No Subject'); ?></div>
                                            <?php if ($log['template_id']): ?>
                                                <div class="small">
                                                    <a href="edit_template.php?id=<?php echo $log['template_id']; ?>" class="text-decoration-none template-name">
                                                        <i class="bi bi-file-earmark-text"></i> <?php echo htmlspecialchars($log['template_name'] ?? 'Unknown Template'); ?>
                                                    </a>
                                                    <?php if ($log['is_birthday_template']): ?>
                                                        <span class="badge bg-warning text-dark ms-1">Birthday</span>
                    <?php endif; ?>
    </div>
                                            <?php endif; ?>
                                                    </div>
                                                </td>
                                    <td data-label="Status">
                                        <?php if ($log['status'] === 'success'): ?>
                                            <span class="badge bg-success">Success</span>
                                        <?php elseif ($log['status'] === 'failed'): ?>
                                            <span class="badge bg-danger" data-bs-toggle="tooltip" 
                                                  title="<?php echo htmlspecialchars($log['error_message'] ?? 'Unknown error'); ?>">
                                                Failed
                                                <i class="bi bi-info-circle-fill ms-1"></i>
                                            </span>
                    <?php else: ?>
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars(ucfirst($log['status'] ?? 'Unknown')); ?></span>
                    <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                </tbody>
            </table>
                        </div>
                    </div>
        <?php else: ?>
            <div class="alert alert-info text-center">
                <i class="bi bi-info-circle me-2"></i> No email logs found matching your criteria.
                </div>
        <?php endif; ?>
    </div>
</div>

<!-- Pagination Controls -->
<?php if ($totalPages > 1): ?>
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                <a class="page-link" href="?page=<?php echo $page - 1; ?>&limit=<?php echo $limit; ?>&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>&email_type=<?php echo $emailType; ?>&template_id=<?php echo $templateId; ?>&status=<?php echo $status; ?>&sort=<?php echo $sort; ?>&search=<?php echo urlencode($search); ?>&tag=<?php echo urlencode($tag); ?>" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            
            <?php
            // Calculate range of page numbers to display
            $startPage = max(1, min($page - 2, $totalPages - 4));
            $endPage = min($totalPages, max($page + 2, 5));
            
            // Show first page if not in range
            if ($startPage > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=1&limit=<?php echo $limit; ?>&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>&email_type=<?php echo $emailType; ?>&template_id=<?php echo $templateId; ?>&status=<?php echo $status; ?>&sort=<?php echo $sort; ?>&search=<?php echo urlencode($search); ?>&tag=<?php echo urlencode($tag); ?>">1</a>
                </li>
                <?php if ($startPage > 2): ?>
                    <li class="page-item disabled">
                        <a class="page-link" href="#">...</a>
                    </li>
                <?php endif;
            endif;
            
            // Display page numbers
            for ($i = $startPage; $i <= $endPage; $i++): ?>
                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>&limit=<?php echo $limit; ?>&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>&email_type=<?php echo $emailType; ?>&template_id=<?php echo $templateId; ?>&status=<?php echo $status; ?>&sort=<?php echo $sort; ?>&search=<?php echo urlencode($search); ?>&tag=<?php echo urlencode($tag); ?>"><?php echo $i; ?></a>
                </li>
            <?php endfor;
            
            // Show last page if not in range
            if ($endPage < $totalPages): ?>
                <?php if ($endPage < $totalPages - 1): ?>
                    <li class="page-item disabled">
                        <a class="page-link" href="#">...</a>
                    </li>
                <?php endif; ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $totalPages; ?>&limit=<?php echo $limit; ?>&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>&email_type=<?php echo $emailType; ?>&template_id=<?php echo $templateId; ?>&status=<?php echo $status; ?>&sort=<?php echo $sort; ?>&search=<?php echo urlencode($search); ?>&tag=<?php echo urlencode($tag); ?>"><?php echo $totalPages; ?></a>
                </li>
            <?php endif; ?>
            
            <li class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                <a class="page-link" href="?page=<?php echo $page + 1; ?>&limit=<?php echo $limit; ?>&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>&email_type=<?php echo $emailType; ?>&template_id=<?php echo $templateId; ?>&status=<?php echo $status; ?>&sort=<?php echo $sort; ?>&search=<?php echo urlencode($search); ?>&tag=<?php echo urlencode($tag); ?>" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        </ul>
    </nav>
<?php endif; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Auto-submit form when filter changes
    document.querySelectorAll('#filterForm select, #filterForm input[type="date"]').forEach(element => {
        element.addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });
    
    // Handle failed email notifications more elegantly
    document.querySelectorAll('.badge.bg-danger').forEach(badge => {
        badge.addEventListener('mouseenter', function() {
            const errorMessage = this.getAttribute('title');
            if (errorMessage && errorMessage.includes('no email')) {
                this.innerHTML = 'Missing Email <i class="bi bi-exclamation-circle-fill ms-1"></i>';
            }
        });
        
        badge.addEventListener('mouseleave', function() {
            this.innerHTML = 'Failed <i class="bi bi-info-circle-fill ms-1"></i>';
        });
    });
    
    // Highlight rows with missing emails
    document.querySelectorAll('.empty-email').forEach(element => {
        const row = element.closest('tr');
        if (row) {
            row.classList.add('table-warning');
        }
    });
    
    // Add smooth scrolling to table
    const tableContainer = document.querySelector('.table-responsive');
    if (tableContainer) {
        tableContainer.style.scrollBehavior = 'smooth';
    }
});

// Function to clear specific filter
function clearFilter(filterName) {
    const form = document.getElementById('filterForm');
    const input = form.elements[filterName];
    
    if (input) {
        if (input.tagName === 'SELECT') {
            input.value = filterName === 'email_type' || filterName === 'status' ? 'all' : '0';
        } else {
            input.value = '';
        }
        form.submit();
    }
}

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // Email Performance Over Time Chart
    const timeChartCtx = document.getElementById('timeChart');
    if (timeChartCtx) {
        const timeData = <?php echo json_encode(array_reverse($timeStats)); ?>;

        new Chart(timeChartCtx, {
            type: 'line',
            data: {
                labels: timeData.map(item => new Date(item.date).toLocaleDateString()),
                datasets: [{
                    label: 'Emails Sent',
                    data: timeData.map(item => item.emails_sent),
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Emails Opened',
                    data: timeData.map(item => item.opened),
                    borderColor: '#198754',
                    backgroundColor: 'rgba(25, 135, 84, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Email Performance Over Time'
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    // Email Status Distribution Chart
    const statusChartCtx = document.getElementById('statusChart');
    if (statusChartCtx) {
        const summaryData = <?php echo json_encode($summaryStats); ?>;

        new Chart(statusChartCtx, {
            type: 'doughnut',
            data: {
                labels: ['Successful', 'Failed', 'Opened'],
                datasets: [{
                    data: [
                        summaryData.successful_emails || 0,
                        summaryData.failed_emails || 0,
                        summaryData.emails_opened || 0
                    ],
                    backgroundColor: [
                        '#198754',
                        '#dc3545',
                        '#0d6efd'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Email Status Distribution'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Open Rate by Email Type Chart
    const typeChartCtx = document.getElementById('typeChart');
    if (typeChartCtx) {
        // Get email type statistics
        fetch('ajax/get_email_type_stats.php')
            .then(response => response.json())
            .then(data => {
                new Chart(typeChartCtx, {
                    type: 'bar',
                    data: {
                        labels: data.map(item => item.email_type),
                        datasets: [{
                            label: 'Total Sent',
                            data: data.map(item => item.total_sent),
                            backgroundColor: 'rgba(13, 110, 253, 0.7)',
                            borderColor: '#0d6efd',
                            borderWidth: 1
                        }, {
                            label: 'Opened',
                            data: data.map(item => item.total_opened),
                            backgroundColor: 'rgba(25, 135, 84, 0.7)',
                            borderColor: '#198754',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Email Performance by Type'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
            })
            .catch(error => console.error('Error loading email type stats:', error));
    }
}

// Function to refresh all charts
function refreshCharts() {
    // Show loading indicator
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(container => {
        container.style.opacity = '0.5';
    });

    // Destroy existing charts
    Chart.helpers.each(Chart.instances, function(instance) {
        instance.destroy();
    });

    // Reinitialize charts
    setTimeout(() => {
        initializeCharts();
        chartContainers.forEach(container => {
            container.style.opacity = '1';
        });
    }, 500);
}
</script>

<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<?php include 'includes/footer.php'; ?>