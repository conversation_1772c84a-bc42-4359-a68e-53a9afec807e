<?php
/**
 * Change Language via AJAX
 * Handles language switching requests
 */

// Include language system
require_once __DIR__ . '/../includes/language.php';

// Check if request is AJAX
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid request']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['language'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Language parameter required']);
    exit;
}

$language = trim($input['language']);

// Validate language
global $lang;
$availableLanguages = array_keys($lang->getAvailableLanguages());

if (!in_array($language, $availableLanguages)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid language']);
    exit;
}

// Set language
if ($lang->setLanguage($language)) {
    echo json_encode([
        'success' => true,
        'message' => 'Language changed successfully',
        'language' => $language,
        'language_name' => $lang->getAvailableLanguages()[$language],
        'direction' => $lang->getLanguageDirection($language)
    ]);
} else {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to change language']);
}
?>
