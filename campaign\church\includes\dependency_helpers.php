<?php
/**
 * Dependency Helper Functions
 * 
 * This file provides helper functions for managing external dependencies.
 */

/**
 * Get the URL for a library asset (CSS, JS, etc.)
 * If local assets are available, it returns the local path, otherwise the CDN URL
 * 
 * @param string $cdnUrl The original CDN URL
 * @param bool $forceLocal Whether to force using local assets
 * @return string The URL to use
 */
function get_asset_url($cdnUrl, $forceLocal = false) {
    static $references = null;
    static $baseUrl = null;
    
    // Initialize references if not already loaded
    if ($references === null) {
        $referencePath = dirname(__DIR__) . '/vendor/assets/references.php';
        if (file_exists($referencePath)) {
            $references = require_once $referencePath;
        } else {
            $references = [];
        }
    }
    
    // Initialize base URL if not already set
    if ($baseUrl === null) {
        $baseUrl = defined('SITE_URL') ? SITE_URL : '';
    }
    
    // If we're using local assets and we have a reference for this URL
    if (($forceLocal || defined('USE_LOCAL_ASSETS') && USE_LOCAL_ASSETS) && isset($references[$cdnUrl])) {
        return $baseUrl . '/vendor/' . $references[$cdnUrl];
    }
    
    // Otherwise, return the CDN URL
    return $cdnUrl;
}

/**
 * Output a CSS link tag for an asset
 * 
 * @param string $cdnUrl The original CDN URL
 * @param bool $forceLocal Whether to force using local assets
 */
function css_asset($cdnUrl, $forceLocal = false) {
    $url = get_asset_url($cdnUrl, $forceLocal);
    echo '<link rel="stylesheet" href="' . htmlspecialchars($url) . '">' . PHP_EOL;
}

/**
 * Output a JavaScript script tag for an asset
 * 
 * @param string $cdnUrl The original CDN URL
 * @param bool $forceLocal Whether to force using local assets
 */
function js_asset($cdnUrl, $forceLocal = false) {
    $url = get_asset_url($cdnUrl, $forceLocal);
    echo '<script src="' . htmlspecialchars($url) . '"></script>' . PHP_EOL;
}