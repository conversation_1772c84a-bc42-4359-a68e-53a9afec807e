/**
 * Accessibility Manager - WCAG 2.1 Compliance
 * Handles accessibility features, keyboard navigation, and screen reader support
 */

class AccessibilityManager {
    constructor() {
        this.isKeyboardNavigation = false;
        this.highContrast = false;
        this.reducedMotion = false;
        this.fontSize = 'normal';
        this.announcements = [];
        
        this.init();
    }
    
    init() {
        // Detect user preferences
        this.detectPreferences();
        
        // Setup keyboard navigation detection
        this.setupKeyboardDetection();
        
        // Setup skip links
        this.setupSkipLinks();
        
        // Setup ARIA live regions
        this.setupLiveRegions();
        
        // Setup form validation
        this.setupFormValidation();
        
        // Setup table sorting
        this.setupTableSorting();
        
        // Setup modal accessibility
        this.setupModalAccessibility();
        
        // Setup dropdown accessibility
        this.setupDropdownAccessibility();
        
        // Load saved preferences
        this.loadAccessibilityPreferences();
        
        console.log('Accessibility Manager initialized');
    }
    
    detectPreferences() {
        // Detect reduced motion preference
        if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            this.reducedMotion = true;
            document.body.classList.add('reduced-motion');
        }
        
        // Detect high contrast preference
        if (window.matchMedia && window.matchMedia('(prefers-contrast: high)').matches) {
            this.highContrast = true;
            document.body.classList.add('high-contrast');
        }
        
        // Listen for preference changes
        if (window.matchMedia) {
            window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
                this.reducedMotion = e.matches;
                document.body.classList.toggle('reduced-motion', e.matches);
            });
            
            window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
                this.highContrast = e.matches;
                document.body.classList.toggle('high-contrast', e.matches);
            });
        }
    }
    
    setupKeyboardDetection() {
        // Detect keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                this.isKeyboardNavigation = true;
                document.body.classList.add('keyboard-navigation');
            }
        });
        
        document.addEventListener('mousedown', () => {
            this.isKeyboardNavigation = false;
            document.body.classList.remove('keyboard-navigation');
        });
    }
    
    setupSkipLinks() {
        // Create skip link if it doesn't exist
        if (!document.querySelector('.skip-link')) {
            const skipLink = document.createElement('a');
            skipLink.href = '#main-content';
            skipLink.className = 'skip-link';
            skipLink.textContent = 'Skip to main content';
            
            document.body.insertBefore(skipLink, document.body.firstChild);
        }
        
        // Ensure main content has ID
        const mainContent = document.querySelector('.main-content, main, #content');
        if (mainContent && !mainContent.id) {
            mainContent.id = 'main-content';
        }
    }
    
    setupLiveRegions() {
        // Create ARIA live regions for announcements
        if (!document.getElementById('aria-live-polite')) {
            const politeRegion = document.createElement('div');
            politeRegion.id = 'aria-live-polite';
            politeRegion.setAttribute('aria-live', 'polite');
            politeRegion.setAttribute('aria-atomic', 'true');
            politeRegion.className = 'sr-only';
            document.body.appendChild(politeRegion);
        }
        
        if (!document.getElementById('aria-live-assertive')) {
            const assertiveRegion = document.createElement('div');
            assertiveRegion.id = 'aria-live-assertive';
            assertiveRegion.setAttribute('aria-live', 'assertive');
            assertiveRegion.setAttribute('aria-atomic', 'true');
            assertiveRegion.className = 'sr-only';
            document.body.appendChild(assertiveRegion);
        }
    }
    
    setupFormValidation() {
        // Enhanced form validation with ARIA
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (!form.matches('form')) return;
            
            const invalidFields = form.querySelectorAll(':invalid');
            if (invalidFields.length > 0) {
                e.preventDefault();
                
                // Focus first invalid field
                invalidFields[0].focus();
                
                // Announce validation errors
                this.announce(`Form has ${invalidFields.length} validation error${invalidFields.length > 1 ? 's' : ''}. Please correct and try again.`, 'assertive');
            }
        });
        
        // Real-time validation feedback
        document.addEventListener('blur', (e) => {
            const field = e.target;
            if (!field.matches('input, select, textarea')) return;
            
            this.validateField(field);
        }, true);
    }
    
    validateField(field) {
        const isValid = field.checkValidity();
        const errorId = field.id + '-error';
        let errorElement = document.getElementById(errorId);
        
        // Remove existing validation classes and messages
        field.classList.remove('is-valid', 'is-invalid');
        if (errorElement) {
            errorElement.remove();
        }
        
        if (!isValid) {
            field.classList.add('is-invalid');
            field.setAttribute('aria-invalid', 'true');
            
            // Create error message
            errorElement = document.createElement('div');
            errorElement.id = errorId;
            errorElement.className = 'invalid-feedback';
            errorElement.setAttribute('role', 'alert');
            errorElement.textContent = field.validationMessage;
            
            field.setAttribute('aria-describedby', errorId);
            field.parentNode.appendChild(errorElement);
        } else if (field.value) {
            field.classList.add('is-valid');
            field.setAttribute('aria-invalid', 'false');
            field.removeAttribute('aria-describedby');
        }
    }
    
    setupTableSorting() {
        // Add ARIA sorting attributes to sortable tables
        document.querySelectorAll('table[data-sortable] th').forEach(th => {
            if (th.hasAttribute('data-sort')) {
                th.setAttribute('role', 'columnheader');
                th.setAttribute('aria-sort', 'none');
                th.setAttribute('tabindex', '0');
                
                // Add keyboard support
                th.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        th.click();
                    }
                });
                
                // Update ARIA sort on click
                th.addEventListener('click', () => {
                    // Reset other columns
                    th.closest('table').querySelectorAll('th[aria-sort]').forEach(otherTh => {
                        if (otherTh !== th) {
                            otherTh.setAttribute('aria-sort', 'none');
                        }
                    });
                    
                    // Toggle current column
                    const currentSort = th.getAttribute('aria-sort');
                    if (currentSort === 'none' || currentSort === 'descending') {
                        th.setAttribute('aria-sort', 'ascending');
                        this.announce(`Table sorted by ${th.textContent} ascending`, 'polite');
                    } else {
                        th.setAttribute('aria-sort', 'descending');
                        this.announce(`Table sorted by ${th.textContent} descending`, 'polite');
                    }
                });
            }
        });
    }
    
    setupModalAccessibility() {
        // Enhanced modal accessibility
        document.addEventListener('shown.bs.modal', (e) => {
            const modal = e.target;
            
            // Focus management
            const focusableElements = modal.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            
            if (focusableElements.length > 0) {
                focusableElements[0].focus();
            }
            
            // Trap focus within modal
            modal.addEventListener('keydown', (e) => {
                if (e.key === 'Tab') {
                    const firstFocusable = focusableElements[0];
                    const lastFocusable = focusableElements[focusableElements.length - 1];
                    
                    if (e.shiftKey && document.activeElement === firstFocusable) {
                        e.preventDefault();
                        lastFocusable.focus();
                    } else if (!e.shiftKey && document.activeElement === lastFocusable) {
                        e.preventDefault();
                        firstFocusable.focus();
                    }
                }
            });
        });
    }
    
    setupDropdownAccessibility() {
        // Enhanced dropdown keyboard navigation
        document.addEventListener('keydown', (e) => {
            const dropdown = e.target.closest('.dropdown');
            if (!dropdown) return;
            
            const toggle = dropdown.querySelector('.dropdown-toggle');
            const menu = dropdown.querySelector('.dropdown-menu');
            const items = menu ? menu.querySelectorAll('.dropdown-item:not(.disabled)') : [];
            
            if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                e.preventDefault();
                
                if (!menu.classList.contains('show')) {
                    toggle.click();
                    return;
                }
                
                const currentIndex = Array.from(items).indexOf(document.activeElement);
                let nextIndex;
                
                if (e.key === 'ArrowDown') {
                    nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
                } else {
                    nextIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
                }
                
                items[nextIndex].focus();
            } else if (e.key === 'Escape' && menu && menu.classList.contains('show')) {
                e.preventDefault();
                toggle.click();
                toggle.focus();
            }
        });
    }
    
    // Public API methods
    announce(message, priority = 'polite') {
        const region = document.getElementById(`aria-live-${priority}`);
        if (region) {
            region.textContent = message;
            
            // Clear after announcement
            setTimeout(() => {
                region.textContent = '';
            }, 1000);
        }
    }
    
    setFontSize(size) {
        const validSizes = ['small', 'normal', 'large', 'extra-large'];
        if (!validSizes.includes(size)) return;
        
        // Remove existing font size classes
        validSizes.forEach(s => {
            document.body.classList.remove(`font-size-${s}`);
        });
        
        // Add new font size class
        document.body.classList.add(`font-size-${size}`);
        this.fontSize = size;
        
        // Save preference
        this.saveAccessibilityPreference('font_size', size);
        
        this.announce(`Font size changed to ${size}`, 'polite');
    }
    
    toggleHighContrast() {
        this.highContrast = !this.highContrast;
        document.body.classList.toggle('high-contrast', this.highContrast);
        
        // Save preference
        this.saveAccessibilityPreference('high_contrast', this.highContrast ? '1' : '0');
        
        this.announce(`High contrast ${this.highContrast ? 'enabled' : 'disabled'}`, 'polite');
    }
    
    toggleReducedMotion() {
        this.reducedMotion = !this.reducedMotion;
        document.body.classList.toggle('reduced-motion', this.reducedMotion);
        
        // Save preference
        this.saveAccessibilityPreference('reduced_motion', this.reducedMotion ? '1' : '0');
        
        this.announce(`Reduced motion ${this.reducedMotion ? 'enabled' : 'disabled'}`, 'polite');
    }
    
    saveAccessibilityPreference(key, value) {
        // Save to localStorage
        localStorage.setItem(`accessibility_${key}`, value);
        
        // Save to database
        if (window.themeManager) {
            window.themeManager.saveToUserPreferences(key, value);
        }
    }
    
    loadAccessibilityPreferences() {
        // Load font size
        const savedFontSize = localStorage.getItem('accessibility_font_size');
        if (savedFontSize) {
            this.setFontSize(savedFontSize);
        }
        
        // Load high contrast
        const savedHighContrast = localStorage.getItem('accessibility_high_contrast');
        if (savedHighContrast === '1') {
            this.toggleHighContrast();
        }
        
        // Load reduced motion
        const savedReducedMotion = localStorage.getItem('accessibility_reduced_motion');
        if (savedReducedMotion === '1') {
            this.toggleReducedMotion();
        }
    }
    
    // Color contrast checker
    checkColorContrast(foreground, background) {
        // Convert hex to RGB
        const fgRgb = this.hexToRgb(foreground);
        const bgRgb = this.hexToRgb(background);
        
        if (!fgRgb || !bgRgb) return null;
        
        // Calculate relative luminance
        const fgLuminance = this.getRelativeLuminance(fgRgb);
        const bgLuminance = this.getRelativeLuminance(bgRgb);
        
        // Calculate contrast ratio
        const lighter = Math.max(fgLuminance, bgLuminance);
        const darker = Math.min(fgLuminance, bgLuminance);
        const contrast = (lighter + 0.05) / (darker + 0.05);
        
        return {
            ratio: contrast,
            aa: contrast >= 4.5,
            aaa: contrast >= 7,
            aaLarge: contrast >= 3,
            aaaLarge: contrast >= 4.5
        };
    }
    
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }
    
    getRelativeLuminance(rgb) {
        const { r, g, b } = rgb;
        const [rs, gs, bs] = [r, g, b].map(c => {
            c = c / 255;
            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
        });
        return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
    }
}

// Initialize accessibility manager when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.accessibilityManager = new AccessibilityManager();
});

// Export for use in other scripts
window.AccessibilityManager = AccessibilityManager;
