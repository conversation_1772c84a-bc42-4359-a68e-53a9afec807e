# Birthday Email System Setup

This document provides instructions for setting up and maintaining the automatic birthday email system for Freedom Assembly Church International.

## Overview

The birthday email system automatically sends personalized emails to church members on their birthdays and in advance (2 and 3 days before). The system uses beautiful HTML templates that include the member's photo and personalized message.

## Files

- `send_birthday_reminders.php`: The main class that handles finding members with upcoming birthdays and sending emails
- `cron_birthday_reminders.php`: Script designed to be run via cron job for daily automated sending
- `admin/send_birthday_emails.php`: Admin interface for manually triggering birthday emails and viewing logs
- `track.php`: Handles email open tracking via a transparent pixel
- `admin/email_analytics.php`: Dashboard for viewing email performance metrics
- `sql/email_tracking.sql`: SQL script to create the email tracking table

## Setting Up Automated Emails

### Option 1: Using Cron Jobs (Linux/Unix Hosting)

1. Access your server's crontab:
   ```
   crontab -e
   ```

2. Add the following line to run the script daily at 7 AM:
   ```
   0 7 * * * php /path/to/your/church/cron_birthday_reminders.php >> /path/to/your/church/logs/cron.log 2>&1
   ```

3. Save and exit. The cron job will now run automatically every day.

### Option 2: Using Windows Task Scheduler

1. Open Task Scheduler
2. Create a new Basic Task
3. Name it "Church Birthday Emails"
4. Set the trigger to Daily at 7:00 AM
5. Set the action to "Start a program"
6. Program/script: `C:\path\to\php\php.exe`
7. Add arguments: `C:\path\to\your\church\cron_birthday_reminders.php`
8. Finish the wizard

### Option 3: Using Hosting Control Panel

If you're using a hosting control panel like cPanel:

1. Log in to your control panel
2. Find the "Cron Jobs" or "Scheduled Tasks" section
3. Add a new cron job
4. Set it to run daily
5. Command: `php /home/<USER>/public_html/church/cron_birthday_reminders.php`

## Email Templates

Birthday email templates are stored in the `email_templates` table with the `is_birthday_template` flag set to `TRUE`. 

### Template Placeholders

The following placeholders can be used in your templates:

#### Basic Placeholders:
- `{full_name}` or `{member_name}` or `[NAME]`: Will be replaced with the member's full name
- `{member_image}` or `{image_path}`: Will be replaced with the member's profile image
- `{DAYS}` or `[DAYS]`: Will be replaced with the number of days until the birthday (for advance notices)

#### Advanced Placeholders:
- `{occupation}`: Member's occupation
- `{phone}`: Member's phone number
- `{email}`: Member's email address
- `{address}`: Member's home address
- `{birthday_date}`: Formatted birthday date (month and day)
- `{birthday_year}`: Year of birth
- `{current_year}`: Current year
- `{current_date}`: Current date (formatted)
- `{age}`: Member's age (calculated from birth date)

### Adding New Templates

You can add new templates directly to the database. Make sure to:

1. Set `is_birthday_template` to `TRUE` for birthday templates
2. Use proper HTML with inline CSS for maximum email client compatibility
3. Include the placeholders mentioned above where appropriate

## Email Tracking

The system now includes email tracking capabilities to monitor when emails are opened.

### How It Works

1. A unique tracking pixel is added to each email
2. When the email is opened, the pixel loads from your server
3. The system records the open time, user agent, and IP address
4. This data is stored in the `email_tracking` table

### Setting Up Email Tracking

1. Run the `sql/email_tracking.sql` script to create the necessary database table
2. Make sure the `track.php` file is accessible from the web
3. Email tracking is enabled by default but can be disabled by calling `$reminder->setTracking(false)`

### Viewing Tracking Data

Access the email analytics dashboard at `admin/email_analytics.php` to view:
- Overall open rates
- Template performance metrics
- Recent email opens
- Charts and graphs of email performance

## Admin Notifications

The system now sends automatic notifications to administrators when emails fail to send.

### Notification Features

1. After each batch of emails is sent, a summary report is generated
2. If any emails failed to send, an admin notification is triggered
3. The notification includes details about which emails failed and why
4. Successful emails are also listed for reference

### Configuring Admin Notifications

The admin email is set in the `BirthdayReminder` constructor. You can change it by:
1. Modifying the default in the class (`<EMAIL>`)
2. Passing a different email when creating the reminder instance: `$reminder = new BirthdayReminder($pdo, '<EMAIL>')`

## Troubleshooting

### Checking Logs

Email sending logs are stored in:
- Database: `email_logs` table
- File: `logs/birthday_reminders.log`

### Common Issues

1. **Emails not sending**: Check SMTP settings in `send_birthday_reminders.php`
2. **Images not displaying**: Ensure member profile images exist at the paths stored in the database
3. **Cron job not running**: Check server logs and ensure PHP has permission to execute the script
4. **Tracking not working**: Make sure `track.php` is accessible and the database table exists

## Manual Operation

Administrators can manually trigger the birthday email system by:

1. Log in to the admin panel
2. Navigate to "Send Birthday Emails" page
3. Click the "Send Birthday Emails Now" button

This is useful for testing or if the automated system fails. 