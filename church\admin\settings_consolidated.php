<?php
/**
 * Consolidated Settings Management
 * Comprehensive settings management combining all settings pages functionality
 * Features: General, Contact, Social Media, Email, System, Security, Appearance, Branding, Integrations
 */

// Start session first
session_start();

// Set admin session if not already set (for testing)
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = 4;
    $_SESSION['admin_username'] = 'admin';
    $_SESSION['admin_email'] = '<EMAIL>';
    $_SESSION['admin_full_name'] = 'Church Administrator';
    $_SESSION['admin_role'] = 'admin';
    $_SESSION['CREATED'] = time();
    $_SESSION['LAST_ACTIVITY'] = time();
}

// Include database config
require_once '../config.php';

// Set page title
$page_title = 'Settings Management';

// Helper functions
function get_site_setting($key, $default = '') {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM appearance_settings WHERE setting_name = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetchColumn();
        return $result !== false ? $result : $default;
    } catch (PDOException $e) {
        return $default;
    }
}

function update_site_setting($key, $value) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            INSERT INTO appearance_settings (setting_name, setting_value, updated_at)
            VALUES (?, ?, NOW())
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
        ");
        return $stmt->execute([$key, $value]);
    } catch (PDOException $e) {
        return false;
    }
}

function update_multiple_settings($settings) {
    global $pdo;
    try {
        $pdo->beginTransaction();
        
        foreach ($settings as $key => $value) {
            update_site_setting($key, $value);
        }
        
        $pdo->commit();
        return true;
    } catch (Exception $e) {
        $pdo->rollback();
        return false;
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_general':
                    $generalSettings = [
                        'site_title' => $_POST['site_title'] ?? '',
                        'admin_title' => $_POST['admin_title'] ?? '',
                        'site_description' => $_POST['site_description'] ?? '',
                        'site_keywords' => $_POST['site_keywords'] ?? '',
                        'organization_type' => $_POST['organization_type'] ?? 'church',
                        'organization_name' => $_POST['organization_name'] ?? '',
                        'organization_mission' => $_POST['organization_mission'] ?? '',
                        'organization_vision' => $_POST['organization_vision'] ?? '',
                        'organization_values' => $_POST['organization_values'] ?? '',
                        'footer_text' => $_POST['footer_text'] ?? ''
                    ];
                    
                    if (update_multiple_settings($generalSettings)) {
                        $success_message = "General settings updated successfully!";
                    } else {
                        $error_message = "Failed to update general settings.";
                    }
                    break;
                    
                case 'update_contact':
                    $contactSettings = [
                        'contact_phone' => $_POST['contact_phone'] ?? '',
                        'contact_email' => $_POST['contact_email'] ?? '',
                        'contact_address' => $_POST['contact_address'] ?? '',
                        'contact_city' => $_POST['contact_city'] ?? '',
                        'contact_state' => $_POST['contact_state'] ?? '',
                        'contact_zip' => $_POST['contact_zip'] ?? '',
                        'contact_country' => $_POST['contact_country'] ?? '',
                        'office_hours' => $_POST['office_hours'] ?? '',
                        'emergency_contact' => $_POST['emergency_contact'] ?? ''
                    ];
                    
                    if (update_multiple_settings($contactSettings)) {
                        $success_message = "Contact information updated successfully!";
                    } else {
                        $error_message = "Failed to update contact information.";
                    }
                    break;
                    
                case 'update_social':
                    $socialSettings = [
                        'facebook_url' => $_POST['facebook_url'] ?? '',
                        'twitter_url' => $_POST['twitter_url'] ?? '',
                        'instagram_url' => $_POST['instagram_url'] ?? '',
                        'youtube_url' => $_POST['youtube_url'] ?? '',
                        'linkedin_url' => $_POST['linkedin_url'] ?? '',
                        'tiktok_url' => $_POST['tiktok_url'] ?? '',
                        'website_url' => $_POST['website_url'] ?? '',
                        'blog_url' => $_POST['blog_url'] ?? ''
                    ];
                    
                    if (update_multiple_settings($socialSettings)) {
                        $success_message = "Social media settings updated successfully!";
                    } else {
                        $error_message = "Failed to update social media settings.";
                    }
                    break;
                    
                case 'update_email':
                    $emailSettings = [
                        'smtp_host' => $_POST['smtp_host'] ?? '',
                        'smtp_port' => $_POST['smtp_port'] ?? '587',
                        'smtp_username' => $_POST['smtp_username'] ?? '',
                        'smtp_password' => $_POST['smtp_password'] ?? '',
                        'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls',
                        'from_email' => $_POST['from_email'] ?? '',
                        'from_name' => $_POST['from_name'] ?? '',
                        'reply_to_email' => $_POST['reply_to_email'] ?? '',
                        'email_signature' => $_POST['email_signature'] ?? '',
                        'enable_email_queue' => isset($_POST['enable_email_queue']) ? '1' : '0'
                    ];
                    
                    if (update_multiple_settings($emailSettings)) {
                        $success_message = "Email settings updated successfully!";
                    } else {
                        $error_message = "Failed to update email settings.";
                    }
                    break;
                    
                case 'update_system':
                    $systemSettings = [
                        'timezone' => $_POST['timezone'] ?? 'America/New_York',
                        'date_format' => $_POST['date_format'] ?? 'Y-m-d',
                        'time_format' => $_POST['time_format'] ?? 'H:i',
                        'currency_symbol' => $_POST['currency_symbol'] ?? '$',
                        'currency_code' => $_POST['currency_code'] ?? 'USD',
                        'language' => $_POST['language'] ?? 'en',
                        'items_per_page' => $_POST['items_per_page'] ?? '25',
                        'session_timeout' => $_POST['session_timeout'] ?? '3600',
                        'max_upload_size' => $_POST['max_upload_size'] ?? '10',
                        'backup_retention_days' => $_POST['backup_retention_days'] ?? '30'
                    ];
                    
                    if (update_multiple_settings($systemSettings)) {
                        $success_message = "System preferences updated successfully!";
                    } else {
                        $error_message = "Failed to update system preferences.";
                    }
                    break;
                    
                case 'update_terminology':
                    $terminologySettings = [
                        'member_term' => $_POST['member_term'] ?? 'Member',
                        'leader_term' => $_POST['leader_term'] ?? 'Pastor',
                        'group_term' => $_POST['group_term'] ?? 'Ministry',
                        'event_term' => $_POST['event_term'] ?? 'Service',
                        'donation_term' => $_POST['donation_term'] ?? 'Offering',
                        'term_members' => $_POST['term_members'] ?? 'Members',
                        'term_events' => $_POST['term_events'] ?? 'Events',
                        'term_campaigns' => $_POST['term_campaigns'] ?? 'Campaigns',
                        'term_groups' => $_POST['term_groups'] ?? 'Groups',
                        'term_leaders' => $_POST['term_leaders'] ?? 'Leaders',
                        'term_volunteers' => $_POST['term_volunteers'] ?? 'Volunteers',
                        'term_donations' => $_POST['term_donations'] ?? 'Donations',
                        'term_ministries' => $_POST['term_ministries'] ?? 'Ministries'
                    ];
                    
                    if (update_multiple_settings($terminologySettings)) {
                        $success_message = "Terminology settings updated successfully!";
                    } else {
                        $error_message = "Failed to update terminology settings.";
                    }
                    break;
                    
                case 'update_appearance':
                    $appearanceSettings = [
                        'primary_color' => $_POST['primary_color'] ?? '#007bff',
                        'secondary_color' => $_POST['secondary_color'] ?? '#6c757d',
                        'success_color' => $_POST['success_color'] ?? '#28a745',
                        'danger_color' => $_POST['danger_color'] ?? '#dc3545',
                        'warning_color' => $_POST['warning_color'] ?? '#ffc107',
                        'info_color' => $_POST['info_color'] ?? '#17a2b8',
                        'theme_mode' => $_POST['theme_mode'] ?? 'light',
                        'admin_theme' => $_POST['admin_theme'] ?? 'default',
                        'enable_dark_mode' => isset($_POST['enable_dark_mode']) ? '1' : '0',
                        'primary_font' => $_POST['primary_font'] ?? 'Inter',
                        'font_size_base' => $_POST['font_size_base'] ?? '16',
                        'sidebar_bg_color' => $_POST['sidebar_bg_color'] ?? '#343a40',
                        'sidebar_text_color' => $_POST['sidebar_text_color'] ?? '#ffffff',
                        'custom_css' => $_POST['custom_css'] ?? ''
                    ];
                    
                    if (update_multiple_settings($appearanceSettings)) {
                        $success_message = "Appearance settings updated successfully!";
                    } else {
                        $error_message = "Failed to update appearance settings.";
                    }
                    break;
                    
                case 'update_integrations':
                    $integrationSettings = [
                        'google_analytics_id' => $_POST['google_analytics_id'] ?? '',
                        'facebook_pixel_id' => $_POST['facebook_pixel_id'] ?? '',
                        'google_maps_api_key' => $_POST['google_maps_api_key'] ?? '',
                        'whatsapp_api_token' => $_POST['whatsapp_api_token'] ?? '',
                        'sms_api_key' => $_POST['sms_api_key'] ?? '',
                        'payment_gateway' => $_POST['payment_gateway'] ?? 'paypal',
                        'stripe_public_key' => $_POST['stripe_public_key'] ?? '',
                        'stripe_secret_key' => $_POST['stripe_secret_key'] ?? '',
                        'paypal_client_id' => $_POST['paypal_client_id'] ?? '',
                        'enable_api_access' => isset($_POST['enable_api_access']) ? '1' : '0'
                    ];
                    
                    if (update_multiple_settings($integrationSettings)) {
                        $success_message = "Integration settings updated successfully!";
                    } else {
                        $error_message = "Failed to update integration settings.";
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get all current settings
$allSettingKeys = [
    // General
    'site_title', 'admin_title', 'site_description', 'site_keywords', 'organization_type',
    'organization_name', 'organization_mission', 'organization_vision', 'organization_values', 'footer_text',
    // Contact
    'contact_phone', 'contact_email', 'contact_address', 'contact_city', 'contact_state',
    'contact_zip', 'contact_country', 'office_hours', 'emergency_contact',
    // Social Media
    'facebook_url', 'twitter_url', 'instagram_url', 'youtube_url', 'linkedin_url',
    'tiktok_url', 'website_url', 'blog_url',
    // Email
    'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption',
    'from_email', 'from_name', 'reply_to_email', 'email_signature', 'enable_email_queue',
    // System
    'timezone', 'date_format', 'time_format', 'currency_symbol', 'currency_code',
    'language', 'items_per_page', 'session_timeout', 'max_upload_size', 'backup_retention_days',
    // Terminology
    'member_term', 'leader_term', 'group_term', 'event_term', 'donation_term',
    'term_members', 'term_events', 'term_campaigns', 'term_groups', 'term_leaders',
    'term_volunteers', 'term_donations', 'term_ministries',
    // Appearance
    'primary_color', 'secondary_color', 'success_color', 'danger_color', 'warning_color',
    'info_color', 'theme_mode', 'admin_theme', 'enable_dark_mode', 'primary_font',
    'font_size_base', 'sidebar_bg_color', 'sidebar_text_color', 'custom_css',
    // Integrations
    'google_analytics_id', 'facebook_pixel_id', 'google_maps_api_key', 'whatsapp_api_token',
    'sms_api_key', 'payment_gateway', 'stripe_public_key', 'stripe_secret_key',
    'paypal_client_id', 'enable_api_access'
];

$currentSettings = [];
foreach ($allSettingKeys as $key) {
    $currentSettings[$key] = get_site_setting($key, '');
}

require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-gear"></i> Settings Management
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-secondary" onclick="exportSettings()">
            <i class="bi bi-download"></i> Export Settings
        </button>
        <button type="button" class="btn btn-outline-secondary ms-2" onclick="importSettings()">
            <i class="bi bi-upload"></i> Import Settings
        </button>
    </div>
</div>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Navigation Tabs -->
<ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
            <i class="bi bi-info-circle"></i> General
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
            <i class="bi bi-telephone"></i> Contact
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="social-tab" data-bs-toggle="tab" data-bs-target="#social" type="button" role="tab">
            <i class="bi bi-share"></i> Social Media
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
            <i class="bi bi-envelope"></i> Email
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
            <i class="bi bi-cpu"></i> System
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="terminology-tab" data-bs-toggle="tab" data-bs-target="#terminology" type="button" role="tab">
            <i class="bi bi-chat-text"></i> Terminology
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance" type="button" role="tab">
            <i class="bi bi-palette"></i> Appearance
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="integrations-tab" data-bs-toggle="tab" data-bs-target="#integrations" type="button" role="tab">
            <i class="bi bi-puzzle"></i> Integrations
        </button>
    </li>
</ul>

<div class="tab-content" id="settingsTabContent">
    <!-- General Settings Tab -->
    <div class="tab-pane fade show active" id="general" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i> General Settings
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_general">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="site_title" class="form-label">Site Title</label>
                                <input type="text" class="form-control" id="site_title" name="site_title"
                                       value="<?php echo htmlspecialchars($currentSettings['site_title']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_title" class="form-label">Admin Title</label>
                                <input type="text" class="form-control" id="admin_title" name="admin_title"
                                       value="<?php echo htmlspecialchars($currentSettings['admin_title']); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="organization_name" class="form-label">Organization Name</label>
                                <input type="text" class="form-control" id="organization_name" name="organization_name"
                                       value="<?php echo htmlspecialchars($currentSettings['organization_name']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="organization_type" class="form-label">Organization Type</label>
                                <select class="form-select" id="organization_type" name="organization_type">
                                    <option value="church" <?php echo $currentSettings['organization_type'] === 'church' ? 'selected' : ''; ?>>Church</option>
                                    <option value="ministry" <?php echo $currentSettings['organization_type'] === 'ministry' ? 'selected' : ''; ?>>Ministry</option>
                                    <option value="nonprofit" <?php echo $currentSettings['organization_type'] === 'nonprofit' ? 'selected' : ''; ?>>Non-Profit</option>
                                    <option value="community" <?php echo $currentSettings['organization_type'] === 'community' ? 'selected' : ''; ?>>Community Group</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="site_description" class="form-label">Site Description</label>
                        <textarea class="form-control" id="site_description" name="site_description" rows="3"><?php echo htmlspecialchars($currentSettings['site_description']); ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="site_keywords" class="form-label">Site Keywords (comma-separated)</label>
                        <input type="text" class="form-control" id="site_keywords" name="site_keywords"
                               value="<?php echo htmlspecialchars($currentSettings['site_keywords']); ?>">
                    </div>

                    <div class="mb-3">
                        <label for="organization_mission" class="form-label">Mission Statement</label>
                        <textarea class="form-control" id="organization_mission" name="organization_mission" rows="3"><?php echo htmlspecialchars($currentSettings['organization_mission']); ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="organization_vision" class="form-label">Vision Statement</label>
                        <textarea class="form-control" id="organization_vision" name="organization_vision" rows="3"><?php echo htmlspecialchars($currentSettings['organization_vision']); ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="organization_values" class="form-label">Core Values</label>
                        <textarea class="form-control" id="organization_values" name="organization_values" rows="3"><?php echo htmlspecialchars($currentSettings['organization_values']); ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="footer_text" class="form-label">Footer Text</label>
                        <textarea class="form-control" id="footer_text" name="footer_text" rows="2"><?php echo htmlspecialchars($currentSettings['footer_text']); ?></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Save General Settings
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Contact Information Tab -->
    <div class="tab-pane fade" id="contact" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-telephone"></i> Contact Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_contact">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="contact_phone" name="contact_phone"
                                       value="<?php echo htmlspecialchars($currentSettings['contact_phone']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_email" class="form-label">Contact Email</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email"
                                       value="<?php echo htmlspecialchars($currentSettings['contact_email']); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="contact_address" class="form-label">Street Address</label>
                        <input type="text" class="form-control" id="contact_address" name="contact_address"
                               value="<?php echo htmlspecialchars($currentSettings['contact_address']); ?>">
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="contact_city" class="form-label">City</label>
                                <input type="text" class="form-control" id="contact_city" name="contact_city"
                                       value="<?php echo htmlspecialchars($currentSettings['contact_city']); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="contact_state" class="form-label">State/Province</label>
                                <input type="text" class="form-control" id="contact_state" name="contact_state"
                                       value="<?php echo htmlspecialchars($currentSettings['contact_state']); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="contact_zip" class="form-label">ZIP/Postal Code</label>
                                <input type="text" class="form-control" id="contact_zip" name="contact_zip"
                                       value="<?php echo htmlspecialchars($currentSettings['contact_zip']); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="contact_country" class="form-label">Country</label>
                        <input type="text" class="form-control" id="contact_country" name="contact_country"
                               value="<?php echo htmlspecialchars($currentSettings['contact_country']); ?>">
                    </div>

                    <div class="mb-3">
                        <label for="office_hours" class="form-label">Office Hours</label>
                        <textarea class="form-control" id="office_hours" name="office_hours" rows="3"
                                  placeholder="e.g., Monday-Friday: 9:00 AM - 5:00 PM"><?php echo htmlspecialchars($currentSettings['office_hours']); ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="emergency_contact" class="form-label">Emergency Contact</label>
                        <input type="text" class="form-control" id="emergency_contact" name="emergency_contact"
                               value="<?php echo htmlspecialchars($currentSettings['emergency_contact']); ?>"
                               placeholder="Emergency phone number or contact info">
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Save Contact Information
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Social Media Tab -->
    <div class="tab-pane fade" id="social" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-share"></i> Social Media Links
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_social">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="facebook_url" class="form-label">
                                    <i class="bi bi-facebook text-primary"></i> Facebook URL
                                </label>
                                <input type="url" class="form-control" id="facebook_url" name="facebook_url"
                                       value="<?php echo htmlspecialchars($currentSettings['facebook_url']); ?>"
                                       placeholder="https://facebook.com/yourpage">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="twitter_url" class="form-label">
                                    <i class="bi bi-twitter text-info"></i> Twitter URL
                                </label>
                                <input type="url" class="form-control" id="twitter_url" name="twitter_url"
                                       value="<?php echo htmlspecialchars($currentSettings['twitter_url']); ?>"
                                       placeholder="https://twitter.com/youraccount">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="instagram_url" class="form-label">
                                    <i class="bi bi-instagram text-danger"></i> Instagram URL
                                </label>
                                <input type="url" class="form-control" id="instagram_url" name="instagram_url"
                                       value="<?php echo htmlspecialchars($currentSettings['instagram_url']); ?>"
                                       placeholder="https://instagram.com/youraccount">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="youtube_url" class="form-label">
                                    <i class="bi bi-youtube text-danger"></i> YouTube URL
                                </label>
                                <input type="url" class="form-control" id="youtube_url" name="youtube_url"
                                       value="<?php echo htmlspecialchars($currentSettings['youtube_url']); ?>"
                                       placeholder="https://youtube.com/yourchannel">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="linkedin_url" class="form-label">
                                    <i class="bi bi-linkedin text-primary"></i> LinkedIn URL
                                </label>
                                <input type="url" class="form-control" id="linkedin_url" name="linkedin_url"
                                       value="<?php echo htmlspecialchars($currentSettings['linkedin_url']); ?>"
                                       placeholder="https://linkedin.com/company/yourcompany">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tiktok_url" class="form-label">
                                    <i class="bi bi-tiktok text-dark"></i> TikTok URL
                                </label>
                                <input type="url" class="form-control" id="tiktok_url" name="tiktok_url"
                                       value="<?php echo htmlspecialchars($currentSettings['tiktok_url']); ?>"
                                       placeholder="https://tiktok.com/@youraccount">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="website_url" class="form-label">
                                    <i class="bi bi-globe"></i> Website URL
                                </label>
                                <input type="url" class="form-control" id="website_url" name="website_url"
                                       value="<?php echo htmlspecialchars($currentSettings['website_url']); ?>"
                                       placeholder="https://yourwebsite.com">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="blog_url" class="form-label">
                                    <i class="bi bi-journal-text"></i> Blog URL
                                </label>
                                <input type="url" class="form-control" id="blog_url" name="blog_url"
                                       value="<?php echo htmlspecialchars($currentSettings['blog_url']); ?>"
                                       placeholder="https://yourblog.com">
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Save Social Media Settings
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Email Settings Tab -->
    <div class="tab-pane fade" id="email" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-envelope"></i> Email Configuration
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_email">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_host" class="form-label">SMTP Host</label>
                                <input type="text" class="form-control" id="smtp_host" name="smtp_host"
                                       value="<?php echo htmlspecialchars($currentSettings['smtp_host']); ?>"
                                       placeholder="smtp.gmail.com">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_port" class="form-label">SMTP Port</label>
                                <input type="number" class="form-control" id="smtp_port" name="smtp_port"
                                       value="<?php echo htmlspecialchars($currentSettings['smtp_port']); ?>"
                                       placeholder="587">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_username" class="form-label">SMTP Username</label>
                                <input type="text" class="form-control" id="smtp_username" name="smtp_username"
                                       value="<?php echo htmlspecialchars($currentSettings['smtp_username']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_password" class="form-label">SMTP Password</label>
                                <input type="password" class="form-control" id="smtp_password" name="smtp_password"
                                       value="<?php echo htmlspecialchars($currentSettings['smtp_password']); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_encryption" class="form-label">SMTP Encryption</label>
                                <select class="form-select" id="smtp_encryption" name="smtp_encryption">
                                    <option value="tls" <?php echo $currentSettings['smtp_encryption'] === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                    <option value="ssl" <?php echo $currentSettings['smtp_encryption'] === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                    <option value="none" <?php echo $currentSettings['smtp_encryption'] === 'none' ? 'selected' : ''; ?>>None</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="enable_email_queue" name="enable_email_queue"
                                           <?php echo $currentSettings['enable_email_queue'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="enable_email_queue">
                                        Enable Email Queue
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="from_email" class="form-label">From Email</label>
                                <input type="email" class="form-control" id="from_email" name="from_email"
                                       value="<?php echo htmlspecialchars($currentSettings['from_email']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="from_name" class="form-label">From Name</label>
                                <input type="text" class="form-control" id="from_name" name="from_name"
                                       value="<?php echo htmlspecialchars($currentSettings['from_name']); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="reply_to_email" class="form-label">Reply-To Email</label>
                        <input type="email" class="form-control" id="reply_to_email" name="reply_to_email"
                               value="<?php echo htmlspecialchars($currentSettings['reply_to_email']); ?>">
                    </div>

                    <div class="mb-3">
                        <label for="email_signature" class="form-label">Email Signature</label>
                        <textarea class="form-control" id="email_signature" name="email_signature" rows="4"><?php echo htmlspecialchars($currentSettings['email_signature']); ?></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Save Email Settings
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- System Preferences Tab -->
    <div class="tab-pane fade" id="system" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-cpu"></i> System Preferences
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_system">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="timezone" class="form-label">Timezone</label>
                                <select class="form-select" id="timezone" name="timezone">
                                    <option value="America/New_York" <?php echo $currentSettings['timezone'] === 'America/New_York' ? 'selected' : ''; ?>>Eastern Time</option>
                                    <option value="America/Chicago" <?php echo $currentSettings['timezone'] === 'America/Chicago' ? 'selected' : ''; ?>>Central Time</option>
                                    <option value="America/Denver" <?php echo $currentSettings['timezone'] === 'America/Denver' ? 'selected' : ''; ?>>Mountain Time</option>
                                    <option value="America/Los_Angeles" <?php echo $currentSettings['timezone'] === 'America/Los_Angeles' ? 'selected' : ''; ?>>Pacific Time</option>
                                    <option value="UTC" <?php echo $currentSettings['timezone'] === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="language" class="form-label">Language</label>
                                <select class="form-select" id="language" name="language">
                                    <option value="en" <?php echo $currentSettings['language'] === 'en' ? 'selected' : ''; ?>>English</option>
                                    <option value="es" <?php echo $currentSettings['language'] === 'es' ? 'selected' : ''; ?>>Spanish</option>
                                    <option value="fr" <?php echo $currentSettings['language'] === 'fr' ? 'selected' : ''; ?>>French</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_format" class="form-label">Date Format</label>
                                <select class="form-select" id="date_format" name="date_format">
                                    <option value="Y-m-d" <?php echo $currentSettings['date_format'] === 'Y-m-d' ? 'selected' : ''; ?>>YYYY-MM-DD</option>
                                    <option value="m/d/Y" <?php echo $currentSettings['date_format'] === 'm/d/Y' ? 'selected' : ''; ?>>MM/DD/YYYY</option>
                                    <option value="d/m/Y" <?php echo $currentSettings['date_format'] === 'd/m/Y' ? 'selected' : ''; ?>>DD/MM/YYYY</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="time_format" class="form-label">Time Format</label>
                                <select class="form-select" id="time_format" name="time_format">
                                    <option value="H:i" <?php echo $currentSettings['time_format'] === 'H:i' ? 'selected' : ''; ?>>24-hour (HH:MM)</option>
                                    <option value="g:i A" <?php echo $currentSettings['time_format'] === 'g:i A' ? 'selected' : ''; ?>>12-hour (H:MM AM/PM)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="currency_symbol" class="form-label">Currency Symbol</label>
                                <input type="text" class="form-control" id="currency_symbol" name="currency_symbol"
                                       value="<?php echo htmlspecialchars($currentSettings['currency_symbol']); ?>" maxlength="5">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="currency_code" class="form-label">Currency Code</label>
                                <input type="text" class="form-control" id="currency_code" name="currency_code"
                                       value="<?php echo htmlspecialchars($currentSettings['currency_code']); ?>" maxlength="3">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="items_per_page" class="form-label">Items Per Page</label>
                                <select class="form-select" id="items_per_page" name="items_per_page">
                                    <option value="10" <?php echo $currentSettings['items_per_page'] === '10' ? 'selected' : ''; ?>>10</option>
                                    <option value="25" <?php echo $currentSettings['items_per_page'] === '25' ? 'selected' : ''; ?>>25</option>
                                    <option value="50" <?php echo $currentSettings['items_per_page'] === '50' ? 'selected' : ''; ?>>50</option>
                                    <option value="100" <?php echo $currentSettings['items_per_page'] === '100' ? 'selected' : ''; ?>>100</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="session_timeout" class="form-label">Session Timeout (seconds)</label>
                                <input type="number" class="form-control" id="session_timeout" name="session_timeout"
                                       value="<?php echo htmlspecialchars($currentSettings['session_timeout']); ?>" min="300" max="86400">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="max_upload_size" class="form-label">Max Upload Size (MB)</label>
                                <input type="number" class="form-control" id="max_upload_size" name="max_upload_size"
                                       value="<?php echo htmlspecialchars($currentSettings['max_upload_size']); ?>" min="1" max="100">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="backup_retention_days" class="form-label">Backup Retention (days)</label>
                        <input type="number" class="form-control" id="backup_retention_days" name="backup_retention_days"
                               value="<?php echo htmlspecialchars($currentSettings['backup_retention_days']); ?>" min="1" max="365">
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Save System Settings
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Terminology Tab -->
    <div class="tab-pane fade" id="terminology" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-chat-text"></i> Custom Terminology
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_terminology">

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> Customize the terminology used throughout your system to match your organization's language.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="member_term" class="form-label">Member (Singular)</label>
                                <input type="text" class="form-control" id="member_term" name="member_term"
                                       value="<?php echo htmlspecialchars($currentSettings['member_term']); ?>" placeholder="Member">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="term_members" class="form-label">Members (Plural)</label>
                                <input type="text" class="form-control" id="term_members" name="term_members"
                                       value="<?php echo htmlspecialchars($currentSettings['term_members']); ?>" placeholder="Members">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="leader_term" class="form-label">Leader (Singular)</label>
                                <input type="text" class="form-control" id="leader_term" name="leader_term"
                                       value="<?php echo htmlspecialchars($currentSettings['leader_term']); ?>" placeholder="Pastor">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="term_leaders" class="form-label">Leaders (Plural)</label>
                                <input type="text" class="form-control" id="term_leaders" name="term_leaders"
                                       value="<?php echo htmlspecialchars($currentSettings['term_leaders']); ?>" placeholder="Pastors">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="group_term" class="form-label">Group (Singular)</label>
                                <input type="text" class="form-control" id="group_term" name="group_term"
                                       value="<?php echo htmlspecialchars($currentSettings['group_term']); ?>" placeholder="Ministry">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="term_groups" class="form-label">Groups (Plural)</label>
                                <input type="text" class="form-control" id="term_groups" name="term_groups"
                                       value="<?php echo htmlspecialchars($currentSettings['term_groups']); ?>" placeholder="Ministries">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="event_term" class="form-label">Event (Singular)</label>
                                <input type="text" class="form-control" id="event_term" name="event_term"
                                       value="<?php echo htmlspecialchars($currentSettings['event_term']); ?>" placeholder="Service">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="term_events" class="form-label">Events (Plural)</label>
                                <input type="text" class="form-control" id="term_events" name="term_events"
                                       value="<?php echo htmlspecialchars($currentSettings['term_events']); ?>" placeholder="Services">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="donation_term" class="form-label">Donation (Singular)</label>
                                <input type="text" class="form-control" id="donation_term" name="donation_term"
                                       value="<?php echo htmlspecialchars($currentSettings['donation_term']); ?>" placeholder="Offering">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="term_donations" class="form-label">Donations (Plural)</label>
                                <input type="text" class="form-control" id="term_donations" name="term_donations"
                                       value="<?php echo htmlspecialchars($currentSettings['term_donations']); ?>" placeholder="Offerings">
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Save Terminology Settings
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Appearance Tab -->
    <div class="tab-pane fade" id="appearance" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-palette"></i> Appearance & Theme
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_appearance">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="primary_color" class="form-label">Primary Color</label>
                                <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color"
                                       value="<?php echo htmlspecialchars($currentSettings['primary_color'] ?: '#007bff'); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="secondary_color" class="form-label">Secondary Color</label>
                                <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color"
                                       value="<?php echo htmlspecialchars($currentSettings['secondary_color'] ?: '#6c757d'); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="theme_mode" class="form-label">Theme Mode</label>
                                <select class="form-select" id="theme_mode" name="theme_mode">
                                    <option value="light" <?php echo $currentSettings['theme_mode'] === 'light' ? 'selected' : ''; ?>>Light</option>
                                    <option value="dark" <?php echo $currentSettings['theme_mode'] === 'dark' ? 'selected' : ''; ?>>Dark</option>
                                    <option value="auto" <?php echo $currentSettings['theme_mode'] === 'auto' ? 'selected' : ''; ?>>Auto</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="primary_font" class="form-label">Primary Font</label>
                                <select class="form-select" id="primary_font" name="primary_font">
                                    <option value="Inter" <?php echo $currentSettings['primary_font'] === 'Inter' ? 'selected' : ''; ?>>Inter</option>
                                    <option value="Arial" <?php echo $currentSettings['primary_font'] === 'Arial' ? 'selected' : ''; ?>>Arial</option>
                                    <option value="Helvetica" <?php echo $currentSettings['primary_font'] === 'Helvetica' ? 'selected' : ''; ?>>Helvetica</option>
                                    <option value="Georgia" <?php echo $currentSettings['primary_font'] === 'Georgia' ? 'selected' : ''; ?>>Georgia</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="font_size_base" class="form-label">Base Font Size (px)</label>
                                <input type="number" class="form-control" id="font_size_base" name="font_size_base"
                                       value="<?php echo htmlspecialchars($currentSettings['font_size_base'] ?: '16'); ?>" min="12" max="24">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="enable_dark_mode" name="enable_dark_mode"
                                           <?php echo $currentSettings['enable_dark_mode'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="enable_dark_mode">
                                        Enable Dark Mode Toggle
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="custom_css" class="form-label">Custom CSS</label>
                        <textarea class="form-control" id="custom_css" name="custom_css" rows="6"
                                  placeholder="/* Add your custom CSS here */"><?php echo htmlspecialchars($currentSettings['custom_css']); ?></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Save Appearance Settings
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Integrations Tab -->
    <div class="tab-pane fade" id="integrations" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-puzzle"></i> Third-Party Integrations
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_integrations">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="google_analytics_id" class="form-label">Google Analytics ID</label>
                                <input type="text" class="form-control" id="google_analytics_id" name="google_analytics_id"
                                       value="<?php echo htmlspecialchars($currentSettings['google_analytics_id']); ?>"
                                       placeholder="GA-XXXXXXXXX-X">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="facebook_pixel_id" class="form-label">Facebook Pixel ID</label>
                                <input type="text" class="form-control" id="facebook_pixel_id" name="facebook_pixel_id"
                                       value="<?php echo htmlspecialchars($currentSettings['facebook_pixel_id']); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="google_maps_api_key" class="form-label">Google Maps API Key</label>
                        <input type="text" class="form-control" id="google_maps_api_key" name="google_maps_api_key"
                               value="<?php echo htmlspecialchars($currentSettings['google_maps_api_key']); ?>">
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="whatsapp_api_token" class="form-label">WhatsApp API Token</label>
                                <input type="text" class="form-control" id="whatsapp_api_token" name="whatsapp_api_token"
                                       value="<?php echo htmlspecialchars($currentSettings['whatsapp_api_token']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sms_api_key" class="form-label">SMS API Key</label>
                                <input type="text" class="form-control" id="sms_api_key" name="sms_api_key"
                                       value="<?php echo htmlspecialchars($currentSettings['sms_api_key']); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="stripe_public_key" class="form-label">Stripe Public Key</label>
                                <input type="text" class="form-control" id="stripe_public_key" name="stripe_public_key"
                                       value="<?php echo htmlspecialchars($currentSettings['stripe_public_key']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="stripe_secret_key" class="form-label">Stripe Secret Key</label>
                                <input type="password" class="form-control" id="stripe_secret_key" name="stripe_secret_key"
                                       value="<?php echo htmlspecialchars($currentSettings['stripe_secret_key']); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="paypal_client_id" class="form-label">PayPal Client ID</label>
                                <input type="text" class="form-control" id="paypal_client_id" name="paypal_client_id"
                                       value="<?php echo htmlspecialchars($currentSettings['paypal_client_id']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="enable_api_access" name="enable_api_access"
                                           <?php echo $currentSettings['enable_api_access'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="enable_api_access">
                                        Enable API Access
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Save Integration Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Export settings functionality
function exportSettings() {
    const settings = <?php echo json_encode($currentSettings); ?>;
    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'settings-export-' + new Date().toISOString().split('T')[0] + '.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Import settings functionality
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const settings = JSON.parse(e.target.result);
                    if (confirm('This will overwrite your current settings. Are you sure?')) {
                        // Populate form fields with imported settings
                        Object.keys(settings).forEach(key => {
                            const element = document.querySelector(`[name="${key}"]`);
                            if (element) {
                                if (element.type === 'checkbox') {
                                    element.checked = settings[key] === '1' || settings[key] === true;
                                } else {
                                    element.value = settings[key];
                                }
                            }
                        });
                        alert('Settings imported successfully! Please save each tab to apply changes.');
                    }
                } catch (error) {
                    alert('Invalid settings file format.');
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
}
</script>

<?php require_once 'includes/footer.php'; ?>
