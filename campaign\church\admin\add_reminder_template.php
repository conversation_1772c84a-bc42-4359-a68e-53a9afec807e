<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include config file which has PDO connection
require_once '../config.php';

// HTML header
echo '<!DOCTYPE html>
<html>
<head>
    <title>Add Reminder Template</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Add/Update Birthday Reminder Template</h1>';

try {
    // Global PDO connection should be available from config.php
    
    // Prepare the template data
    $template_name = 'Birthday Reminder Template';
    $subject = '{full_name}\'s birthday is coming up in {days_text}!';
    $content = '<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 20px; border-radius: 5px;">
    <div style="text-align: center; margin-bottom: 20px;">
        <img src="https://freedomassemblychurch.org/wp-content/uploads/2023/10/church-logo.png" alt="Church Logo" style="max-width: 200px;">
    </div>
    
    <div style="background-color: #fff; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
        <h2 style="color: #3498db; text-align: center;">Birthday Reminder</h2>
        
        <p>Dear {full_name},</p>
        
        <p>We\'re excited to remind you that your birthday is coming up in <strong>{days_text}</strong> on <strong>{upcoming_birthday_formatted}</strong>!</p>
        
        <p>At Freedom Assembly Church International, we value each member of our church family and want to make your special day memorable.</p>
        
        <p>We\'re looking forward to celebrating with you on {upcoming_birthday_day}, {upcoming_birthday_date}!</p>
        
        <div style="text-align: center; margin: 30px 0;">
            <div style="display: inline-block; background-color: #3498db; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; font-weight: bold;">Happy Early Birthday!</div>
        </div>
        
        <p>Many blessings,<br>
        Freedom Assembly Church International Team</p>
    </div>
    
    <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;">
        <p>Freedom Assembly Church International<br>
        123 Church Street, Cityville, ST 12345<br>
        <EMAIL> | (555) 123-4567</p>
    </div>
</div>';
    $is_birthday_template = 0; // This is a reminder template
    
    echo '<h2>Template Details</h2>';
    echo '<pre>';
    echo "Template Name: $template_name\n";
    echo "Subject: $subject\n";
    echo "Is Birthday Template: " . ($is_birthday_template ? 'Yes' : 'No') . "\n";
    echo '</pre>';
    
    // Check if a template with this name already exists
    $check_stmt = $pdo->prepare('SELECT id FROM email_templates WHERE template_name = ? AND is_birthday_template = ?');
    $check_stmt->execute([$template_name, $is_birthday_template]);
    $existing = $check_stmt->fetch();
    
    if ($existing) {
        // Update the existing template
        $update = $pdo->prepare('UPDATE email_templates SET subject = ?, content = ? WHERE id = ?');
        $update->execute([$subject, $content, $existing['id']]);
        echo '<p class="success">Updated existing template with ID: ' . $existing['id'] . '</p>';
    } else {
        // Insert a new template
        $insert = $pdo->prepare('INSERT INTO email_templates (template_name, subject, content, is_birthday_template) VALUES (?, ?, ?, ?)');
        $insert->execute([$template_name, $subject, $content, $is_birthday_template]);
        echo '<p class="success">Inserted new template with ID: ' . $pdo->lastInsertId() . '</p>';
    }
    
    // List all reminder templates
    $stmt = $pdo->query('SELECT id, template_name, subject, is_birthday_template FROM email_templates WHERE is_birthday_template = 0');
    $templates = $stmt->fetchAll();
    
    echo '<h2>Existing Reminder Templates</h2>';
    echo '<table border="1" cellpadding="5" cellspacing="0">';
    echo '<tr><th>ID</th><th>Name</th><th>Subject</th></tr>';
    
    foreach ($templates as $template) {
        echo '<tr>';
        echo '<td>' . $template['id'] . '</td>';
        echo '<td>' . htmlspecialchars($template['template_name']) . '</td>';
        echo '<td>' . htmlspecialchars($template['subject']) . '</td>';
        echo '</tr>';
    }
    
    echo '</table>';
    
    echo '<p><a href="send_birthday_emails.php">Go to Birthday Email Management</a></p>';
    
} catch (PDOException $e) {
    echo '<p class="error">Database Error: ' . $e->getMessage() . '</p>';
} catch (Exception $e) {
    echo '<p class="error">Error: ' . $e->getMessage() . '</p>';
}

// HTML footer
echo '</body></html>';
?> 