<?php
/**
 * File Upload Test
 * 
 * Tests file upload functionality, including validation, storage, and retrieval
 */

class FileUploadTest
{
    private $pdo;
    private $uploadDir;
    
    public function __construct()
    {
        global $pdo;
        $this->pdo = $pdo;
        
        // Set upload directory for tests
        $this->uploadDir = dirname(__DIR__, 2) . '/uploads/test';
        
        // Ensure test upload directory exists
        if (!file_exists($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }
    
    /**
     * Test file upload directory creation
     */
    public function testUploadDirectoryCreation()
    {
        // Generate a random subdirectory
        $subDir = 'test_' . time();
        $fullPath = $this->uploadDir . '/' . $subDir;
        
        // Remove directory if it already exists
        if (file_exists($fullPath)) {
            rmdir($fullPath);
        }
        
        // Create directory
        $created = mkdir($fullPath, 0755, true);
        
        // Verify directory exists
        $exists = file_exists($fullPath) && is_dir($fullPath);
        
        // Clean up
        if ($exists) {
            rmdir($fullPath);
        }
        
        return [
            'name' => 'Upload Directory Creation',
            'success' => $created && $exists,
            'message' => $created && $exists ? 
                "Successfully created upload directory" : 
                "Failed to create upload directory"
        ];
    }
    
    /**
     * Test file upload simulation
     */
    public function testFileUpload()
    {
        // Create a test file
        $fileName = 'test_file_' . time() . '.txt';
        $filePath = $this->uploadDir . '/' . $fileName;
        $fileContent = 'This is a test file for upload testing';
        
        // Write test file
        $written = file_put_contents($filePath, $fileContent);
        
        // Verify file exists
        $fileExists = file_exists($filePath);
        $contentCorrect = ($fileExists && file_get_contents($filePath) === $fileContent);
        
        // Simulate database entry for upload
        $sql = "CREATE TABLE IF NOT EXISTS test_uploads (
            id INT(11) NOT NULL AUTO_INCREMENT,
            filename VARCHAR(255) NOT NULL,
            original_filename VARCHAR(255) NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            size INT(11) NOT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        )";
        
        $this->pdo->exec($sql);
        
        // Insert upload record
        $sql = "INSERT INTO test_uploads (filename, original_filename, mime_type, size, created_at) 
                VALUES (?, ?, ?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        $dbSuccess = $stmt->execute([
            $fileName,
            'original_' . $fileName,
            'text/plain',
            strlen($fileContent),
            date('Y-m-d H:i:s')
        ]);
        
        $uploadId = $this->pdo->lastInsertId();
        
        // Verify database record
        $sql = "SELECT * FROM test_uploads WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$uploadId]);
        $upload = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $dbRecordExists = ($upload !== false);
        $dbDataCorrect = (
            $upload && 
            $upload['filename'] === $fileName &&
            $upload['mime_type'] === 'text/plain' &&
            $upload['size'] === strlen($fileContent)
        );
        
        // Clean up
        if ($fileExists) {
            unlink($filePath);
        }
        
        return [
            'name' => 'File Upload',
            'success' => $written && $fileExists && $contentCorrect && $dbSuccess && $dbRecordExists && $dbDataCorrect,
            'message' => $written && $fileExists && $contentCorrect && $dbSuccess && $dbRecordExists && $dbDataCorrect ? 
                "Successfully simulated file upload and database recording" : 
                "Failed to simulate file upload or database recording"
        ];
    }
    
    /**
     * Test file validation
     */
    public function testFileValidation()
    {
        // Test valid file types
        $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'];
        $validMimeTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'application/pdf',
            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        
        // Test cases
        $testCases = [
            // Valid files
            [
                'name' => 'Valid image',
                'file' => [
                    'name' => 'test.jpg',
                    'type' => 'image/jpeg',
                    'size' => 500000, // 500KB
                    'error' => UPLOAD_ERR_OK
                ],
                'maxSize' => 1048576, // 1MB
                'expected' => true
            ],
            // Invalid extension
            [
                'name' => 'Invalid extension',
                'file' => [
                    'name' => 'dangerous.php',
                    'type' => 'application/x-php',
                    'size' => 1000,
                    'error' => UPLOAD_ERR_OK
                ],
                'maxSize' => 1048576, // 1MB
                'expected' => false
            ],
            // File too large
            [
                'name' => 'File too large',
                'file' => [
                    'name' => 'large.jpg',
                    'type' => 'image/jpeg',
                    'size' => 2000000, // 2MB
                    'error' => UPLOAD_ERR_OK
                ],
                'maxSize' => 1048576, // 1MB
                'expected' => false
            ],
            // Upload error
            [
                'name' => 'Upload error',
                'file' => [
                    'name' => 'error.jpg',
                    'type' => 'image/jpeg',
                    'size' => 500000,
                    'error' => UPLOAD_ERR_INI_SIZE
                ],
                'maxSize' => 1048576, // 1MB
                'expected' => false
            ]
        ];
        
        $results = [];
        $allPassed = true;
        
        foreach ($testCases as $case) {
            // Validate file
            $isValid = $this->validateFile($case['file'], $validExtensions, $validMimeTypes, $case['maxSize']);
            $passed = ($isValid === $case['expected']);
            
            if (!$passed) {
                $allPassed = false;
            }
            
            $results[] = [
                'case' => $case['name'],
                'passed' => $passed,
                'expected' => $case['expected'],
                'actual' => $isValid
            ];
        }
        
        return [
            'name' => 'File Validation',
            'success' => $allPassed,
            'message' => $allPassed ? 
                "All file validation tests passed" : 
                "Some file validation tests failed: " . json_encode($results)
        ];
    }
    
    /**
     * Helper method to validate file
     */
    private function validateFile($file, $allowedExtensions, $allowedMimeTypes, $maxSize)
    {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return false;
        }
        
        // Check file size
        if ($file['size'] > $maxSize) {
            return false;
        }
        
        // Get file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        // Check file extension
        if (!in_array($extension, $allowedExtensions)) {
            return false;
        }
        
        // Check MIME type
        if (!in_array($file['type'], $allowedMimeTypes)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Run all tests
     */
    public function runAllTests()
    {
        $results = [];
        $results[] = $this->testUploadDirectoryCreation();
        $results[] = $this->testFileUpload();
        $results[] = $this->testFileValidation();
        
        return $results;
    }
} 