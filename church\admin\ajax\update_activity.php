<?php
/**
 * AJAX endpoint to update the user's last activity time
 * 
 * This endpoint is called periodically by the client to update
 * the server's session timeout tracking.
 */

// Include session manager
require_once '../includes/session-manager.php';

// Check for valid admin session
if (!isset($_SESSION['admin_id'])) {
    // Return error if not logged in
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Not logged in',
        'status' => 'not_logged_in'
    ]);
    exit;
}

// Check if this is an AJAX request
if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
    strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
    
    // Update the last activity time
    updateLastActivity();
    
    // Get the remaining time
    $remainingTime = getRemainingSessionTime();
    
    // Return success response with remaining time
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'remainingTime' => $remainingTime,
        'timestamp' => time()
    ]);
    exit;
} else {
    // Return error for non-AJAX requests
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request type',
        'status' => 'invalid_request'
    ]);
    exit;
} 