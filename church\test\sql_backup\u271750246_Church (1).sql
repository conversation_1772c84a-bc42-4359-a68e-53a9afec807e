-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1:3306
-- Generation Time: Mar 10, 2025 at 02:26 PM
-- Server version: 10.11.10-MariaDB
-- PHP Version: 7.2.34

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `u271750246_Church`
--

-- --------------------------------------------------------

--
-- Table structure for table `activity_logs`
--

CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `operation_type` varchar(50) NOT NULL,
  `table_affected` varchar(50) NOT NULL,
  `data_summary` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `username`, `password`, `email`, `full_name`, `created_at`, `updated_at`) VALUES
(1, 'admin', '0192023a7bbd73250516f069df18b500', '<EMAIL>', 'System Administrator', '2025-03-02 16:31:16', '2025-03-03 06:16:09');

-- --------------------------------------------------------

--
-- Table structure for table `automated_emails_settings`
--

CREATE TABLE `automated_emails_settings` (
  `id` int(11) NOT NULL,
  `email_type` enum('birthday','reminder','welcome') NOT NULL,
  `template_ids` text NOT NULL,
  `send_time` time DEFAULT '07:00:00',
  `days_before` int(11) DEFAULT 3,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `automated_emails_settings`
--

INSERT INTO `automated_emails_settings` (`id`, `email_type`, `template_ids`, `send_time`, `days_before`, `created_at`, `updated_at`) VALUES
(1, 'birthday', '3,5,10,7', '04:00:00', 3, '2025-03-08 12:14:27', '2025-03-08 12:14:27'),
(2, 'reminder', '6,13,15', '06:00:00', 1, '2025-03-08 12:14:55', '2025-03-08 12:14:55');

-- --------------------------------------------------------

--
-- Table structure for table `contacts`
--

CREATE TABLE `contacts` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `source` varchar(50) DEFAULT 'import',
  `group_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `contacts`
--

INSERT INTO `contacts` (`id`, `email`, `name`, `source`, `group_id`, `created_at`, `updated_at`) VALUES
(1, '<EMAIL>', 'Sea60035', 'import', NULL, '2025-03-10 12:17:12', '2025-03-10 12:17:12');

-- --------------------------------------------------------

--
-- Table structure for table `contact_email_logs`
--

CREATE TABLE `contact_email_logs` (
  `id` int(11) NOT NULL,
  `recipient_id` int(11) NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `email_type` varchar(20) DEFAULT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `sent_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` varchar(20) DEFAULT NULL,
  `error_message` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contact_groups`
--

CREATE TABLE `contact_groups` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `contact_groups`
--

INSERT INTO `contact_groups` (`id`, `name`, `description`, `created_at`) VALUES
(1, '555', '555', '2025-03-10 12:17:25');

-- --------------------------------------------------------

--
-- Table structure for table `contact_group_members`
--

CREATE TABLE `contact_group_members` (
  `contact_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `contact_group_members`
--

INSERT INTO `contact_group_members` (`contact_id`, `group_id`, `created_at`) VALUES
(1, 1, '2025-03-10 12:17:37');

-- --------------------------------------------------------

--
-- Table structure for table `email_logs`
--

CREATE TABLE `email_logs` (
  `id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `email_type` varchar(20) DEFAULT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `sent_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` varchar(20) DEFAULT NULL,
  `error_message` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `email_logs`
--

INSERT INTO `email_logs` (`id`, `member_id`, `template_id`, `email_type`, `subject`, `sent_at`, `status`, `error_message`) VALUES
(1, 3, 3, NULL, 'Happy Birthday, Godwin!', '2025-03-08 09:47:28', 'success', NULL),
(2, 3, 3, 'birthday', NULL, '2025-03-08 09:50:14', 'success', NULL),
(3, 3, 10, 'birthday', NULL, '2025-03-08 09:50:43', 'success', NULL),
(4, 3, 5, 'birthday', NULL, '2025-03-08 09:51:04', 'success', NULL),
(5, 3, 7, 'birthday', NULL, '2025-03-08 09:51:17', 'success', NULL),
(6, 4, NULL, NULL, NULL, '2025-03-08 09:53:55', 'sent', ''),
(7, 4, 19, 'b_notification', NULL, '2025-03-08 11:59:50', 'success', NULL),
(8, 4, 19, 'b_notification', NULL, '2025-03-08 11:59:50', 'success', NULL),
(9, 3, 21, 'b_notification', NULL, '2025-03-08 12:02:10', 'success', NULL),
(10, 3, 21, 'b_notification', NULL, '2025-03-08 12:02:10', 'success', NULL),
(11, 3, 25, 'bulk', 'Newsletter from Freedom Assembly Church International', '2025-03-08 12:10:59', 'success', NULL),
(12, 4, 25, 'bulk', 'Newsletter from Freedom Assembly Church International', '2025-03-08 12:11:02', 'success', NULL),
(13, 3, 3, NULL, 'Happy Birthday, Godwin!', '2025-03-08 15:27:50', 'success', NULL),
(14, 4, 7, NULL, 'Happy Birthday, Ndivhuwo!', '2025-03-08 15:28:41', 'success', NULL),
(15, 3, 3, NULL, 'Happy Birthday, Godwin!', '2025-03-09 09:27:10', 'success', NULL),
(16, 3, 24, 'bulk', 'Newsletter from Freedom Assembly Church International', '2025-03-09 15:21:23', 'success', NULL),
(17, 4, 24, 'bulk', 'Newsletter from Freedom Assembly Church International', '2025-03-09 15:21:26', 'success', NULL),
(18, 3, 23, 'bulk', 'A Special Message from Freedom Assembly Church International', '2025-03-09 15:23:52', 'success', NULL),
(19, 4, 23, 'bulk', 'A Special Message from Freedom Assembly Church International', '2025-03-09 15:23:56', 'success', NULL),
(20, 3, 23, 'bulk', 'A Special Message from Freedom Assembly Church International', '2025-03-10 06:33:16', 'success', NULL),
(21, 4, 23, 'bulk', 'A Special Message from Freedom Assembly Church International', '2025-03-10 06:33:19', 'success', NULL),
(22, 3, 23, 'bulk', 'A Special Message from Freedom Assembly Church International', '2025-03-10 06:36:47', 'success', NULL),
(23, 4, 23, 'bulk', 'A Special Message from Freedom Assembly Church International', '2025-03-10 06:36:50', 'success', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `email_settings`
--

CREATE TABLE `email_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `email_settings`
--

INSERT INTO `email_settings` (`id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
(23, 'sender_name', 'Freedom Assembly Church International', '2025-03-06 21:38:40', '2025-03-08 09:42:57'),
(41, 'smtp_host', 'smtp.hostinger.com', '2025-03-06 22:09:05', '2025-03-08 09:42:57'),
(42, 'smtp_auth', '1', '2025-03-06 22:09:05', '2025-03-06 22:09:05'),
(43, 'smtp_username', '<EMAIL>', '2025-03-06 22:09:05', '2025-03-08 09:42:57'),
(44, 'smtp_password', 'p2=a;]A5', '2025-03-06 22:09:05', '2025-03-08 09:42:57'),
(45, 'smtp_secure', 'ssl', '2025-03-06 22:09:05', '2025-03-06 22:09:05'),
(46, 'smtp_port', '465', '2025-03-06 22:09:05', '2025-03-06 22:09:05'),
(47, 'sender_email', '<EMAIL>', '2025-03-06 22:09:05', '2025-03-08 09:42:57'),
(57, 'contact_email', '<EMAIL>', '2025-03-08 12:22:12', '2025-03-08 12:22:12');

-- --------------------------------------------------------

--
-- Table structure for table `email_templates`
--

CREATE TABLE `email_templates` (
  `id` int(11) NOT NULL,
  `template_name` varchar(50) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `is_birthday_template` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `template_category` enum('general','birthday','bulk') DEFAULT 'general'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `email_templates`
--

INSERT INTO `email_templates` (`id`, `template_name`, `subject`, `content`, `is_birthday_template`, `created_at`, `updated_at`, `template_category`) VALUES
(3, 'Birthday Template 1: Elegant Blue Theme', 'Happy Birthday, {full_name}! From Freedom Assembly Church', '<!-- Birthday Template 1: Elegant Blue Theme -->\r\n<div style=\"max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 20px;\">\r\n    <div style=\"text-align: center; background-color: #ffffff; padding: 20px; border-radius: 10px; box-shadow: 0 2px 6px rgba(0,0,0,0.1);\">\r\n        <h1 style=\"color: #1e90ff; font-size: 28px;\">🎉🎂 Happy Birthday, {full_name}! 🎂🎉</h1>\r\n        \r\n        <img src=\"{member_image}\" alt=\"{full_name}\" style=\"width: 160px; height: 160px; border-radius: 50%; border: 4px solid #1e90ff; margin-top: 15px;\">\r\n        \r\n        <p style=\"font-size: 16px; line-height: 1.6; margin-top: 15px;\">🎈🎁 On behalf of <strong>Freedom Assembly Church International</strong>, we celebrate you today! May your special day be filled with joy, love, and abundant blessings. 🎊🙏</p>\r\n        \r\n        <p style=\"font-size: 16px; font-style: italic; color: #555; background-color: #f0f8ff; padding: 15px; border-radius: 6px;\">✨ \"The Lord bless you and keep you; the Lord make His face shine on you and be gracious to you.\" – Numbers 6:24-25 ✨</p>\r\n        \r\n        <p style=\"margin-top: 20px; font-size: 16px;\">🥳🎂 Have a fantastic and blessed birthday! 🎂🎈</p>\r\n    </div>\r\n</div>', 1, '2025-03-02 15:07:36', '2025-03-08 10:30:47', 'birthday'),
(5, 'Birthday Template 2: Warm Gold Theme', 'Happy Birthday, {full_name}! Special Wishes For You', '<!-- Birthday Template 2: Warm Gold Theme -->\r\n<div style=\"max-width: 600px; margin: 0 auto; font-family: \'Georgia\', serif; color: #5a4a42; background-color: #fdf7e4; padding: 20px;\">\r\n    <div style=\"text-align: center; background-color: #fff3d4; padding: 20px; border-radius: 10px; box-shadow: 0 2px 6px rgba(0,0,0,0.1);\">\r\n        <h1 style=\"color: #d4a017; font-size: 26px;\">🌟🎊 A Blessed Birthday to You, {full_name}! 🎊🌟</h1>\r\n        \r\n        <img src=\"{member_image}\" alt=\"{full_name}\" style=\"width: 160px; height: 160px; border-radius: 50%; border: 4px solid #d4a017; margin-top: 15px;\">\r\n        \r\n        <p style=\"font-size: 17px; line-height: 1.6; margin-top: 15px;\">🎂🎁 <strong>Freedom Assembly Church International</strong> celebrates your life and all that God is doing through you! May your day be filled with divine joy and peace. 🎶✨</p>\r\n        \r\n        <p style=\"font-size: 16px; font-style: italic; color: #5a4a42; background-color: #f7e4c1; padding: 15px; border-radius: 6px;\">📖 \"This is the day the Lord has made; let us rejoice and be glad in it.\" – Psalm 118:24 🎵</p>\r\n        \r\n        <p style=\"margin-top: 20px; font-size: 16px;\">🎊🎈 May this year be filled with God’s abundant blessings! 🎈🎂</p>\r\n    </div>\r\n</div>', 1, '2025-03-02 15:10:22', '2025-03-08 10:34:24', 'birthday'),
(6, 'Birthday Reminder Template 1: Classic Blue Theme', 'Your birthday is coming up in {days_text} - We can\'t wait to celebrate!', '<!-- Birthday Reminder Email Template 1: Classic Blue Theme -->\r\n<div style=\"max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 20px; border-radius: 5px;\">\r\n    <div style=\"text-align: center; margin-bottom: 20px;\">\r\n        <img src=\"{church_logo}\" alt=\"Church Logo\" style=\"max-width: 200px;\">\r\n    </div>\r\n    \r\n    <div style=\"background-color: #fff; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);\">\r\n        <h2 style=\"color: #3498db; text-align: center;\">🎉🎂 Birthday Reminder 🎂🎉</h2>\r\n        \r\n        <p>Dear {full_name},</p>\r\n        \r\n        <p>We\'re excited to remind you that your birthday is coming up in <strong>{days_text}</strong> on <strong>{upcoming_birthday_formatted}</strong>! 🎈🎊</p>\r\n        \r\n        <p>At <strong>Freedom Assembly Church International</strong>, we cherish every member of our church family and want to make your special day unforgettable. 🥳✨</p>\r\n        \r\n        <p>We are looking forward to celebrating with you on <strong>{upcoming_birthday_day}, {upcoming_birthday_date}</strong>! 🎂🙏</p>\r\n        \r\n        <div style=\"text-align: center; margin: 30px 0;\">\r\n            <div style=\"display: inline-block; background-color: #3498db; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; font-weight: bold;\">🎉 Happy Early Birthday! 🎁</div>\r\n        </div>\r\n        \r\n        <p>🎊 Many blessings,<br>\r\n        <strong>Freedom Assembly Church International Team</strong></p>\r\n    </div>\r\n    \r\n    <div style=\"text-align: center; margin-top: 20px; font-size: 12px; color: #666;\">\r\n        <p>Freedom Assembly Church International<br>\r\n        2520 Albertina Sisulu Road, Florida 1709, Johannesburg, South Africa<br>\r\n        📧 <EMAIL> | 📞 +27102712223</p>\r\n    </div>\r\n</div>', 0, '2025-03-02 15:10:22', '2025-03-08 10:51:12', 'general'),
(7, 'Birthday Template 4', 'Celebrating {full_name}\'s Special Day with Freedom Assembly', '<div style=\"max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 20px;\">\r\n    <div style=\"text-align: center; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\r\n        <h1 style=\"color: #3498db; font-size: 28px; margin-bottom: 20px;\">Happy Birthday!</h1>\r\n        \r\n        <img src=\"{member_image}\" alt=\"{full_name}\" style=\"width: 150px; height: 150px; border-radius: 75px; margin: 20px auto; display: block; object-fit: cover; border: 5px solid #f8f9fa; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\r\n        \r\n        <div style=\"background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;\">\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 15px;\">Dear {full_name},</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 20px;\">On behalf of Freedom Assembly Church International, we wish you a very Happy Birthday! May this special day bring you joy, peace, and blessings.</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 20px;\">We are grateful to have you as part of our church family and pray that this new year of your life brings you closer to God\'s purpose for you.</p>\r\n        </div>\r\n        \r\n        <div style=\"background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; margin: 20px 0;\">\r\n            <h2 style=\"color: #3498db; font-size: 20px; margin-bottom: 15px;\">Birthday Scripture</h2>\r\n            <p style=\"font-style: italic; color: #555; font-size: 16px; line-height: 1.6;\">\"For I know the plans I have for you,\" declares the LORD, \"plans to prosper you and not to harm you, plans to give you hope and a future.\" - Jeremiah 29:11</p>\r\n        </div>\r\n        \r\n        <p style=\"font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;\">May God continue to bless you abundantly!</p>\r\n        \r\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;\">\r\n            <p style=\"color: #666; font-size: 14px;\">Blessings,<br><strong>Freedom Assembly Church International</strong></p>\r\n        </div>\r\n    </div>\r\n</div>', 1, '2025-03-03 07:04:44', '2025-03-08 11:19:08', 'birthday'),
(10, 'Birthday Template 3: Modern Green Theme', 'Wishing {full_name} a Blessed Birthday!', '<!-- Birthday Template 3: Modern Green Theme -->\r\n<div style=\"max-width: 600px; margin: 0 auto; font-family: \'Verdana\', sans-serif; color: #2f4f4f; background-color: #e8f5e9; padding: 20px;\">\r\n    <div style=\"text-align: center; background-color: #ffffff; padding: 20px; border-radius: 10px; box-shadow: 0 2px 6px rgba(0,0,0,0.1);\">\r\n        <h1 style=\"color: #2e8b57; font-size: 27px;\">🎂🎉 Happy Birthday, {full_name}! 🎉🎂</h1>\r\n        \r\n        <img src=\"{member_image}\" alt=\"{full_name}\" style=\"width: 160px; height: 160px; border-radius: 50%; border: 4px solid #2e8b57; margin-top: 15px;\">\r\n        \r\n        <p style=\"font-size: 16px; line-height: 1.6; margin-top: 15px;\">🎈🎁 <strong>Freedom Assembly Church International</strong> is grateful to have you as part of our family. May this new year bring you closer to your divine purpose and fill your heart with happiness! 🙌✨</p>\r\n        \r\n        <p style=\"font-size: 16px; font-style: italic; color: #2f4f4f; background-color: #d0f0c0; padding: 15px; border-radius: 6px;\">📖 \"Delight yourself in the Lord, and He will give you the desires of your heart.\" – Psalm 37:4 💖</p>\r\n        \r\n        <p style=\"margin-top: 20px; font-size: 16px;\">🥳🎂 Wishing you a joyful and prosperous year ahead! 🎂🎈</p>\r\n    </div>\r\n</div>', 1, '2025-03-03 08:02:16', '2025-03-08 10:36:35', 'birthday'),
(12, 'Welcome Email', 'Welcome to Freedom Assembly, {full_name}!', '<div style=\"max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 20px;\">\r\n    <div style=\"text-align: center; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\r\n        <h1 style=\"color: #2c3e50; font-size: 28px; margin-bottom: 20px;\">Welcome to Freedom Assembly Church International!</h1>\r\n        \r\n        <img src=\"{member_image}\" alt=\"{full_name}\" style=\"width: 150px; height: 150px; border-radius: 75px; margin: 20px auto; display: block; object-fit: cover; border: 5px solid #f8f9fa; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\r\n        \r\n        <div style=\"background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;\">\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 15px;\">Dear {full_name},</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 20px;\">Thank you for registering with Freedom Assembly Church International. We are delighted to have you as part of our church family!</p>\r\n        </div>\r\n\r\n        <div style=\"background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; margin: 20px 0;\">\r\n            <h2 style=\"color: #3498db; font-size: 20px; margin-bottom: 15px;\">Your Registration Details</h2>\r\n            <table style=\"width: 100%; border-collapse: collapse;\">\r\n                <tr>\r\n                    <td style=\"padding: 10px; border-bottom: 1px solid #eee; color: #666;\">Full Name:</td>\r\n                    <td style=\"padding: 10px; border-bottom: 1px solid #eee;\">{full_name}</td>\r\n                </tr>\r\n                <tr>\r\n                    <td style=\"padding: 10px; border-bottom: 1px solid #eee; color: #666;\">Email:</td>\r\n                    <td style=\"padding: 10px; border-bottom: 1px solid #eee;\">{email}</td>\r\n                </tr>\r\n                <tr>\r\n                    <td style=\"padding: 10px; color: #666;\">Phone:</td>\r\n                    <td style=\"padding: 10px;\">{phone_number}</td>\r\n                </tr>\r\n            </table>\r\n        </div>\r\n\r\n        <p style=\"font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;\">We look forward to seeing you in our next service!</p>\r\n\r\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;\">\r\n            <p style=\"color: #666; font-size: 14px;\">Blessings,<br><strong>Freedom Assembly Church International</strong></p>\r\n        </div>\r\n    </div>\r\n</div>', 0, '2025-03-03 13:05:36', '2025-03-08 10:52:36', 'general'),
(13, 'Birthday Reminder Template 2: Warm Gold Theme', '{full_name}\'s birthday is coming up in {days_text}!', '<!-- Birthday Reminder Email Template 2: Warm Gold Theme -->\r\n<div style=\"max-width: 600px; margin: 0 auto; font-family: Georgia, serif; color: #5a4a42; background-color: #fdf7e4; padding: 20px; border-radius: 5px;\">\r\n    <div style=\"text-align: center; margin-bottom: 20px;\">\r\n        <img src=\"{church_logo}\" alt=\"Church Logo\" style=\"max-width: 200px;\">\r\n    </div>\r\n    \r\n    <div style=\"background-color: #fff3d4; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);\">\r\n        <h2 style=\"color: #d4a017; text-align: center;\">🌟🎊 A Special Birthday Reminder 🎊🌟</h2>\r\n        \r\n        <p>Dear {full_name},</p>\r\n        \r\n        <p>Your special day is almost here! 🎂🎉 In just <strong>{days_text}</strong>, we will celebrate your birthday on <strong>{upcoming_birthday_formatted}</strong>! 🎈✨</p>\r\n        \r\n        <p>At <strong>Freedom Assembly Church International</strong>, we appreciate and value you as part of our family. May your birthday be filled with love and joy. 🥳🙏</p>\r\n        \r\n        <p>We are looking forward to celebrating you on <strong>{upcoming_birthday_day}, {upcoming_birthday_date}</strong>! 🎁💖</p>\r\n        \r\n        <div style=\"text-align: center; margin: 30px 0;\">\r\n            <div style=\"display: inline-block; background-color: #d4a017; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; font-weight: bold;\">🎈 Happy Early Birthday! 🎉</div>\r\n        </div>\r\n        \r\n        <p>🎊 Stay blessed,<br>\r\n        <strong>Freedom Assembly Church International Team</strong></p>\r\n    </div>\r\n</div>', 0, '2025-03-03 14:57:16', '2025-03-08 10:54:33', 'general'),
(14, 'Member Upcoming Birthday Notification 1', 'Celebrate with us! {birthday_member_name}\'s birthday is coming up {days_text}', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f9f7ff;\r\n            color: #333;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 0 auto;\r\n            padding: 20px;\r\n        }\r\n        .card {\r\n            background-color: #ffffff;\r\n            border-radius: 12px;\r\n            box-shadow: 0 5px 20px rgba(95, 46, 234, 0.15);\r\n            overflow: hidden;\r\n        }\r\n        .card-header {\r\n            background: linear-gradient(135deg, #5f2eea, #a485fd);\r\n            color: white;\r\n            padding: 30px;\r\n            text-align: center;\r\n        }\r\n        .card-header h1 {\r\n            margin: 0;\r\n            font-size: 28px;\r\n            font-weight: bold;\r\n        }\r\n        .card-header p {\r\n            margin: 10px 0 0;\r\n            opacity: 0.9;\r\n            font-size: 16px;\r\n        }\r\n        .card-body {\r\n            padding: 30px;\r\n        }\r\n        .greeting {\r\n            font-size: 18px;\r\n            margin-bottom: 25px;\r\n            line-height: 1.6;\r\n        }\r\n        .member-profile {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            margin: 30px 0;\r\n            position: relative;\r\n        }\r\n        .floating-balloons {\r\n            position: absolute;\r\n            width: 100%;\r\n            height: 100%;\r\n            top: -20px;\r\n            left: 0;\r\n            background-image: url(\"data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%235f2eea\' fill-opacity=\'0.1\' fill-rule=\'evenodd\'/%3E%3C/svg%3E\");\r\n            opacity: 0.5;\r\n            z-index: 1;\r\n        }\r\n        .profile-photo {\r\n            width: 160px;\r\n            height: 160px;\r\n            border-radius: 50%;\r\n            border: 5px solid #f9f7ff;\r\n            box-shadow: 0 5px 15px rgba(95, 46, 234, 0.2);\r\n            overflow: hidden;\r\n            position: relative;\r\n            z-index: 2;\r\n        }\r\n        .profile-photo img {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n        }\r\n        .profile-name {\r\n            margin: 15px 0 5px;\r\n            font-size: 24px;\r\n            font-weight: bold;\r\n            color: #5f2eea;\r\n            z-index: 2;\r\n        }\r\n        .birthday-details {\r\n            background-color: #f9f7ff;\r\n            border-radius: 10px;\r\n            padding: 20px;\r\n            margin: 20px 0;\r\n            text-align: center;\r\n            border: 1px solid #e9e3ff;\r\n        }\r\n        .birthday-date {\r\n            font-size: 20px;\r\n            font-weight: bold;\r\n            color: #5f2eea;\r\n            margin-bottom: 10px;\r\n        }\r\n        .birthday-countdown {\r\n            display: inline-block;\r\n            background: linear-gradient(135deg, #5f2eea, #a485fd);\r\n            color: white;\r\n            padding: 8px 20px;\r\n            border-radius: 20px;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            margin-top: 10px;\r\n        }\r\n        .celebration-suggestions {\r\n            background-color: #f9f7ff;\r\n            border-radius: 10px;\r\n            padding: 20px;\r\n            margin: 25px 0;\r\n        }\r\n        .celebration-suggestions h3 {\r\n            color: #5f2eea;\r\n            margin-top: 0;\r\n            font-size: 18px;\r\n        }\r\n        .celebration-list {\r\n            display: grid;\r\n            grid-template-columns: 1fr 1fr;\r\n            gap: 15px;\r\n            margin-top: 15px;\r\n        }\r\n        .celebration-item {\r\n            background-color: white;\r\n            padding: 15px;\r\n            border-radius: 8px;\r\n            border-left: 3px solid #5f2eea;\r\n            font-size: 15px;\r\n        }\r\n        .action-button {\r\n            display: block;\r\n            width: 220px;\r\n            margin: 30px auto;\r\n            padding: 14px 0;\r\n            background: linear-gradient(135deg, #5f2eea, #a485fd);\r\n            color: white;\r\n            text-decoration: none;\r\n            text-align: center;\r\n            border-radius: 30px;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            box-shadow: 0 4px 10px rgba(95, 46, 234, 0.3);\r\n            transition: all 0.3s ease;\r\n        }\r\n        .action-button:hover {\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 6px 15px rgba(95, 46, 234, 0.4);\r\n        }\r\n        .scripture {\r\n            font-style: italic;\r\n            color: #666;\r\n            text-align: center;\r\n            margin: 25px 0;\r\n            padding: 15px;\r\n            border-top: 1px solid #e9e3ff;\r\n            border-bottom: 1px solid #e9e3ff;\r\n        }\r\n        .footer {\r\n            background-color: #f9f7ff;\r\n            border-top: 1px solid #e9e3ff;\r\n            padding: 20px;\r\n            text-align: center;\r\n            color: #666;\r\n            font-size: 14px;\r\n        }\r\n        .church-name {\r\n            color: #5f2eea;\r\n            font-weight: bold;\r\n        }\r\n        @media (max-width: 600px) {\r\n            .celebration-list {\r\n                grid-template-columns: 1fr;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <!-- Birthday Email 1 -->\r\n        <div class=\"card\">\r\n            <div class=\"card-header\">\r\n                <h1>Happy Birthday, {birthday_member_name}!</h1>\r\n                <p>Let\'s Celebrate This Special Day!</p>\r\n            </div>\r\n            <div class=\"card-body\">\r\n                <div class=\"greeting\">\r\n                    <p>Dear {full_name},</p>\r\n                    <p>We are thrilled to announce that our dear member, <strong>{birthday_member_name}</strong>, will be celebrating their birthday <strong>{days_text}</strong>!</p>\r\n                </div>\r\n                \r\n                <div class=\"member-profile\">\r\n                    <div class=\"floating-balloons\"></div>\r\n                    <div class=\"profile-photo\">\r\n                        <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\">\r\n                    </div>\r\n                    <h2 class=\"profile-name\">{birthday_member_name}</h2>\r\n                </div>\r\n                \r\n                <div class=\"birthday-details\">\r\n                    <p>Birthday Date:</p>\r\n                    <div class=\"birthday-date\">{upcoming_birthday_formatted}</div>\r\n                    <p>Turning:</p>\r\n                    <div class=\"birthday-countdown\">{birthday_member_age} Years Old</div>\r\n                </div>\r\n\r\n                <div class=\"celebration-suggestions\">\r\n                    <h3>How You Can Celebrate:</h3>\r\n                    <div class=\"celebration-list\">\r\n                        <div class=\"celebration-item\">Send a heartfelt note of blessing</div>\r\n                        <div class=\"celebration-item\">Offer a prayer for their joy and growth</div>\r\n                        <div class=\"celebration-item\">Gift them something meaningful</div>\r\n                        <div class=\"celebration-item\">Share a verse of encouragement</div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"scripture\">\r\n                    \"The LORD has done great things for us, and we are filled with joy.\" - Psalm 126:3\r\n                </div>\r\n                \r\n                <a href=\"#\" class=\"action-button\">Send Birthday Wishes</a>\r\n            </div>\r\n            <div class=\"footer\">\r\n                <p>With love from,<br><span class=\"church-name\">Freedom Assembly Church</span></p>\r\n                            </div>\r\n        </div>', 0, '2025-03-03 15:03:11', '2025-03-08 11:21:21', 'general'),
(15, 'Birthday Reminder Template 3: Modern Green Theme', 'Your Birthday is Coming Up {days_text}!', '<!-- Birthday Reminder Email Template 3: Modern Green Theme -->\r\n<div style=\"max-width: 600px; margin: 0 auto; font-family: Verdana, sans-serif; color: #2f4f4f; background-color: #e8f5e9; padding: 20px; border-radius: 5px;\">\r\n    <div style=\"text-align: center; margin-bottom: 20px;\">\r\n        <img src=\"{church_logo}\" alt=\"Church Logo\" style=\"max-width: 200px;\">\r\n    </div>\r\n    \r\n    <div style=\"background-color: #ffffff; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);\">\r\n        <h2 style=\"color: #2e8b57; text-align: center;\">🎂🎉 Birthday Alert! 🎉🎂</h2>\r\n        \r\n        <p>Dear {full_name},</p>\r\n        \r\n        <p>Guess what? Your birthday is just around the corner! 🎁🎊 In <strong>{days_text}</strong>, on <strong>{upcoming_birthday_formatted}</strong>, we will celebrate YOU! 🎈🥳</p>\r\n        \r\n        <p>At <strong>Freedom Assembly Church International</strong>, we thank God for your life and all that He has in store for you. May your new year be filled with favor and grace! 🙌🎉</p>\r\n        \r\n        <p>We can’t wait to celebrate you on <strong>{upcoming_birthday_day}, {upcoming_birthday_date}</strong>! 🎊✨</p>\r\n        \r\n        <div style=\"text-align: center; margin: 30px 0;\">\r\n            <div style=\"display: inline-block; background-color: #2e8b57; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; font-weight: bold;\">🎁 Wishing You a Blessed Year! 🎂</div>\r\n        </div>\r\n        \r\n        <p>🎊 Blessings always,<br>\r\n        <strong>Freedom Assembly Church International Team</strong></p>\r\n    </div>\r\n</div>', 0, '2025-03-03 17:58:42', '2025-03-08 11:00:20', 'general'),
(17, 'Member Upcoming Birthday Notification 2', 'Join us in celebrating a birthday {days_text}!', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #fdf0f6;\r\n            color: #333;\r\n            text-align: center;\r\n        }\r\n\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 40px auto;\r\n            padding: 20px;\r\n            background-color: #fff;\r\n            border-radius: 20px;\r\n            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);\r\n        }\r\n\r\n        .card {\r\n            overflow: hidden;\r\n            border-radius: 16px;\r\n        }\r\n\r\n        .header {\r\n            background: linear-gradient(135deg, #ff6f61, #fbb6ac);\r\n            padding: 40px;\r\n            color: white;\r\n            border-radius: 16px 16px 0 0;\r\n        }\r\n\r\n        .header h1 {\r\n            font-size: 28px;\r\n            margin: 0;\r\n            font-weight: bold;\r\n        }\r\n\r\n        .content {\r\n            padding: 20px;\r\n        }\r\n\r\n        .photo-frame {\r\n            position: relative;\r\n            margin: 20px auto;\r\n            width: 140px;\r\n            height: 140px;\r\n            border-radius: 50%;\r\n            border: 5px solid white;\r\n            overflow: hidden;\r\n            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);\r\n        }\r\n\r\n        .member-photo {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n        }\r\n\r\n        .member-name {\r\n            font-size: 26px;\r\n            font-weight: bold;\r\n            color: #ff6f61;\r\n            margin-top: 15px;\r\n        }\r\n\r\n        .birthday-info {\r\n            background: #ffeff3;\r\n            padding: 15px;\r\n            border-radius: 12px;\r\n            margin: 20px auto;\r\n            display: inline-block;\r\n            text-align: center;\r\n            width: 100%;\r\n        }\r\n\r\n        .birthday-date, .birthday-age {\r\n            font-weight: bold;\r\n            font-size: 20px;\r\n            color: #ff6f61;\r\n        }\r\n\r\n        .message {\r\n            font-size: 18px;\r\n            line-height: 1.6;\r\n            margin: 20px 0;\r\n            color: #555;\r\n        }\r\n\r\n        .wish-list {\r\n            text-align: left;\r\n            background: #f8f9fa;\r\n            border-radius: 12px;\r\n            padding: 20px;\r\n        }\r\n\r\n        .wish-list h3 {\r\n            color: #ff6f61;\r\n            margin-top: 0;\r\n        }\r\n\r\n        .wish-list ul {\r\n            padding-left: 20px;\r\n        }\r\n\r\n        .wish-list li {\r\n            margin: 8px 0;\r\n            font-size: 16px;\r\n        }\r\n\r\n        .cta-button {\r\n            display: inline-block;\r\n            background: linear-gradient(135deg, #ff6f61, #fbb6ac);\r\n            color: white;\r\n            text-decoration: none;\r\n            padding: 12px 20px;\r\n            border-radius: 30px;\r\n            font-weight: bold;\r\n            transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n            margin-top: 20px;\r\n        }\r\n\r\n        .cta-button:hover {\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 5px 15px rgba(255, 111, 97, 0.3);\r\n        }\r\n\r\n        .footer {\r\n            padding: 20px;\r\n            background: #ffeff3;\r\n            color: #718096;\r\n            font-size: 14px;\r\n            border-radius: 0 0 16px 16px;\r\n        }\r\n\r\n        .footer-links {\r\n            text-align: center;\r\n            padding: 20px 0;\r\n            border-top: 1px solid #eee;\r\n            margin-top: 20px;\r\n        }\r\n\r\n        .footer-links .view-online {\r\n            color: #666;\r\n            text-decoration: none;\r\n            font-size: 12px;\r\n        }\r\n\r\n        .tracking-element {\r\n            position: absolute !important;\r\n            width: 1px !important;\r\n            height: 1px !important;\r\n            padding: 0 !important;\r\n            margin: -1px !important;\r\n            overflow: hidden !important;\r\n            clip: rect(0,0,0,0) !important;\r\n            white-space: nowrap !important;\r\n            border: 0 !important;\r\n            opacity: 0 !important;\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>Upcoming Birthday!</h1>\r\n            </div>\r\n            <div class=\"content\">\r\n                <p>Hello <strong>{full_name}</strong>,</p>\r\n                <p>We are excited to celebrate a special birthday in our church family <strong>{days_text}</strong>!</p>\r\n                \r\n                <div class=\"photo-frame\">\r\n                    <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\" class=\"member-photo\">\r\n                </div>\r\n                \r\n                <div class=\"member-name\">{birthday_member_name}</div>\r\n                \r\n                <div class=\"birthday-info\">\r\n                    <p>Birthday on:</p>\r\n                    <div class=\"birthday-date\">{upcoming_birthday_formatted}</div>\r\n                    <div class=\"birthday-age\">{birthday_member_age} years</div>\r\n                </div>\r\n                \r\n                <div class=\"message\">\r\n                    <p>Let\'s make this day memorable for {birthday_member_name}!</p>\r\n                </div>\r\n                \r\n                <div class=\"wish-list\">\r\n                    <h3>Ways to Celebrate:</h3>\r\n                    <ul>\r\n                        <li>Send a thoughtful message or prayer</li>\r\n                        <li>Give them a call</li>\r\n                        <li>Share an inspiring scripture verse</li>\r\n                        <li>Consider a small gift or card</li>\r\n                    </ul>\r\n                </div>\r\n                \r\n                <a href=\"#\" class=\"cta-button\">Send Birthday Wishes</a>\r\n            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <p>With blessings,<br><strong>Freedom Assembly Church</strong></p>\r\n                            </div>\r\n        </div>\r\n    </div>\r\n    \r\n    <!-- At the end of your template, before </body> -->\r\n    <div class=\"tracking-element\" aria-hidden=\"true\">\r\n        {tracking_pixel}\r\n    </div>\r\n    <div class=\"footer-links\">\r\n        <a href=\"{view_online_url}\" class=\"view-online\">View in browser</a>\r\n    </div>\r\n</body>\r\n</html>', 0, '2025-03-03 18:33:51', '2025-03-08 11:27:41', 'general'),
(18, 'Member Upcoming Birthday Notification 3', 'Celebrate a birthday {days_text}!', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f5f3ff;\r\n            color: #333;\r\n        }\r\n        .container {\r\n            max-width: 650px;\r\n            margin: 0 auto;\r\n            padding: 20px;\r\n        }\r\n        .card {\r\n            background-color: #ffffff;\r\n            border-radius: 20px;\r\n            box-shadow: 0 10px 40px rgba(95, 46, 234, 0.1);\r\n            overflow: hidden;\r\n            padding: 30px;\r\n        }\r\n        .card-header {\r\n            background: linear-gradient(135deg, #5f2eea, #9a8bf0);\r\n            color: white;\r\n            text-align: center;\r\n            border-radius: 15px;\r\n            padding: 40px;\r\n            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .card-header h1 {\r\n            margin: 0;\r\n            font-size: 36px;\r\n            font-weight: bold;\r\n            text-transform: uppercase;\r\n        }\r\n        .card-header p {\r\n            font-size: 18px;\r\n            margin-top: 10px;\r\n            opacity: 0.85;\r\n        }\r\n        .card-body {\r\n            padding: 30px;\r\n        }\r\n        .greeting {\r\n            font-size: 20px;\r\n            margin-bottom: 25px;\r\n            line-height: 1.8;\r\n            color: #555;\r\n        }\r\n        .member-profile {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            position: relative;\r\n            margin: 20px 0;\r\n        }\r\n        .floating-balloons {\r\n            position: absolute;\r\n            top: -20px;\r\n            left: 0;\r\n            width: 100%;\r\n            height: 100%;\r\n            background-image: url(\"data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%235f2eea\' fill-opacity=\'0.1\' fill-rule=\'evenodd\'/%3E%3C/svg%3E\");\r\n            opacity: 0.6;\r\n            z-index: 1;\r\n        }\r\n        .profile-photo {\r\n            width: 180px;\r\n            height: 180px;\r\n            border-radius: 50%;\r\n            border: 8px solid #f9f7ff;\r\n            box-shadow: 0 8px 30px rgba(95, 46, 234, 0.2);\r\n            position: relative;\r\n            z-index: 2;\r\n        }\r\n        .profile-photo img {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n            border-radius: 50%;\r\n        }\r\n        .profile-name {\r\n            margin: 20px 0;\r\n            font-size: 28px;\r\n            font-weight: bold;\r\n            color: #5f2eea;\r\n            z-index: 2;\r\n        }\r\n        .birthday-details {\r\n            background-color: #f7f4ff;\r\n            border-radius: 12px;\r\n            padding: 25px;\r\n            margin: 25px 0;\r\n            text-align: center;\r\n            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .birthday-date {\r\n            font-size: 24px;\r\n            font-weight: bold;\r\n            color: #5f2eea;\r\n            margin-bottom: 10px;\r\n        }\r\n        .birthday-countdown {\r\n            display: inline-block;\r\n            background: linear-gradient(135deg, #5f2eea, #a485fd);\r\n            color: white;\r\n            padding: 10px 25px;\r\n            border-radius: 25px;\r\n            font-weight: bold;\r\n            font-size: 18px;\r\n            margin-top: 12px;\r\n        }\r\n        .celebration-suggestions {\r\n            background-color: #f9f7ff;\r\n            border-radius: 12px;\r\n            padding: 25px;\r\n            margin: 25px 0;\r\n            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .celebration-suggestions h3 {\r\n            color: #5f2eea;\r\n            font-size: 20px;\r\n            margin-top: 0;\r\n        }\r\n        .celebration-list {\r\n            display: grid;\r\n            grid-template-columns: 1fr 1fr;\r\n            gap: 20px;\r\n            margin-top: 20px;\r\n        }\r\n        .celebration-item {\r\n            background-color: white;\r\n            padding: 18px;\r\n            border-radius: 10px;\r\n            border-left: 5px solid #5f2eea;\r\n            font-size: 16px;\r\n            color: #444;\r\n        }\r\n        .action-button {\r\n            display: block;\r\n            width: 240px;\r\n            margin: 30px auto;\r\n            padding: 16px 0;\r\n            background: linear-gradient(135deg, #5f2eea, #a485fd);\r\n            color: white;\r\n            text-decoration: none;\r\n            text-align: center;\r\n            border-radius: 50px;\r\n            font-weight: bold;\r\n            font-size: 18px;\r\n            box-shadow: 0 6px 15px rgba(95, 46, 234, 0.3);\r\n            transition: all 0.3s ease;\r\n        }\r\n        .action-button:hover {\r\n            transform: translateY(-3px);\r\n            box-shadow: 0 8px 20px rgba(95, 46, 234, 0.4);\r\n        }\r\n        .scripture {\r\n            font-style: italic;\r\n            color: #666;\r\n            text-align: center;\r\n            margin: 25px 0;\r\n            padding: 15px;\r\n            border-top: 2px solid #e9e3ff;\r\n            border-bottom: 2px solid #e9e3ff;\r\n        }\r\n        .footer {\r\n            background-color: #f9f7ff;\r\n            border-top: 2px solid #e9e3ff;\r\n            padding: 30px;\r\n            text-align: center;\r\n            color: #666;\r\n            font-size: 16px;\r\n        }\r\n        .church-name {\r\n            color: #5f2eea;\r\n            font-weight: bold;\r\n        }\r\n        @media (max-width: 600px) {\r\n            .celebration-list {\r\n                grid-template-columns: 1fr;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"card-header\">\r\n                <h1>Birthday Celebration</h1>\r\n                <p>Let\'s make this birthday unforgettable!</p>\r\n            </div>\r\n            \r\n            <div class=\"card-body\">\r\n                <div class=\"greeting\">\r\n                    <p>Dear <strong>{full_name}</strong>,</p>\r\n                    <p>We\'re thrilled to announce that <strong>{birthday_member_name}</strong> will be celebrating their special day in <strong>{days_text}</strong>!</p>\r\n                </div>\r\n                \r\n                <div class=\"member-profile\">\r\n                    <div class=\"floating-balloons\"></div>\r\n                    <div class=\"profile-photo\">\r\n                        <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\">\r\n                    </div>\r\n                    <h2 class=\"profile-name\">{birthday_member_name}</h2>\r\n                </div>\r\n                \r\n                <div class=\"birthday-details\">\r\n                    <p>Birthday Date:</p>\r\n                    <div class=\"birthday-date\">{upcoming_birthday_formatted}</div>\r\n                    <p>Turning:</p>\r\n                    <div class=\"birthday-countdown\">{birthday_member_age} Years Old</div>\r\n                </div>\r\n                \r\n                <p>Let\'s show {birthday_member_name} our love and appreciation on this special day!</p>\r\n                \r\n                <div class=\"celebration-suggestions\">\r\n                    <h3>Ways to Celebrate:</h3>\r\n                    <div class=\"celebration-list\">\r\n                        <div class=\"celebration-item\">Send a personal message of blessing</div>\r\n                        <div class=\"celebration-item\">Offer a prayer for their new year</div>\r\n                        <div class=\"celebration-item\">Share a meaningful scripture</div>\r\n                        <div class=\"celebration-item\">Consider a small gift or card</div>\r\n                    </div>\r\n                </div>\r\n                \r\n                <div class=\"scripture\">\r\n                    \"This is the day that the LORD has made; let us rejoice and be glad in it.\" - Psalm 118:24\r\n                </div>\r\n                \r\n                <a href=\"#\" class=\"action-button\">Send Birthday Wishes</a>\r\n            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <p>With blessings from,<br><span class=\"church-name\">Freedom Assembly Church</span></p>\r\n                            </div>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>', 0, '2025-03-03 18:33:56', '2025-03-08 11:29:35', 'general');
INSERT INTO `email_templates` (`id`, `template_name`, `subject`, `content`, `is_birthday_template`, `created_at`, `updated_at`, `template_category`) VALUES
(19, 'Member Upcoming Birthday Notification 4', 'Join us in celebrating a birthday {days_text}!', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #fdf2e9;\r\n            color: #2d3748;\r\n            line-height: 1.5;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 20px auto;\r\n            padding: 0 15px;\r\n        }\r\n        .card {\r\n            background: #ffffff;\r\n            border-radius: 20px;\r\n            box-shadow: 0 8px 25px rgba(255, 107, 0, 0.2);\r\n            overflow: hidden;\r\n            width: 100%;\r\n        }\r\n        .header {\r\n            background: linear-gradient(120deg, #ff6b00, #ff9e40);\r\n            padding: 40px 20px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n            border-bottom: 5px solid #ff9e40;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 30px;\r\n            font-weight: bold;\r\n        }\r\n        .content {\r\n            padding: 30px 20px;\r\n            text-align: center;\r\n        }\r\n        .greeting {\r\n            font-size: 18px;\r\n            margin: 0 0 25px;\r\n            color: #4a5568;\r\n        }\r\n        .photo-container {\r\n            margin: 0 auto 20px;\r\n            width: 140px;\r\n            height: 140px;\r\n        }\r\n        .photo {\r\n            width: 100%;\r\n            height: 100%;\r\n            border-radius: 50%;\r\n            border: 5px solid #fff8e6;\r\n            overflow: hidden;\r\n            box-shadow: 0 6px 15px rgba(255, 107, 0, 0.3);\r\n        }\r\n        .photo img {\r\n            width: 100%;\r\n            height: 100%;\r\n            display: block;\r\n            object-fit: cover;\r\n        }\r\n        .member-name {\r\n            font-size: 28px;\r\n            font-weight: 700;\r\n            color: #ff6b00;\r\n            margin: 15px 0;\r\n            text-transform: uppercase;\r\n        }\r\n        .birthday-info {\r\n            background: #fff2cc;\r\n            border-radius: 15px;\r\n            padding: 20px;\r\n            margin: 25px 0;\r\n            display: inline-block;\r\n            border: 2px solid #ff9e40;\r\n        }\r\n        .birthday-date {\r\n            font-size: 22px;\r\n            font-weight: 600;\r\n            color: #ff6b00;\r\n            margin: 5px 0;\r\n        }\r\n        .birthday-age {\r\n            font-size: 16px;\r\n            font-weight: 700;\r\n            color: #ffffff;\r\n            background: #ff6b00;\r\n            padding: 8px 18px;\r\n            border-radius: 15px;\r\n            display: inline-block;\r\n            margin-top: 10px;\r\n        }\r\n        .suggestions {\r\n            margin: 25px 0;\r\n            text-align: left;\r\n            background: #fff2cc;\r\n            padding: 20px;\r\n            border-radius: 15px;\r\n        }\r\n        .suggestions h3 {\r\n            color: #ff6b00;\r\n            font-size: 20px;\r\n            margin: 0 0 15px;\r\n            font-weight: 600;\r\n        }\r\n        .suggestions ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .suggestions li {\r\n            padding: 10px 0;\r\n            border-bottom: 1px solid #ffe4b3;\r\n            color: #4a5568;\r\n            font-size: 16px;\r\n        }\r\n        .suggestions li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .cta-button {\r\n            display: inline-block;\r\n            background: linear-gradient(120deg, #ff6b00, #ff9e40);\r\n            color: #ffffff;\r\n            text-decoration: none;\r\n            padding: 12px 35px;\r\n            border-radius: 30px;\r\n            font-weight: 600;\r\n            font-size: 18px;\r\n            margin: 20px 0;\r\n            box-shadow: 0 6px 15px rgba(255, 107, 0, 0.3);\r\n            transition: 0.3s ease;\r\n        }\r\n        .cta-button:hover {\r\n            background: linear-gradient(120deg, #ff9e40, #ff6b00);\r\n            transform: scale(1.05);\r\n        }\r\n        .footer {\r\n            background: #fdf2e9;\r\n            padding: 15px;\r\n            text-align: center;\r\n            font-size: 14px;\r\n            color: #718096;\r\n            border-top: 1px solid #ffe4b3;\r\n        }\r\n        .church-name {\r\n            color: #ff6b00;\r\n            font-weight: 700;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 10px;\r\n            font-size: 12px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #718096;\r\n            text-decoration: none;\r\n        }\r\n        .unsubscribe a:hover {\r\n            text-decoration: underline;\r\n        }\r\n        /* Mobile view optimized as a notification */\r\n        @media screen and (max-width: 480px) {\r\n            .container {\r\n                max-width: 320px;\r\n                margin: 10px auto;\r\n                padding: 0;\r\n            }\r\n            .card {\r\n                border-radius: 15px;\r\n                box-shadow: 0 6px 20px rgba(255, 107, 0, 0.2);\r\n                border: 1px solid #ff9e40;\r\n            }\r\n            .header {\r\n                padding: 20px;\r\n            }\r\n            .header h1 {\r\n                font-size: 22px;\r\n            }\r\n            .content {\r\n                padding: 15px;\r\n            }\r\n            .greeting {\r\n                font-size: 16px;\r\n                margin-bottom: 15px;\r\n            }\r\n            .photo-container {\r\n                width: 90px;\r\n                height: 90px;\r\n                margin: 0 auto 15px;\r\n            }\r\n            .photo {\r\n                border-width: 4px;\r\n            }\r\n            .member-name {\r\n                font-size: 20px;\r\n                margin: 10px 0;\r\n            }\r\n            .birthday-info {\r\n                padding: 12px;\r\n                margin: 15px 0;\r\n                border: 1px solid #ff9e40;\r\n            }\r\n            .birthday-date {\r\n                font-size: 16px;\r\n            }\r\n            .birthday-age {\r\n                font-size: 14px;\r\n                padding: 5px 12px;\r\n            }\r\n            .suggestions {\r\n                padding: 12px;\r\n                text-align: center;\r\n            }\r\n            .suggestions h3 {\r\n                font-size: 16px;\r\n                margin: 0 0 10px;\r\n            }\r\n            .cta-button {\r\n                padding: 10px 25px;\r\n                font-size: 16px;\r\n                width: 80%;\r\n                margin: 10px auto;\r\n            }\r\n            .footer {\r\n                padding: 10px;\r\n                font-size: 12px;\r\n            }\r\n            .unsubscribe {\r\n                margin-top: 5px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>🎉 Upcoming Birthday Joy! 🎉</h1>\r\n            </div>\r\n            <div class=\"content\">\r\n                <div class=\"greeting\">\r\n                    <p>Hello <strong>{full_name}</strong>,</p>\r\n                    <p>Get ready to celebrate with us! A special birthday is coming up in our church family <strong>{days_text}</strong> and we want you to be part of the celebration! 🎂🎈</p>\r\n                </div>\r\n\r\n                <div class=\"photo-container\">\r\n                    <div class=\"photo\">\r\n                        <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\">\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"member-name\">{birthday_member_name}</div>\r\n\r\n                <div class=\"birthday-info\">\r\n                    <p><strong>Birthday:</strong></p>\r\n                    <div class=\"birthday-date\">{upcoming_birthday_formatted}</div>\r\n                    <div class=\"birthday-age\">{birthday_member_age} Years</div>\r\n                </div>\r\n\r\n                <div class=\"suggestions\">\r\n                    <h3>🎁 Celebration Ideas 🎉</h3>\r\n                    <ul>\r\n                        <li>Send a heartfelt birthday message ✉️</li>\r\n                        <li>Offer a prayer for blessings 🙏</li>\r\n                        <li>Share an uplifting Bible verse 📖</li>\r\n                        <li>Surprise them with a small thoughtful gift 🎁</li>\r\n                    </ul>\r\n                </div>\r\n\r\n                <a href=\"#\" class=\"cta-button\">Send Your Best Wishes 🎉</a>\r\n            </div>\r\n            <div class=\"footer\">\r\n                <p>Blessings from <span class=\"church-name\">Freedom Assembly Church</span></p>\r\n                <div class=\"unsubscribe\">\r\n                    <a href=\"#\">Unsubscribe from these birthday updates</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    {tracking_pixel}\r\n</body>\r\n</html>', 0, '2025-03-03 18:46:54', '2025-03-08 11:33:24', 'general'),
(20, 'Member Upcoming Birthday Notification 5', 'Help celebrate {birthday_member_name}\'s special day {days_text}!', '<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n    <title>Interactive Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f0f7ff;\r\n            color: #333;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 0 auto;\r\n            padding: 20px;\r\n        }\r\n        .card {\r\n            background-color: #ffffff;\r\n            border-radius: 16px;\r\n            box-shadow: 0 10px 30px rgba(0,0,0,0.1);\r\n            overflow: hidden;\r\n            position: relative;\r\n        }\r\n        .header {\r\n            background: linear-gradient(135deg, #ff5b94, #ff9b7d);\r\n            color: white;\r\n            padding: 30px 20px;\r\n            text-align: center;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 28px;\r\n            font-weight: bold;\r\n            text-shadow: 0 2px 4px rgba(0,0,0,0.2);\r\n        }\r\n        .header p {\r\n            margin: 10px 0 0;\r\n            font-size: 16px;\r\n            opacity: 0.9;\r\n        }\r\n        .content {\r\n            padding: 30px;\r\n            color: #4a5568;\r\n        }\r\n        .member-container {\r\n            text-align: center;\r\n            position: relative;\r\n            margin-bottom: 30px;\r\n        }\r\n        .celebration-icon {\r\n            position: absolute;\r\n            width: 100%;\r\n            height: 100%;\r\n            top: 0;\r\n            left: 0;\r\n            background-image: url(\"data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'260\' height=\'260\' viewBox=\'0 0 260 260\'%3E%3Cg fill=\'none\'%3E%3Cpath fill=\'%23FFD700\' d=\'M130 10l17.5 53.8h56.6l-45.8 33.3 17.5 53.8-45.8-33.3-45.8 33.3 17.5-53.8-45.8-33.3h56.6z\'/%3E%3C/g%3E%3C/svg%3E\");\r\n            background-repeat: no-repeat;\r\n            background-position: center;\r\n            opacity: 0.05;\r\n            z-index: 1;\r\n        }\r\n        .photo-frame {\r\n            position: relative;\r\n            width: 180px;\r\n            height: 180px;\r\n            margin: 0 auto 20px;\r\n            z-index: 2;\r\n        }\r\n        .photo-circle {\r\n            width: 170px;\r\n            height: 170px;\r\n            border-radius: 50%;\r\n            border: 5px solid #fff;\r\n            overflow: hidden;\r\n            margin: 0 auto;\r\n            box-shadow: 0 5px 15px rgba(0,0,0,0.2);\r\n        }\r\n        .member-photo {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n        }\r\n        .member-name {\r\n            color: #ff5b94;\r\n            font-size: 26px;\r\n            font-weight: bold;\r\n            margin: 10px 0;\r\n            position: relative;\r\n            z-index: 2;\r\n        }\r\n        .birthday-info {\r\n            background-color: #fff5f8;\r\n            border-radius: 10px;\r\n            padding: 20px;\r\n            margin: 20px 0;\r\n            text-align: center;\r\n            border-left: 4px solid #ff5b94;\r\n        }\r\n        .birthday-date {\r\n            color: #ff5b94;\r\n            font-weight: bold;\r\n            font-size: 20px;\r\n            margin-bottom: 10px;\r\n        }\r\n        .birthday-age {\r\n            display: inline-block;\r\n            background: linear-gradient(135deg, #ff5b94, #ff9b7d);\r\n            color: white;\r\n            padding: 5px 15px;\r\n            border-radius: 20px;\r\n            font-weight: bold;\r\n            font-size: 18px;\r\n            margin: 10px 0;\r\n        }\r\n        .action-buttons {\r\n            margin: 30px 0;\r\n            text-align: center;\r\n        }\r\n        .button {\r\n            display: inline-block;\r\n            padding: 12px 24px;\r\n            background: linear-gradient(135deg, #ff5b94, #ff9b7d);\r\n            color: white;\r\n            text-decoration: none;\r\n            border-radius: 30px;\r\n            font-weight: bold;\r\n            margin: 8px;\r\n            box-shadow: 0 4px 10px rgba(255, 91, 148, 0.3);\r\n            transition: all 0.3s ease;\r\n        }\r\n        .button:hover {\r\n            transform: translateY(-3px);\r\n            box-shadow: 0 6px 15px rgba(255, 91, 148, 0.4);\r\n        }\r\n        .button.secondary {\r\n            background: white;\r\n            color: #ff5b94;\r\n            border: 2px solid #ff5b94;\r\n        }\r\n        .celebration-ideas {\r\n            background-color: #f8f9fa;\r\n            border-radius: 10px;\r\n            padding: 20px;\r\n            margin: 25px 0;\r\n        }\r\n        .celebration-ideas h3 {\r\n            color: #ff5b94;\r\n            margin-top: 0;\r\n            text-align: center;\r\n            font-size: 20px;\r\n        }\r\n        .celebration-ideas ul {\r\n            list-style-type: none;\r\n            padding: 0;\r\n        }\r\n        .celebration-ideas li {\r\n            padding: 10px 15px;\r\n            margin-bottom: 10px;\r\n            background-color: white;\r\n            border-radius: 8px;\r\n            border-left: 3px solid #ff5b94;\r\n        }\r\n        .celebration-ideas li i {\r\n            color: #ff5b94;\r\n            margin-right: 10px;\r\n        }\r\n        .message {\r\n            font-style: italic;\r\n            color: #666;\r\n            text-align: center;\r\n            margin: 25px 0;\r\n            padding: 20px;\r\n            background-color: #f9f9f9;\r\n            border-radius: 10px;\r\n        }\r\n        .footer {\r\n            background-color: #f0f7ff;\r\n            border-top: 1px solid #e2e8f0;\r\n            padding: 20px;\r\n            text-align: center;\r\n            color: #718096;\r\n            font-size: 14px;\r\n        }\r\n        .church-name {\r\n            color: #ff5b94;\r\n            font-weight: bold;\r\n        }\r\n        @media only screen and (max-width: 600px) {\r\n            .container {\r\n                padding: 10px;\r\n            }\r\n            .card {\r\n                border-radius: 10px;\r\n            }\r\n            .header {\r\n                padding: 20px 10px;\r\n            }\r\n            .content {\r\n                padding: 20px 15px;\r\n            }\r\n            .photo-frame {\r\n                width: 150px;\r\n                height: 150px;\r\n            }\r\n            .photo-circle {\r\n                width: 140px;\r\n                height: 140px;\r\n            }\r\n            .button {\r\n                display: block;\r\n                margin: 10px auto;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>Birthday Celebration!</h1>\r\n                <p>Let\'s make it special together</p>\r\n            </div>\r\n            \r\n            <div class=\"content\">\r\n                <p>Hello <strong>{full_name}</strong>,</p>\r\n                <p>We\'re excited to let you know that one of our church family members has a birthday coming up <strong>{days_text}</strong>!</p>\r\n                \r\n                <div class=\"member-container\">\r\n                    <div class=\"celebration-icon\"></div>\r\n                    <div class=\"photo-frame\">\r\n                        <div class=\"photo-circle\">\r\n                            <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\" class=\"member-photo\">\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"member-name\">{birthday_member_name}</div>\r\n                </div>\r\n                \r\n                <div class=\"birthday-info\">\r\n                    <p>Birthday Celebration on:</p>\r\n                    <div class=\"birthday-date\">{upcoming_birthday_formatted}</div>\r\n                    <p>Turning:</p>\r\n                    <div class=\"birthday-age\">{birthday_member_age} Years</div>\r\n                </div>\r\n                \r\n                <div class=\"action-buttons\">\r\n                    <a href=\"mailto:{birthday_member_email}?subject=Happy%20Birthday%20Wishes&body=Dear%20{birthday_member_name},%0A%0AI%20wanted%20to%20wish%20you%20a%20very%20happy%20birthday!%20May%20your%20day%20be%20filled%20with%20joy,%20and%20God\'s%20blessings%20be%20upon%20you%20in%20this%20new%20year%20of%20your%20life.%0A%0ABlessings,%0A{full_name}\" class=\"button\">\r\n                        Send Birthday Wishes\r\n                    </a>\r\n                    <a href=\"tel:{birthday_member_phone}\" class=\"button secondary\">\r\n                        Call to Wish\r\n                    </a>\r\n                </div>\r\n                \r\n                <div class=\"celebration-ideas\">\r\n                    <h3>Ways to Make Their Day Special</h3>\r\n                    <ul>\r\n                        <li>Send a thoughtful gift or card</li>\r\n                        <li>Share a prayer or blessing for their year ahead</li>\r\n                        <li>Share an encouraging scripture verse</li>\r\n                        <li>Invite them for coffee or a meal to celebrate</li>\r\n                        <li>Organize a video call with multiple friends</li>\r\n                    </ul>\r\n                </div>\r\n                \r\n                <div class=\"message\">\r\n                    \"This is the day the Lord has made; We will rejoice and be glad in it.\" - Psalm 118:24\r\n                </div>\r\n                \r\n                <p style=\"text-align: center;\">Let\'s come together to celebrate {birthday_member_name}\'s special day and show them how much they mean to our church family!</p>\r\n            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <p>With love from,<br><span class=\"church-name\">Freedom Assembly Church</span></p>\r\n                            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n    {tracking_pixel}\r\n</body>\r\n</html>', 0, '2025-03-03 19:26:45', '2025-03-08 11:39:06', 'general'),
(21, 'Member Upcoming Birthday Notification 6', 'Celebrate with us! {birthday_member_name}\'s birthday is coming up {days_text}', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f4faff;\r\n            color: #2d3748;\r\n            line-height: 1.5;\r\n        }\r\n        .container {\r\n            max-width: 580px;\r\n            margin: 20px auto;\r\n            padding: 0 10px;\r\n        }\r\n        .card {\r\n            background: #ffffff;\r\n            border-radius: 15px;\r\n            box-shadow: 0 6px 20px rgba(0, 122, 255, 0.15);\r\n            overflow: hidden;\r\n            width: 100%;\r\n        }\r\n        .header {\r\n            background: linear-gradient(135deg, #007aff, #00c6ff);\r\n            padding: 25px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 24px;\r\n            font-weight: bold;\r\n        }\r\n        .header p {\r\n            margin: 6px 0 0;\r\n            font-size: 14px;\r\n            opacity: 0.9;\r\n        }\r\n        .content {\r\n            padding: 20px;\r\n            text-align: center;\r\n        }\r\n        .greeting {\r\n            font-size: 16px;\r\n            margin: 0 0 15px;\r\n            color: #4a5568;\r\n        }\r\n        .profile {\r\n            margin: 20px 0;\r\n        }\r\n        .photo {\r\n            width: 140px;\r\n            height: 140px;\r\n            border-radius: 50%;\r\n            border: 4px solid #f4faff;\r\n            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);\r\n            overflow: hidden;\r\n            margin: 0 auto;\r\n        }\r\n        .photo img {\r\n            width: 100%;\r\n            height: 100%;\r\n            display: block;\r\n            object-fit: cover;\r\n        }\r\n        .member-name {\r\n            font-size: 22px;\r\n            font-weight: 600;\r\n            color: #007aff;\r\n            margin: 10px 0 5px;\r\n        }\r\n        .birthday-details {\r\n            background: #e6f0ff;\r\n            border-radius: 10px;\r\n            padding: 15px;\r\n            margin: 20px 0;\r\n            display: inline-block;\r\n        }\r\n        .birthday-date {\r\n            font-size: 17px;\r\n            font-weight: 600;\r\n            color: #007aff;\r\n            margin: 0 0 6px;\r\n        }\r\n        .birthday-age {\r\n            font-size: 15px;\r\n            font-weight: bold;\r\n            color: #ffffff;\r\n            background: #007aff;\r\n            padding: 5px 12px;\r\n            border-radius: 15px;\r\n            display: inline-block;\r\n        }\r\n        .countdown {\r\n            font-size: 14px;\r\n            color: #4a5568;\r\n            margin: 10px 0 0;\r\n        }\r\n        .suggestions {\r\n            background: #e6f0ff;\r\n            border-radius: 10px;\r\n            padding: 15px;\r\n            margin: 20px 0;\r\n            text-align: left;\r\n        }\r\n        .suggestions h3 {\r\n            color: #007aff;\r\n            font-size: 16px;\r\n            margin: 0 0 10px;\r\n        }\r\n        .suggestions ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .suggestions li {\r\n            padding: 8px 0;\r\n            border-bottom: 1px solid #cce0ff;\r\n            font-size: 14px;\r\n            color: #4a5568;\r\n        }\r\n        .suggestions li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .tracking-pixel { position: absolute !important; width: 1px !important; height: 1px !important; padding: 0 !important; margin: -1px !important; overflow: hidden !important; clip: rect(0,0,0,0) !important; white-space: nowrap !important; border: 0 !important; opacity: 0 !important; pointer-events: none !important; } .action-button {\r\n            display: inline-block;\r\n            background: linear-gradient(135deg, #007aff, #00c6ff);\r\n            color: #ffffff;\r\n            text-decoration: none;\r\n            padding: 10px 25px;\r\n            border-radius: 20px;\r\n            font-weight: 600;\r\n            font-size: 15px;\r\n            margin: 15px 0;\r\n            box-shadow: 0 4px 10px rgba(0, 122, 255, 0.3);\r\n        }\r\n        .footer {\r\n            background: #f4faff;\r\n            padding: 15px;\r\n            text-align: center;\r\n            font-size: 12px;\r\n            color: #718096;\r\n            border-top: 1px solid #cce0ff;\r\n        }\r\n        .church-name {\r\n            color: #007aff;\r\n            font-weight: bold;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 8px;\r\n            font-size: 10px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #718096;\r\n            text-decoration: none;\r\n        }\r\n        @media (max-width: 480px) {\r\n            .container {\r\n                padding: 0 5px;\r\n            }\r\n            .card {\r\n                border-radius: 10px;\r\n            }\r\n            .header {\r\n                padding: 20px;\r\n            }\r\n            .header h1 {\r\n                font-size: 20px;\r\n            }\r\n            .content {\r\n                padding: 15px;\r\n            }\r\n            .photo {\r\n                width: 110px;\r\n                height: 110px;\r\n            }\r\n            .member-name {\r\n                font-size: 18px;\r\n            }\r\n            .tracking-pixel { position: absolute !important; width: 1px !important; height: 1px !important; padding: 0 !important; margin: -1px !important; overflow: hidden !important; clip: rect(0,0,0,0) !important; white-space: nowrap !important; border: 0 !important; opacity: 0 !important; pointer-events: none !important; } .action-button {\r\n                padding: 8px 20px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>Happy Birthday Celebration!</h1>\r\n                <p>Join us in honoring a special church family member</p>\r\n            </div>\r\n            \r\n            <div class=\"content\">\r\n                <div class=\"greeting\">\r\n                    <p>Hello {first_name},</p>\r\n                    <p>We\'re thrilled to celebrate {birthday_member_name}\'s birthday, just {days_until_birthday} days away!</p>\r\n                </div>\r\n                \r\n                <div class=\"profile\">\r\n                    <div class=\"photo\">\r\n                        <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\">\r\n                    </div>\r\n                    <div class=\"member-name\">{birthday_member_name}</div>\r\n                </div>\r\n                \r\n                <div class=\"birthday-details\">\r\n                    <div class=\"birthday-date\">{upcoming_birthday_formatted}</div>\r\n                    <div class=\"birthday-age\">Turning {birthday_member_age}</div>\r\n                    <div class=\"countdown\">Only {days_until_birthday} days left!</div>\r\n                </div>\r\n                \r\n                <div class=\"suggestions\">\r\n                    <h3>Ways to Bless {birthday_member_name}:</h3>\r\n                    <ul>\r\n                        <li>Send a heartfelt birthday message</li>\r\n                        <li>Pray for their upcoming year</li>\r\n                        <li>Share an encouraging scripture</li>\r\n                        <li>Consider a thoughtful card or gift</li>\r\n                    </ul>\r\n                </div>\r\n                \r\n                <a href=\"mailto:{birthday_member_email}?subject=Happy%20Birthday%20{birthday_member_name}!&body=Dear%20{birthday_member_name},%0D%0A%0D%0AWishing%20you%20a%20wonderful%20birthday%20filled%20with%20joy%20and%20blessings%20from%20your%20Freedom%20Assembly%20Church%20family!%0D%0A%0D%0A[Add%20your%20personal%20message%20here]%0D%0A%0D%0ABlessings,%0D%0A{full_name}\" class=\"action-button\">Send Birthday Wishes</a>\r\n            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <p>Blessings from <span class=\"church-name\">Freedom Assembly Church</span></p>\r\n                <div class=\"unsubscribe\">\r\n                    <a href=\"#\">Unsubscribe from these updates</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n    {tracking_pixel}\r\n</body>\r\n</html>', 0, '2025-03-03 19:32:05', '2025-03-08 11:42:01', 'general'),
(22, 'Standard Bulk Email Template', 'Important Message from {church_name}', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n    <title>Message from {church_name}</title>\r\n    <style>\r\n        body {\r\n            font-family: \'Arial\', sans-serif;\r\n            line-height: 1.6;\r\n            color: #3E3E3E;\r\n            background-color: #f5f5f5;\r\n            margin: 0;\r\n            padding: 0;\r\n        }\r\n        .container {\r\n            max-width: 650px;\r\n            margin: 0 auto;\r\n            padding: 25px;\r\n            background-color: #ffffff;\r\n            border-radius: 10px;\r\n            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .header {\r\n            background-color: #6c757d;\r\n            color: white;\r\n            padding: 30px;\r\n            text-align: center;\r\n            border-radius: 10px 10px 0 0;\r\n        }\r\n        .header h1 {\r\n            font-family: \'Georgia\', serif;\r\n            font-size: 36px;\r\n            margin: 0;\r\n            text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);\r\n        }\r\n        .content {\r\n            padding: 25px;\r\n        }\r\n        .member-info {\r\n            margin: 20px 0;\r\n            padding: 20px;\r\n            background-color: #f0f4f7;\r\n            border-radius: 8px;\r\n            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\r\n        }\r\n        .member-info h3 {\r\n            font-size: 20px;\r\n            color: #495057;\r\n            margin-bottom: 15px;\r\n        }\r\n        .member-info ul {\r\n            list-style: none;\r\n            padding: 0;\r\n        }\r\n        .member-info li {\r\n            font-size: 14px;\r\n            color: #495057;\r\n            margin-bottom: 8px;\r\n        }\r\n        .member-image {\r\n            width: 120px;\r\n            height: 120px;\r\n            border-radius: 50%;\r\n            margin: 20px auto;\r\n            display: block;\r\n            border: 5px solid #ffffff;\r\n            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .button {\r\n            display: inline-block;\r\n            padding: 12px 25px;\r\n            background-color: #28a745;\r\n            color: white;\r\n            text-decoration: none;\r\n            border-radius: 30px;\r\n            font-size: 16px;\r\n            text-transform: uppercase;\r\n            margin: 20px 0;\r\n            transition: background-color 0.3s ease;\r\n        }\r\n        .button:hover {\r\n            background-color: #218838;\r\n        }\r\n        .footer {\r\n            text-align: center;\r\n            padding: 20px;\r\n            background-color: #f8f9fa;\r\n            color: #6c757d;\r\n            font-size: 12px;\r\n            border-radius: 0 0 10px 10px;\r\n        }\r\n        .footer p {\r\n            margin: 5px 0;\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"header\">\r\n            <h1>{church_name}</h1>\r\n        </div>\r\n        \r\n        <div class=\"content\">\r\n            <img src=\"{member_image}\" alt=\"{recipient_full_name}\" class=\"member-image\">\r\n            \r\n            <h2>Dear {recipient_full_name},</h2>\r\n            \r\n            <div class=\"member-info\">\r\n                <h3>Your Information:</h3>\r\n                <ul>\r\n                    <li><strong>Name:</strong> {recipient_full_name}</li>\r\n                    <li><strong>Email:</strong> {recipient_email}</li>\r\n                    <li><strong>Phone:</strong> {recipient_phone}</li>\r\n                </ul>\r\n            </div>\r\n            \r\n            <p>We hope this message finds you in good health and peace. {church_name} is grateful to have you as part of our community, and we are always here to support you.</p>\r\n            \r\n                     \r\n            <p>If you have any questions or need assistance, please don’t hesitate to reach out to us. We’re here for you!</p>\r\n            \r\n            <a href=\"https://www.freedomassembly.org/\" class=\"button\">Visit Our Website</a>\r\n            \r\n            <p>With blessings and prayers,<br>\r\n            {sender_name}<br>\r\n            {sender_email}</p>\r\n        </div>\r\n        \r\n        <div class=\"footer\">\r\n            <p>&copy; 2024 {church_name}. All rights reserved.</p>\r\n            <p>This email was sent to {recipient_email} as part of a bulk email to {total_recipients} recipients.</p>\r\n            <p>If you wish to unsubscribe, click <a href=\"#\">here</a>.</p>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>', 0, '2025-03-04 16:23:00', '2025-03-08 11:49:46', 'general'),
(23, 'Newsletter Template 1', 'A Special Message from {church_name}', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Message from {church_name}</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f0fff0;\r\n            color: #2d3748;\r\n            line-height: 1.5;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 20px auto;\r\n            padding: 0 10px;\r\n        }\r\n        .card {\r\n            background: #ffffff;\r\n            border-radius: 20px;\r\n            box-shadow: 0 8px 25px rgba(0, 201, 0, 0.2);\r\n            overflow: hidden;\r\n            width: 100%;\r\n            border: 2px solid #00c900;\r\n        }\r\n        .header {\r\n            background: linear-gradient(135deg, #00c900, #ffeb3b);\r\n            padding: 30px 20px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 26px;\r\n            font-weight: bold;\r\n            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .header p {\r\n            margin: 6px 0 0;\r\n            font-size: 15px;\r\n            opacity: 0.95;\r\n        }\r\n        .content {\r\n            padding: 25px;\r\n            text-align: center;\r\n        }\r\n        .member-image-container {\r\n            text-align: center;\r\n            margin: 20px 0;\r\n        }\r\n        .member-image {\r\n            width: 120px;\r\n            height: 120px;\r\n            border-radius: 60px;\r\n            border: 3px solid #00c900;\r\n            box-shadow: 0 4px 12px rgba(0, 201, 0, 0.2);\r\n            object-fit: cover;\r\n        }\r\n        .greeting {\r\n            font-size: 17px;\r\n            margin: 0 0 20px;\r\n            color: #4a5568;\r\n        }\r\n        .message {\r\n            background: #f5fff5;\r\n            border-radius: 12px;\r\n            padding: 20px;\r\n            margin: 20px 0;\r\n            font-size: 16px;\r\n            color: #2d3748;\r\n            border: 2px dashed #ffeb3b;\r\n        }\r\n        .member-info {\r\n            background: #f5fff5;\r\n            border-radius: 12px;\r\n            padding: 20px;\r\n            margin: 20px 0;\r\n            text-align: left;\r\n            border: 2px solid #e0f0e0;\r\n        }\r\n        .member-info h3 {\r\n            color: #00c900;\r\n            margin: 0 0 10px;\r\n            font-size: 17px;\r\n        }\r\n        .member-info ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .member-info li {\r\n            padding: 8px 0;\r\n            border-bottom: 1px solid #e0f0e0;\r\n            font-size: 14px;\r\n            color: #4a5568;\r\n        }\r\n        .member-info li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .suggestions {\r\n            background: #f5fff5;\r\n            border-radius: 12px;\r\n            padding: 15px;\r\n            margin: 20px 0;\r\n            text-align: left;\r\n        }\r\n        .suggestions h3 {\r\n            color: #00c900;\r\n            font-size: 17px;\r\n            margin: 0 0 10px;\r\n        }\r\n        .suggestions ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .suggestions li {\r\n            padding: 8px 0;\r\n            border-bottom: 1px solid #e0f0e0;\r\n            font-size: 14px;\r\n            color: #4a5568;\r\n        }\r\n        .suggestions li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .footer {\r\n            background: #f0fff0;\r\n            padding: 20px;\r\n            text-align: center;\r\n            font-size: 12px;\r\n            color: #718096;\r\n            border-top: 1px solid #e0f0e0;\r\n        }\r\n        .church-name {\r\n            color: #00c900;\r\n            font-weight: bold;\r\n        }\r\n        .sender-info {\r\n            margin: 15px 0;\r\n            font-size: 14px;\r\n            color: #4a5568;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 8px;\r\n            font-size: 10px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #718096;\r\n            text-decoration: none;\r\n        }\r\n        @media (max-width: 480px) {\r\n            .container {\r\n                padding: 0 5px;\r\n            }\r\n            .card {\r\n                border-radius: 15px;\r\n            }\r\n            .header {\r\n                padding: 20px;\r\n            }\r\n            .header h1 {\r\n                font-size: 22px;\r\n            }\r\n            .content {\r\n                padding: 20px;\r\n            }\r\n            .message {\r\n                padding: 15px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>Message from {church_name}</h1>\r\n                <p>To our valued member</p>\r\n            </div>\r\n            \r\n            <div class=\"content\">\r\n                <div class=\"member-image-container\">\r\n                    \r\n                </div>\r\n                \r\n                <div class=\"greeting\">\r\n                    <p>Dear {recipient_first_name},</p>\r\n                </div>\r\n                \r\n                <div class=\"message\">\r\n                    God loves you always.\r\n                </div>\r\n                \r\n                <div class=\"member-info\">\r\n                    <h3>Your Information:</h3>\r\n                    <ul>\r\n                        <li>Full Name: {recipient_full_name}</li>\r\n                        <li>Email: {recipient_email}</li>\r\n                        <li>Phone: {recipient_phone}</li>\r\n                    </ul>\r\n                </div>\r\n                \r\n                <div class=\"suggestions\">\r\n                    <h3>Stay Connected:</h3>\r\n                    <ul>\r\n                        <li>Join our weekly services</li>\r\n                        <li>Participate in community events</li>\r\n                        <li>Follow us on social media</li>\r\n                        <li>Share your testimonies</li>\r\n                    </ul>\r\n                </div>\r\n            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <div class=\"sender-info\">\r\n                    <p>Sent with love by {sender_name}<br>\r\n                    {sender_email}</p>\r\n                </div>\r\n                <p>This message was sent to {recipient_email} as part of a communication to {total_recipients} members.</p>\r\n                <p class=\"church-name\">{church_name}</p>\r\n                <div class=\"unsubscribe\">\r\n                    <a href=\"#\">Manage your email preferences</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>', 0, '2025-03-04 16:30:38', '2025-03-10 06:36:15', 'bulk'),
(24, 'Newsletter Template 2', 'Newsletter from {church_name}', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <title>Newsletter from {church_name}</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f5f5f5;\r\n            color: #333333;\r\n            line-height: 1.6;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 20px auto;\r\n            padding: 0 10px;\r\n            background: #ffffff;\r\n            border-radius: 10px;\r\n            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .header {\r\n            background: linear-gradient(135deg, #3498db, #2ecc71);\r\n            padding: 25px 20px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n            border-top-left-radius: 10px;\r\n            border-top-right-radius: 10px;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 24px;\r\n            font-weight: 600;\r\n        }\r\n        .header p {\r\n            margin: 5px 0 0;\r\n            font-size: 14px;\r\n            opacity: 0.9;\r\n        }\r\n        .content {\r\n            padding: 20px;\r\n        }\r\n        .greeting {\r\n            font-size: 16px;\r\n            margin: 0 0 15px;\r\n            color: #444444;\r\n        }\r\n        .profile {\r\n            text-align: center;\r\n            margin: 20px 0;\r\n        }\r\n        .photo {\r\n            width: 120px;\r\n            height: 120px;\r\n            border-radius: 50%;\r\n            border: 3px solid #f5f5f5;\r\n            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);\r\n            overflow: hidden;\r\n            margin: 0 auto;\r\n        }\r\n        .photo img {\r\n            width: 100%;\r\n            height: 100%;\r\n            display: block;\r\n            object-fit: cover;\r\n        }\r\n        .section {\r\n            background: #fafafa;\r\n            border-radius: 8px;\r\n            padding: 15px;\r\n            margin: 15px 0;\r\n            border: 1px solid #e0e0e0;\r\n        }\r\n        .section h2 {\r\n            color: #3498db;\r\n            font-size: 18px;\r\n            margin: 0 0 10px;\r\n            font-weight: 600;\r\n        }\r\n        .section p {\r\n            margin: 0 0 10px;\r\n            font-size: 14px;\r\n            color: #555555;\r\n        }\r\n        .updates {\r\n            margin: 20px 0;\r\n        }\r\n        .updates h3 {\r\n            color: #2ecc71;\r\n            font-size: 16px;\r\n            margin: 0 0 10px;\r\n            font-weight: 600;\r\n        }\r\n        .updates ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .updates li {\r\n            padding: 8px 0;\r\n            border-bottom: 1px solid #e0e0e0;\r\n            font-size: 14px;\r\n            color: #555555;\r\n        }\r\n        .updates li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .footer {\r\n            background: #f5f5f5;\r\n            padding: 15px;\r\n            text-align: center;\r\n            font-size: 12px;\r\n            color: #777777;\r\n            border-top: 1px solid #e0e0e0;\r\n            border-bottom-left-radius: 10px;\r\n            border-bottom-right-radius: 10px;\r\n        }\r\n        .church-name {\r\n            color: #3498db;\r\n            font-weight: bold;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 8px;\r\n            font-size: 10px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #777777;\r\n            text-decoration: none;\r\n        }\r\n        @media (max-width: 480px) {\r\n            .container {\r\n                padding: 0 5px;\r\n            }\r\n            .header {\r\n                padding: 20px;\r\n            }\r\n            .header h1 {\r\n                font-size: 20px;\r\n            }\r\n            .content {\r\n                padding: 15px;\r\n            }\r\n            .photo {\r\n                width: 100px;\r\n                height: 100px;\r\n            }\r\n            .section h2 {\r\n                font-size: 16px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>Newsletter from {church_name}</h1>\r\n                <p>Connecting with all {total_recipients} of our members</p>\r\n            </div>\r\n            \r\n            <div class=\"content\">\r\n                <div class=\"greeting\">\r\n                    <p>Hello {recipient_first_name},</p>\r\n                    <p>Welcome to this month\'s update from {church_name}! We\'re so glad to share this with you and all {total_recipients} of our wonderful members.</p>\r\n                </div>\r\n                \r\n             \r\n                \r\n                <div class=\"section\">\r\n                    <h2>A Message from {sender_name}</h2>\r\n                    <p>Greetings, {recipient_full_name}! I hope this newsletter finds you well. At {church_name}, we\'re committed to fostering a community of faith, love, and support. Thank you for being a vital part of our family!</p>\r\n                </div>\r\n                \r\n                <div class=\"updates\">\r\n                    <h3>Community Updates</h3>\r\n                    <ul>\r\n                        <li>Join us this Sunday for a special service at 10 AM.</li>\r\n                        <li>Our next fellowship event is scheduled for next Saturday\'s details to follow!</li>\r\n                        <li>We\'re launching a new outreach program\'s stay tuned for ways to get involved.</li>\r\n                        <li>Prayer meetings are now every Wednesday at 7 PM.</li>\r\n                    </ul>\r\n                </div>\r\n            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <p>Sent with blessings from <span class=\"church-name\">{church_name}</span><br>By {sender_name}</p>\r\n                <div class=\"unsubscribe\">\r\n                    <a href=\"#\">Unsubscribe from these updates</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>', 0, '2025-03-04 16:41:38', '2025-03-08 17:40:35', 'bulk');
INSERT INTO `email_templates` (`id`, `template_name`, `subject`, `content`, `is_birthday_template`, `created_at`, `updated_at`, `template_category`) VALUES
(25, 'Newsletter Template 3', 'Newsletter from {church_name}', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\r\n    <title>Newsletter from {church_name}</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f7f9fc;\r\n            color: #333333;\r\n            line-height: 1.6;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 20px auto;\r\n            padding: 0 10px;\r\n            background: #ffffff;\r\n            border-radius: 10px;\r\n            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .header {\r\n            background: linear-gradient(135deg, #1e3c72, #2a5298);\r\n            padding: 30px 20px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n            border-top-left-radius: 10px;\r\n            border-top-right-radius: 10px;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 26px;\r\n            font-weight: 600;\r\n        }\r\n        .header p {\r\n            margin: 5px 0 0;\r\n            font-size: 14px;\r\n            opacity: 0.9;\r\n        }\r\n        .content {\r\n            padding: 20px;\r\n        }\r\n        .greeting {\r\n            font-size: 16px;\r\n            margin: 0 0 15px;\r\n            color: #444444;\r\n        }\r\n        .message {\r\n            background: #f0f4f8;\r\n            border-radius: 8px;\r\n            padding: 15px;\r\n            margin: 20px 0;\r\n            border: 1px solid #d1d9e6;\r\n        }\r\n        .message h2 {\r\n            color: #1e3c72;\r\n            font-size: 18px;\r\n            margin: 0 0 10px;\r\n            font-weight: 600;\r\n        }\r\n        .message p {\r\n            margin: 0 0 10px;\r\n            font-size: 14px;\r\n            color: #555555;\r\n        }\r\n        .updates {\r\n            margin: 20px 0;\r\n        }\r\n        .updates h3 {\r\n            color: #2a5298;\r\n            font-size: 16px;\r\n            margin: 0 0 10px;\r\n            font-weight: 600;\r\n        }\r\n        .updates ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .updates li {\r\n            padding: 8px 0;\r\n            border-bottom: 1px solid #e0e0e0;\r\n            font-size: 14px;\r\n            color: #555555;\r\n        }\r\n        .updates li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .cta {\r\n            text-align: center;\r\n            margin: 20px 0;\r\n        }\r\n        .cta a {\r\n            display: inline-block;\r\n            background: #2ecc71;\r\n            color: #ffffff;\r\n            text-decoration: none;\r\n            padding: 10px 20px;\r\n            border-radius: 5px;\r\n            font-weight: 600;\r\n            font-size: 14px;\r\n        }\r\n        .footer {\r\n            background: #f7f9fc;\r\n            padding: 15px;\r\n            text-align: center;\r\n            font-size: 12px;\r\n            color: #777777;\r\n            border-top: 1px solid #e0e0e0;\r\n            border-bottom-left-radius: 10px;\r\n            border-bottom-right-radius: 10px;\r\n        }\r\n        .church-name {\r\n            color: #1e3c72;\r\n            font-weight: bold;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 8px;\r\n            font-size: 10px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #777777;\r\n            text-decoration: none;\r\n        }\r\n        @media (max-width: 480px) {\r\n            .container {\r\n                padding: 0 5px;\r\n            }\r\n            .header {\r\n                padding: 20px;\r\n            }\r\n            .header h1 {\r\n                font-size: 22px;\r\n            }\r\n            .content {\r\n                padding: 15px;\r\n            }\r\n            .message h2 {\r\n                font-size: 16px;\r\n            }\r\n            .updates h3 {\r\n                font-size: 15px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"header\">\r\n            <h1>Newsletter from {church_name}</h1>\r\n            <p>Connecting with all {total_recipients} of our cherished members</p>\r\n        </div>\r\n        \r\n        <div class=\"content\">\r\n            <div class=\"greeting\">\r\n                <p>Hello {recipient_first_name},</p>\r\n                <p>Welcome to this month\'s newsletter from {church_name}. We\'re delighted to share these updates with you and all {total_recipients} members of our community.</p>\r\n            </div>\r\n            \r\n            <div class=\"message\">\r\n                <h2>A Message from {sender_name}</h2>\r\n                <p>Dear {recipient_full_name},</p>\r\n                <p>I hope this message finds you well. At {church_name}, we\'re grateful for your presence and commitment to our shared journey of faith. This newsletter is a small way to keep us connected and inspired. Thank you for being a vital part of our family!</p>\r\n            </div>\r\n            \r\n            <div class=\"updates\">\r\n                <h3>Community Updates</h3>\r\n                <ul>\r\n                    <li>Join us this Sunday for a special service at 10 AM.</li>\r\n                    <li>Our next fellowship event is scheduled for next Saturday\'s details to follow!</li>\r\n                    <li>We\'re launching a new outreach program\'s stay tuned for ways to get involved.</li>\r\n                    <li>Prayer meetings are now every Wednesday at 7 PM.</li>\r\n                </ul>\r\n            </div>\r\n            \r\n            <div class=\"cta\">\r\n                <a href=\"https://www.freedomassembly.org/\">Visit Our Website</a>\r\n            </div>\r\n        </div>\r\n        \r\n        <div class=\"footer\">\r\n            <p>Sent with love from <span class=\"church-name\">{church_name}</span><br>By {sender_name}</p>\r\n            <div class=\"unsubscribe\">\r\n                <a href=\"#\">Unsubscribe from these updates</a>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>', 0, '2025-03-04 20:45:01', '2025-03-08 11:58:10', 'bulk');

-- --------------------------------------------------------

--
-- Table structure for table `email_tracking`
--

CREATE TABLE `email_tracking` (
  `id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `tracking_id` varchar(50) NOT NULL,
  `email_content` text DEFAULT NULL,
  `email_type` varchar(20) NOT NULL,
  `sent_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `opened_at` timestamp NULL DEFAULT NULL,
  `opened_count` int(11) DEFAULT 0,
  `user_agent` varchar(255) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `email_tracking`
--

INSERT INTO `email_tracking` (`id`, `member_id`, `tracking_id`, `email_content`, `email_type`, `sent_at`, `opened_at`, `opened_count`, `user_agent`, `ip_address`) VALUES
(1, 3, 'track_67cc1230abb344.14424845', NULL, 'birthday', '2025-03-08 09:47:28', NULL, 0, NULL, NULL),
(2, 3, '95cc6a9112b7504a00727c1f4f4caf4e', '<div style=\"max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 20px;\">\r\n    <div style=\"text-align: center; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\r\n        <h1 style=\"color: #3498db; font-size: 28px; margin-bottom: 20px;\">Happy Birthday!</h1>\r\n        \r\n        <img src=\"https://freedomassemblydb.online/church/uploads/67cc11b088f39.jpg\" alt=\"Godwin Bointa\" style=\"width: 150px; height: 150px; border-radius: 75px; margin: 20px auto; display: block; object-fit: cover; border: 5px solid #f8f9fa; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\r\n        \r\n        <div style=\"background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;\">\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 15px;\">Dear Godwin Bointa,</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 20px;\">On behalf of Freedom Assembly Church International, we wish you a very Happy Birthday! May this special day bring you joy, peace, and blessings.</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 20px;\">We are grateful to have you as part of our church family and pray that this new year of your life brings you closer to God\'s purpose for you.</p>\r\n        </div>\r\n        \r\n        <div style=\"background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; margin: 20px 0;\">\r\n            <h2 style=\"color: #3498db; font-size: 20px; margin-bottom: 15px;\">Birthday Scripture</h2>\r\n            <p style=\"font-style: italic; color: #555; font-size: 16px; line-height: 1.6;\">\"For I know the plans I have for you,\" declares the LORD, \"plans to prosper you and not to harm you, plans to give you hope and a future.\" - Jeremiah 29:11</p>\r\n        </div>\r\n        \r\n        <p style=\"font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;\">May God continue to bless you abundantly!</p>\r\n        \r\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;\">\r\n            <p style=\"color: #666; font-size: 14px;\">Blessings,<br><strong>Freedom Assembly Church International</strong></p>\r\n        </div>\r\n    </div>\r\n</div>', 'birthday', '2025-03-08 09:50:14', NULL, 0, NULL, NULL),
(3, 3, 'track_67cc12d6d413e3.67069915', NULL, 'birthday', '2025-03-08 09:50:14', NULL, 0, NULL, NULL),
(4, 3, '50102a4f4799994d516f953c343da73a', '<div style=\"max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 20px;\">\r\n    <div style=\"text-align: center; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\r\n        <h1 style=\"color: #3498db; font-size: 28px; margin-bottom: 20px;\">Happy Birthday!</h1>\r\n        \r\n        <img src=\"https://freedomassemblydb.online/church/uploads/67cc11b088f39.jpg\" alt=\"Godwin Bointa\" style=\"width: 150px; height: 150px; border-radius: 75px; margin: 20px auto; display: block; object-fit: cover; border: 5px solid #f8f9fa; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\r\n        \r\n        <div style=\"background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;\">\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 15px;\">Dear Godwin Bointa,</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 20px;\">On behalf of Freedom Assembly Church International, we wish you a very Happy Birthday! May this special day bring you joy, peace, and blessings.</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 20px;\">We are grateful to have you as part of our church family and pray that this new year of your life brings you closer to God\'s purpose for you.</p>\r\n        </div>\r\n        \r\n        <div style=\"background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; margin: 20px 0;\">\r\n            <h2 style=\"color: #3498db; font-size: 20px; margin-bottom: 15px;\">Birthday Scripture</h2>\r\n            <p style=\"font-style: italic; color: #555; font-size: 16px; line-height: 1.6;\">\"For I know the plans I have for you,\" declares the LORD, \"plans to prosper you and not to harm you, plans to give you hope and a future.\" - Jeremiah 29:11</p>\r\n        </div>\r\n        \r\n        <p style=\"font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;\">May God continue to bless you abundantly!</p>\r\n        \r\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;\">\r\n            <p style=\"color: #666; font-size: 14px;\">Blessings,<br><strong>Freedom Assembly Church International</strong></p>\r\n        </div>\r\n    </div>\r\n</div>', 'birthday', '2025-03-08 09:50:43', NULL, 0, NULL, NULL),
(5, 3, 'track_67cc12f38fb8a1.95634814', NULL, 'birthday', '2025-03-08 09:50:43', NULL, 0, NULL, NULL),
(6, 3, 'b0f97e9e11996c32cd8a44579ea043ae', '<div style=\"max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 20px;\">\r\n    <div style=\"text-align: center; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\r\n        <h1 style=\"color: #3498db; font-size: 28px; margin-bottom: 20px;\">Happy Birthday!</h1>\r\n        \r\n        <img src=\"https://freedomassemblydb.online/church/uploads/67cc11b088f39.jpg\" alt=\"Godwin Bointa\" style=\"width: 150px; height: 150px; border-radius: 75px; margin: 20px auto; display: block; object-fit: cover; border: 5px solid #f8f9fa; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\r\n        \r\n        <div style=\"background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;\">\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 15px;\">Dear Godwin Bointa,</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 20px;\">On behalf of Freedom Assembly Church International, we wish you a very Happy Birthday! May this special day bring you joy, peace, and blessings.</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 20px;\">We are grateful to have you as part of our church family and pray that this new year of your life brings you closer to God\'s purpose for you.</p>\r\n        </div>\r\n        \r\n        <div style=\"background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; margin: 20px 0;\">\r\n            <h2 style=\"color: #3498db; font-size: 20px; margin-bottom: 15px;\">Birthday Scripture</h2>\r\n            <p style=\"font-style: italic; color: #555; font-size: 16px; line-height: 1.6;\">\"For I know the plans I have for you,\" declares the LORD, \"plans to prosper you and not to harm you, plans to give you hope and a future.\" - Jeremiah 29:11</p>\r\n        </div>\r\n        \r\n        <p style=\"font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;\">May God continue to bless you abundantly!</p>\r\n        \r\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;\">\r\n            <p style=\"color: #666; font-size: 14px;\">Blessings,<br><strong>Freedom Assembly Church International</strong></p>\r\n        </div>\r\n    </div>\r\n</div>', 'birthday', '2025-03-08 09:51:04', NULL, 0, NULL, NULL),
(7, 3, 'track_67cc1308e30ab2.40028417', NULL, 'birthday', '2025-03-08 09:51:04', NULL, 0, NULL, NULL),
(8, 3, 'fb84dc19369dafec0828b1d66a0b36f0', '<div style=\"max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 20px;\">\r\n    <div style=\"text-align: center; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\r\n        <h1 style=\"color: #3498db; font-size: 28px; margin-bottom: 20px;\">Happy Birthday!</h1>\r\n        \r\n        <img src=\"https://freedomassemblydb.online/church/uploads/67cc11b088f39.jpg\" alt=\"Godwin Bointa\" style=\"width: 150px; height: 150px; border-radius: 75px; margin: 20px auto; display: block; object-fit: cover; border: 5px solid #f8f9fa; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\">\r\n        \r\n        <div style=\"background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;\">\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 15px;\">Dear Godwin Bointa,</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 20px;\">On behalf of Freedom Assembly Church International, we wish you a very Happy Birthday! May this special day bring you joy, peace, and blessings.</p>\r\n            <p style=\"font-size: 16px; line-height: 1.6; margin-bottom: 20px;\">We are grateful to have you as part of our church family and pray that this new year of your life brings you closer to God\'s purpose for you.</p>\r\n        </div>\r\n        \r\n        <div style=\"background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; margin: 20px 0;\">\r\n            <h2 style=\"color: #3498db; font-size: 20px; margin-bottom: 15px;\">Birthday Scripture</h2>\r\n            <p style=\"font-style: italic; color: #555; font-size: 16px; line-height: 1.6;\">\"For I know the plans I have for you,\" declares the LORD, \"plans to prosper you and not to harm you, plans to give you hope and a future.\" - Jeremiah 29:11</p>\r\n        </div>\r\n        \r\n        <p style=\"font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;\">May God continue to bless you abundantly!</p>\r\n        \r\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;\">\r\n            <p style=\"color: #666; font-size: 14px;\">Blessings,<br><strong>Freedom Assembly Church International</strong></p>\r\n        </div>\r\n    </div>\r\n</div>', 'birthday', '2025-03-08 09:51:16', NULL, 0, NULL, NULL),
(9, 3, 'track_67cc1315c878d4.39818503', NULL, 'birthday', '2025-03-08 09:51:17', NULL, 0, NULL, NULL),
(10, 4, '69e19cf027a018792ff8024be01cf838', 'Join us in celebrating a birthday tomorrow!', 'b_notification', '2025-03-08 11:59:49', NULL, 0, NULL, NULL),
(11, 4, '69e19cf027a018792ff8024be01cf838', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #fdf2e9;\r\n            color: #2d3748;\r\n            line-height: 1.5;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 20px auto;\r\n            padding: 0 15px;\r\n        }\r\n        .card {\r\n            background: #ffffff;\r\n            border-radius: 20px;\r\n            box-shadow: 0 8px 25px rgba(255, 107, 0, 0.2);\r\n            overflow: hidden;\r\n            width: 100%;\r\n        }\r\n        .header {\r\n            background: linear-gradient(120deg, #ff6b00, #ff9e40);\r\n            padding: 40px 20px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n            border-bottom: 5px solid #ff9e40;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 30px;\r\n            font-weight: bold;\r\n        }\r\n        .content {\r\n            padding: 30px 20px;\r\n            text-align: center;\r\n        }\r\n        .greeting {\r\n            font-size: 18px;\r\n            margin: 0 0 25px;\r\n            color: #4a5568;\r\n        }\r\n        .photo-container {\r\n            margin: 0 auto 20px;\r\n            width: 140px;\r\n            height: 140px;\r\n        }\r\n        .photo {\r\n            width: 100%;\r\n            height: 100%;\r\n            border-radius: 50%;\r\n            border: 5px solid #fff8e6;\r\n            overflow: hidden;\r\n            box-shadow: 0 6px 15px rgba(255, 107, 0, 0.3);\r\n        }\r\n        .photo img {\r\n            width: 100%;\r\n            height: 100%;\r\n            display: block;\r\n            object-fit: cover;\r\n        }\r\n        .member-name {\r\n            font-size: 28px;\r\n            font-weight: 700;\r\n            color: #ff6b00;\r\n            margin: 15px 0;\r\n            text-transform: uppercase;\r\n        }\r\n        .birthday-info {\r\n            background: #fff2cc;\r\n            border-radius: 15px;\r\n            padding: 20px;\r\n            margin: 25px 0;\r\n            display: inline-block;\r\n            border: 2px solid #ff9e40;\r\n        }\r\n        .birthday-date {\r\n            font-size: 22px;\r\n            font-weight: 600;\r\n            color: #ff6b00;\r\n            margin: 5px 0;\r\n        }\r\n        .birthday-age {\r\n            font-size: 16px;\r\n            font-weight: 700;\r\n            color: #ffffff;\r\n            background: #ff6b00;\r\n            padding: 8px 18px;\r\n            border-radius: 15px;\r\n            display: inline-block;\r\n            margin-top: 10px;\r\n        }\r\n        .suggestions {\r\n            margin: 25px 0;\r\n            text-align: left;\r\n            background: #fff2cc;\r\n            padding: 20px;\r\n            border-radius: 15px;\r\n        }\r\n        .suggestions h3 {\r\n            color: #ff6b00;\r\n            font-size: 20px;\r\n            margin: 0 0 15px;\r\n            font-weight: 600;\r\n        }\r\n        .suggestions ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .suggestions li {\r\n            padding: 10px 0;\r\n            border-bottom: 1px solid #ffe4b3;\r\n            color: #4a5568;\r\n            font-size: 16px;\r\n        }\r\n        .suggestions li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .cta-button {\r\n            display: inline-block;\r\n            background: linear-gradient(120deg, #ff6b00, #ff9e40);\r\n            color: #ffffff;\r\n            text-decoration: none;\r\n            padding: 12px 35px;\r\n            border-radius: 30px;\r\n            font-weight: 600;\r\n            font-size: 18px;\r\n            margin: 20px 0;\r\n            box-shadow: 0 6px 15px rgba(255, 107, 0, 0.3);\r\n            transition: 0.3s ease;\r\n        }\r\n        .cta-button:hover {\r\n            background: linear-gradient(120deg, #ff9e40, #ff6b00);\r\n            transform: scale(1.05);\r\n        }\r\n        .footer {\r\n            background: #fdf2e9;\r\n            padding: 15px;\r\n            text-align: center;\r\n            font-size: 14px;\r\n            color: #718096;\r\n            border-top: 1px solid #ffe4b3;\r\n        }\r\n        .church-name {\r\n            color: #ff6b00;\r\n            font-weight: 700;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 10px;\r\n            font-size: 12px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #718096;\r\n            text-decoration: none;\r\n        }\r\n        .unsubscribe a:hover {\r\n            text-decoration: underline;\r\n        }\r\n        /* Mobile view optimized as a notification */\r\n        @media screen and (max-width: 480px) {\r\n            .container {\r\n                max-width: 320px;\r\n                margin: 10px auto;\r\n                padding: 0;\r\n            }\r\n            .card {\r\n                border-radius: 15px;\r\n                box-shadow: 0 6px 20px rgba(255, 107, 0, 0.2);\r\n                border: 1px solid #ff9e40;\r\n            }\r\n            .header {\r\n                padding: 20px;\r\n            }\r\n            .header h1 {\r\n                font-size: 22px;\r\n            }\r\n            .content {\r\n                padding: 15px;\r\n            }\r\n            .greeting {\r\n                font-size: 16px;\r\n                margin-bottom: 15px;\r\n            }\r\n            .photo-container {\r\n                width: 90px;\r\n                height: 90px;\r\n                margin: 0 auto 15px;\r\n            }\r\n            .photo {\r\n                border-width: 4px;\r\n            }\r\n            .member-name {\r\n                font-size: 20px;\r\n                margin: 10px 0;\r\n            }\r\n            .birthday-info {\r\n                padding: 12px;\r\n                margin: 15px 0;\r\n                border: 1px solid #ff9e40;\r\n            }\r\n            .birthday-date {\r\n                font-size: 16px;\r\n            }\r\n            .birthday-age {\r\n                font-size: 14px;\r\n                padding: 5px 12px;\r\n            }\r\n            .suggestions {\r\n                padding: 12px;\r\n                text-align: center;\r\n            }\r\n            .suggestions h3 {\r\n                font-size: 16px;\r\n                margin: 0 0 10px;\r\n            }\r\n            .cta-button {\r\n                padding: 10px 25px;\r\n                font-size: 16px;\r\n                width: 80%;\r\n                margin: 10px auto;\r\n            }\r\n            .footer {\r\n                padding: 10px;\r\n                font-size: 12px;\r\n            }\r\n            .unsubscribe {\r\n                margin-top: 5px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>🎉 Upcoming Birthday Joy! 🎉</h1>\r\n            </div>\r\n            <div class=\"content\">\r\n                <div class=\"greeting\">\r\n                    <p>Hello <strong>Ndivhuwo Machiba</strong>,</p>\r\n                    <p>Get ready to celebrate with us! A special birthday is coming up in our church family <strong>tomorrow</strong> and we want you to be part of the celebration! 🎂🎈</p>\r\n                </div>\r\n\r\n                <div class=\"photo-container\">\r\n                    <div class=\"photo\">\r\n                        <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\">\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"member-name\">{birthday_member_name}</div>\r\n\r\n                <div class=\"birthday-info\">\r\n                    <p><strong>Birthday:</strong></p>\r\n                    <div class=\"birthday-date\">Sunday, March 9, 2025</div>\r\n                    <div class=\"birthday-age\">{birthday_member_age} Years</div>\r\n                </div>\r\n\r\n                <div class=\"suggestions\">\r\n                    <h3>🎁 Celebration Ideas 🎉</h3>\r\n                    <ul>\r\n                        <li>Send a heartfelt birthday message ✉️</li>\r\n                        <li>Offer a prayer for blessings 🙏</li>\r\n                        <li>Share an uplifting Bible verse 📖</li>\r\n                        <li>Surprise them with a small thoughtful gift 🎁</li>\r\n                    </ul>\r\n                </div>\r\n\r\n                <a href=\"#\" class=\"cta-button\">Send Your Best Wishes 🎉</a>\r\n            </div>\r\n            <div class=\"footer\">\r\n                <p>Blessings from <span class=\"church-name\">Freedom Assembly Church</span></p>\r\n                <div class=\"unsubscribe\">\r\n                    <a href=\"#\">Unsubscribe from these birthday updates</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    \r\n</body>\r\n</html>', 'b_notification', '2025-03-08 11:59:49', NULL, 0, NULL, NULL),
(12, 4, '69e19cf027a018792ff8024be01cf838', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #fdf2e9;\r\n            color: #2d3748;\r\n            line-height: 1.5;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 20px auto;\r\n            padding: 0 15px;\r\n        }\r\n        .card {\r\n            background: #ffffff;\r\n            border-radius: 20px;\r\n            box-shadow: 0 8px 25px rgba(255, 107, 0, 0.2);\r\n            overflow: hidden;\r\n            width: 100%;\r\n        }\r\n        .header {\r\n            background: linear-gradient(120deg, #ff6b00, #ff9e40);\r\n            padding: 40px 20px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n            border-bottom: 5px solid #ff9e40;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 30px;\r\n            font-weight: bold;\r\n        }\r\n        .content {\r\n            padding: 30px 20px;\r\n            text-align: center;\r\n        }\r\n        .greeting {\r\n            font-size: 18px;\r\n            margin: 0 0 25px;\r\n            color: #4a5568;\r\n        }\r\n        .photo-container {\r\n            margin: 0 auto 20px;\r\n            width: 140px;\r\n            height: 140px;\r\n        }\r\n        .photo {\r\n            width: 100%;\r\n            height: 100%;\r\n            border-radius: 50%;\r\n            border: 5px solid #fff8e6;\r\n            overflow: hidden;\r\n            box-shadow: 0 6px 15px rgba(255, 107, 0, 0.3);\r\n        }\r\n        .photo img {\r\n            width: 100%;\r\n            height: 100%;\r\n            display: block;\r\n            object-fit: cover;\r\n        }\r\n        .member-name {\r\n            font-size: 28px;\r\n            font-weight: 700;\r\n            color: #ff6b00;\r\n            margin: 15px 0;\r\n            text-transform: uppercase;\r\n        }\r\n        .birthday-info {\r\n            background: #fff2cc;\r\n            border-radius: 15px;\r\n            padding: 20px;\r\n            margin: 25px 0;\r\n            display: inline-block;\r\n            border: 2px solid #ff9e40;\r\n        }\r\n        .birthday-date {\r\n            font-size: 22px;\r\n            font-weight: 600;\r\n            color: #ff6b00;\r\n            margin: 5px 0;\r\n        }\r\n        .birthday-age {\r\n            font-size: 16px;\r\n            font-weight: 700;\r\n            color: #ffffff;\r\n            background: #ff6b00;\r\n            padding: 8px 18px;\r\n            border-radius: 15px;\r\n            display: inline-block;\r\n            margin-top: 10px;\r\n        }\r\n        .suggestions {\r\n            margin: 25px 0;\r\n            text-align: left;\r\n            background: #fff2cc;\r\n            padding: 20px;\r\n            border-radius: 15px;\r\n        }\r\n        .suggestions h3 {\r\n            color: #ff6b00;\r\n            font-size: 20px;\r\n            margin: 0 0 15px;\r\n            font-weight: 600;\r\n        }\r\n        .suggestions ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .suggestions li {\r\n            padding: 10px 0;\r\n            border-bottom: 1px solid #ffe4b3;\r\n            color: #4a5568;\r\n            font-size: 16px;\r\n        }\r\n        .suggestions li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .cta-button {\r\n            display: inline-block;\r\n            background: linear-gradient(120deg, #ff6b00, #ff9e40);\r\n            color: #ffffff;\r\n            text-decoration: none;\r\n            padding: 12px 35px;\r\n            border-radius: 30px;\r\n            font-weight: 600;\r\n            font-size: 18px;\r\n            margin: 20px 0;\r\n            box-shadow: 0 6px 15px rgba(255, 107, 0, 0.3);\r\n            transition: 0.3s ease;\r\n        }\r\n        .cta-button:hover {\r\n            background: linear-gradient(120deg, #ff9e40, #ff6b00);\r\n            transform: scale(1.05);\r\n        }\r\n        .footer {\r\n            background: #fdf2e9;\r\n            padding: 15px;\r\n            text-align: center;\r\n            font-size: 14px;\r\n            color: #718096;\r\n            border-top: 1px solid #ffe4b3;\r\n        }\r\n        .church-name {\r\n            color: #ff6b00;\r\n            font-weight: 700;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 10px;\r\n            font-size: 12px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #718096;\r\n            text-decoration: none;\r\n        }\r\n        .unsubscribe a:hover {\r\n            text-decoration: underline;\r\n        }\r\n        /* Mobile view optimized as a notification */\r\n        @media screen and (max-width: 480px) {\r\n            .container {\r\n                max-width: 320px;\r\n                margin: 10px auto;\r\n                padding: 0;\r\n            }\r\n            .card {\r\n                border-radius: 15px;\r\n                box-shadow: 0 6px 20px rgba(255, 107, 0, 0.2);\r\n                border: 1px solid #ff9e40;\r\n            }\r\n            .header {\r\n                padding: 20px;\r\n            }\r\n            .header h1 {\r\n                font-size: 22px;\r\n            }\r\n            .content {\r\n                padding: 15px;\r\n            }\r\n            .greeting {\r\n                font-size: 16px;\r\n                margin-bottom: 15px;\r\n            }\r\n            .photo-container {\r\n                width: 90px;\r\n                height: 90px;\r\n                margin: 0 auto 15px;\r\n            }\r\n            .photo {\r\n                border-width: 4px;\r\n            }\r\n            .member-name {\r\n                font-size: 20px;\r\n                margin: 10px 0;\r\n            }\r\n            .birthday-info {\r\n                padding: 12px;\r\n                margin: 15px 0;\r\n                border: 1px solid #ff9e40;\r\n            }\r\n            .birthday-date {\r\n                font-size: 16px;\r\n            }\r\n            .birthday-age {\r\n                font-size: 14px;\r\n                padding: 5px 12px;\r\n            }\r\n            .suggestions {\r\n                padding: 12px;\r\n                text-align: center;\r\n            }\r\n            .suggestions h3 {\r\n                font-size: 16px;\r\n                margin: 0 0 10px;\r\n            }\r\n            .cta-button {\r\n                padding: 10px 25px;\r\n                font-size: 16px;\r\n                width: 80%;\r\n                margin: 10px auto;\r\n            }\r\n            .footer {\r\n                padding: 10px;\r\n                font-size: 12px;\r\n            }\r\n            .unsubscribe {\r\n                margin-top: 5px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>🎉 Upcoming Birthday Joy! 🎉</h1>\r\n            </div>\r\n            <div class=\"content\">\r\n                <div class=\"greeting\">\r\n                    <p>Hello <strong>Ndivhuwo Machiba</strong>,</p>\r\n                    <p>Get ready to celebrate with us! A special birthday is coming up in our church family <strong>tomorrow</strong> and we want you to be part of the celebration! 🎂🎈</p>\r\n                </div>\r\n\r\n                <div class=\"photo-container\">\r\n                    <div class=\"photo\">\r\n                        <img src=\"https://freedomassemblydb.online/church/uploads/67cc11b088f39.jpg\" alt=\"Godwin\">\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"member-name\">Godwin</div>\r\n\r\n                <div class=\"birthday-info\">\r\n                    <p><strong>Birthday:</strong></p>\r\n                    <div class=\"birthday-date\">Sunday, March 9, 2025</div>\r\n                    <div class=\"birthday-age\">40 Years</div>\r\n                </div>\r\n\r\n                <div class=\"suggestions\">\r\n                    <h3>🎁 Celebration Ideas 🎉</h3>\r\n                    <ul>\r\n                        <li>Send a heartfelt birthday message ✉️</li>\r\n                        <li>Offer a prayer for blessings 🙏</li>\r\n                        <li>Share an uplifting Bible verse 📖</li>\r\n                        <li>Surprise them with a small thoughtful gift 🎁</li>\r\n                    </ul>\r\n                </div>\r\n\r\n                <a href=\"#\" class=\"cta-button\">Send Your Best Wishes 🎉</a>\r\n            </div>\r\n            <div class=\"footer\">\r\n                <p>Blessings from <span class=\"church-name\">Freedom Assembly Church</span></p>\r\n                <div class=\"unsubscribe\">\r\n                    <a href=\"#\">Unsubscribe from these birthday updates</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    \r\n</body>\r\n</html>', 'b_notification', '2025-03-08 11:59:49', NULL, 0, NULL, NULL),
(13, 4, 'track_67cc31368cd9f2.00587624', NULL, 'b_notification', '2025-03-08 11:59:50', NULL, 0, NULL, NULL),
(14, 4, 'track_67cc31368cef90.04515785', NULL, 'b_notification', '2025-03-08 11:59:50', NULL, 0, NULL, NULL),
(15, 3, 'a5c4bd08c597ba2c9bc7ce25d0cd91ba', 'Celebrate with us! {birthday_member_name}\'s birthday is coming up in 7 days', 'b_notification', '2025-03-08 12:02:09', NULL, 0, NULL, NULL),
(16, 3, 'a5c4bd08c597ba2c9bc7ce25d0cd91ba', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f4faff;\r\n            color: #2d3748;\r\n            line-height: 1.5;\r\n        }\r\n        .container {\r\n            max-width: 580px;\r\n            margin: 20px auto;\r\n            padding: 0 10px;\r\n        }\r\n        .card {\r\n            background: #ffffff;\r\n            border-radius: 15px;\r\n            box-shadow: 0 6px 20px rgba(0, 122, 255, 0.15);\r\n            overflow: hidden;\r\n            width: 100%;\r\n        }\r\n        .header {\r\n            background: linear-gradient(135deg, #007aff, #00c6ff);\r\n            padding: 25px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 24px;\r\n            font-weight: bold;\r\n        }\r\n        .header p {\r\n            margin: 6px 0 0;\r\n            font-size: 14px;\r\n            opacity: 0.9;\r\n        }\r\n        .content {\r\n            padding: 20px;\r\n            text-align: center;\r\n        }\r\n        .greeting {\r\n            font-size: 16px;\r\n            margin: 0 0 15px;\r\n            color: #4a5568;\r\n        }\r\n        .profile {\r\n            margin: 20px 0;\r\n        }\r\n        .photo {\r\n            width: 140px;\r\n            height: 140px;\r\n            border-radius: 50%;\r\n            border: 4px solid #f4faff;\r\n            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);\r\n            overflow: hidden;\r\n            margin: 0 auto;\r\n        }\r\n        .photo img {\r\n            width: 100%;\r\n            height: 100%;\r\n            display: block;\r\n            object-fit: cover;\r\n        }\r\n        .member-name {\r\n            font-size: 22px;\r\n            font-weight: 600;\r\n            color: #007aff;\r\n            margin: 10px 0 5px;\r\n        }\r\n        .birthday-details {\r\n            background: #e6f0ff;\r\n            border-radius: 10px;\r\n            padding: 15px;\r\n            margin: 20px 0;\r\n            display: inline-block;\r\n        }\r\n        .birthday-date {\r\n            font-size: 17px;\r\n            font-weight: 600;\r\n            color: #007aff;\r\n            margin: 0 0 6px;\r\n        }\r\n        .birthday-age {\r\n            font-size: 15px;\r\n            font-weight: bold;\r\n            color: #ffffff;\r\n            background: #007aff;\r\n            padding: 5px 12px;\r\n            border-radius: 15px;\r\n            display: inline-block;\r\n        }\r\n        .countdown {\r\n            font-size: 14px;\r\n            color: #4a5568;\r\n            margin: 10px 0 0;\r\n        }\r\n        .suggestions {\r\n            background: #e6f0ff;\r\n            border-radius: 10px;\r\n            padding: 15px;\r\n            margin: 20px 0;\r\n            text-align: left;\r\n        }\r\n        .suggestions h3 {\r\n            color: #007aff;\r\n            font-size: 16px;\r\n            margin: 0 0 10px;\r\n        }\r\n        .suggestions ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .suggestions li {\r\n            padding: 8px 0;\r\n            border-bottom: 1px solid #cce0ff;\r\n            font-size: 14px;\r\n            color: #4a5568;\r\n        }\r\n        .suggestions li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .tracking-pixel { position: absolute !important; width: 1px !important; height: 1px !important; padding: 0 !important; margin: -1px !important; overflow: hidden !important; clip: rect(0,0,0,0) !important; white-space: nowrap !important; border: 0 !important; opacity: 0 !important; pointer-events: none !important; } .action-button {\r\n            display: inline-block;\r\n            background: linear-gradient(135deg, #007aff, #00c6ff);\r\n            color: #ffffff;\r\n            text-decoration: none;\r\n            padding: 10px 25px;\r\n            border-radius: 20px;\r\n            font-weight: 600;\r\n            font-size: 15px;\r\n            margin: 15px 0;\r\n            box-shadow: 0 4px 10px rgba(0, 122, 255, 0.3);\r\n        }\r\n        .footer {\r\n            background: #f4faff;\r\n            padding: 15px;\r\n            text-align: center;\r\n            font-size: 12px;\r\n            color: #718096;\r\n            border-top: 1px solid #cce0ff;\r\n        }\r\n        .church-name {\r\n            color: #007aff;\r\n            font-weight: bold;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 8px;\r\n            font-size: 10px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #718096;\r\n            text-decoration: none;\r\n        }\r\n        @media (max-width: 480px) {\r\n            .container {\r\n                padding: 0 5px;\r\n            }\r\n            .card {\r\n                border-radius: 10px;\r\n            }\r\n            .header {\r\n                padding: 20px;\r\n            }\r\n            .header h1 {\r\n                font-size: 20px;\r\n            }\r\n            .content {\r\n                padding: 15px;\r\n            }\r\n            .photo {\r\n                width: 110px;\r\n                height: 110px;\r\n            }\r\n            .member-name {\r\n                font-size: 18px;\r\n            }\r\n            .tracking-pixel { position: absolute !important; width: 1px !important; height: 1px !important; padding: 0 !important; margin: -1px !important; overflow: hidden !important; clip: rect(0,0,0,0) !important; white-space: nowrap !important; border: 0 !important; opacity: 0 !important; pointer-events: none !important; } .action-button {\r\n                padding: 8px 20px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>Happy Birthday Celebration!</h1>\r\n                <p>Join us in honoring a special church family member</p>\r\n            </div>\r\n            \r\n            <div class=\"content\">\r\n                <div class=\"greeting\">\r\n                    <p>Hello Godwin,</p>\r\n                    <p>We\'re thrilled to celebrate {birthday_member_name}\'s birthday, just {days_until_birthday} days away!</p>\r\n                </div>\r\n                \r\n                <div class=\"profile\">\r\n                    <div class=\"photo\">\r\n                        <img src=\"{birthday_member_image}\" alt=\"{birthday_member_name}\">\r\n                    </div>\r\n                    <div class=\"member-name\">{birthday_member_name}</div>\r\n                </div>\r\n                \r\n                <div class=\"birthday-details\">\r\n                    <div class=\"birthday-date\">Saturday, March 15, 2025</div>\r\n                    <div class=\"birthday-age\">Turning {birthday_member_age}</div>\r\n                    <div class=\"countdown\">Only {days_until_birthday} days left!</div>\r\n                </div>\r\n                \r\n                <div class=\"suggestions\">\r\n                    <h3>Ways to Bless {birthday_member_name}:</h3>\r\n                    <ul>\r\n                        <li>Send a heartfelt birthday message</li>\r\n                        <li>Pray for their upcoming year</li>\r\n                        <li>Share an encouraging scripture</li>\r\n                        <li>Consider a thoughtful card or gift</li>\r\n                    </ul>\r\n                </div>\r\n                \r\n                <a href=\"mailto:{birthday_member_email}?subject=Happy%20Birthday%20{birthday_member_name}!&body=Dear%20{birthday_member_name},%0D%0A%0D%0AWishing%20you%20a%20wonderful%20birthday%20filled%20with%20joy%20and%20blessings%20from%20your%20Freedom%20Assembly%20Church%20family!%0D%0A%0D%0A[Add%20your%20personal%20message%20here]%0D%0A%0D%0ABlessings,%0D%0AGodwin Bointa\" class=\"action-button\">Send Birthday Wishes</a>\r\n            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <p>Blessings from <span class=\"church-name\">Freedom Assembly Church</span></p>\r\n                <div class=\"unsubscribe\">\r\n                    <a href=\"#\">Unsubscribe from these updates</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n    \r\n</body>\r\n</html>', 'b_notification', '2025-03-08 12:02:09', NULL, 0, NULL, NULL),
(17, 3, 'a5c4bd08c597ba2c9bc7ce25d0cd91ba', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Birthday Celebration</title>\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: \"Segoe UI\", Arial, sans-serif;\r\n            background-color: #f4faff;\r\n            color: #2d3748;\r\n            line-height: 1.5;\r\n        }\r\n        .container {\r\n            max-width: 580px;\r\n            margin: 20px auto;\r\n            padding: 0 10px;\r\n        }\r\n        .card {\r\n            background: #ffffff;\r\n            border-radius: 15px;\r\n            box-shadow: 0 6px 20px rgba(0, 122, 255, 0.15);\r\n            overflow: hidden;\r\n            width: 100%;\r\n        }\r\n        .header {\r\n            background: linear-gradient(135deg, #007aff, #00c6ff);\r\n            padding: 25px;\r\n            text-align: center;\r\n            color: #ffffff;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 24px;\r\n            font-weight: bold;\r\n        }\r\n        .header p {\r\n            margin: 6px 0 0;\r\n            font-size: 14px;\r\n            opacity: 0.9;\r\n        }\r\n        .content {\r\n            padding: 20px;\r\n            text-align: center;\r\n        }\r\n        .greeting {\r\n            font-size: 16px;\r\n            margin: 0 0 15px;\r\n            color: #4a5568;\r\n        }\r\n        .profile {\r\n            margin: 20px 0;\r\n        }\r\n        .photo {\r\n            width: 140px;\r\n            height: 140px;\r\n            border-radius: 50%;\r\n            border: 4px solid #f4faff;\r\n            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);\r\n            overflow: hidden;\r\n            margin: 0 auto;\r\n        }\r\n        .photo img {\r\n            width: 100%;\r\n            height: 100%;\r\n            display: block;\r\n            object-fit: cover;\r\n        }\r\n        .member-name {\r\n            font-size: 22px;\r\n            font-weight: 600;\r\n            color: #007aff;\r\n            margin: 10px 0 5px;\r\n        }\r\n        .birthday-details {\r\n            background: #e6f0ff;\r\n            border-radius: 10px;\r\n            padding: 15px;\r\n            margin: 20px 0;\r\n            display: inline-block;\r\n        }\r\n        .birthday-date {\r\n            font-size: 17px;\r\n            font-weight: 600;\r\n            color: #007aff;\r\n            margin: 0 0 6px;\r\n        }\r\n        .birthday-age {\r\n            font-size: 15px;\r\n            font-weight: bold;\r\n            color: #ffffff;\r\n            background: #007aff;\r\n            padding: 5px 12px;\r\n            border-radius: 15px;\r\n            display: inline-block;\r\n        }\r\n        .countdown {\r\n            font-size: 14px;\r\n            color: #4a5568;\r\n            margin: 10px 0 0;\r\n        }\r\n        .suggestions {\r\n            background: #e6f0ff;\r\n            border-radius: 10px;\r\n            padding: 15px;\r\n            margin: 20px 0;\r\n            text-align: left;\r\n        }\r\n        .suggestions h3 {\r\n            color: #007aff;\r\n            font-size: 16px;\r\n            margin: 0 0 10px;\r\n        }\r\n        .suggestions ul {\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        .suggestions li {\r\n            padding: 8px 0;\r\n            border-bottom: 1px solid #cce0ff;\r\n            font-size: 14px;\r\n            color: #4a5568;\r\n        }\r\n        .suggestions li:last-child {\r\n            border-bottom: none;\r\n        }\r\n        .tracking-pixel { position: absolute !important; width: 1px !important; height: 1px !important; padding: 0 !important; margin: -1px !important; overflow: hidden !important; clip: rect(0,0,0,0) !important; white-space: nowrap !important; border: 0 !important; opacity: 0 !important; pointer-events: none !important; } .action-button {\r\n            display: inline-block;\r\n            background: linear-gradient(135deg, #007aff, #00c6ff);\r\n            color: #ffffff;\r\n            text-decoration: none;\r\n            padding: 10px 25px;\r\n            border-radius: 20px;\r\n            font-weight: 600;\r\n            font-size: 15px;\r\n            margin: 15px 0;\r\n            box-shadow: 0 4px 10px rgba(0, 122, 255, 0.3);\r\n        }\r\n        .footer {\r\n            background: #f4faff;\r\n            padding: 15px;\r\n            text-align: center;\r\n            font-size: 12px;\r\n            color: #718096;\r\n            border-top: 1px solid #cce0ff;\r\n        }\r\n        .church-name {\r\n            color: #007aff;\r\n            font-weight: bold;\r\n        }\r\n        .unsubscribe {\r\n            margin-top: 8px;\r\n            font-size: 10px;\r\n        }\r\n        .unsubscribe a {\r\n            color: #718096;\r\n            text-decoration: none;\r\n        }\r\n        @media (max-width: 480px) {\r\n            .container {\r\n                padding: 0 5px;\r\n            }\r\n            .card {\r\n                border-radius: 10px;\r\n            }\r\n            .header {\r\n                padding: 20px;\r\n            }\r\n            .header h1 {\r\n                font-size: 20px;\r\n            }\r\n            .content {\r\n                padding: 15px;\r\n            }\r\n            .photo {\r\n                width: 110px;\r\n                height: 110px;\r\n            }\r\n            .member-name {\r\n                font-size: 18px;\r\n            }\r\n            .tracking-pixel { position: absolute !important; width: 1px !important; height: 1px !important; padding: 0 !important; margin: -1px !important; overflow: hidden !important; clip: rect(0,0,0,0) !important; white-space: nowrap !important; border: 0 !important; opacity: 0 !important; pointer-events: none !important; } .action-button {\r\n                padding: 8px 20px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"card\">\r\n            <div class=\"header\">\r\n                <h1>Happy Birthday Celebration!</h1>\r\n                <p>Join us in honoring a special church family member</p>\r\n            </div>\r\n            \r\n            <div class=\"content\">\r\n                <div class=\"greeting\">\r\n                    <p>Hello Godwin,</p>\r\n                    <p>We\'re thrilled to celebrate Ndivhuwo\'s birthday, just {days_until_birthday} days away!</p>\r\n                </div>\r\n                \r\n                <div class=\"profile\">\r\n                    <div class=\"photo\">\r\n                        <img src=\"https://freedomassemblydb.online/church/uploads/67cc13b35c7ea.jpg\" alt=\"Ndivhuwo\">\r\n                    </div>\r\n                    <div class=\"member-name\">Ndivhuwo</div>\r\n                </div>\r\n                \r\n                <div class=\"birthday-details\">\r\n                    <div class=\"birthday-date\">Saturday, March 15, 2025</div>\r\n                    <div class=\"birthday-age\">Turning 35</div>\r\n                    <div class=\"countdown\">Only {days_until_birthday} days left!</div>\r\n                </div>\r\n                \r\n                <div class=\"suggestions\">\r\n                    <h3>Ways to Bless Ndivhuwo:</h3>\r\n                    <ul>\r\n                        <li>Send a heartfelt birthday message</li>\r\n                        <li>Pray for their upcoming year</li>\r\n                        <li>Share an encouraging scripture</li>\r\n                        <li>Consider a thoughtful card or gift</li>\r\n                    </ul>\r\n                </div>\r\n                \r\n                <a href=\"mailto:<EMAIL>?subject=Happy%20Birthday%20Ndivhuwo!&body=Dear%20Ndivhuwo,%0D%0A%0D%0AWishing%20you%20a%20wonderful%20birthday%20filled%20with%20joy%20and%20blessings%20from%20your%20Freedom%20Assembly%20Church%20family!%0D%0A%0D%0A[Add%20your%20personal%20message%20here]%0D%0A%0D%0ABlessings,%0D%0AGodwin Bointa\" class=\"action-button\">Send Birthday Wishes</a>\r\n            </div>\r\n            \r\n            <div class=\"footer\">\r\n                <p>Blessings from <span class=\"church-name\">Freedom Assembly Church</span></p>\r\n                <div class=\"unsubscribe\">\r\n                    <a href=\"#\">Unsubscribe from these updates</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n    \r\n</body>\r\n</html>', 'b_notification', '2025-03-08 12:02:09', NULL, 0, NULL, NULL),
(18, 3, 'track_67cc31c234d2f4.65892515', NULL, 'b_notification', '2025-03-08 12:02:10', NULL, 0, NULL, NULL),
(19, 3, 'track_67cc31c234e121.64514286', NULL, 'b_notification', '2025-03-08 12:02:10', NULL, 0, NULL, NULL),
(20, 3, 'track_67cc33d29c02e5.16798217', NULL, 'bulk', '2025-03-08 12:10:59', NULL, 0, NULL, NULL),
(21, 4, 'track_67cc33d60d18f8.40750886', NULL, 'bulk', '2025-03-08 12:11:02', NULL, 0, NULL, NULL),
(22, 3, 'track_67cc61f67ee765.01944055', NULL, 'birthday', '2025-03-08 15:27:50', NULL, 0, NULL, NULL),
(23, 4, 'track_67cc62293e8d87.72359167', NULL, 'birthday', '2025-03-08 15:28:41', NULL, 0, NULL, NULL),
(24, 3, 'track_67cd5eee0888d0.15889905', NULL, 'birthday', '2025-03-09 09:27:10', NULL, 0, NULL, NULL),
(25, 3, 'track_67cdb1f2ecfde4.15313232', NULL, 'bulk', '2025-03-09 15:21:23', NULL, 0, NULL, NULL),
(26, 4, 'track_67cdb1f5a73057.29719205', NULL, 'bulk', '2025-03-09 15:21:26', NULL, 0, NULL, NULL),
(27, 3, 'track_67cdb2881392a5.04194482', NULL, 'bulk', '2025-03-09 15:23:52', NULL, 0, NULL, NULL),
(28, 4, 'track_67cdb28b99b2b2.80318542', NULL, 'bulk', '2025-03-09 15:23:56', NULL, 0, NULL, NULL),
(29, 3, 'track_67ce87ab6412b3.99807469', NULL, 'bulk', '2025-03-10 06:33:16', NULL, 0, NULL, NULL),
(30, 4, 'track_67ce87aedc6385.32664448', NULL, 'bulk', '2025-03-10 06:33:19', NULL, 0, NULL, NULL),
(31, 3, 'track_67ce887e762f09.17640508', NULL, 'bulk', '2025-03-10 06:36:47', NULL, 0, NULL, NULL),
(32, 4, 'track_67ce88820d8413.38982866', NULL, 'bulk', '2025-03-10 06:36:50', NULL, 0, NULL, NULL);

-- --------------------------------------------------------

--
-- Stand-in structure for view `email_tracking_stats`
-- (See below for the actual view)
--
CREATE TABLE `email_tracking_stats` (
`email_type` varchar(20)
,`template_name` varchar(50)
,`emails_sent` bigint(21)
,`emails_opened` bigint(21)
,`open_rate` decimal(26,2)
,`avg_opens_per_email` decimal(14,4)
);

-- --------------------------------------------------------

--
-- Table structure for table `members`
--

CREATE TABLE `members` (
  `id` int(11) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `occupation` varchar(100) DEFAULT NULL,
  `image_path` varchar(255) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `date_of_birth` date DEFAULT NULL,
  `birth_date` date DEFAULT NULL,
  `home_address` text DEFAULT NULL,
  `phone_number` varchar(20) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` varchar(20) DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `members`
--

INSERT INTO `members` (`id`, `full_name`, `first_name`, `last_name`, `occupation`, `image_path`, `email`, `date_of_birth`, `birth_date`, `home_address`, `phone_number`, `message`, `created_at`, `updated_at`, `status`) VALUES
(3, 'Godwin Bointa', 'Godwin', 'Bointa', 'IT', 'uploads/67cc11b088f39.jpg', '<EMAIL>', NULL, '1985-03-09', '17 Sjampanje Street', '+27686814477', 'Wonderful', '2025-03-08 09:45:20', '2025-03-08 09:45:20', 'active'),
(4, 'Ndivhuwo Machiba', 'Ndivhuwo', 'Machiba', 'Chef', 'uploads/67cc13b35c7ea.jpg', '<EMAIL>', NULL, '1990-03-15', '17 Sjampanje Street', '+27686814477', 'Thank you for successfully completing this application', '2025-03-08 09:53:55', '2025-03-08 09:53:55', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
(1, 'site_title', 'Freedom Assembly Church International', '2025-03-06 19:33:51', '2025-03-08 12:22:12'),
(2, 'admin_title', 'Profile Pilot', '2025-03-06 19:33:51', '2025-03-06 21:48:19'),
(3, 'site_description', 'A welcoming church community dedicated to worship, fellowship, and service.', '2025-03-06 19:33:51', '2025-03-06 19:33:51'),
(4, 'site_keywords', 'church, worship, community, faith, prayer', '2025-03-06 19:33:51', '2025-03-06 19:33:51'),
(6, 'contact_phone', '+27102712223', '2025-03-06 19:33:51', '2025-03-08 12:22:12'),
(7, 'church_address', '2520 Albertina Sisulu Road Florida 1709 Johannesburg South Africa', '2025-03-06 19:33:51', '2025-03-08 12:22:12'),
(8, 'church_service_times', 'Sunday: 9:00 AM, 11:00 AM\r\nWednesday: 7:00 PM', '2025-03-06 19:33:51', '2025-03-06 21:48:19'),
(9, 'footer_text', '2025 Freedom Assembly Church International. All rights reserved.', '2025-03-06 19:33:51', '2025-03-08 12:22:12'),
(10, 'social_facebook', 'https://www.facebook.com/ltmstream', '2025-03-06 19:33:51', '2025-03-08 12:22:12'),
(11, 'social_twitter', 'https://x.com/truefreedomite', '2025-03-06 19:33:51', '2025-03-08 12:22:12'),
(12, 'social_instagram', 'https://www.instagram.com/truefreedomite', '2025-03-06 19:33:51', '2025-03-08 12:22:12'),
(13, 'social_youtube', 'https://www.youtube.com/@freedomassemblychurch', '2025-03-06 19:33:51', '2025-03-08 12:22:12'),
(36, 'sender_name', 'Support Team', '2025-03-06 20:16:48', '2025-03-06 20:40:25'),
(121, 'email_smtp_host', 'smtp.hostinger.com', '2025-03-06 22:07:11', '2025-03-08 09:42:57'),
(122, 'email_smtp_auth', '1', '2025-03-06 22:07:11', '2025-03-06 22:07:11'),
(123, 'email_smtp_username', '<EMAIL>', '2025-03-06 22:07:11', '2025-03-08 09:42:57'),
(124, 'email_smtp_password', 'p2=a;]A5', '2025-03-06 22:07:11', '2025-03-08 09:42:57'),
(125, 'email_smtp_secure', 'ssl', '2025-03-06 22:07:11', '2025-03-08 09:42:57'),
(126, 'email_smtp_port', '465', '2025-03-06 22:07:11', '2025-03-08 09:42:57'),
(127, 'email_sender_email', '<EMAIL>', '2025-03-06 22:07:11', '2025-03-08 09:42:57'),
(128, 'email_sender_name', 'Freedom Assembly Church International', '2025-03-06 22:07:11', '2025-03-08 09:42:57'),
(129, 'admin_email', '<EMAIL>', '2025-03-06 22:07:11', '2025-03-08 09:42:57'),
(138, 'smtp_host', 'smtp.example.com', '2025-03-06 22:09:05', '2025-03-06 22:09:05'),
(139, 'smtp_auth', '1', '2025-03-06 22:09:05', '2025-03-06 22:09:05'),
(140, 'smtp_username', '<EMAIL>', '2025-03-06 22:09:05', '2025-03-06 22:09:05'),
(141, 'smtp_password', 'password123', '2025-03-06 22:09:05', '2025-03-06 22:09:05'),
(142, 'smtp_secure', 'ssl', '2025-03-06 22:09:05', '2025-03-06 22:09:05'),
(143, 'smtp_port', '465', '2025-03-06 22:09:05', '2025-03-06 22:09:05'),
(144, 'sender_email', '<EMAIL>', '2025-03-06 22:09:05', '2025-03-06 22:09:05'),
(163, 'contact_email', '<EMAIL>', '2025-03-08 12:22:12', '2025-03-08 12:22:12'),
(164, 'email_contact_email', '<EMAIL>', '2025-03-08 12:22:12', '2025-03-08 12:22:12');

-- --------------------------------------------------------

--
-- Table structure for table `whatsapp_logs`
--

CREATE TABLE `whatsapp_logs` (
  `id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `template_id` int(11) NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'initiated',
  `message` text DEFAULT NULL,
  `sent_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `whatsapp_settings`
--

CREATE TABLE `whatsapp_settings` (
  `id` int(11) NOT NULL DEFAULT 1,
  `sender_number` varchar(20) NOT NULL,
  `sender_name` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `whatsapp_settings`
--

INSERT INTO `whatsapp_settings` (`id`, `sender_number`, `sender_name`) VALUES
(1, '+27846114757', 'Freedom Assembly Church');

-- --------------------------------------------------------

--
-- Table structure for table `whatsapp_templates`
--

CREATE TABLE `whatsapp_templates` (
  `id` int(11) NOT NULL,
  `template_name` varchar(100) NOT NULL,
  `message_content` text NOT NULL,
  `is_birthday` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `whatsapp_templates`
--

INSERT INTO `whatsapp_templates` (`id`, `template_name`, `message_content`, `is_birthday`, `created_at`) VALUES
(1, 'birthday', 'Happy Birthday \r\nYou are the best', 0, '2025-03-04 00:01:54');

-- --------------------------------------------------------

--
-- Structure for view `email_tracking_stats`
--
DROP TABLE IF EXISTS `email_tracking_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`u271750246_Church`@`127.0.0.1` SQL SECURITY DEFINER VIEW `email_tracking_stats`  AS SELECT `email_tracking`.`email_type` AS `email_type`, `email_templates`.`template_name` AS `template_name`, count(distinct `email_tracking`.`id`) AS `emails_sent`, count(distinct case when `email_tracking`.`opened_at` is not null then `email_tracking`.`id` end) AS `emails_opened`, round(count(distinct case when `email_tracking`.`opened_at` is not null then `email_tracking`.`id` end) / count(distinct `email_tracking`.`id`) * 100,2) AS `open_rate`, avg(`email_tracking`.`opened_count`) AS `avg_opens_per_email` FROM (`email_tracking` left join `email_templates` on(`email_templates`.`template_name` like concat('%',`email_tracking`.`email_type`,'%'))) GROUP BY `email_tracking`.`email_type`, `email_templates`.`template_name` ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- Indexes for table `automated_emails_settings`
--
ALTER TABLE `automated_emails_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `contacts`
--
ALTER TABLE `contacts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_email` (`email`);

--
-- Indexes for table `contact_email_logs`
--
ALTER TABLE `contact_email_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `contact_id` (`recipient_id`),
  ADD KEY `template_id` (`template_id`);

--
-- Indexes for table `contact_groups`
--
ALTER TABLE `contact_groups`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_name` (`name`);

--
-- Indexes for table `contact_group_members`
--
ALTER TABLE `contact_group_members`
  ADD PRIMARY KEY (`contact_id`,`group_id`);

--
-- Indexes for table `email_logs`
--
ALTER TABLE `email_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `template_id` (`template_id`);

--
-- Indexes for table `email_settings`
--
ALTER TABLE `email_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `email_templates`
--
ALTER TABLE `email_templates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `email_tracking`
--
ALTER TABLE `email_tracking`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_tracking_id` (`tracking_id`),
  ADD KEY `idx_member_email_type` (`member_id`,`email_type`);

--
-- Indexes for table `members`
--
ALTER TABLE `members`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `whatsapp_logs`
--
ALTER TABLE `whatsapp_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `template_id` (`template_id`);

--
-- Indexes for table `whatsapp_settings`
--
ALTER TABLE `whatsapp_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `whatsapp_templates`
--
ALTER TABLE `whatsapp_templates`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activity_logs`
--
ALTER TABLE `activity_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `automated_emails_settings`
--
ALTER TABLE `automated_emails_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `contacts`
--
ALTER TABLE `contacts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `contact_email_logs`
--
ALTER TABLE `contact_email_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `contact_groups`
--
ALTER TABLE `contact_groups`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `email_logs`
--
ALTER TABLE `email_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `email_settings`
--
ALTER TABLE `email_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=58;

--
-- AUTO_INCREMENT for table `email_templates`
--
ALTER TABLE `email_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `email_tracking`
--
ALTER TABLE `email_tracking`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT for table `members`
--
ALTER TABLE `members`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=165;

--
-- AUTO_INCREMENT for table `whatsapp_logs`
--
ALTER TABLE `whatsapp_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `whatsapp_templates`
--
ALTER TABLE `whatsapp_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `contact_email_logs`
--
ALTER TABLE `contact_email_logs`
  ADD CONSTRAINT `contact_email_logs_ibfk_1` FOREIGN KEY (`recipient_id`) REFERENCES `contacts` (`id`),
  ADD CONSTRAINT `contact_email_logs_ibfk_2` FOREIGN KEY (`template_id`) REFERENCES `email_templates` (`id`);

--
-- Constraints for table `email_logs`
--
ALTER TABLE `email_logs`
  ADD CONSTRAINT `email_logs_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `email_logs_ibfk_2` FOREIGN KEY (`template_id`) REFERENCES `email_templates` (`id`);

--
-- Constraints for table `email_tracking`
--
ALTER TABLE `email_tracking`
  ADD CONSTRAINT `email_tracking_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
