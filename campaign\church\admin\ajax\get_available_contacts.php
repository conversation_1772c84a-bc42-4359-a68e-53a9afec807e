<?php
session_start();
require_once '../../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

if (!isset($_GET['group_id'])) {
    echo json_encode(['success' => false, 'error' => 'Group ID is required']);
    exit();
}

// Get pagination and sorting parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 40; // Default to 40 items per page
$sort_field = isset($_GET['sort']) ? $_GET['sort'] : 'name';
$sort_order = isset($_GET['order']) ? $_GET['order'] : 'ASC';
$get_all = isset($_GET['get_all']) && $_GET['get_all'] == 'true';

// Validate parameters
if ($page < 1) $page = 1;
if ($limit < 10) $limit = 10;
if ($limit > 1000) $limit = 1000; // Allow up to 1000 items

// Validate sort field
$allowed_sort_fields = ['name', 'email', 'created_at'];
if (!in_array($sort_field, $allowed_sort_fields)) {
    $sort_field = 'name';
}

// Validate sort order
$sort_order = strtoupper($sort_order) === 'ASC' ? 'ASC' : 'DESC';

// Calculate offset
$offset = ($page - 1) * $limit;

try {
    // Get total count
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM contacts c 
                          WHERE c.id NOT IN (
                              SELECT contact_id FROM contact_group_members WHERE group_id = ?
                          )");
    $stmt->execute([$_GET['group_id']]);
    $total_contacts = $stmt->fetchColumn();
    $total_pages = ceil($total_contacts / $limit);
    
    // Get results with sorting
    if ($get_all) {
        // For bulk operations, get all contacts without pagination
        $stmt = $pdo->prepare("SELECT c.id, c.name, c.email FROM contacts c 
                              WHERE c.id NOT IN (
                                  SELECT contact_id FROM contact_group_members WHERE group_id = ?
                              ) 
                              ORDER BY c.$sort_field $sort_order");
        $stmt->execute([$_GET['group_id']]);
        $all_contacts = $stmt->fetchAll();
        
        // For paginated display
        $stmt = $pdo->prepare("SELECT c.* FROM contacts c 
                              WHERE c.id NOT IN (
                                  SELECT contact_id FROM contact_group_members WHERE group_id = ?
                              ) 
                              ORDER BY c.$sort_field $sort_order 
                              LIMIT ? OFFSET ?");
        $stmt->execute([$_GET['group_id'], $limit, $offset]);
        $contacts = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'contacts' => $contacts,
            'all_contacts' => $all_contacts,
            'pagination' => [
                'total' => $total_contacts,
                'pages' => $total_pages,
                'current' => $page,
                'limit' => $limit,
                'start' => $offset + 1,
                'end' => min($offset + $limit, $total_contacts)
            ]
        ]);
    } else {
        // Get paginated results with sorting
        $stmt = $pdo->prepare("SELECT c.* FROM contacts c 
                              WHERE c.id NOT IN (
                                  SELECT contact_id FROM contact_group_members WHERE group_id = ?
                              ) 
                              ORDER BY c.$sort_field $sort_order 
                              LIMIT ? OFFSET ?");
        $stmt->execute([$_GET['group_id'], $limit, $offset]);
        $contacts = $stmt->fetchAll();
        
        // Calculate pagination ranges
        $start_number = $offset + 1;
        $end_number = min($offset + $limit, $total_contacts);
        
        echo json_encode([
            'success' => true,
            'contacts' => $contacts,
            'pagination' => [
                'total' => $total_contacts,
                'pages' => $total_pages,
                'current' => $page,
                'limit' => $limit,
                'start' => $start_number,
                'end' => $end_number
            ]
        ]);
    }
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'error' => 'Error fetching available contacts: ' . $e->getMessage()]);
}
?> 