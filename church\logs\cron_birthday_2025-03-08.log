[2025-03-08 09:30:19] Request received: format=json, cron_key=fac***
[2025-03-08 09:30:19] Request details: {"LSPHP_ProcessGroup":"on","PATH":"\/usr\/local\/bin:\/bin:\/usr\/bin","HTTP_ACCEPT":"*\/*","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"_ga=GA1.2.925245497.1741034631; _ga_9Q6H0QETRF=GS1.2.1741034631.1.1.1741034642.49.0.0; PHPSESSID=407a8i88161nrpss748611bgb0","HTTP_HOST":"freedomassemblydb.online","HTTP_REFERER":"https:\/\/freedomassemblydb.online\/church\/admin\/index.php","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_SEC_CH_UA":"\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"cors","HTTP_SEC_FETCH_DEST":"empty","HTTP_PRIORITY":"u=1, i","DOCUMENT_ROOT":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html","REMOTE_ADDR":"*************","REMOTE_PORT":"10782","SERVER_ADDR":"************","SERVER_NAME":"freedomassemblydb.online","SERVER_ADMIN":"","SERVER_PORT":"443","REQUEST_SCHEME":"https","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTPS":"on","CRAWLER_USLEEP":"1000","CRAWLER_LOAD_LIMIT_ENFORCE":"25","H_PLATFORM":"Hostinger","H_TYPE":"shared","H_CANARY":"false","H_STAGING":"false","X_SPDY":"HTTP3","SSL_PROTOCOL":"QUIC","SSL_CIPHER":"TLS_AES_128_GCM_SHA256","SSL_CIPHER_USEKEYSIZE":"128","SSL_CIPHER_ALGKEYSIZE":"128","SCRIPT_FILENAME":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html\/church\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_URI":"https:\/\/freedomassemblydb.online\/church\/birthday_reminders.php","SCRIPT_URL":"\/church\/birthday_reminders.php","SCRIPT_NAME":"\/church\/birthday_reminders.php","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_SOFTWARE":"LiteSpeed","REQUEST_METHOD":"GET","X-LSCACHE":"on,crawler,esi,combine","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741426219.827689,"REQUEST_TIME":1741426219}
[2025-03-08 09:30:19] Access granted: via cron key
[2025-03-08 09:30:19] Starting birthday reminders cron job
[2025-03-08 09:30:19] PHP Error [2]: Undefined variable $pdo in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 09:30:19] Uncaught Exception: Call to a member function prepare() on null in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 09:30:35] Request received: format=json, cron_key=fac***
[2025-03-08 09:30:35] Request details: {"LSPHP_ProcessGroup":"on","PATH":"\/usr\/local\/bin:\/bin:\/usr\/bin","HTTP_ACCEPT":"*\/*","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"_ga=GA1.2.925245497.1741034631; _ga_9Q6H0QETRF=GS1.2.1741034631.1.1.1741034642.49.0.0; PHPSESSID=407a8i88161nrpss748611bgb0","HTTP_HOST":"freedomassemblydb.online","HTTP_REFERER":"https:\/\/freedomassemblydb.online\/church\/admin\/index.php","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_SEC_CH_UA":"\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"cors","HTTP_SEC_FETCH_DEST":"empty","HTTP_PRIORITY":"u=1, i","DOCUMENT_ROOT":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html","REMOTE_ADDR":"*************","REMOTE_PORT":"10782","SERVER_ADDR":"************","SERVER_NAME":"freedomassemblydb.online","SERVER_ADMIN":"","SERVER_PORT":"443","REQUEST_SCHEME":"https","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTPS":"on","CRAWLER_USLEEP":"1000","CRAWLER_LOAD_LIMIT_ENFORCE":"25","H_PLATFORM":"Hostinger","H_TYPE":"shared","H_CANARY":"false","H_STAGING":"false","X_SPDY":"HTTP3","SSL_PROTOCOL":"QUIC","SSL_CIPHER":"TLS_AES_128_GCM_SHA256","SSL_CIPHER_USEKEYSIZE":"128","SSL_CIPHER_ALGKEYSIZE":"128","SCRIPT_FILENAME":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html\/church\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_URI":"https:\/\/freedomassemblydb.online\/church\/birthday_reminders.php","SCRIPT_URL":"\/church\/birthday_reminders.php","SCRIPT_NAME":"\/church\/birthday_reminders.php","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_SOFTWARE":"LiteSpeed","REQUEST_METHOD":"GET","X-LSCACHE":"on,crawler,esi,combine","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741426235.042278,"REQUEST_TIME":1741426235}
[2025-03-08 09:30:35] Access granted: via cron key
[2025-03-08 09:30:35] Starting birthday reminders cron job
[2025-03-08 09:30:35] PHP Error [2]: Undefined variable $pdo in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 09:30:35] Uncaught Exception: Call to a member function prepare() on null in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 09:38:25] Request received: format=json, cron_key=fac***
[2025-03-08 09:38:25] Request details: {"LSPHP_ProcessGroup":"on","PATH":"\/usr\/local\/bin:\/bin:\/usr\/bin","HTTP_ACCEPT":"*\/*","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"_ga=GA1.2.925245497.1741034631; _ga_9Q6H0QETRF=GS1.2.1741034631.1.1.1741034642.49.0.0; PHPSESSID=407a8i88161nrpss748611bgb0","HTTP_HOST":"freedomassemblydb.online","HTTP_REFERER":"https:\/\/freedomassemblydb.online\/church\/admin\/index.php","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_SEC_CH_UA":"\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"cors","HTTP_SEC_FETCH_DEST":"empty","HTTP_PRIORITY":"u=1, i","DOCUMENT_ROOT":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html","REMOTE_ADDR":"*************","REMOTE_PORT":"10788","SERVER_ADDR":"************","SERVER_NAME":"freedomassemblydb.online","SERVER_ADMIN":"","SERVER_PORT":"443","REQUEST_SCHEME":"https","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTPS":"on","CRAWLER_USLEEP":"1000","CRAWLER_LOAD_LIMIT_ENFORCE":"25","H_PLATFORM":"Hostinger","H_TYPE":"shared","H_CANARY":"false","H_STAGING":"false","X_SPDY":"HTTP3","SSL_PROTOCOL":"QUIC","SSL_CIPHER":"TLS_AES_128_GCM_SHA256","SSL_CIPHER_USEKEYSIZE":"128","SSL_CIPHER_ALGKEYSIZE":"128","SCRIPT_FILENAME":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html\/church\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_URI":"https:\/\/freedomassemblydb.online\/church\/birthday_reminders.php","SCRIPT_URL":"\/church\/birthday_reminders.php","SCRIPT_NAME":"\/church\/birthday_reminders.php","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_SOFTWARE":"LiteSpeed","REQUEST_METHOD":"GET","X-LSCACHE":"on,crawler,esi,combine","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741426705.353791,"REQUEST_TIME":1741426705}
[2025-03-08 09:38:25] Access granted: via cron key
[2025-03-08 09:38:25] Starting birthday reminders cron job
[2025-03-08 09:38:25] PHP Error [2]: Undefined variable $pdo in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 09:38:25] Uncaught Exception: Call to a member function prepare() on null in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 09:38:29] Request received: format=json, cron_key=fac***
[2025-03-08 09:38:29] Request details: {"LSPHP_ProcessGroup":"on","PATH":"\/usr\/local\/bin:\/bin:\/usr\/bin","HTTP_ACCEPT":"*\/*","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"_ga=GA1.2.925245497.1741034631; _ga_9Q6H0QETRF=GS1.2.1741034631.1.1.1741034642.49.0.0; PHPSESSID=407a8i88161nrpss748611bgb0","HTTP_HOST":"freedomassemblydb.online","HTTP_REFERER":"https:\/\/freedomassemblydb.online\/church\/admin\/index.php","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_SEC_CH_UA":"\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"cors","HTTP_SEC_FETCH_DEST":"empty","HTTP_PRIORITY":"u=1, i","DOCUMENT_ROOT":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html","REMOTE_ADDR":"*************","REMOTE_PORT":"10788","SERVER_ADDR":"************","SERVER_NAME":"freedomassemblydb.online","SERVER_ADMIN":"","SERVER_PORT":"443","REQUEST_SCHEME":"https","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTPS":"on","CRAWLER_USLEEP":"1000","CRAWLER_LOAD_LIMIT_ENFORCE":"25","H_PLATFORM":"Hostinger","H_TYPE":"shared","H_CANARY":"false","H_STAGING":"false","X_SPDY":"HTTP3","SSL_PROTOCOL":"QUIC","SSL_CIPHER":"TLS_AES_128_GCM_SHA256","SSL_CIPHER_USEKEYSIZE":"128","SSL_CIPHER_ALGKEYSIZE":"128","SCRIPT_FILENAME":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html\/church\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_URI":"https:\/\/freedomassemblydb.online\/church\/birthday_reminders.php","SCRIPT_URL":"\/church\/birthday_reminders.php","SCRIPT_NAME":"\/church\/birthday_reminders.php","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_SOFTWARE":"LiteSpeed","REQUEST_METHOD":"GET","X-LSCACHE":"on,crawler,esi,combine","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741426709.634481,"REQUEST_TIME":1741426709}
[2025-03-08 09:38:29] Access granted: via cron key
[2025-03-08 09:38:29] Starting birthday reminders cron job
[2025-03-08 09:38:29] PHP Error [2]: Undefined variable $pdo in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 09:38:29] Uncaught Exception: Call to a member function prepare() on null in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 09:46:44] Request received: format=json, cron_key=fac***
[2025-03-08 09:46:44] Request details: {"LSPHP_ProcessGroup":"on","PATH":"\/usr\/local\/bin:\/bin:\/usr\/bin","HTTP_ACCEPT":"*\/*","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"_ga=GA1.2.925245497.1741034631; _ga_9Q6H0QETRF=GS1.2.1741034631.1.1.1741034642.49.0.0; PHPSESSID=407a8i88161nrpss748611bgb0","HTTP_HOST":"freedomassemblydb.online","HTTP_REFERER":"https:\/\/freedomassemblydb.online\/church\/admin\/index.php","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_SEC_CH_UA":"\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"cors","HTTP_SEC_FETCH_DEST":"empty","HTTP_PRIORITY":"u=1, i","DOCUMENT_ROOT":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html","REMOTE_ADDR":"*************","REMOTE_PORT":"10907","SERVER_ADDR":"************","SERVER_NAME":"freedomassemblydb.online","SERVER_ADMIN":"","SERVER_PORT":"443","REQUEST_SCHEME":"https","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTPS":"on","CRAWLER_USLEEP":"1000","CRAWLER_LOAD_LIMIT_ENFORCE":"25","H_PLATFORM":"Hostinger","H_TYPE":"shared","H_CANARY":"false","H_STAGING":"false","X_SPDY":"HTTP3","SSL_PROTOCOL":"QUIC","SSL_CIPHER":"TLS_AES_128_GCM_SHA256","SSL_CIPHER_USEKEYSIZE":"128","SSL_CIPHER_ALGKEYSIZE":"128","SCRIPT_FILENAME":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html\/church\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_URI":"https:\/\/freedomassemblydb.online\/church\/birthday_reminders.php","SCRIPT_URL":"\/church\/birthday_reminders.php","SCRIPT_NAME":"\/church\/birthday_reminders.php","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_SOFTWARE":"LiteSpeed","REQUEST_METHOD":"GET","X-LSCACHE":"on,crawler,esi,combine","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741427204.717584,"REQUEST_TIME":1741427204}
[2025-03-08 09:46:44] Access granted: via cron key
[2025-03-08 09:46:44] Starting birthday reminders cron job
[2025-03-08 09:46:44] PHP Error [2]: Undefined variable $pdo in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 09:46:44] Uncaught Exception: Call to a member function prepare() on null in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 09:54:21] Request received: format=json, cron_key=fac***
[2025-03-08 09:54:21] Request details: {"LSPHP_ProcessGroup":"on","PATH":"\/usr\/local\/bin:\/bin:\/usr\/bin","HTTP_ACCEPT":"*\/*","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"_ga=GA1.2.925245497.1741034631; _ga_9Q6H0QETRF=GS1.2.1741034631.1.1.1741034642.49.0.0; PHPSESSID=407a8i88161nrpss748611bgb0","HTTP_HOST":"freedomassemblydb.online","HTTP_REFERER":"https:\/\/freedomassemblydb.online\/church\/admin\/index.php","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_SEC_CH_UA":"\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"cors","HTTP_SEC_FETCH_DEST":"empty","HTTP_PRIORITY":"u=1, i","DOCUMENT_ROOT":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html","REMOTE_ADDR":"*************","REMOTE_PORT":"10920","SERVER_ADDR":"************","SERVER_NAME":"freedomassemblydb.online","SERVER_ADMIN":"","SERVER_PORT":"443","REQUEST_SCHEME":"https","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTPS":"on","CRAWLER_USLEEP":"1000","CRAWLER_LOAD_LIMIT_ENFORCE":"25","H_PLATFORM":"Hostinger","H_TYPE":"shared","H_CANARY":"false","H_STAGING":"false","X_SPDY":"HTTP3","SSL_PROTOCOL":"QUIC","SSL_CIPHER":"TLS_AES_128_GCM_SHA256","SSL_CIPHER_USEKEYSIZE":"128","SSL_CIPHER_ALGKEYSIZE":"128","SCRIPT_FILENAME":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html\/church\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_URI":"https:\/\/freedomassemblydb.online\/church\/birthday_reminders.php","SCRIPT_URL":"\/church\/birthday_reminders.php","SCRIPT_NAME":"\/church\/birthday_reminders.php","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_SOFTWARE":"LiteSpeed","REQUEST_METHOD":"GET","X-LSCACHE":"on,crawler,esi,combine","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741427661.522493,"REQUEST_TIME":1741427661}
[2025-03-08 09:54:21] Access granted: via cron key
[2025-03-08 09:54:21] Starting birthday reminders cron job
[2025-03-08 09:54:21] PHP Error [2]: Undefined variable $pdo in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 09:54:21] Uncaught Exception: Call to a member function prepare() on null in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 12:08:29] Request received: format=json, cron_key=fac***
[2025-03-08 12:08:29] Request details: {"LSPHP_ProcessGroup":"on","PATH":"\/usr\/local\/bin:\/bin:\/usr\/bin","HTTP_ACCEPT":"*\/*","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"_ga=GA1.2.925245497.1741034631; _ga_9Q6H0QETRF=GS1.2.1741034631.1.1.1741034642.49.0.0; PHPSESSID=407a8i88161nrpss748611bgb0","HTTP_HOST":"freedomassemblydb.online","HTTP_REFERER":"https:\/\/freedomassemblydb.online\/church\/admin\/index.php","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_SEC_CH_UA":"\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"cors","HTTP_SEC_FETCH_DEST":"empty","HTTP_PRIORITY":"u=1, i","DOCUMENT_ROOT":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html","REMOTE_ADDR":"*************","REMOTE_PORT":"10782","SERVER_ADDR":"************","SERVER_NAME":"freedomassemblydb.online","SERVER_ADMIN":"","SERVER_PORT":"443","REQUEST_SCHEME":"https","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTPS":"on","CRAWLER_USLEEP":"1000","CRAWLER_LOAD_LIMIT_ENFORCE":"25","H_PLATFORM":"Hostinger","H_TYPE":"shared","H_CANARY":"false","H_STAGING":"false","X_SPDY":"HTTP3","SSL_PROTOCOL":"QUIC","SSL_CIPHER":"TLS_AES_128_GCM_SHA256","SSL_CIPHER_USEKEYSIZE":"128","SSL_CIPHER_ALGKEYSIZE":"128","SCRIPT_FILENAME":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html\/church\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_URI":"https:\/\/freedomassemblydb.online\/church\/birthday_reminders.php","SCRIPT_URL":"\/church\/birthday_reminders.php","SCRIPT_NAME":"\/church\/birthday_reminders.php","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_SOFTWARE":"LiteSpeed","REQUEST_METHOD":"GET","X-LSCACHE":"on,crawler,esi,combine","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741435709.250817,"REQUEST_TIME":1741435709}
[2025-03-08 12:08:29] Access granted: via cron key
[2025-03-08 12:08:29] Starting birthday reminders cron job
[2025-03-08 12:08:29] PHP Error [2]: Undefined variable $pdo in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 12:08:29] Uncaught Exception: Call to a member function prepare() on null in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 12:08:37] Request received: format=json, cron_key=fac***
[2025-03-08 12:08:37] Request details: {"LSPHP_ProcessGroup":"on","PATH":"\/usr\/local\/bin:\/bin:\/usr\/bin","HTTP_ACCEPT":"*\/*","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"_ga=GA1.2.925245497.1741034631; _ga_9Q6H0QETRF=GS1.2.1741034631.1.1.1741034642.49.0.0; PHPSESSID=407a8i88161nrpss748611bgb0","HTTP_HOST":"freedomassemblydb.online","HTTP_REFERER":"https:\/\/freedomassemblydb.online\/church\/admin\/index.php","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_SEC_CH_UA":"\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"cors","HTTP_SEC_FETCH_DEST":"empty","HTTP_PRIORITY":"u=1, i","DOCUMENT_ROOT":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html","REMOTE_ADDR":"*************","REMOTE_PORT":"10782","SERVER_ADDR":"************","SERVER_NAME":"freedomassemblydb.online","SERVER_ADMIN":"","SERVER_PORT":"443","REQUEST_SCHEME":"https","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTPS":"on","CRAWLER_USLEEP":"1000","CRAWLER_LOAD_LIMIT_ENFORCE":"25","H_PLATFORM":"Hostinger","H_TYPE":"shared","H_CANARY":"false","H_STAGING":"false","X_SPDY":"HTTP3","SSL_PROTOCOL":"QUIC","SSL_CIPHER":"TLS_AES_128_GCM_SHA256","SSL_CIPHER_USEKEYSIZE":"128","SSL_CIPHER_ALGKEYSIZE":"128","SCRIPT_FILENAME":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html\/church\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_URI":"https:\/\/freedomassemblydb.online\/church\/birthday_reminders.php","SCRIPT_URL":"\/church\/birthday_reminders.php","SCRIPT_NAME":"\/church\/birthday_reminders.php","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_SOFTWARE":"LiteSpeed","REQUEST_METHOD":"GET","X-LSCACHE":"on,crawler,esi,combine","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741435717.03615,"REQUEST_TIME":1741435717}
[2025-03-08 12:08:37] Access granted: via cron key
[2025-03-08 12:08:37] Starting birthday reminders cron job
[2025-03-08 12:08:37] PHP Error [2]: Undefined variable $pdo in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 12:08:37] Uncaught Exception: Call to a member function prepare() on null in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 12:08:44] Request received: format=json, cron_key=fac***
[2025-03-08 12:08:44] Request details: {"LSPHP_ProcessGroup":"on","PATH":"\/usr\/local\/bin:\/bin:\/usr\/bin","HTTP_ACCEPT":"*\/*","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"_ga=GA1.2.925245497.1741034631; _ga_9Q6H0QETRF=GS1.2.1741034631.1.1.1741034642.49.0.0; PHPSESSID=407a8i88161nrpss748611bgb0","HTTP_HOST":"freedomassemblydb.online","HTTP_REFERER":"https:\/\/freedomassemblydb.online\/church\/admin\/index.php","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_SEC_CH_UA":"\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"cors","HTTP_SEC_FETCH_DEST":"empty","HTTP_PRIORITY":"u=1, i","DOCUMENT_ROOT":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html","REMOTE_ADDR":"*************","REMOTE_PORT":"10782","SERVER_ADDR":"************","SERVER_NAME":"freedomassemblydb.online","SERVER_ADMIN":"","SERVER_PORT":"443","REQUEST_SCHEME":"https","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTPS":"on","CRAWLER_USLEEP":"1000","CRAWLER_LOAD_LIMIT_ENFORCE":"25","H_PLATFORM":"Hostinger","H_TYPE":"shared","H_CANARY":"false","H_STAGING":"false","X_SPDY":"HTTP3","SSL_PROTOCOL":"QUIC","SSL_CIPHER":"TLS_AES_128_GCM_SHA256","SSL_CIPHER_USEKEYSIZE":"128","SSL_CIPHER_ALGKEYSIZE":"128","SCRIPT_FILENAME":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html\/church\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_URI":"https:\/\/freedomassemblydb.online\/church\/birthday_reminders.php","SCRIPT_URL":"\/church\/birthday_reminders.php","SCRIPT_NAME":"\/church\/birthday_reminders.php","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_SOFTWARE":"LiteSpeed","REQUEST_METHOD":"GET","X-LSCACHE":"on,crawler,esi,combine","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741435724.819567,"REQUEST_TIME":1741435724}
[2025-03-08 12:08:44] Access granted: via cron key
[2025-03-08 12:08:44] Starting birthday reminders cron job
[2025-03-08 12:08:44] PHP Error [2]: Undefined variable $pdo in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 12:08:44] Uncaught Exception: Call to a member function prepare() on null in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 12:29:23] Request received: format=json, cron_key=fac***
[2025-03-08 12:29:23] Request details: {"LSPHP_ProcessGroup":"on","PATH":"\/usr\/local\/bin:\/bin:\/usr\/bin","HTTP_ACCEPT":"*\/*","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"_ga=GA1.2.925245497.1741034631; _ga_9Q6H0QETRF=GS1.2.1741034631.1.1.1741034642.49.0.0; PHPSESSID=407a8i88161nrpss748611bgb0","HTTP_HOST":"freedomassemblydb.online","HTTP_REFERER":"https:\/\/freedomassemblydb.online\/church\/admin\/index.php","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_SEC_CH_UA":"\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"cors","HTTP_SEC_FETCH_DEST":"empty","HTTP_PRIORITY":"u=1, i","DOCUMENT_ROOT":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html","REMOTE_ADDR":"*************","REMOTE_PORT":"53749","SERVER_ADDR":"************","SERVER_NAME":"freedomassemblydb.online","SERVER_ADMIN":"","SERVER_PORT":"443","REQUEST_SCHEME":"https","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTPS":"on","CRAWLER_USLEEP":"1000","CRAWLER_LOAD_LIMIT_ENFORCE":"25","H_PLATFORM":"Hostinger","H_TYPE":"shared","H_CANARY":"false","H_STAGING":"false","X_SPDY":"HTTP3","SSL_PROTOCOL":"QUIC","SSL_CIPHER":"TLS_AES_128_GCM_SHA256","SSL_CIPHER_USEKEYSIZE":"128","SSL_CIPHER_ALGKEYSIZE":"128","SCRIPT_FILENAME":"\/home\/<USER>\/domains\/freedomassemblydb.online\/public_html\/church\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_URI":"https:\/\/freedomassemblydb.online\/church\/birthday_reminders.php","SCRIPT_URL":"\/church\/birthday_reminders.php","SCRIPT_NAME":"\/church\/birthday_reminders.php","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_SOFTWARE":"LiteSpeed","REQUEST_METHOD":"GET","X-LSCACHE":"on,crawler,esi,combine","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741436963.081983,"REQUEST_TIME":1741436963}
[2025-03-08 12:29:23] Access granted: via cron key
[2025-03-08 12:29:23] Starting birthday reminders cron job
[2025-03-08 12:29:23] PHP Error [2]: Undefined variable $pdo in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 12:29:23] Uncaught Exception: Call to a member function prepare() on null in /home/<USER>/domains/freedomassemblydb.online/public_html/church/birthday_reminders.php on line 108
[2025-03-08 14:28:14] PHP Error [2]: Constant SECURE_CRON_KEY already defined in C:\xampp\htdocs\church\birthday_reminders.php on line 64
[2025-03-08 14:28:14] Request received: format=json, cron_key=fac***
[2025-03-08 14:28:14] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/church\/birthday_reminders.php","REMOTE_PORT":"57862","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/church\/birthday_reminders.php","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741440494.627838,"REQUEST_TIME":1741440494}
[2025-03-08 14:28:14] Access granted: via cron key
[2025-03-08 14:28:14] Starting birthday reminders cron job
[2025-03-08 14:31:47] PHP Error [2]: Constant SECURE_CRON_KEY already defined in C:\xampp\htdocs\church\birthday_reminders.php on line 64
[2025-03-08 14:31:47] Request received: format=json, cron_key=fac***
[2025-03-08 14:31:47] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/church\/birthday_reminders.php","REMOTE_PORT":"57931","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/church\/birthday_reminders.php","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741440707.364348,"REQUEST_TIME":1741440707}
[2025-03-08 14:31:47] Access granted: via cron key
[2025-03-08 14:31:47] Starting birthday reminders cron job
[2025-03-08 15:54:29] PHP Error [2]: Constant SECURE_CRON_KEY already defined in C:\xampp\htdocs\church\birthday_reminders.php on line 64
[2025-03-08 15:54:29] Request received: format=json, cron_key=fac***
[2025-03-08 15:54:29] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/church\/birthday_reminders.php","REMOTE_PORT":"60370","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/church\/birthday_reminders.php","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741445669.156824,"REQUEST_TIME":1741445669}
[2025-03-08 15:54:29] Access granted: via cron key
[2025-03-08 15:54:29] Starting birthday reminders cron job
[2025-03-08 15:54:57] PHP Error [2]: Constant SECURE_CRON_KEY already defined in C:\xampp\htdocs\church\birthday_reminders.php on line 64
[2025-03-08 15:54:57] Request received: format=json, cron_key=fac***
[2025-03-08 15:54:57] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/church\/birthday_reminders.php","REMOTE_PORT":"60420","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/church\/birthday_reminders.php","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741445697.651895,"REQUEST_TIME":1741445697}
[2025-03-08 15:54:57] Access granted: via cron key
[2025-03-08 15:54:57] Starting birthday reminders cron job
[2025-03-08 16:03:31] PHP Error [2]: Constant SECURE_CRON_KEY already defined in C:\xampp\htdocs\church\birthday_reminders.php on line 64
[2025-03-08 16:03:31] Request received: format=json, cron_key=fac***
[2025-03-08 16:03:31] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/church\/birthday_reminders.php","REMOTE_PORT":"60962","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/church\/birthday_reminders.php","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741446211.565666,"REQUEST_TIME":1741446211}
[2025-03-08 16:03:31] Access granted: via cron key
[2025-03-08 16:03:31] Starting birthday reminders cron job
[2025-03-08 16:03:33] PHP Error [2]: Constant SECURE_CRON_KEY already defined in C:\xampp\htdocs\church\birthday_reminders.php on line 64
[2025-03-08 16:03:33] Request received: format=json, cron_key=fac***
[2025-03-08 16:03:33] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/church\/birthday_reminders.php","REMOTE_PORT":"60968","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/church\/birthday_reminders.php","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741446213.102423,"REQUEST_TIME":1741446213}
[2025-03-08 16:03:33] Access granted: via cron key
[2025-03-08 16:03:33] Starting birthday reminders cron job
[2025-03-08 16:07:45] PHP Error [2]: Constant SECURE_CRON_KEY already defined in C:\xampp\htdocs\church\birthday_reminders.php on line 64
[2025-03-08 16:07:45] Request received: format=json, cron_key=fac***
[2025-03-08 16:07:45] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\xampp\\php;C:\\Program Files\\dotnet\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\anaconda3;C:\\Users\\<USER>\\anaconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\anaconda3\\Library\\bin;C:\\Users\\<USER>\\anaconda3\\Scripts;C;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/church\/birthday_reminders.php","REMOTE_PORT":"61051","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/church\/birthday_reminders.php","PHP_SELF":"\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1741446465.434151,"REQUEST_TIME":1741446465}
[2025-03-08 16:07:45] Access granted: via cron key
[2025-03-08 16:07:45] Starting birthday reminders cron job
