<?php
// <PERSON><PERSON><PERSON> to verify mobile-friendly raffle template
require_once __DIR__ . '/../config.php';

try {
    echo "<h2>Verifying Mobile-Friendly Template</h2>";
    
    // Get the mobile-friendly template
    $stmt = $pdo->prepare("SELECT id, template_name, subject FROM email_templates WHERE template_name = 'The Big Raffle Winner (Mobile-Friendly)' LIMIT 1");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "<p>❌ Mobile-friendly template not found!</p>";
    } else {
        echo "<p>✅ Found mobile-friendly template ID: " . $template['id'] . " - " . htmlspecialchars($template['template_name']) . "</p>";
    }
    
    // Check for raffle_reply_email setting
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key = 'raffle_reply_email' LIMIT 1");
    $stmt->execute();
    $setting = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$setting) {
        echo "<p>❌ raffle_reply_email setting not found!</p>";
    } else {
        echo "<p>✅ Found raffle_reply_email setting: " . htmlspecialchars($setting['setting_value']) . "</p>";
    }
    
    // List all raffle templates
    $stmt = $pdo->prepare("SELECT id, template_name, created_at FROM email_templates WHERE template_name LIKE '%Raffle%' ORDER BY id DESC");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>All Raffle Templates:</h3>";
    if (count($templates) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Template Name</th><th>Created At</th></tr>";
        
        foreach ($templates as $tpl) {
            echo "<tr>";
            echo "<td>" . $tpl['id'] . "</td>";
            echo "<td>" . htmlspecialchars($tpl['template_name']) . "</td>";
            echo "<td>" . $tpl['created_at'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No raffle templates found!</p>";
    }
    
    // Check if the database has the reply-to email setting
    echo "<h3>Email Settings:</h3>";
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE '%email%' OR setting_key LIKE '%reply%'");
    $stmt->execute();
    $email_settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($email_settings) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Setting Key</th><th>Setting Value</th></tr>";
        
        foreach ($email_settings as $setting) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($setting['setting_key']) . "</td>";
            echo "<td>" . htmlspecialchars($setting['setting_value']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No email settings found!</p>";
    }
    
    echo "<h3>Summary:</h3>";
    if ($template && $setting) {
        echo "<p style='color:green;'>✅ Setup is complete! The mobile-friendly template and reply email settings are ready.</p>";
        echo "<p>You can access the template from the admin panel at <a href='../admin/email_templates.php'>Email Templates</a>.</p>";
        echo "<p>Key improvements in this template:</p>";
        echo "<ul>";
        echo "<li>Hardcoded email address: " . htmlspecialchars($setting['setting_value']) . "</li>";
        echo "<li>Extra line breaks between each item for better mobile formatting</li>";
        echo "<li>Fixed lottery numbers (14, 28, 53, 67, 92, 9)</li>";
        echo "</ul>";
    } else {
        echo "<p style='color:red;'>❌ Setup is incomplete. Please check the issues above.</p>";
    }
    
} catch (PDOException $e) {
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
} 