<?php
/**
 * URL Updater Tool
 * 
 * This script updates hardcoded URLs in PHP files to use environment constants.
 * It helps maintain consistency and allows for easier environment switching.
 */

// Configuration
$baseDir = dirname(__DIR__); // Parent directory of tools folder
$domains = [
    'freedomassemblydb.online',
    'freedomassemblychurch.org',
    'localhost/church'
];

// Path mappings: pattern => replacement constant
$mappings = [
    // Base URLs
    'https?:\/\/(?:www\.)?freedomassemblydb\.online\/church\/?(?!\S)' => 'SITE_URL',
    'https?:\/\/(?:www\.)?freedomassemblydb\.online\/church\/admin\/?(?!\S)' => 'ADMIN_URL',
    
    // Image paths
    'https?:\/\/(?:www\.)?freedomassemblydb\.online\/church\/assets\/images\/' => 'IMAGES_URL . \'/\'',
    'https?:\/\/(?:www\.)?freedomassemblydb\.online\/church\/assets\/' => 'ASSETS_URL . \'/\'',
    'https?:\/\/(?:www\.)?freedomassemblydb\.online\/church\/uploads\/' => 'UPLOADS_URL . \'/\'',
    
    // Church logo specific paths
    'https?:\/\/(?:www\.)?freedomassemblydb\.online\/church\/assets\/images\/banner\.jpg' => 'CHURCH_LOGO',
    'https?:\/\/(?:www\.)?freedomassemblychurch\.org\/wp-content\/uploads\/2023\/10\/church-logo\.png' => 'CHURCH_LOGO',
    
    // Email addresses
    'church@freedomassemblydb\.online' => 'CHURCH_EMAIL',
    'admin@freedomassemblydb\.online' => 'ADMIN_EMAIL',
];

// Dry run by default (set to false to actually make changes)
$dryRun = true;
$verboseOutput = true;

// Process command line arguments
foreach ($argv as $arg) {
    if ($arg === '--update') {
        $dryRun = false;
    }
    if ($arg === '--quiet') {
        $verboseOutput = false;
    }
}

// Log function
function logMessage($message, $level = 'INFO') {
    global $verboseOutput;
    if ($verboseOutput || $level === 'ERROR') {
        echo "[$level] $message\n";
    }
}

// Update files in a directory
function updateDirectory($dir, $mappings, $dryRun = true) {
    $items = scandir($dir);
    $updatedFiles = 0;
    
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') {
            continue;
        }
        
        $path = $dir . DIRECTORY_SEPARATOR . $item;
        
        // Skip excluded directories
        if (is_dir($path) && in_array($item, ['vendor', 'node_modules', '.git', 'logs'])) {
            continue;
        }
        
        if (is_dir($path)) {
            $updatedFiles += updateDirectory($path, $mappings, $dryRun);
        } else {
            // Only process PHP files
            if (pathinfo($path, PATHINFO_EXTENSION) === 'php') {
                $updated = updateFile($path, $mappings, $dryRun);
                if ($updated) {
                    $updatedFiles++;
                }
            }
        }
    }
    
    return $updatedFiles;
}

// Update a single file
function updateFile($filePath, $mappings, $dryRun = true) {
    $originalContent = file_get_contents($filePath);
    $updatedContent = $originalContent;
    $changed = false;
    
    foreach ($mappings as $pattern => $replacement) {
        // Only process PHP code, not strings within PHP
        $updatedContent = preg_replace_callback(
            "/($pattern)/i",
            function($matches) use ($replacement, &$changed) {
                $changed = true;
                return "' . $replacement . '";
            },
            $updatedContent
        );
        
        // Handle different context: variables assigned directly
        $updatedContent = preg_replace_callback(
            "/(\\\$[a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*)\s*=\s*['\"]($pattern)['\"]/i",
            function($matches) use ($replacement, &$changed) {
                $changed = true;
                return "{$matches[1]} = $replacement";
            },
            $updatedContent
        );
    }
    
    if ($changed) {
        $relativePath = str_replace(dirname(__DIR__), '', $filePath);
        logMessage("Updated file: $relativePath");
        
        if (!$dryRun) {
            file_put_contents($filePath, $updatedContent);
        }
        return true;
    }
    
    return false;
}

// Start updating
logMessage("Starting URL update process" . ($dryRun ? " (DRY RUN)" : ""));

$startTime = microtime(true);
$updatedFiles = updateDirectory($baseDir, $mappings, $dryRun);
$elapsedTime = microtime(true) - $startTime;

logMessage("Process completed in " . number_format($elapsedTime, 2) . " seconds");
logMessage("Total files updated: $updatedFiles");

if ($dryRun) {
    logMessage("This was a dry run. Use --update flag to actually update files.");
} 