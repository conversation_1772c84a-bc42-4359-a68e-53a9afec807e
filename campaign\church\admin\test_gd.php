<?php
// Simple GD extension test
echo "<h2>GD Extension Test</h2>";

if (extension_loaded('gd')) {
    echo "<div style='color: green; font-weight: bold;'>✅ GD Extension is ENABLED!</div>";
    
    $gd_info = gd_info();
    echo "<h3>GD Information:</h3>";
    echo "<ul>";
    foreach ($gd_info as $key => $value) {
        if (is_bool($value)) {
            $value = $value ? 'Yes' : 'No';
        }
        echo "<li><strong>$key:</strong> $value</li>";
    }
    echo "</ul>";
    
    echo "<h3>Supported Image Formats:</h3>";
    echo "<ul>";
    if (imagetypes() & IMG_JPG) echo "<li>✅ JPEG</li>";
    if (imagetypes() & IMG_PNG) echo "<li>✅ PNG</li>";
    if (imagetypes() & IMG_GIF) echo "<li>✅ GIF</li>";
    if (imagetypes() & IMG_WEBP) echo "<li>✅ WebP</li>";
    echo "</ul>";
    
} else {
    echo "<div style='color: red; font-weight: bold;'>❌ GD Extension is NOT enabled</div>";
    echo "<p>Please follow the instructions to enable GD extension in your php.ini file.</p>";
}

echo "<br><a href='logo_management.php'>← Back to Logo Management</a>";
?>
