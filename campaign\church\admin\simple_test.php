<?php
session_start();
$_SESSION['admin_id'] = 1;
require_once '../config.php';

echo "<h2>Simple Bulk Delete Test</h2>";

// Check if the contact we tried to delete still exists
try {
    $stmt = $pdo->prepare("SELECT id, name, email FROM contacts WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $contact = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($contact) {
        echo "Contact still exists: ID {$contact['id']}, Name: {$contact['name']}, Email: {$contact['email']}<br>";
        
        // Try to delete it manually
        echo "<br>Attempting manual deletion...<br>";
        
        $pdo->beginTransaction();
        
        // Delete related records first
        $stmt = $pdo->prepare("DELETE FROM contact_email_logs WHERE recipient_id = ?");
        $stmt->execute([$contact['id']]);
        echo "Deleted email logs<br>";
        
        $stmt = $pdo->prepare("DELETE FROM email_tracking WHERE contact_id = ?");
        $stmt->execute([$contact['id']]);
        echo "Deleted email tracking<br>";
        
        $stmt = $pdo->prepare("DELETE FROM contact_group_members WHERE contact_id = ?");
        $stmt->execute([$contact['id']]);
        echo "Deleted group memberships<br>";
        
        // Delete the contact
        $stmt = $pdo->prepare("DELETE FROM contacts WHERE id = ?");
        $result = $stmt->execute([$contact['id']]);
        $rows = $stmt->rowCount();
        
        echo "Delete result: " . ($result ? "Success" : "Failed") . " (Rows affected: $rows)<br>";
        
        if ($result && $rows > 0) {
            $pdo->commit();
            echo "✅ Contact successfully deleted!<br>";
        } else {
            $pdo->rollBack();
            echo "❌ Contact deletion failed<br>";
        }
        
    } else {
        echo "Contact not found - it may have been deleted successfully<br>";
    }
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo "Error: " . $e->getMessage() . "<br>";
}

echo "<br><a href='contacts.php'>Back to Contacts</a>";
?>
