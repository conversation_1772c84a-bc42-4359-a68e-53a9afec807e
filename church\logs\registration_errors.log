[2025-03-02 16:38:08] Database error: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'email' Context: {"code":"23000","email":"<EMAIL>"} Server Info: {"PHP_VERSION":"8.2.12","SERVER_SOFTWARE":"PHP 8.2.12 Development Server","REQUEST_URI":"/process_registration.php","HTTP_REFERER":"http://127.0.0.1:56317/?ide_webview_request_time=1740929828078","REMOTE_ADDR":"::1"}
[02-Mar-2025 16:51:41 Europe/Berlin] POST data received: Array
(
    [full_name] => J<PERSON>hn
    [occupation] => IT
    [email] => <EMAIL>
    [phone_number] => 5464466878
    [home_address] => NU
    [birth_date] => 2025-03-03
    [message] => Thank yu
)

[03-Mar-2025 07:13:16 Europe/Berlin] POST data received: Array
(
    [full_name] => <PERSON><PERSON> Junior
    [occupation] => IT
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street
    [birth_date] => 1985-05-25
    [message] => Yes, prayer for success
)

[03-Mar-2025 11:23:14 UTC] POST data received: Array
(
    [full_name] => Brother James
    [occupation] => Bong
    [email] => <EMAIL>
    [phone_number] => +27846114757
    [home_address] => 11 Testing China Street
Millionaire Platform
    [birth_date] => 1963-04-03
    [message] => Blessing for Long life and Prosperity
)

[04-Mar-2025 09:08:36 UTC] POST data received: Array
(
    [full_name] => kelvin
    [occupation] => Little
    [email] => <EMAIL>
    [phone_number] => +27846114757
    [home_address] => Long Street Cape Town 
    [birth_date] => 1965-03-05
    [message] => I want to buy a house
)

[04-Mar-2025 09:08:36 UTC] Sending email to: <EMAIL> with subject: Welcome to Freedom Assembly, kelvin!
[04-Mar-2025 09:08:36 UTC] Email attempt #1 to <EMAIL>
[04-Mar-2025 09:08:38 UTC] Email sent <NAME_EMAIL> on attempt #1
[07-Mar-2025 09:55:19 UTC] POST data received: Array
(
    [full_name] => Paul Lukas
    [occupation] => Manager
    [email] => <EMAIL>
    [phone_number] => +27846117474
    [home_address] => 11 Kent Road
    [birth_date] => 1965-03-09
    [message] => house
)

[07-Mar-2025 09:55:19 UTC] Attempting to send email to: <EMAIL> with subject: Welcome to Freedom Assembly, Paul Lukas!
[07-Mar-2025 09:55:19 UTC] Using SMTP settings: Host=smtp.hostinger.com, Username=<EMAIL>, Port=465
[07-Mar-2025 09:55:19 UTC] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: EHLO profilepilot.io

[07-Mar-2025 09:55:19 UTC] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[07-Mar-2025 09:55:19 UTC] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[07-Mar-2025 09:55:19 UTC] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[07-Mar-2025 09:55:19 UTC] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: DATA

[07-Mar-2025 09:55:19 UTC] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: Date: Fri, 7 Mar 2025 09:55:19 +0000

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: To: Paul Lukas <<EMAIL>>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: From: Profile Pilot <<EMAIL>>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: Subject: Welcome to Freedom Assembly, Paul Lukas!

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=iso-8859-1

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: 

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 20px;">

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:     <div style="text-align: center; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:         <h1 style="color: #2c3e50; font-size: 28px; margin-bottom: 20px;">Welcome to Freedom Assembly Church International!</h1>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:         

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:         <img src="https://profilepilot.io/church/uploads/67cac2874ba56.jpeg" alt="Paul Lukas" style="width: 150px; height: 150px; border-radius: 75px; margin: 20px auto; display: block; object-fit: cover; border: 5px solid #f8f9fa; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:         

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:         <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; margin-bottom: 15px;">Dear Paul Lukas,</p>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">Thank you for registering with Freedom Assembly Church International. We are delighted to have you as part of our church family!</p>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: 

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:         <div style="background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; margin: 20px 0;">

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:             <h2 style="color: #3498db; font-size: 20px; margin-bottom: 15px;">Your Registration Details</h2>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:             <table style="width: 100%; border-collapse: collapse;">

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:                 <tr>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:                     <td style="padding: 10px; border-bottom: 1px solid #eee; color: #666;">Full Name:</td>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:                     <td style="padding: 10px; border-bottom: 1px solid #eee;">Paul Lukas</td>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:                 </tr>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:                 <tr>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:                     <td style="padding: 10px; border-bottom: 1px solid #eee; color: #666;">Email:</td>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:                     <td style="padding: 10px; border-bottom: 1px solid #eee;"><EMAIL></td>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:                 </tr>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:                 <tr>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:                     <td style="padding: 10px; color: #666;">Phone:</td>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:                     <td style="padding: 10px;">+27846117474</td>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:                 </tr>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:             </table>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: 

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">We look forward to seeing you in our next service!</p>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: 

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">Blessings,<br><strong>Freedom Assembly Church International</strong></p>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: </div>

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: 

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: .

[07-Mar-2025 09:55:19 UTC] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4Z8M9350RRzGY3Xs

[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: RSET

[07-Mar-2025 09:55:19 UTC] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[07-Mar-2025 09:55:19 UTC] Email sent <NAME_EMAIL>
[07-Mar-2025 09:55:19 UTC] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[07-Mar-2025 09:55:19 UTC] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[08-Mar-2025 09:39:56 UTC] POST data received: Array
(
    [full_name] => Godwin Bointa
    [occupation] => IT
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street
    [birth_date] => 1985-03-09
    [message] => Wonderful
)

[08-Mar-2025 09:39:56 UTC] Attempting to send email to: <EMAIL> with subject: Welcome to Freedom Assembly, Godwin Bointa!
[08-Mar-2025 09:39:56 UTC] Using SMTP settings: Host=smtp.example.com, Username=<EMAIL>, Port=587
[08-Mar-2025 09:39:56 UTC] SMTP Debug [1]: SMTP ERROR: Failed to connect to server: php_network_getaddresses: getaddrinfo for smtp.example.com failed: Name or service not known (0)
[08-Mar-2025 09:39:56 UTC] SMTP Debug [2]: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[08-Mar-2025 09:39:56 UTC] Email sending failed: SMTP Error: Could not connect to SMTP host. Failed to connect to server
[2025-03-08 09:39:56] Failed to send welcome email Context: {"email":"<EMAIL>"} Server Info: {"PHP_VERSION":"8.2.27","SERVER_SOFTWARE":"LiteSpeed","REQUEST_URI":"/church/process_registration.php","HTTP_REFERER":"https://freedomassemblydb.online/church/index.php","REMOTE_ADDR":"*************"}
[08-Mar-2025 09:45:20 UTC] POST data received: Array
(
    [full_name] => Godwin Bointa
    [occupation] => IT
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street
    [birth_date] => 1985-03-09
    [message] => Wonderful
)

[08-Mar-2025 09:45:20 UTC] Attempting to send email to: <EMAIL> with subject: Welcome to Freedom Assembly, Godwin Bointa!
[08-Mar-2025 09:45:20 UTC] Using SMTP settings: Host=smtp.hostinger.com, Username=<EMAIL>, Port=465
[08-Mar-2025 09:45:20 UTC] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: EHLO freedomassemblydb.online

[08-Mar-2025 09:45:20 UTC] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[08-Mar-2025 09:45:20 UTC] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[08-Mar-2025 09:45:20 UTC] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[08-Mar-2025 09:45:20 UTC] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: DATA

[08-Mar-2025 09:45:20 UTC] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 8 Mar 2025 09:45:20 +0000

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: To: Godwin Bointa <<EMAIL>>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church International <<EMAIL>>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: Subject: Welcome to Freedom Assembly, Godwin Bointa!

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=iso-8859-1

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: 

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 20px;">

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:     <div style="text-align: center; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:         <h1 style="color: #2c3e50; font-size: 28px; margin-bottom: 20px;">Welcome to Freedom Assembly Church International!</h1>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:         

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:         <img src="https://freedomassemblydb.online/church/uploads/67cc11b088f39.jpg" alt="Godwin Bointa" style="width: 150px; height: 150px; border-radius: 75px; margin: 20px auto; display: block; object-fit: cover; border: 5px solid #f8f9fa; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:         

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:         <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; margin-bottom: 15px;">Dear Godwin Bointa,</p>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:             <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">Thank you for registering with Freedom Assembly Church International. We are delighted to have you as part of our church family!</p>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: 

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:         <div style="background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; margin: 20px 0;">

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:             <h2 style="color: #3498db; font-size: 20px; margin-bottom: 15px;">Your Registration Details</h2>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:             <table style="width: 100%; border-collapse: collapse;">

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:                 <tr>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:                     <td style="padding: 10px; border-bottom: 1px solid #eee; color: #666;">Full Name:</td>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:                     <td style="padding: 10px; border-bottom: 1px solid #eee;">Godwin Bointa</td>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:                 </tr>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:                 <tr>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:                     <td style="padding: 10px; border-bottom: 1px solid #eee; color: #666;">Email:</td>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:                     <td style="padding: 10px; border-bottom: 1px solid #eee;"><EMAIL></td>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:                 </tr>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:                 <tr>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:                     <td style="padding: 10px; color: #666;">Phone:</td>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:                     <td style="padding: 10px;">+27686814477</td>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:                 </tr>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:             </table>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: 

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:         <p style="font-size: 16px; line-height: 1.6; color: #2c3e50; margin: 20px 0;">We look forward to seeing you in our next service!</p>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: 

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:             <p style="color: #666; font-size: 14px;">Blessings,<br><strong>Freedom Assembly Church International</strong></p>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: </div>

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: 

[08-Mar-2025 09:45:20 UTC] SMTP Debug [1]: CLIENT -> SERVER: .

[08-Mar-2025 09:45:21 UTC] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4Z8yv46Sm7zHTnHM

[08-Mar-2025 09:45:21 UTC] SMTP Debug [1]: CLIENT -> SERVER: RSET

[08-Mar-2025 09:45:21 UTC] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[08-Mar-2025 09:45:21 UTC] Email sent <NAME_EMAIL>
[08-Mar-2025 09:45:21 UTC] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[08-Mar-2025 09:45:21 UTC] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[17-Mar-2025 08:53:04 UTC] POST data received: Array
(
    [full_name] => Ndivhuwo Machiba
    [occupation] => Chef
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street
    [birth_date] => 1963-03-18
    [message] => Thank you
)

[18-Mar-2025 11:24:38 Europe/Berlin] POST data received: Array
(
    [full_name] => Mike John
    [occupation] => Chef
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => NYRT
    [birth_date] => 1985-03-19
    [message] => Thank you
)

[19-Mar-2025 12:11:19 Europe/Berlin] POST data received: Array
(
    [full_name] => Jude John
    [occupation] => Chef
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => NY
    [birth_date] => 1998-03-21
    [message] => Thank you
)

[19-Mar-2025 12:11:19 Europe/Berlin] Welcome email settings: Array
(
    [template_ids] => 12
)

[19-Mar-2025 12:11:19 Europe/Berlin] Member data for welcome email: Array
(
    [full_name] => Jude John
    [first_name] => Jude
    [last_name] => John
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => NY
    [occupation] => Chef
    [image_path] => uploads/67daa65752fce.jpg
)

[19-Mar-2025 12:11:19 Europe/Berlin] Selected welcome template ID: 12
[19-Mar-2025 12:11:19 Europe/Berlin] Using template: Welcome Email Template
[19-Mar-2025 12:11:19 Europe/Berlin] Template content length: 2979
[19-Mar-2025 12:11:19 Europe/Berlin] Member data for template (first 3 items): full_name: Jude John, email: <EMAIL>, no birth_date
[19-Mar-2025 12:11:19 Europe/Berlin] Error getting site setting: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchbdclean.site_settings' doesn't exist
[19-Mar-2025 12:11:19 Europe/Berlin] Error getting site setting: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchbdclean.site_settings' doesn't exist
[19-Mar-2025 12:11:19 Europe/Berlin] Error getting site setting: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchbdclean.site_settings' doesn't exist
[19-Mar-2025 12:11:19 Europe/Berlin] Error getting site setting: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchbdclean.site_settings' doesn't exist
[19-Mar-2025 12:11:19 Europe/Berlin] Error getting site setting: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchbdclean.site_settings' doesn't exist
[19-Mar-2025 12:11:19 Europe/Berlin] Template after replacement: Welcome to Freedom Assembly, Jude John!...
[19-Mar-2025 12:11:19 Europe/Berlin] Member data for template (first 3 items): full_name: Jude John, email: <EMAIL>, no birth_date
[19-Mar-2025 12:11:19 Europe/Berlin] Error getting site setting: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchbdclean.site_settings' doesn't exist
[19-Mar-2025 12:11:19 Europe/Berlin] Error getting site setting: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchbdclean.site_settings' doesn't exist
[19-Mar-2025 12:11:19 Europe/Berlin] Error getting site setting: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchbdclean.site_settings' doesn't exist
[19-Mar-2025 12:11:19 Europe/Berlin] Error getting site setting: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchbdclean.site_settings' doesn't exist
[19-Mar-2025 12:11:19 Europe/Berlin] Error getting site setting: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'churchbdclean.site_settings' doesn't exist
[19-Mar-2025 12:11:19 Europe/Berlin] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[19-Mar-2025 12:11:19 Europe/Berlin] Processed subject: Welcome to Freedom Assembly, Jude John!
[19-Mar-2025 12:11:19 Europe/Berlin] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Jude John!
[26-Mar-2025 11:59:23 Europe/Berlin] POST data received: Array
(
    [full_name] => Bointa Clifford
    [occupation] => Chef
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street
    [birth_date] => 1954-03-26
    [message] => Thank you Lord
)

[2025-03-26 11:59:23] Database error: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'email' Context: {"code":"23000","email":"<EMAIL>"} Server Info: {"PHP_VERSION":"8.2.12","SERVER_SOFTWARE":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12","REQUEST_URI":"/church/process_registration.php","HTTP_REFERER":"http://localhost/church/","REMOTE_ADDR":"::1"}
[26-Mar-2025 12:00:12 Europe/Berlin] POST data received: Array
(
    [full_name] => Bointa Clifford
    [occupation] => Chef
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street
    [birth_date] => 1982-03-26
    [message] => Thank you Lord
)

[26-Mar-2025 12:00:12 Europe/Berlin] Welcome email settings: Array
(
    [template_ids] => 12
)

[26-Mar-2025 12:00:12 Europe/Berlin] Member data for welcome email: Array
(
    [full_name] => Bointa Clifford
    [first_name] => Bointa
    [last_name] => Clifford
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street
    [occupation] => Chef
    [image_path] => uploads/67e3de3cafa01.jpg
)

[26-Mar-2025 12:00:12 Europe/Berlin] Selected welcome template ID: 12
[26-Mar-2025 12:00:12 Europe/Berlin] Using template: Welcome Email Template
[26-Mar-2025 12:00:12 Europe/Berlin] Template content length: 2971
[26-Mar-2025 12:00:12 Europe/Berlin] Member data for template (first 3 items): full_name: Bointa Clifford, email: <EMAIL>, no birth_date
[26-Mar-2025 12:00:12 Europe/Berlin] Template after replacement: Welcome to Freedom Assembly, Bointa Clifford!...
[26-Mar-2025 12:00:12 Europe/Berlin] Member data for template (first 3 items): full_name: Bointa Clifford, email: <EMAIL>, no birth_date
[26-Mar-2025 12:00:12 Europe/Berlin] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[26-Mar-2025 12:00:12 Europe/Berlin] Processed subject: Welcome to Freedom Assembly, Bointa Clifford!
[26-Mar-2025 12:00:12 Europe/Berlin] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Bointa Clifford!
[30-Mar-2025 04:39:08 UTC] POST data received: Array
(
    [full_name] => Mike John
    [occupation] => IT
    [email] => <EMAIL>
    [phone_number] => +27457851255
    [home_address] => 48688 Victoria Ln, Oakhurst, CA 93644, United States
    [birth_date] => 1985-03-30
    [message] => 
)

[30-Mar-2025 04:39:08 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[30-Mar-2025 04:39:08 UTC] Member data for welcome email: Array
(
    [full_name] => Mike John
    [first_name] => Mike
    [last_name] => John
    [email] => <EMAIL>
    [phone_number] => +27457851255
    [home_address] => 48688 Victoria Ln, Oakhurst, CA 93644, United States
    [occupation] => IT
    [image_path] => uploads/67e8caecc7fae.jpg
)

[30-Mar-2025 04:39:08 UTC] Selected welcome template ID: 38
[30-Mar-2025 04:39:08 UTC] Using template: Welcome Email Template
[30-Mar-2025 04:39:08 UTC] Template content length: 2705
[30-Mar-2025 04:39:08 UTC] Member data for template (first 3 items): full_name: Mike John, email: <EMAIL>, no birth_date
[30-Mar-2025 04:39:08 UTC] Template after replacement: Welcome to Freedom Assembly, Mike John!...
[30-Mar-2025 04:39:08 UTC] Member data for template (first 3 items): full_name: Mike John, email: <EMAIL>, no birth_date
[30-Mar-2025 04:39:08 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[30-Mar-2025 04:39:08 UTC] Processed subject: Welcome to Freedom Assembly, Mike John!
[30-Mar-2025 04:39:08 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Mike John!
[30-Mar-2025 05:38:47 UTC] POST data received: Array
(
    [full_name] => Godwin Bointa
    [occupation] => IT
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street
    [birth_date] => 1985-03-30
    [message] => Thank you Lord
)

[30-Mar-2025 05:38:47 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[30-Mar-2025 05:38:47 UTC] Member data for welcome email: Array
(
    [full_name] => Godwin Bointa
    [first_name] => Godwin
    [last_name] => Bointa
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street
    [occupation] => IT
    [image_path] => uploads/67e8d8e7aa0eb.jpg
)

[30-Mar-2025 05:38:47 UTC] Selected welcome template ID: 38
[30-Mar-2025 05:38:47 UTC] Using template: Welcome Email Template
[30-Mar-2025 05:38:47 UTC] Template content length: 2705
[30-Mar-2025 05:38:47 UTC] Member data for template (first 3 items): full_name: Godwin Bointa, email: <EMAIL>, no birth_date
[30-Mar-2025 05:38:47 UTC] Template after replacement: Welcome to Freedom Assembly, Godwin Bointa!...
[30-Mar-2025 05:38:47 UTC] Member data for template (first 3 items): full_name: Godwin Bointa, email: <EMAIL>, no birth_date
[30-Mar-2025 05:38:47 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[30-Mar-2025 05:38:47 UTC] Processed subject: Welcome to Freedom Assembly, Godwin Bointa!
[30-Mar-2025 05:38:47 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Godwin Bointa!
[31-Mar-2025 09:40:56 UTC] POST data received: Array
(
    [full_name] => Godwin Junior Bointa
    [occupation] => IT Self Employed 
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street Wilgeheuwel 
    [birth_date] => 1987-05-25
    [message] => Thank you Lord
)

[31-Mar-2025 09:40:56 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[31-Mar-2025 09:40:56 UTC] Member data for welcome email: Array
(
    [full_name] => Godwin Junior Bointa
    [first_name] => Godwin
    [last_name] => Junior Bointa
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street Wilgeheuwel
    [occupation] => IT Self Employed
    [image_path] => uploads/67ea63289f038.jpeg
)

[31-Mar-2025 09:40:56 UTC] Selected welcome template ID: 38
[31-Mar-2025 09:40:56 UTC] Using template: Welcome Email Template
[31-Mar-2025 09:40:56 UTC] Template content length: 2705
[31-Mar-2025 09:40:56 UTC] Member data for template (first 3 items): full_name: Godwin Junior Bointa, email: <EMAIL>, no birth_date
[31-Mar-2025 09:40:56 UTC] Template after replacement: Welcome to Freedom Assembly, Godwin Junior Bointa!...
[31-Mar-2025 09:40:56 UTC] Member data for template (first 3 items): full_name: Godwin Junior Bointa, email: <EMAIL>, no birth_date
[31-Mar-2025 09:40:56 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[31-Mar-2025 09:40:56 UTC] Processed subject: Welcome to Freedom Assembly, Godwin Junior Bointa!
[31-Mar-2025 09:40:56 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Godwin Junior Bointa!
[31-Mar-2025 10:07:43 UTC] POST data received: Array
(
    [full_name] => Godwin Junior Bointa
    [occupation] => IT Self Employed 
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street Wilgeheuwel 
    [birth_date] => 1987-05-25
    [message] => The Lord is Good
)

[31-Mar-2025 10:07:43 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[31-Mar-2025 10:07:43 UTC] Member data for welcome email: Array
(
    [full_name] => Godwin Junior Bointa
    [first_name] => Godwin
    [last_name] => Junior Bointa
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street Wilgeheuwel
    [occupation] => IT Self Employed
    [image_path] => uploads/67ea696f0be1b.jpeg
)

[31-Mar-2025 10:07:43 UTC] Selected welcome template ID: 38
[31-Mar-2025 10:07:43 UTC] Using template: Welcome Email Template
[31-Mar-2025 10:07:43 UTC] Template content length: 2705
[31-Mar-2025 10:07:43 UTC] Member data for template (first 3 items): full_name: Godwin Junior Bointa, email: <EMAIL>, no birth_date
[31-Mar-2025 10:07:43 UTC] Template after replacement: Welcome to Freedom Assembly, Godwin Junior Bointa!...
[31-Mar-2025 10:07:43 UTC] Member data for template (first 3 items): full_name: Godwin Junior Bointa, email: <EMAIL>, no birth_date
[31-Mar-2025 10:07:43 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[31-Mar-2025 10:07:43 UTC] Processed subject: Welcome to Freedom Assembly, Godwin Junior Bointa!
[31-Mar-2025 10:07:43 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Godwin Junior Bointa!
[31-Mar-2025 11:40:04 UTC] POST data received: Array
(
    [full_name] => Mgcini Bhebhe 
    [occupation] => Signarama west rand
    [email] => <EMAIL>
    [phone_number] => 0710670804
    [home_address] => 34 Langeberg avenue Bosmont Johannesburg 
    [birth_date] => 1986-03-11
    [message] => 🙏🙏🙏
)

[31-Mar-2025 11:40:04 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[31-Mar-2025 11:40:04 UTC] Member data for welcome email: Array
(
    [full_name] => Mgcini Bhebhe
    [first_name] => Mgcini
    [last_name] => Bhebhe
    [email] => <EMAIL>
    [phone_number] => 0710670804
    [home_address] => 34 Langeberg avenue Bosmont Johannesburg
    [occupation] => Signarama west rand
    [image_path] => uploads/67ea7f1450c37.png
)

[31-Mar-2025 11:40:04 UTC] Selected welcome template ID: 38
[31-Mar-2025 11:40:04 UTC] Using template: Welcome Email Template
[31-Mar-2025 11:40:04 UTC] Template content length: 2705
[31-Mar-2025 11:40:04 UTC] Member data for template (first 3 items): full_name: Mgcini Bhebhe, email: <EMAIL>, no birth_date
[31-Mar-2025 11:40:04 UTC] Template after replacement: Welcome to Freedom Assembly, Mgcini Bhebhe!...
[31-Mar-2025 11:40:04 UTC] Member data for template (first 3 items): full_name: Mgcini Bhebhe, email: <EMAIL>, no birth_date
[31-Mar-2025 11:40:04 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[31-Mar-2025 11:40:04 UTC] Processed subject: Welcome to Freedom Assembly, Mgcini Bhebhe!
[31-Mar-2025 11:40:04 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Mgcini Bhebhe!
[31-Mar-2025 12:43:35 UTC] POST data received: Array
(
    [full_name] => KINGSLEY ADAMS 
    [occupation] => Sales and Marketing (also self employed)
    [email] => <EMAIL>
    [phone_number] => +27724883288
    [home_address] => 474 CHIRONIA STREET, WILRO PARK, ROODEPOORT, JOHANNESBURG, GAUTENG 
    [birth_date] => 1974-07-09
    [message] => 
)

[31-Mar-2025 12:43:35 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[31-Mar-2025 12:43:35 UTC] Member data for welcome email: Array
(
    [full_name] => KINGSLEY ADAMS
    [first_name] => KINGSLEY
    [last_name] => ADAMS
    [email] => <EMAIL>
    [phone_number] => +27724883288
    [home_address] => 474 CHIRONIA STREET, WILRO PARK, ROODEPOORT, JOHANNESBURG, GAUTENG
    [occupation] => Sales and Marketing (also self employed)
    [image_path] => uploads/67ea8df7cd650.jpg
)

[31-Mar-2025 12:43:35 UTC] Selected welcome template ID: 38
[31-Mar-2025 12:43:35 UTC] Using template: Welcome Email Template
[31-Mar-2025 12:43:35 UTC] Template content length: 2705
[31-Mar-2025 12:43:35 UTC] Member data for template (first 3 items): full_name: KINGSLEY ADAMS, email: <EMAIL>, no birth_date
[31-Mar-2025 12:43:35 UTC] Template after replacement: Welcome to Freedom Assembly, KINGSLEY ADAMS!...
[31-Mar-2025 12:43:35 UTC] Member data for template (first 3 items): full_name: KINGSLEY ADAMS, email: <EMAIL>, no birth_date
[31-Mar-2025 12:43:35 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[31-Mar-2025 12:43:35 UTC] Processed subject: Welcome to Freedom Assembly, KINGSLEY ADAMS!
[31-Mar-2025 12:43:35 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, KINGSLEY ADAMS!
[31-Mar-2025 18:28:40 UTC] No POST data received
[31-Mar-2025 21:50:00 UTC] POST data received: Array
(
    [full_name] => Kolawole Adebayo Ogunmesa 
    [occupation] => Fashion designer 
    [email] => <EMAIL>
    [phone_number] => 0723372160
    [home_address] => 14 Anthony avenue randfontein west rand 
    [birth_date] => 2025-04-11
    [message] => Divine visitation over business, family and calling 
)

[31-Mar-2025 21:50:00 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[31-Mar-2025 21:50:00 UTC] Member data for welcome email: Array
(
    [full_name] => Kolawole Adebayo Ogunmesa
    [first_name] => Kolawole
    [last_name] => Adebayo Ogunmesa
    [email] => <EMAIL>
    [phone_number] => 0723372160
    [home_address] => 14 Anthony avenue randfontein west rand
    [occupation] => Fashion designer
    [image_path] => uploads/67eb0e0859cae.jpeg
)

[31-Mar-2025 21:50:00 UTC] Selected welcome template ID: 38
[31-Mar-2025 21:50:00 UTC] Using template: Welcome Email Template
[31-Mar-2025 21:50:00 UTC] Template content length: 2705
[31-Mar-2025 21:50:00 UTC] Member data for template (first 3 items): full_name: Kolawole Adebayo Ogunmesa, email: <EMAIL>, no birth_date
[31-Mar-2025 21:50:00 UTC] Template after replacement: Welcome to Freedom Assembly, Kolawole Adebayo Ogunmesa!...
[31-Mar-2025 21:50:00 UTC] Member data for template (first 3 items): full_name: Kolawole Adebayo Ogunmesa, email: <EMAIL>, no birth_date
[31-Mar-2025 21:50:00 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[31-Mar-2025 21:50:00 UTC] Processed subject: Welcome to Freedom Assembly, Kolawole Adebayo Ogunmesa!
[31-Mar-2025 21:50:00 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Kolawole Adebayo Ogunmesa!
[01-Apr-2025 09:18:02 UTC] POST data received: Array
(
    [full_name] => Roy Mafirowanda
    [occupation] => Head of Procurement 
    [email] => <EMAIL>
    [phone_number] => +***********
    [home_address] => Unit 3 Oregon, Honeydew Residential Estate.
Wilgeheuwel. 1725
    [birth_date] => 1986-05-27
    [message] => Strength, Prayer life, Deliverance, Financial, Health and Business success
)

[01-Apr-2025 09:18:02 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[01-Apr-2025 09:18:02 UTC] Member data for welcome email: Array
(
    [full_name] => Roy Mafirowanda
    [first_name] => Roy
    [last_name] => Mafirowanda
    [email] => <EMAIL>
    [phone_number] => +***********
    [home_address] => Unit 3 Oregon, Honeydew Residential Estate.
Wilgeheuwel. 1725
    [occupation] => Head of Procurement
    [image_path] => uploads/67ebaf4a6742b.jpg
)

[01-Apr-2025 09:18:02 UTC] Selected welcome template ID: 38
[01-Apr-2025 09:18:02 UTC] Using template: Welcome Email Template
[01-Apr-2025 09:18:02 UTC] Template content length: 2705
[01-Apr-2025 09:18:02 UTC] Member data for template (first 3 items): full_name: Roy Mafirowanda, email: <EMAIL>, no birth_date
[01-Apr-2025 09:18:02 UTC] Template after replacement: Welcome to Freedom Assembly, Roy Mafirowanda!...
[01-Apr-2025 09:18:02 UTC] Member data for template (first 3 items): full_name: Roy Mafirowanda, email: <EMAIL>, no birth_date
[01-Apr-2025 09:18:02 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[01-Apr-2025 09:18:02 UTC] Processed subject: Welcome to Freedom Assembly, Roy Mafirowanda!
[01-Apr-2025 09:18:02 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Roy Mafirowanda!
[01-Apr-2025 09:26:57 UTC] POST data received: Array
(
    [full_name] => Gift Andre Thiesen
    [occupation] => EMS
    [email] => <EMAIL>
    [phone_number] => 0842628620
    [home_address] => Unit 66 Inyati Lodge
Allens Nek Roodepoort
    [birth_date] => 1986-04-01
    [message] => God's Grace and Mercy over life and my family
)

[01-Apr-2025 09:26:57 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[01-Apr-2025 09:26:57 UTC] Member data for welcome email: Array
(
    [full_name] => Gift Andre Thiesen
    [first_name] => Gift
    [last_name] => Andre Thiesen
    [email] => <EMAIL>
    [phone_number] => 0842628620
    [home_address] => Unit 66 Inyati Lodge
Allens Nek Roodepoort
    [occupation] => EMS
    [image_path] => uploads/67ebb16162402.jpg
)

[01-Apr-2025 09:26:57 UTC] Selected welcome template ID: 38
[01-Apr-2025 09:26:57 UTC] Using template: Welcome Email Template
[01-Apr-2025 09:26:57 UTC] Template content length: 2705
[01-Apr-2025 09:26:57 UTC] Member data for template (first 3 items): full_name: Gift Andre Thiesen, email: <EMAIL>, no birth_date
[01-Apr-2025 09:26:57 UTC] Template after replacement: Welcome to Freedom Assembly, Gift Andre Thiesen!...
[01-Apr-2025 09:26:57 UTC] Member data for template (first 3 items): full_name: Gift Andre Thiesen, email: <EMAIL>, no birth_date
[01-Apr-2025 09:26:57 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[01-Apr-2025 09:26:57 UTC] Processed subject: Welcome to Freedom Assembly, Gift Andre Thiesen!
[01-Apr-2025 09:26:57 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Gift Andre Thiesen!
[01-Apr-2025 09:28:03 UTC] POST data received: Array
(
    [full_name] => Gift Andre Thiesen
    [occupation] => EMS
    [email] => <EMAIL>
    [phone_number] => 0842628620
    [home_address] => Unit 66 Inyati Lodge
Allens Nek Roodepoort
    [birth_date] => 1986-11-05
    [message] => God's Grace and Mercy over life and my family
)

[2025-04-01 09:28:03] Database error: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'email' Context: {"code":"23000","email":"<EMAIL>"} Server Info: {"PHP_VERSION":"8.2.27","SERVER_SOFTWARE":"LiteSpeed","REQUEST_URI":"/church/process_registration.php","HTTP_REFERER":"https://freedomassemblydb.online/church/index.php","REMOTE_ADDR":"***********"}
[01-Apr-2025 09:47:36 UTC] POST data received: Array
(
    [full_name] => Adekunle Osoare
    [occupation] => Medical Doctor, Pastor
    [email] => <EMAIL>
    [phone_number] => +27741130034
    [home_address] => 152, Cradle Ridge Estate, Falls Road, Krugersdoorp 
    [birth_date] => 1965-09-18
    [message] => 
)

[01-Apr-2025 09:47:36 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[01-Apr-2025 09:47:36 UTC] Member data for welcome email: Array
(
    [full_name] => Adekunle Osoare
    [first_name] => Adekunle
    [last_name] => Osoare
    [email] => <EMAIL>
    [phone_number] => +27741130034
    [home_address] => 152, Cradle Ridge Estate, Falls Road, Krugersdoorp
    [occupation] => Medical Doctor, Pastor
    [image_path] => uploads/67ebb638e576a.jpg
)

[01-Apr-2025 09:47:36 UTC] Selected welcome template ID: 38
[01-Apr-2025 09:47:36 UTC] Using template: Welcome Email Template
[01-Apr-2025 09:47:36 UTC] Template content length: 2705
[01-Apr-2025 09:47:36 UTC] Member data for template (first 3 items): full_name: Adekunle Osoare, email: <EMAIL>, no birth_date
[01-Apr-2025 09:47:36 UTC] Template after replacement: Welcome to Freedom Assembly, Adekunle Osoare!...
[01-Apr-2025 09:47:36 UTC] Member data for template (first 3 items): full_name: Adekunle Osoare, email: <EMAIL>, no birth_date
[01-Apr-2025 09:47:36 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[01-Apr-2025 09:47:36 UTC] Processed subject: Welcome to Freedom Assembly, Adekunle Osoare!
[01-Apr-2025 09:47:36 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Adekunle Osoare!
[01-Apr-2025 10:50:12 UTC] POST data received: Array
(
    [full_name] => Tony Jerry Anthony 
    [occupation] => Safety Practitioner
    [email] => <EMAIL>
    [phone_number] => +27834273720
    [home_address] => 49 Seventh Avenue Florida park 
    [birth_date] => 1973-06-09
    [message] => Oh Lord thy will be done in my life
)

[01-Apr-2025 10:50:12 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[01-Apr-2025 10:50:12 UTC] Member data for welcome email: Array
(
    [full_name] => Tony Jerry Anthony
    [first_name] => Tony
    [last_name] => Jerry Anthony
    [email] => <EMAIL>
    [phone_number] => +27834273720
    [home_address] => 49 Seventh Avenue Florida park
    [occupation] => Safety Practitioner
    [image_path] => uploads/67ebc4e4e0ac1.jpg
)

[01-Apr-2025 10:50:12 UTC] Selected welcome template ID: 38
[01-Apr-2025 10:50:12 UTC] Using template: Welcome Email Template
[01-Apr-2025 10:50:12 UTC] Template content length: 2705
[01-Apr-2025 10:50:12 UTC] Member data for template (first 3 items): full_name: Tony Jerry Anthony, email: <EMAIL>, no birth_date
[01-Apr-2025 10:50:12 UTC] Template after replacement: Welcome to Freedom Assembly, Tony Jerry Anthony!...
[01-Apr-2025 10:50:12 UTC] Member data for template (first 3 items): full_name: Tony Jerry Anthony, email: <EMAIL>, no birth_date
[01-Apr-2025 10:50:12 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[01-Apr-2025 10:50:12 UTC] Processed subject: Welcome to Freedom Assembly, Tony Jerry Anthony!
[01-Apr-2025 10:50:12 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Tony Jerry Anthony!
[01-Apr-2025 12:23:48 UTC] POST data received: Array
(
    [full_name] => Olushola Fateye 
    [occupation] => Medical Doctor
    [email] => <EMAIL>
    [phone_number] => +27799715294
    [home_address] => 21 CR SWART STREET, WILROPARK, ROODEPOORT 
    [birth_date] => 1974-02-13
    [message] => Moving forward and gaining territories 
)

[01-Apr-2025 12:23:48 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[01-Apr-2025 12:23:48 UTC] Member data for welcome email: Array
(
    [full_name] => Olushola Fateye
    [first_name] => Olushola
    [last_name] => Fateye
    [email] => <EMAIL>
    [phone_number] => +27799715294
    [home_address] => 21 CR SWART STREET, WILROPARK, ROODEPOORT
    [occupation] => Medical Doctor
    [image_path] => uploads/67ebdad48bc71.jpg
)

[01-Apr-2025 12:23:48 UTC] Selected welcome template ID: 38
[01-Apr-2025 12:23:48 UTC] Using template: Welcome Email Template
[01-Apr-2025 12:23:48 UTC] Template content length: 2705
[01-Apr-2025 12:23:48 UTC] Member data for template (first 3 items): full_name: Olushola Fateye, email: <EMAIL>, no birth_date
[01-Apr-2025 12:23:48 UTC] Template after replacement: Welcome to Freedom Assembly, Olushola Fateye!...
[01-Apr-2025 12:23:48 UTC] Member data for template (first 3 items): full_name: Olushola Fateye, email: <EMAIL>, no birth_date
[01-Apr-2025 12:23:48 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[01-Apr-2025 12:23:48 UTC] Processed subject: Welcome to Freedom Assembly, Olushola Fateye!
[01-Apr-2025 12:23:48 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Olushola Fateye!
[01-Apr-2025 16:22:30 UTC] POST data received: Array
(
    [full_name] => Akinbode Oso
    [occupation] => Self Employed 
    [email] => <EMAIL>
    [phone_number] => +27717768222
    [home_address] => Millard Place, 
720 Roworth Street Discovery, 1709 Roodepoort, Johannesburg 
    [birth_date] => 1983-12-16
    [message] => For God's Mercy,Grace and Favour over me, my house hold, family and all my loved ones.
Grant me speed in my life endeavors
God's Divine intervention and expansion over Freedom Assembly Int'L
I pray for God's mighty hand upon Freedom Assemblite and all Church leaders. 
)

[01-Apr-2025 16:22:30 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[01-Apr-2025 16:22:30 UTC] Member data for welcome email: Array
(
    [full_name] => Akinbode Oso
    [first_name] => Akinbode
    [last_name] => Oso
    [email] => <EMAIL>
    [phone_number] => +27717768222
    [home_address] => Millard Place, 
720 Roworth Street Discovery, 1709 Roodepoort, Johannesburg
    [occupation] => Self Employed
    [image_path] => uploads/67ec12c657928.jpg
)

[01-Apr-2025 16:22:30 UTC] Selected welcome template ID: 38
[01-Apr-2025 16:22:30 UTC] Using template: Welcome Email Template
[01-Apr-2025 16:22:30 UTC] Template content length: 2705
[01-Apr-2025 16:22:30 UTC] Member data for template (first 3 items): full_name: Akinbode Oso, email: <EMAIL>, no birth_date
[01-Apr-2025 16:22:30 UTC] Template after replacement: Welcome to Freedom Assembly, Akinbode Oso!...
[01-Apr-2025 16:22:30 UTC] Member data for template (first 3 items): full_name: Akinbode Oso, email: <EMAIL>, no birth_date
[01-Apr-2025 16:22:30 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[01-Apr-2025 16:22:30 UTC] Processed subject: Welcome to Freedom Assembly, Akinbode Oso!
[01-Apr-2025 16:22:30 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Akinbode Oso!
[01-Apr-2025 17:22:10 UTC] POST data received: Array
(
    [full_name] => Akinbode Oso
    [occupation] => Self Employed 
    [email] => <EMAIL>
    [phone_number] => +27717768222
    [home_address] => Millard Place
720 Roworth Place Discovery, 1709 Roodepoort. Jhb 
    [birth_date] => 1983-12-16
    [message] => God's mercy, grace and Favour over me, my house hold and family, love ones
Grants me speed in all my endeavors
God's mighty hand upon Freedom Assemblite and all Church leaders
God's Divine intervention and expansion within and worldwide. 
)

[2025-04-01 17:22:10] Database error: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'email' Context: {"code":"23000","email":"<EMAIL>"} Server Info: {"PHP_VERSION":"8.2.27","SERVER_SOFTWARE":"LiteSpeed","REQUEST_URI":"/church/process_registration.php","HTTP_REFERER":"https://freedomassemblydb.online/church/index.php","REMOTE_ADDR":"***************"}
[01-Apr-2025 18:24:25 UTC] No POST data received
[02-Apr-2025 10:06:45 UTC] POST data received: Array
(
    [full_name] => Akinbode Oso
    [occupation] => Self Employed 
    [email] => <EMAIL>
    [phone_number] => +27717768222
    [home_address] => Millard Place
720 Roworth Place, Discovery 1709
Roodepoort. Jhb. 
    [birth_date] => 1984-12-16
    [message] => God's mercy, grace and Favour over me, my house hold and family, love ones
Grants me speed in all my endeavors
God's mighty hand upon Freedom Assemblite and all Church leaders
God's Divine intervention and expansion within and worldwide.
)

[02-Apr-2025 10:06:45 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[02-Apr-2025 10:06:45 UTC] Member data for welcome email: Array
(
    [full_name] => Akinbode Oso
    [first_name] => Akinbode
    [last_name] => Oso
    [email] => <EMAIL>
    [phone_number] => +27717768222
    [home_address] => Millard Place
720 Roworth Place, Discovery 1709
Roodepoort. Jhb.
    [occupation] => Self Employed
    [image_path] => uploads/67ed0c351721f.jpg
)

[02-Apr-2025 10:06:45 UTC] Selected welcome template ID: 38
[02-Apr-2025 10:06:45 UTC] Using template: Welcome Email Template
[02-Apr-2025 10:06:45 UTC] Template content length: 2705
[02-Apr-2025 10:06:45 UTC] Member data for template (first 3 items): full_name: Akinbode Oso, email: <EMAIL>, no birth_date
[02-Apr-2025 10:06:45 UTC] Template after replacement: Welcome to Freedom Assembly, Akinbode Oso!...
[02-Apr-2025 10:06:45 UTC] Member data for template (first 3 items): full_name: Akinbode Oso, email: <EMAIL>, no birth_date
[02-Apr-2025 10:06:45 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[02-Apr-2025 10:06:45 UTC] Processed subject: Welcome to Freedom Assembly, Akinbode Oso!
[02-Apr-2025 10:06:45 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Akinbode Oso!
[02-Apr-2025 10:56:19 UTC] POST data received: Array
(
    [full_name] => Godwin Junior Bointa
    [occupation] => IT Self Employed 
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street Wilgeheuwel Roodepoort 
    [birth_date] => 1987-05-02
    [message] => Thanks to God
)

[02-Apr-2025 10:56:19 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[02-Apr-2025 10:56:19 UTC] Member data for welcome email: Array
(
    [full_name] => Godwin Junior Bointa
    [first_name] => Godwin
    [last_name] => Junior Bointa
    [email] => <EMAIL>
    [phone_number] => +27686814477
    [home_address] => 17 Sjampanje Street Wilgeheuwel Roodepoort
    [occupation] => IT Self Employed
    [image_path] => uploads/67ed17d309fda.jpeg
)

[02-Apr-2025 10:56:19 UTC] Selected welcome template ID: 38
[02-Apr-2025 10:56:19 UTC] Using template: Welcome Email Template
[02-Apr-2025 10:56:19 UTC] Template content length: 2705
[02-Apr-2025 10:56:19 UTC] Member data for template (first 3 items): full_name: Godwin Junior Bointa, email: <EMAIL>, no birth_date
[02-Apr-2025 10:56:19 UTC] Template after replacement: Welcome to Freedom Assembly, Godwin Junior Bointa!...
[02-Apr-2025 10:56:19 UTC] Member data for template (first 3 items): full_name: Godwin Junior Bointa, email: <EMAIL>, no birth_date
[02-Apr-2025 10:56:19 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[02-Apr-2025 10:56:19 UTC] Processed subject: Welcome to Freedom Assembly, Godwin Junior Bointa!
[02-Apr-2025 10:56:19 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Godwin Junior Bointa!
[02-Apr-2025 18:50:46 UTC] No POST data received
[03-Apr-2025 19:19:03 UTC] No POST data received
[04-Apr-2025 20:06:57 UTC] No POST data received
[05-Apr-2025 20:34:22 UTC] No POST data received
[06-Apr-2025 20:47:36 UTC] No POST data received
[07-Apr-2025 21:10:46 UTC] No POST data received
[08-Apr-2025 22:34:48 UTC] No POST data received
[12-Apr-2025 19:49:17 UTC] No POST data received
[13-Apr-2025 20:58:46 UTC] No POST data received
[16-Apr-2025 19:39:48 UTC] No POST data received
[18-Apr-2025 19:53:51 UTC] No POST data received
[21-Apr-2025 11:44:37 UTC] No POST data received
[23-Apr-2025 08:18:46 UTC] POST data received: Array
(
    [full_name] => Marcus Naidoo
    [occupation] => Self Employed
    [email] => <EMAIL>
    [phone_number] => 0672514643
    [home_address] => 18aFourth Street
Florida lake
Indigo sky 4
    [birth_date] => 1985-04-06
    [message] => 
)

[23-Apr-2025 08:18:46 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[23-Apr-2025 08:18:46 UTC] Member data for welcome email: Array
(
    [full_name] => Marcus Naidoo
    [first_name] => Marcus
    [last_name] => Naidoo
    [email] => <EMAIL>
    [phone_number] => 0672514643
    [home_address] => 18aFourth Street
Florida lake
Indigo sky 4
    [occupation] => Self Employed
    [image_path] => uploads/6808a266a3dd0.jpg
)

[23-Apr-2025 08:18:46 UTC] Selected welcome template ID: 38
[23-Apr-2025 08:18:46 UTC] Using template: Welcome Email Template
[23-Apr-2025 08:18:46 UTC] Template content length: 2705
[23-Apr-2025 08:18:46 UTC] Member data for template (first 3 items): full_name: Marcus Naidoo, email: <EMAIL>, no birth_date
[23-Apr-2025 08:18:46 UTC] Template after replacement: Welcome to Freedom Assembly, Marcus Naidoo!...
[23-Apr-2025 08:18:46 UTC] Member data for template (first 3 items): full_name: Marcus Naidoo, email: <EMAIL>, no birth_date
[23-Apr-2025 08:18:46 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[23-Apr-2025 08:18:46 UTC] Processed subject: Welcome to Freedom Assembly, Marcus Naidoo!
[23-Apr-2025 08:18:46 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Marcus Naidoo!
[24-Apr-2025 17:42:24 UTC] No POST data received
[25-Apr-2025 23:19:15 UTC] No POST data received
[26-Apr-2025 18:01:04 UTC] No POST data received
[28-Apr-2025 15:51:02 UTC] No POST data received
[29-Apr-2025 22:36:15 UTC] No POST data received
[03-May-2025 16:50:39 UTC] No POST data received
[04-May-2025 10:23:32 UTC] No POST data received
[06-May-2025 23:32:14 UTC] No POST data received
[24-May-2025 16:07:09 UTC] No POST data received
[27-May-2025 03:40:39 UTC] No POST data received
[31-May-2025 10:28:58 UTC] POST data received: Array
(
    [full_name] => RICHARD MWENYI
    [occupation] => social work graduate. Currently doing transport
    [email] => <EMAIL>
    [phone_number] => +27619006145
    [home_address] => 21 3rd Avenue westden
    [birth_date] => 1988-08-14
    [message] => Spiritual Growth
Working permit 
Financial breakthrough 
)

[31-May-2025 10:28:58 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[31-May-2025 10:28:58 UTC] Member data for welcome email: Array
(
    [full_name] => RICHARD MWENYI
    [first_name] => RICHARD
    [last_name] => MWENYI
    [email] => <EMAIL>
    [phone_number] => +27619006145
    [home_address] => 21 3rd Avenue westden
    [occupation] => social work graduate. Currently doing transport
    [image_path] => uploads/683ad9eacd058.jpg
)

[31-May-2025 10:28:58 UTC] Selected welcome template ID: 38
[31-May-2025 10:28:58 UTC] Using template: Welcome Email Template
[31-May-2025 10:28:58 UTC] Template content length: 2705
[31-May-2025 10:28:58 UTC] Member data for template (first 3 items): full_name: RICHARD MWENYI, email: <EMAIL>, no birth_date
[31-May-2025 10:28:58 UTC] Template after replacement: Welcome to Freedom Assembly, RICHARD MWENYI!...
[31-May-2025 10:28:58 UTC] Member data for template (first 3 items): full_name: RICHARD MWENYI, email: <EMAIL>, no birth_date
[31-May-2025 10:28:58 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[31-May-2025 10:28:58 UTC] Processed subject: Welcome to Freedom Assembly, RICHARD MWENYI!
[31-May-2025 10:28:58 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, RICHARD MWENYI!
[31-May-2025 13:48:05 UTC] POST data received: Array
(
    [full_name] => Mgcini
    [occupation] => Signage 
    [email] => <EMAIL>
    [phone_number] => 0027710670804
    [home_address] => 34 Langeberg Bosmont Johannesburg Gauteng
    [birth_date] => 1986-03-11
    [message] => 
)

[31-May-2025 13:48:05 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[31-May-2025 13:48:05 UTC] Member data for welcome email: Array
(
    [full_name] => Mgcini
    [first_name] => Mgcini
    [last_name] => 
    [email] => <EMAIL>
    [phone_number] => 0027710670804
    [home_address] => 34 Langeberg Bosmont Johannesburg Gauteng
    [occupation] => Signage
    [image_path] => uploads/683b08954225d.png
)

[31-May-2025 13:48:05 UTC] Selected welcome template ID: 38
[31-May-2025 13:48:05 UTC] Using template: Welcome Email Template
[31-May-2025 13:48:05 UTC] Template content length: 2705
[31-May-2025 13:48:05 UTC] Member data for template (first 3 items): full_name: Mgcini, email: <EMAIL>, no birth_date
[31-May-2025 13:48:05 UTC] Template after replacement: Welcome to Freedom Assembly, Mgcini!...
[31-May-2025 13:48:05 UTC] Member data for template (first 3 items): full_name: Mgcini, email: <EMAIL>, no birth_date
[31-May-2025 13:48:05 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[31-May-2025 13:48:05 UTC] Processed subject: Welcome to Freedom Assembly, Mgcini!
[31-May-2025 13:48:05 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Mgcini!
[01-Jun-2025 18:58:18 UTC] POST data received: Array
(
    [full_name] => Okoh Ekene Collins. 
    [occupation] => Business. 
    [email] => <EMAIL>
    [phone_number] => 0640136625
    [home_address] => No 40 first avenue Florida Johannesburg. 
    [birth_date] => 1982-04-20
    [message] => A breakthrough bigger than me and Gods mercy. 
)

[01-Jun-2025 18:58:18 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[01-Jun-2025 18:58:18 UTC] Member data for welcome email: Array
(
    [full_name] => Okoh Ekene Collins.
    [first_name] => Okoh
    [last_name] => Ekene Collins.
    [email] => <EMAIL>
    [phone_number] => 0640136625
    [home_address] => No 40 first avenue Florida Johannesburg.
    [occupation] => Business.
    [image_path] => uploads/683ca2ca5b772.jpg
)

[01-Jun-2025 18:58:18 UTC] Selected welcome template ID: 38
[01-Jun-2025 18:58:18 UTC] Using template: Welcome Email Template
[01-Jun-2025 18:58:18 UTC] Template content length: 2705
[01-Jun-2025 18:58:18 UTC] Member data for template (first 3 items): full_name: Okoh Ekene Collins., email: <EMAIL>, no birth_date
[01-Jun-2025 18:58:18 UTC] Template after replacement: Welcome to Freedom Assembly, Okoh Ekene Collins.!...
[01-Jun-2025 18:58:18 UTC] Member data for template (first 3 items): full_name: Okoh Ekene Collins., email: <EMAIL>, no birth_date
[01-Jun-2025 18:58:18 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[01-Jun-2025 18:58:18 UTC] Processed subject: Welcome to Freedom Assembly, Okoh Ekene Collins.!
[01-Jun-2025 18:58:18 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Okoh Ekene Collins.!
[09-Jun-2025 13:53:06 UTC] POST data received: Array
(
    [full_name] => KINGSLEY ADAMS
    [occupation] => SALES REP/ SELF EMPLOYED
    [email] => <EMAIL>
    [phone_number] => +27 72 488 3288
    [home_address] => No. 474 CHIRONIA STREET
WILRO PARK, ROODEPOORT
JOHANNESBURG, GAUTENG, 1724
    [birth_date] => 1974-07-09
    [message] => 
)

[2025-06-09 13:53:06] File upload error: File upload error: File exceeds upload_max_filesize directive Context: {"file":"20190502_091657.jpg","type":"","size":0} Server Info: {"PHP_VERSION":"8.2.28","SERVER_SOFTWARE":"LiteSpeed","REQUEST_URI":"/church/process_registration.php","HTTP_REFERER":"https://freedomassemblydb.online/church/index.php","REMOTE_ADDR":"***************"}
[09-Jun-2025 13:57:13 UTC] POST data received: Array
(
    [full_name] => KINGSLEY ADAMS
    [occupation] => SALES REP/ SELF EMPLOYED
    [email] => <EMAIL>
    [phone_number] => +27 72 488 3288
    [home_address] => NO, 474 CHIRONIA STREET
WILRO PARK, ROODEPOORT
JOHANNESBURG, GAUTENG. 1724
    [birth_date] => 1974-07-09
    [message] => TO GIVE GOD ALL THE GLORY AND THANKSGIVING FOR HIS GRACE AND FAVOUR UPON ME, MY WIFE AND CHILDREN, MY BUSINESS AND ALSO MY WORK IN HIM.
FOR ME TO KNOW HIM MORE AND TO WORK MORE ACCORDING TO HIS LEADING 
)

[2025-06-09 13:57:13] File upload error: File upload error: File exceeds upload_max_filesize directive Context: {"file":"20190502_092436.jpg","type":"","size":0} Server Info: {"PHP_VERSION":"8.2.28","SERVER_SOFTWARE":"LiteSpeed","REQUEST_URI":"/church/process_registration.php","HTTP_REFERER":"https://freedomassemblydb.online/church/index.php?error=file_upload&message=File+upload+error%3A+File+exceeds+upload_max_filesize+directive","REMOTE_ADDR":"***************"}
[13-Jun-2025 11:32:47 UTC] No POST data received
[14-Jun-2025 22:06:26 UTC] No POST data received
[15-Jun-2025 11:29:37 UTC] POST data received: Array
(
    [full_name] => Oluwajomiloju Osoare
    [occupation] => Student/certified BA
    [email] => <EMAIL>
    [phone_number] => 0691045031
    [home_address] => 152 Marabou St Cradle Ridge Estate falls road Pineshaven Krugersdorp
    [birth_date] => 2005-06-21
    [message] => 
)

[15-Jun-2025 11:29:37 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[15-Jun-2025 11:29:37 UTC] Member data for welcome email: Array
(
    [full_name] => Oluwajomiloju Osoare
    [first_name] => Oluwajomiloju
    [last_name] => Osoare
    [email] => <EMAIL>
    [phone_number] => 0691045031
    [home_address] => 152 Marabou St Cradle Ridge Estate falls road Pineshaven Krugersdorp
    [occupation] => Student/certified BA
    [image_path] => uploads/684eaea158a0f.jpeg
)

[15-Jun-2025 11:29:37 UTC] Selected welcome template ID: 38
[15-Jun-2025 11:29:37 UTC] Using template: Welcome Email Template
[15-Jun-2025 11:29:37 UTC] Template content length: 2705
[15-Jun-2025 11:29:37 UTC] Member data for template (first 3 items): full_name: Oluwajomiloju Osoare, email: <EMAIL>, no birth_date
[15-Jun-2025 11:29:37 UTC] Template after replacement: Welcome to Freedom Assembly, Oluwajomiloju Osoare!...
[15-Jun-2025 11:29:37 UTC] Member data for template (first 3 items): full_name: Oluwajomiloju Osoare, email: <EMAIL>, no birth_date
[15-Jun-2025 11:29:37 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[15-Jun-2025 11:29:37 UTC] Processed subject: Welcome to Freedom Assembly, Oluwajomiloju Osoare!
[15-Jun-2025 11:29:37 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Oluwajomiloju Osoare!
[25-Jun-2025 05:57:20 UTC] POST data received: Array
(
    [full_name] => Livhuwani oriel matundu 
    [occupation] => Law enforcement officer 
    [email] => <EMAIL>
    [phone_number] => +27711675969
    [home_address] => 53 chameleon street 
Lion pride 
Village 1 
    [birth_date] => 1988-06-25
    [message] => 
)

[25-Jun-2025 05:57:20 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[25-Jun-2025 05:57:20 UTC] Member data for welcome email: Array
(
    [full_name] => Livhuwani oriel matundu
    [first_name] => Livhuwani
    [last_name] => oriel matundu
    [email] => <EMAIL>
    [phone_number] => +27711675969
    [home_address] => 53 chameleon street 
Lion pride 
Village 1
    [occupation] => Law enforcement officer
    [image_path] => uploads/685b8fc020f79.jpg
)

[25-Jun-2025 05:57:20 UTC] Selected welcome template ID: 38
[25-Jun-2025 05:57:20 UTC] Using template: Welcome Email Template
[25-Jun-2025 05:57:20 UTC] Template content length: 2705
[25-Jun-2025 05:57:20 UTC] Member data for template (first 3 items): full_name: Livhuwani oriel matundu, email: <EMAIL>, no birth_date
[25-Jun-2025 05:57:20 UTC] Template after replacement: Welcome to Freedom Assembly, Livhuwani oriel matundu!...
[25-Jun-2025 05:57:20 UTC] Member data for template (first 3 items): full_name: Livhuwani oriel matundu, email: <EMAIL>, no birth_date
[25-Jun-2025 05:57:20 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[25-Jun-2025 05:57:20 UTC] Processed subject: Welcome to Freedom Assembly, Livhuwani oriel matundu!
[25-Jun-2025 05:57:20 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Livhuwani oriel matundu!
[25-Jun-2025 06:32:54 UTC] POST data received: Array
(
    [full_name] => Adetayo Adeyefa 
    [occupation] => Property Manager  / Film and TV Production 
    [email] => <EMAIL>
    [phone_number] => +27677252635
    [home_address] => No 21 Mitchell Avenue  Discovery Roodepoort 
    [birth_date] => 1984-11-14
    [message] => 
)

[25-Jun-2025 06:32:54 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[25-Jun-2025 06:32:54 UTC] Member data for welcome email: Array
(
    [full_name] => Adetayo Adeyefa
    [first_name] => Adetayo
    [last_name] => Adeyefa
    [email] => <EMAIL>
    [phone_number] => +27677252635
    [home_address] => No 21 Mitchell Avenue  Discovery Roodepoort
    [occupation] => Property Manager  / Film and TV Production
    [image_path] => uploads/685b9816d1463.jpg
)

[25-Jun-2025 06:32:54 UTC] Selected welcome template ID: 38
[25-Jun-2025 06:32:54 UTC] Using template: Welcome Email Template
[25-Jun-2025 06:32:54 UTC] Template content length: 2705
[25-Jun-2025 06:32:54 UTC] Member data for template (first 3 items): full_name: Adetayo Adeyefa, email: <EMAIL>, no birth_date
[25-Jun-2025 06:32:54 UTC] Template after replacement: Welcome to Freedom Assembly, Adetayo Adeyefa!...
[25-Jun-2025 06:32:54 UTC] Member data for template (first 3 items): full_name: Adetayo Adeyefa, email: <EMAIL>, no birth_date
[25-Jun-2025 06:32:54 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[25-Jun-2025 06:32:54 UTC] Processed subject: Welcome to Freedom Assembly, Adetayo Adeyefa!
[25-Jun-2025 06:32:54 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, Adetayo Adeyefa!
[25-Jun-2025 08:49:22 UTC] POST data received: Array
(
    [full_name] => KINGSLEY ADAMS 
    [occupation] => SALES MANAGER/SELF EMPLOYED 
    [email] => <EMAIL>
    [phone_number] => +27724883288
    [home_address] => No 474, Chironia Street 
Wilro Park Roodepoort 
Johannesburg 1724
    [birth_date] => 1974-07-09
    [message] => 
)

[2025-06-25 08:49:22] Database error: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '<EMAIL>' for key 'email' Context: {"code":"23000","email":"<EMAIL>"} Server Info: {"PHP_VERSION":"8.2.28","SERVER_SOFTWARE":"LiteSpeed","REQUEST_URI":"/church/process_registration.php","HTTP_REFERER":"https://freedomassemblydb.online/church/index.php","REMOTE_ADDR":"*************"}
[25-Jun-2025 08:52:09 UTC] POST data received: Array
(
    [full_name] => KINGSLEY ADAMS 
    [occupation] => SALES MANAGER/SELF EMPLOYED 
    [email] => <EMAIL>
    [phone_number] => +27724883288
    [home_address] => No 474, Chironia Street 
Wilro Park Roodepoort 
Johannesburg 1724
    [birth_date] => 1974-07-09
    [message] => 
)

[25-Jun-2025 08:52:09 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[25-Jun-2025 08:52:09 UTC] Member data for welcome email: Array
(
    [full_name] => KINGSLEY ADAMS
    [first_name] => KINGSLEY
    [last_name] => ADAMS
    [email] => <EMAIL>
    [phone_number] => +27724883288
    [home_address] => No 474, Chironia Street 
Wilro Park Roodepoort 
Johannesburg 1724
    [occupation] => SALES MANAGER/SELF EMPLOYED
    [image_path] => uploads/685bb8b913378.jpg
)

[25-Jun-2025 08:52:09 UTC] Selected welcome template ID: 38
[25-Jun-2025 08:52:09 UTC] Using template: Welcome Email Template
[25-Jun-2025 08:52:09 UTC] Template content length: 2705
[25-Jun-2025 08:52:09 UTC] Member data for template (first 3 items): full_name: KINGSLEY ADAMS, email: <EMAIL>, no birth_date
[25-Jun-2025 08:52:09 UTC] Template after replacement: Welcome to Freedom Assembly, KINGSLEY ADAMS!...
[25-Jun-2025 08:52:09 UTC] Member data for template (first 3 items): full_name: KINGSLEY ADAMS, email: <EMAIL>, no birth_date
[25-Jun-2025 08:52:09 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[25-Jun-2025 08:52:09 UTC] Processed subject: Welcome to Freedom Assembly, KINGSLEY ADAMS!
[25-Jun-2025 08:52:09 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, KINGSLEY ADAMS!
[25-Jun-2025 12:05:42 UTC] POST data received: Array
(
    [full_name] => NARTYTRYUT365964NEYHRTGE
    [occupation] => Care
    [profile_image] => 
    [email] => <EMAIL>
    [phone_number] => 83928717181
    [home_address] => Madagascar
    [birth_date] => 1988-00-03
    [message] => 
    [submit] => 
)

[25-Jun-2025 12:05:42 UTC] Welcome email settings: Array
(
    [template_ids] => 38
)

[25-Jun-2025 12:05:42 UTC] Member data for welcome email: Array
(
    [full_name] => NARTYTRYUT365964NEYHRTGE
    [first_name] => NARTYTRYUT365964NEYHRTGE
    [last_name] => 
    [email] => <EMAIL>
    [phone_number] => 83928717181
    [home_address] => Madagascar
    [occupation] => Care
    [image_path] => 
)

[25-Jun-2025 12:05:42 UTC] Selected welcome template ID: 38
[25-Jun-2025 12:05:42 UTC] Using template: Welcome Email Template
[25-Jun-2025 12:05:42 UTC] Template content length: 2705
[25-Jun-2025 12:05:42 UTC] Member data for template (first 3 items): full_name: NARTYTRYUT365964NEYHRTGE, email: <EMAIL>, no birth_date
[25-Jun-2025 12:05:42 UTC] Template after replacement: Welcome to Freedom Assembly, NARTYTRYUT365964NEYHRTGE!...
[25-Jun-2025 12:05:42 UTC] Member data for template (first 3 items): full_name: NARTYTRYUT365964NEYHRTGE, email: <EMAIL>, no birth_date
[25-Jun-2025 12:05:42 UTC] Template after replacement: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7f9; padding: 30px;">
    <div style="text-align: center; background-color: #ffffff; p...
[25-Jun-2025 12:05:42 UTC] Processed subject: Welcome to Freedom Assembly, NARTYTRYUT365964NEYHRTGE!
[25-Jun-2025 12:05:42 UTC] Sending welcome email to: <EMAIL> with subject: Welcome to Freedom Assembly, NARTYTRYUT365964NEYHRTGE!
