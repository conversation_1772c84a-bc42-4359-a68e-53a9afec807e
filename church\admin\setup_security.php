<?php
/**
 * Security Setup Script
 * 
 * This script sets up the security tables and features for the church application.
 * It should be run once to initialize the security features.
 */

// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include the SecurityManager class
require_once '../classes/SecurityManager.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize SecurityManager
$security = new SecurityManager($conn);

// Set security headers
$security->setSecurityHeaders();

// Log this access
$security->logSecurityEvent('Security setup script accessed', [
    'admin_id' => $_SESSION['admin_id']
]);

$messages = [];
$errors = [];

// Function to run SQL query
function runQuery($conn, $query, $params = []) {
    try {
        $stmt = $conn->prepare($query);
        $stmt->execute($params);
        return true;
    } catch (PDOException $e) {
        return $e->getMessage();
    }
}

// Read and execute the SQL file
$sqlFilePaths = [
    '../sql/security_tables.sql',              // Relative path from admin directory
    dirname(__DIR__) . '/sql/security_tables.sql', // Absolute path
    'sql/security_tables.sql',                 // Direct path if ran from root
    __DIR__ . '/sql/security_tables.sql'       // Within admin/sql directory
];

$sqlFile = '';
$loadedPath = '';

foreach ($sqlFilePaths as $path) {
    if (file_exists($path)) {
        $sqlFile = file_get_contents($path);
        $loadedPath = $path;
        $messages[] = "Successfully loaded SQL file from: $path";
        break;
    }
}

if (empty($sqlFile)) {
    $errors[] = "Could not find security_tables.sql file in any of the expected locations.";
    $errors[] = "Searched paths: " . implode(', ', $sqlFilePaths);
    
    // Create log directory if it doesn't exist
    $logDir = dirname(__DIR__) . '/logs';
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    // Log the error
    error_log("Error in setup_security.php: Could not find security_tables.sql in any path: " . implode(', ', $sqlFilePaths));
} else {
    $queries = array_filter(array_map('trim', explode(';', $sqlFile)));

    foreach ($queries as $query) {
        if (empty($query)) continue;
        
        $result = runQuery($conn, $query);
        if ($result === true) {
            if (stripos($query, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`(\w+)`/i', $query, $matches);
                if (isset($matches[1])) {
                    $messages[] = "Created table: " . $matches[1];
                }
            } else if (stripos($query, 'ALTER TABLE') !== false) {
                preg_match('/ALTER TABLE.*?`(\w+)`/i', $query, $matches);
                if (isset($matches[1])) {
                    $messages[] = "Updated table: " . $matches[1];
                }
            }
        } else {
            $errors[] = $result;
        }
    }
}

// Include header
$pageTitle = "Security Setup";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Security Setup</h1>
            </div>

            <?php if (!empty($messages)): ?>
                <div class="alert alert-success">
                    <h4 class="alert-heading">Setup Completed Successfully!</h4>
                    <ul class="mb-0">
                        <?php foreach ($messages as $message): ?>
                            <li><?php echo htmlspecialchars($message); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h4 class="alert-heading">Errors Occurred During Setup</h4>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Next Steps</h5>
                    <ul>
                        <li>View security logs in the <a href="security_audit.php">Security Audit</a> page</li>
                        <li>Configure two-factor authentication in your <a href="profile.php">Profile</a></li>
                        <li>Review and update <a href="security_settings.php">Security Settings</a></li>
                    </ul>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 