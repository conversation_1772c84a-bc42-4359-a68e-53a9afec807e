name: Codecov
on:
  pull_request:
  push:
    branches:
      - "[0-9]+.[0-9]+.x"
  # Allow manually triggering the workflow.
  workflow_dispatch:

jobs:
  run:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php: ['8.3']
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up PHP ${{ matrix.php }}
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          ini-values: assert.exception=1, zend.assertions=1, error_reporting=-1, log_errors_max_len=0, display_errors=On
          tools: composer:v2
          extensions: mongodb, redis

      - name: Get composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        working-directory: ${{ github.workspace }}
        run: |
          composerCommand="install"
          composerOptions=("--no-interaction" "--no-progress" "--ansi")
          # Use `update` if there is no composer.lock file
          if [ ! -f "composer.lock" ]; then
            composerCommand="update"
          fi
          fullCommand="composer ${composerCommand} ${composerOptions[*]}"
          echo "Running: ${fullCommand}"
          ${fullCommand}

      - name: Run PHPUnit to collect coverage
        run: composer phpunit:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
