# Database Export Instructions 
 
This folder contains a chunked export of your database to avoid corruption errors. 
 
## Instructions for Importing 
 
### Option 1: Import individual files in sequence 
1. First import the structure: 01_structure.sql 
2. Then import each data file (02_data_*.sql) 
3. Finally import the clean view: 03_clean_views.sql 
 
### Option 2: Using phpMyAdmin 
1. Upload all files to your server 
2. In phpMyAdmin, create your database 
3. Import files in this order: 
   - 00_email_tracking_tables.sql 
   - 01_structure.sql 
   - All 02_data_*.sql files 
   - 03_clean_views.sql 
 
### Option 3: Using the import_all.sql script 
If your hosting allows using SOURCE commands, you can upload all 
files to the same directory and run the import_all.sql script. 
 
## Notes: 
- All exports are free of DEFINER clauses that cause permission issues 
- Each table is exported separately to avoid long lines that cause errors 
- The view is created last to ensure all required tables exist first 
