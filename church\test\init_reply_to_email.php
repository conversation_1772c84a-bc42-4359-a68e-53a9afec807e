<?php
// <PERSON><PERSON><PERSON> to ensure reply-to email setting is properly configured
require_once __DIR__ . '/../config.php';

try {
    echo "<h2>Configuring Reply-To Email Setting</h2>";
    
    // First check if we have a reply_to_email in email_settings
    $stmt = $pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'reply_to_email' LIMIT 1");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $reply_to_email = null;
    
    if ($result && !empty($result['setting_value'])) {
        $reply_to_email = $result['setting_value'];
        echo "<p>Found existing reply_to_email: " . htmlspecialchars($reply_to_email) . "</p>";
    } else {
        // Check settings table as fallback
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'email_reply_to_email' LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result && !empty($result['setting_value'])) {
            $reply_to_email = $result['setting_value'];
            echo "<p>Found reply-to email in settings table: " . htmlspecialchars($reply_to_email) . "</p>";
        }
    }
    
    // If we still don't have a reply-to email, use the sender email as fallback
    if (!$reply_to_email) {
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'email_sender_email' LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result && !empty($result['setting_value'])) {
            $reply_to_email = $result['setting_value'];
            echo "<p>Using sender email as reply-to: " . htmlspecialchars($reply_to_email) . "</p>";
        } else {
            $reply_to_email = '' . CHURCH_EMAIL . ''; // Default fallback
            echo "<p>Using default reply-to email: " . htmlspecialchars($reply_to_email) . "</p>";
        }
    }
    
    // Ensure the setting exists in both tables with the correct key
    $settings_to_update = [
        ['table' => 'email_settings', 'key' => 'reply_to_email'],
        ['table' => 'settings', 'key' => 'email_reply_to_email']
    ];
    
    foreach ($settings_to_update as $setting) {
        $stmt = $pdo->prepare("INSERT INTO {$setting['table']} (setting_key, setting_value) 
                              VALUES (?, ?) 
                              ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        $stmt->execute([$setting['key'], $reply_to_email]);
        echo "<p>Updated {$setting['table']} with {$setting['key']}</p>";
    }
    
    echo "<h3>Reply-To Email Configuration Complete</h3>";
    echo "<p>The reply-to email is now set to: " . htmlspecialchars($reply_to_email) . "</p>";
    echo "<p>You can update this value in the <a href='../admin/email_settings.php'>Email Settings</a> page.</p>";
    
} catch (PDOException $e) {
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
} 