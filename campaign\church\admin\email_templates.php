<?php
session_start();

// Check for flash messages
$flash_message = '';
$flash_type = '';
if (isset($_SESSION['flash_message'])) {
    $flash_message = $_SESSION['flash_message'];
    $flash_type = $_SESSION['flash_type'] ?? 'success';
    unset($_SESSION['flash_message']);
    unset($_SESSION['flash_type']);
}

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';
$template = null;

// Delete template
if (isset($_GET['delete_id']) && !empty($_GET['delete_id'])) {
    $delete_id = intval($_GET['delete_id']);
    
    // Check if template is in use
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM email_logs WHERE template_id = ?");
    $stmt->execute([$delete_id]);
    $result = $stmt->fetch();
    $is_in_use = $result['count'] > 0;
    
    if ($is_in_use) {
        $error = "Cannot delete template as it is referenced in the email logs.";
    } else {
        $stmt = $conn->prepare("DELETE FROM email_templates WHERE id = ?");
        if ($stmt->execute([$delete_id])) {
            $message = "Template deleted successfully!";
        } else {
            $error = "Error deleting template: " . $conn->errorInfo()[2];
        }
    }
}

// Edit template - redirect to create_template.php with edit_id parameter
if (isset($_GET['edit_id']) && !empty($_GET['edit_id'])) {
    header("Location: create_template.php?edit_id=" . intval($_GET['edit_id']));
    exit();
}

// Set up sorting and pagination parameters
$sort_column = isset($_GET['sort']) ? $_GET['sort'] : 'id';
$sort_order = isset($_GET['order']) ? $_GET['order'] : 'DESC';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = 10; // Items per page

// Validate sort column to prevent SQL injection
$valid_columns = ['id', 'template_name', 'subject', 'is_birthday_template', 'template_category', 'created_at'];
if (!in_array($sort_column, $valid_columns)) {
    $sort_column = 'id';
}

// Validate sort order
if ($sort_order != 'ASC' && $sort_order != 'DESC') {
    $sort_order = 'DESC';
}

// Get total count for pagination
if (!empty($search)) {
    $count_stmt = $conn->prepare("SELECT COUNT(*) FROM email_templates 
                                WHERE template_name LIKE ? OR subject LIKE ?");
    $count_stmt->execute(['%' . $search . '%', '%' . $search . '%']);
} else {
    $count_stmt = $conn->query("SELECT COUNT(*) FROM email_templates");
}
$total_items = $count_stmt->fetchColumn();
$total_pages = ceil($total_items / $per_page);
$page = min($page, max(1, $total_pages)); // Ensure page is within valid range
$offset = ($page - 1) * $per_page;

// Get templates with pagination, sorting, and optional search
if (!empty($search)) {
    $stmt = $conn->prepare("SELECT * FROM email_templates 
                           WHERE template_name LIKE ? OR subject LIKE ? 
                           ORDER BY $sort_column $sort_order
                           LIMIT $offset, $per_page");
    $stmt->execute(['%' . $search . '%', '%' . $search . '%']);
} else {
    $stmt = $conn->prepare("SELECT * FROM email_templates 
                           ORDER BY $sort_column $sort_order
                           LIMIT $offset, $per_page");
    $stmt->execute();
}
$templates = $stmt->fetchAll();

// Close the database connection
$conn = null;

// Set page variables
$page_title = 'Email Templates';
$page_header = 'Email Templates';
$page_description = 'Manage email templates for birthday messages and other communications.';

// Include header
include 'includes/header.php';

// Display flash message if it exists
if (!empty($flash_message)) {
    $alert_class = $flash_type === 'success' ? 'alert-success' : 'alert-danger';
    $icon_class = $flash_type === 'success' ? 'bi-check-circle-fill' : 'bi-exclamation-triangle-fill';
    echo '<div class="alert ' . $alert_class . ' alert-dismissible fade show" role="alert">
            <i class="bi ' . $icon_class . ' me-2"></i>' . htmlspecialchars($flash_message) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

// Display regular error message if it exists
if (!empty($error)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($error) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}
?>

<!-- Instructions panel -->
<div class="alert alert-info mb-4 instruction-panel">
    <div class="d-flex justify-content-between align-items-start">
        <h5><i class="bi bi-info-circle-fill me-2"></i>About Email Templates</h5>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <p class="mb-2">Email templates help you send consistent, professional communications to your church members.</p>
    <ul class="mb-0">
        <li><strong>Birthday Templates:</strong> Used for automated birthday greetings</li>
        <li><strong>Bulk Email Templates:</strong> Used for newsletters and announcements</li>
        <li><strong>Reminder Templates:</strong> Used for follow-ups and special notices</li>
    </ul>
    <div class="mt-2">
        <strong>Note:</strong> Newsletter templates will not include member profile images to maintain a consistent appearance.
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <!-- Search form -->
        <form action="" method="GET" class="d-flex">
            <input type="text" name="search" class="form-control me-2" placeholder="Search templates..." value="<?php echo htmlspecialchars($search); ?>" data-bs-toggle="tooltip" data-bs-placement="top" title="Search by template name or content">
            <button type="submit" class="btn btn-outline-primary">Search</button>
            <?php if (!empty($search)): ?>
                <a href="email_templates.php" class="btn btn-outline-secondary ms-2">Clear</a>
            <?php endif; ?>
        </form>
    </div>
    <div class="col-md-6 text-end">
        <a href="create_template.php" class="btn btn-primary" data-bs-toggle="tooltip" data-bs-placement="left" title="Create a new email template for birthdays, newsletters, or other communications">
            <i class="bi bi-plus-circle"></i> Create New Template
        </a>
    </div>
</div>

<!-- Display message about search results if searching -->
<?php if (!empty($search)): ?>
<div class="alert alert-info">
    <p>Showing <?php echo count($templates); ?> results for "<?php echo htmlspecialchars($search); ?>"</p>
</div>
<?php endif; ?>

<!-- Templates Table -->
<div class="card">
    <div class="card-body">
        <?php if (count($templates) > 0): ?>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>
                            <a href="?sort=id&order=<?php echo ($sort_column == 'id' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                                ID
                                <?php if ($sort_column == 'id'): ?>
                                <i class="bi bi-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="?sort=template_name&order=<?php echo ($sort_column == 'template_name' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                                Template Name
                                <?php if ($sort_column == 'template_name'): ?>
                                <i class="bi bi-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="?sort=subject&order=<?php echo ($sort_column == 'subject' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                                Subject
                                <?php if ($sort_column == 'subject'): ?>
                                <i class="bi bi-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="?sort=template_category&order=<?php echo ($sort_column == 'template_category' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                                Type
                                <?php if ($sort_column == 'template_category'): ?>
                                <i class="bi bi-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="?sort=created_at&order=<?php echo ($sort_column == 'created_at' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                                Created
                                <?php if ($sort_column == 'created_at'): ?>
                                <i class="bi bi-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $row_number = 1;
                    foreach ($templates as $t): 
                    ?>
                    <tr>
                        <td><?php echo $row_number++; ?></td>
                        <td><?php echo $t['id']; ?></td>
                        <td><?php echo htmlspecialchars($t['template_name']); ?></td>
                        <td><?php echo htmlspecialchars($t['subject']); ?></td>
                        <td>
                            <?php 
                            // Check if it's a birthday template first, regardless of category
                            if ($t['is_birthday_template']) {
                                $badge_class = 'bg-warning';
                                $category_label = 'Birthday';
                            } else {
                                // Otherwise use the category from the database
                                $category = $t['template_category'] ?? 'general';
                                switch ($category) {
                                    case 'bulk':
                                        $badge_class = 'bg-primary';
                                        $category_label = 'Bulk';
                                        break;
                                    case 'birthday':
                                        $badge_class = 'bg-warning';
                                        $category_label = 'Birthday';
                                        break;
                                    default:
                                        $badge_class = 'bg-info';
                                        $category_label = 'General';
                                }
                            }
                            ?>
                            <span class="badge <?php echo $badge_class; ?>"><?php echo $category_label; ?></span>
                        </td>
                        <td><?php echo date('M d, Y', strtotime($t['created_at'])); ?></td>
                        <td>
                            <a href="preview_template.php?id=<?php echo $t['id']; ?>" class="btn btn-sm btn-info">
                                <i class="bi bi-eye"></i>
                            </a>
                            <a href="create_template.php?edit_id=<?php echo $t['id']; ?>" class="btn btn-sm btn-primary">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <a href="javascript:void(0);" onclick="confirmDelete(<?php echo $t['id']; ?>)" class="btn btn-sm btn-danger">
                                <i class="bi bi-trash"></i>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <p class="text-center">No templates found. <a href="create_template.php">Create your first template!</a></p>
        <?php endif; ?>
    </div>
    
    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
    <div class="card-footer">
        <nav aria-label="Template pagination">
            <ul class="pagination justify-content-center mb-0">
                <!-- Previous page -->
                <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $page-1; ?>&sort=<?php echo $sort_column; ?>&order=<?php echo $sort_order; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                
                <!-- Page numbers -->
                <?php
                $start_page = max(1, $page - 2);
                $end_page = min($total_pages, $page + 2);
                
                if ($start_page > 1) {
                    echo '<li class="page-item"><a class="page-link" href="?page=1&sort=' . $sort_column . '&order=' . $sort_order . (!empty($search) ? '&search='.urlencode($search) : '') . '">1</a></li>';
                    if ($start_page > 2) {
                        echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                    }
                }
                
                for ($i = $start_page; $i <= $end_page; $i++) {
                    echo '<li class="page-item ' . ($page == $i ? 'active' : '') . '"><a class="page-link" href="?page=' . $i . '&sort=' . $sort_column . '&order=' . $sort_order . (!empty($search) ? '&search='.urlencode($search) : '') . '">' . $i . '</a></li>';
                }
                
                if ($end_page < $total_pages) {
                    if ($end_page < $total_pages - 1) {
                        echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                    }
                    echo '<li class="page-item"><a class="page-link" href="?page=' . $total_pages . '&sort=' . $sort_column . '&order=' . $sort_order . (!empty($search) ? '&search='.urlencode($search) : '') . '">' . $total_pages . '</a></li>';
                }
                ?>
                
                <!-- Next page -->
                <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $page+1; ?>&sort=<?php echo $sort_column; ?>&order=<?php echo $sort_order; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this template? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>

<script>
    function confirmDelete(templateId) {
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        const confirmBtn = document.getElementById('confirmDeleteBtn');
        confirmBtn.href = `?delete_id=${templateId}`;
        deleteModal.show();
    }
</script>

<?php include_once 'includes/footer.php'; ?> 