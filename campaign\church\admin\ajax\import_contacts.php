<?php
session_start();
require_once '../../includes/db.php';
require_once '../../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
    exit;
}

header('Content-Type: application/json');

try {
    // Check if file was uploaded
    if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('No file uploaded or upload error occurred');
    }
    
    $file = $_FILES['import_file'];
    $skipDuplicates = isset($_POST['skip_duplicates']) && $_POST['skip_duplicates'] === '1';
    
    // Validate file type
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if ($fileExtension !== 'csv') {
        throw new Exception('Only CSV files are allowed');
    }
    
    // Validate file size (2MB limit)
    if ($file['size'] > 2 * 1024 * 1024) {
        throw new Exception('File size must be less than 2MB');
    }
    
    // Read CSV file
    $csvData = [];
    if (($handle = fopen($file['tmp_name'], 'r')) !== FALSE) {
        // Read header row
        $headers = fgetcsv($handle);
        if (!$headers) {
            throw new Exception('Invalid CSV file - no headers found');
        }
        
        // Normalize headers (case-insensitive)
        $headers = array_map('strtolower', array_map('trim', $headers));
        
        // Find required columns
        $nameIndex = array_search('name', $headers);
        $emailIndex = array_search('email', $headers);
        $groupsIndex = array_search('groups', $headers);
        
        if ($nameIndex === false || $emailIndex === false) {
            throw new Exception('CSV must contain "Name" and "Email" columns');
        }
        
        // Read data rows
        while (($row = fgetcsv($handle)) !== FALSE) {
            if (count($row) >= 2 && !empty(trim($row[$nameIndex])) && !empty(trim($row[$emailIndex]))) {
                $csvData[] = [
                    'name' => trim($row[$nameIndex]),
                    'email' => trim($row[$emailIndex]),
                    'groups' => $groupsIndex !== false && isset($row[$groupsIndex]) ? trim($row[$groupsIndex]) : ''
                ];
            }
        }
        fclose($handle);
    } else {
        throw new Exception('Could not read CSV file');
    }
    
    if (empty($csvData)) {
        throw new Exception('No valid data found in CSV file');
    }
    
    // Start transaction
    $pdo->beginTransaction();
    
    $importedCount = 0;
    $skippedCount = 0;
    $errors = [];
    
    foreach ($csvData as $index => $contact) {
        try {
            // Validate email
            if (!filter_var($contact['email'], FILTER_VALIDATE_EMAIL)) {
                $errors[] = "Row " . ($index + 2) . ": Invalid email format";
                continue;
            }
            
            // Check for duplicates if skip_duplicates is enabled
            if ($skipDuplicates) {
                $checkStmt = $pdo->prepare("SELECT id FROM contacts WHERE email = ?");
                $checkStmt->execute([$contact['email']]);
                if ($checkStmt->fetch()) {
                    $skippedCount++;
                    continue;
                }
            }
            
            // Insert contact
            $insertStmt = $pdo->prepare("
                INSERT INTO contacts (name, email, created_at) 
                VALUES (?, ?, NOW())
                ON DUPLICATE KEY UPDATE 
                name = VALUES(name), 
                updated_at = NOW()
            ");
            
            $insertStmt->execute([
                $contact['name'],
                $contact['email']
            ]);
            
            $contactId = $pdo->lastInsertId();
            if (!$contactId) {
                // Contact already exists, get its ID
                $getIdStmt = $pdo->prepare("SELECT id FROM contacts WHERE email = ?");
                $getIdStmt->execute([$contact['email']]);
                $contactId = $getIdStmt->fetchColumn();
            }
            
            // Handle groups if specified
            if (!empty($contact['groups']) && $contactId) {
                $groups = array_map('trim', explode(',', $contact['groups']));
                
                foreach ($groups as $groupName) {
                    if (empty($groupName)) continue;
                    
                    // Find or create group
                    $groupStmt = $pdo->prepare("SELECT id FROM contact_groups WHERE name = ?");
                    $groupStmt->execute([$groupName]);
                    $groupId = $groupStmt->fetchColumn();
                    
                    if (!$groupId) {
                        // Create new group
                        $createGroupStmt = $pdo->prepare("
                            INSERT INTO contact_groups (name, description, created_at) 
                            VALUES (?, ?, NOW())
                        ");
                        $createGroupStmt->execute([$groupName, "Auto-created during import"]);
                        $groupId = $pdo->lastInsertId();
                    }
                    
                    // Add contact to group (ignore duplicates)
                    $addToGroupStmt = $pdo->prepare("
                        INSERT IGNORE INTO contact_group_members (contact_id, group_id, added_at) 
                        VALUES (?, ?, NOW())
                    ");
                    $addToGroupStmt->execute([$contactId, $groupId]);
                }
            }
            
            $importedCount++;
            
        } catch (Exception $e) {
            $errors[] = "Row " . ($index + 2) . ": " . $e->getMessage();
        }
    }
    
    // Commit transaction
    $pdo->commit();
    
    $response = [
        'success' => true,
        'imported_count' => $importedCount,
        'skipped_count' => $skippedCount,
        'total_processed' => count($csvData)
    ];
    
    if (!empty($errors)) {
        $response['warnings'] = $errors;
        $response['message'] = "Import completed with warnings. $importedCount contacts imported, $skippedCount skipped.";
    } else {
        $response['message'] = "Import successful! $importedCount contacts imported.";
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
