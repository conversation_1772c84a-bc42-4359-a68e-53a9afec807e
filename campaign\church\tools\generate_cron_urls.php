<?php
/**
 * Dynamic Cron URL Generator
 * 
 * This script generates the correct cron job commands for your specific domain and path.
 * It automatically detects your current setup and provides ready-to-use cron commands.
 */

// Include environment configuration
require_once dirname(__DIR__) . '/environment.php';

// Set content type
header('Content-Type: text/html; charset=UTF-8');

// Get current URL information
$currentUrl = getCurrentUrl();
$baseUrl = SITE_URL;
$cronUrl = CRON_URL;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Cron URL Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .copy-btn {
            position: relative;
            float: right;
            margin-top: -2.5rem;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🔧 Dynamic Cron URL Generator</h1>
        
        <div class="alert alert-info">
            <h5>📍 Detected Configuration</h5>
            <strong>Current URL:</strong> <?php echo htmlspecialchars($currentUrl); ?><br>
            <strong>Base URL:</strong> <?php echo htmlspecialchars($baseUrl); ?><br>
            <strong>Cron URL:</strong> <?php echo htmlspecialchars($cronUrl); ?><br>
            <strong>Environment:</strong> <?php echo htmlspecialchars($GLOBALS['environment'] ?? 'Unknown'); ?>
        </div>

        <div class="row">
            <div class="col-12">
                <h3>🚀 Ready-to-Use Cron Job Commands</h3>
                <p class="text-muted">Copy these commands directly into your hosting provider's cron job manager:</p>
                
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>1. Birthday Reminders (Every 15 minutes at 1 AM)</h5>
                    </div>
                    <div class="card-body">
                        <div class="code-block">*/15 1 * * * wget -q -O /dev/null "<?php echo getCronUrl('birthday_reminders.php'); ?>"</div>
                        <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyToClipboard(this.previousElementSibling.textContent)">Copy</button>
                    </div>
                </div>

                <div class="card mb-3">
                    <div class="card-header">
                        <h5>2. Email Queue Processing (Every 5 minutes) - CRITICAL</h5>
                    </div>
                    <div class="card-body">
                        <div class="code-block">*/5 * * * * wget -q -O /dev/null "<?php echo getCronUrl('process_email_queue.php'); ?>"</div>
                        <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyToClipboard(this.previousElementSibling.textContent)">Copy</button>
                    </div>
                </div>

                <div class="card mb-3">
                    <div class="card-header">
                        <h5>3. Scheduled Emails (Every 5 minutes) - CRITICAL</h5>
                    </div>
                    <div class="card-body">
                        <div class="code-block">*/5 * * * * wget -q -O /dev/null "<?php echo getCronUrl('process_scheduled_emails.php'); ?>"</div>
                        <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyToClipboard(this.previousElementSibling.textContent)">Copy</button>
                    </div>
                </div>

                <div class="card mb-3">
                    <div class="card-header">
                        <h5>4. System Cleanup (Weekly - Sundays at 2 AM)</h5>
                    </div>
                    <div class="card-body">
                        <div class="code-block">0 2 * * 0 wget -q -O /dev/null "<?php echo getCronUrl('system_cleanup.php'); ?>"</div>
                        <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyToClipboard(this.previousElementSibling.textContent)">Copy</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h3>🧪 Test Links</h3>
                <p class="text-muted">Click these links to test if your cron jobs are accessible:</p>
                
                <div class="list-group">
                    <a href="<?php echo getCronUrl('birthday_reminders.php'); ?>" target="_blank" class="list-group-item list-group-item-action">
                        <strong>Birthday Reminders</strong><br>
                        <small class="text-muted"><?php echo getCronUrl('birthday_reminders.php'); ?></small>
                    </a>
                    <a href="<?php echo getCronUrl('process_email_queue.php'); ?>" target="_blank" class="list-group-item list-group-item-action">
                        <strong>Email Queue Processing</strong><br>
                        <small class="text-muted"><?php echo getCronUrl('process_email_queue.php'); ?></small>
                    </a>
                    <a href="<?php echo getCronUrl('process_scheduled_emails.php'); ?>" target="_blank" class="list-group-item list-group-item-action">
                        <strong>Scheduled Emails</strong><br>
                        <small class="text-muted"><?php echo getCronUrl('process_scheduled_emails.php'); ?></small>
                    </a>
                    <a href="<?php echo getCronUrl('system_cleanup.php'); ?>" target="_blank" class="list-group-item list-group-item-action">
                        <strong>System Cleanup</strong><br>
                        <small class="text-muted"><?php echo getCronUrl('system_cleanup.php'); ?></small>
                    </a>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-warning">
                    <h5>⚠️ Important Notes</h5>
                    <ul>
                        <li>Commands #2 and #3 are <strong>CRITICAL FIXES</strong> - they were broken before</li>
                        <li>All URLs are dynamically generated for your current setup</li>
                        <li>Test email delivery to <strong><EMAIL></strong> after setup</li>
                        <li>The cron key <code>fac_2024_secure_cron_8x9q2p5m</code> is required for security</li>
                        <li>These URLs will work regardless of domain or subdirectory changes</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h3>🔧 Alternative Formats</h3>
                <p class="text-muted">If wget doesn't work, try these alternatives:</p>
                
                <div class="card">
                    <div class="card-header">
                        <h6>Using curl instead of wget:</h6>
                    </div>
                    <div class="card-body">
                        <div class="code-block">*/5 * * * * curl -s "<?php echo getCronUrl('process_email_queue.php'); ?>" > /dev/null</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show success message
                const toast = document.createElement('div');
                toast.className = 'alert alert-success position-fixed';
                toast.style.top = '20px';
                toast.style.right = '20px';
                toast.style.zIndex = '9999';
                toast.textContent = 'Copied to clipboard!';
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 2000);
            }).catch(function(err) {
                alert('Failed to copy to clipboard. Please copy manually.');
            });
        }
    </script>
</body>
</html>
