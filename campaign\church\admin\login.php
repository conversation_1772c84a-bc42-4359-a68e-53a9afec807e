<?php
// Include the configuration file
require_once '../config.php';

// Include the session manager first (it will handle session start)
require_once '../admin/includes/session-manager.php';

// After session is started and configured, check if already logged in
if (isset($_SESSION['admin_id'])) {
    header("Location: dashboard.php");
    exit();
}

// Include the SecurityManager class
require_once '../classes/SecurityManager.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize SecurityManager
$security = new SecurityManager($conn);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$lockoutMessage = '';
$timeoutMessage = '';

// Check for timeout message
if (isset($_GET['timeout']) && $_GET['timeout'] == 1) {
    $timeoutMessage = "Your session has expired due to inactivity. Please login again.";
}

// Process login
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        $username = $security->sanitizeInput($_POST['username'], 'text');
        $password = $_POST['password']; // Don't sanitize passwords
        
        // Check if account is locked
        if ($security->isAccountLocked($username)) {
            $lockoutMessage = "This account has been temporarily locked due to multiple failed login attempts. Please try again later or contact an administrator.";
        } else {
            // Retrieve admin with matching username
            $stmt = $conn->prepare("SELECT id, username, password, full_name FROM admins WHERE username = ?");
            $stmt->execute([$username]);
            
            if ($admin = $stmt->fetch()) {
                // Verify password
                if ($security->verifyPassword($password, $admin['password'])) {
                    // Password is correct, reset failed attempts if any
                    $security->resetFailedAttempts($username);
                    
                    // Set session variables
                    $_SESSION['admin_id'] = $admin['id'];
                    $_SESSION['admin_name'] = $admin['full_name'];
                    $_SESSION['admin_username'] = $admin['username'];
                    
                    // Log successful login
                    $security->logSecurityEvent('Successful login', [
                        'admin_id' => $admin['id'],
                        'username' => $admin['username']
                    ]);
                    
                    // Update last login information
                    $stmt = $conn->prepare("UPDATE admins SET last_login_at = NOW(), last_login_ip = ? WHERE id = ?");
                    $stmt->execute([$_SERVER['REMOTE_ADDR'] ?? 'unknown', $admin['id']]);
                    
                    // Check if 2FA is enabled for this admin
                    $stmt = $conn->prepare("SELECT id FROM admin_2fa WHERE admin_id = ? AND is_enabled = 1");
                    $stmt->execute([$admin['id']]);
                    
                    if ($stmt->fetch()) {
                        // 2FA is enabled, set session flag and redirect to 2FA verification
                        $_SESSION['2fa_pending'] = true;
                        $_SESSION['2fa_admin_id'] = $admin['id'];
                        
                        header("Location: verify_2fa.php");
                        exit();
                    } else {
                        // 2FA not enabled, redirect to dashboard
                        header("Location: dashboard.php");
                        exit();
                    }
                } else {
                    // Password is incorrect, record failed attempt
                    $security->recordFailedAttempt($username);
                    $error = "Invalid password. Please try again.";
                }
            } else {
                // Username not found, but still record the attempt to prevent username enumeration
                $security->recordFailedAttempt($username);
                $error = "Invalid username or password. Please try again.";
            }
        }
    }
}

// Close the database connection
$conn = null;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - <?php echo get_organization_name(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 50px;
        }
        .login-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
        }
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-logo h1 {
            color: #343a40;
        }
        .error-message {
            color: #dc3545;
            margin-bottom: 20px;
        }
        .lockout-message {
            color: #dc3545;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .password-requirements {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-logo">
                <h1><?php echo get_organization_type(); ?> Admin</h1>
                <p><?php echo get_organization_name(); ?></p>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="error-message text-center">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($lockoutMessage)): ?>
                <div class="lockout-message text-center">
                    <?php echo $lockoutMessage; ?>
                </div>
            <?php elseif (!empty($timeoutMessage)): ?>
                <div class="error-message text-center">
                    <?php echo $timeoutMessage; ?>
                </div>
            <?php else: ?>
                <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                    <?php echo $security->generateCSRFInput(); ?>
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Login</button>
                    </div>
                    <div class="mt-3 text-center">
                        <a href="forgot_password.php">Forgot Password?</a>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 