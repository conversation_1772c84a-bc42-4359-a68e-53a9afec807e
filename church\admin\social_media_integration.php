<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize variables
$message = '';
$error = '';
$social_settings = [];

// Function to get social media settings
function getSocialMediaSettings($conn) {
    try {
        $stmt = $conn->prepare("SELECT * FROM social_media_settings WHERE 1");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = $setting['setting_value'];
        }
        
        return $result;
    } catch (PDOException $e) {
        error_log("Error fetching social media settings: " . $e->getMessage());
        return [];
    }
}

// Function to update social media settings
function updateSocialMediaSetting($conn, $key, $value) {
    try {
        // Check if setting exists
        $stmt = $conn->prepare("SELECT COUNT(*) FROM social_media_settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $exists = (int)$stmt->fetchColumn();
        
        if ($exists) {
            // Update existing setting
            $stmt = $conn->prepare("UPDATE social_media_settings SET setting_value = ? WHERE setting_key = ?");
            $stmt->execute([$value, $key]);
        } else {
            // Insert new setting
            $stmt = $conn->prepare("INSERT INTO social_media_settings (setting_key, setting_value) VALUES (?, ?)");
            $stmt->execute([$key, $value]);
        }
        
        return true;
    } catch (PDOException $e) {
        error_log("Error updating social media setting: " . $e->getMessage());
        return false;
    }
}

// Check if social_media_settings table exists, if not create it
try {
    $stmt = $conn->prepare("SHOW TABLES LIKE 'social_media_settings'");
    $stmt->execute();
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        $sql = "CREATE TABLE social_media_settings (
            id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(255) NOT NULL UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $conn->exec($sql);
        
        // Insert default settings
        $defaultSettings = [
            'facebook_enabled' => '0',
            'facebook_app_id' => '',
            'facebook_app_secret' => '',
            'facebook_page_id' => '',
            'facebook_access_token' => '',
            'twitter_enabled' => '0',
            'twitter_api_key' => '',
            'twitter_api_secret' => '',
            'twitter_access_token' => '',
            'twitter_access_token_secret' => '',
            'instagram_enabled' => '0',
            'instagram_app_id' => '',
            'instagram_app_secret' => '',
            'instagram_access_token' => '',
            'social_login_enabled' => '0',
            'social_sharing_enabled' => '1',
            'auto_post_birthdays' => '0',
            'auto_post_events' => '0',
            'birthday_post_template' => 'Happy Birthday to {name}! 🎂 Wishing you a blessed day filled with joy and happiness. #BirthdayBlessings #FreedomAssemblyChurch',
            'event_post_template' => 'Join us for {event_name} on {event_date} at {event_time}! {event_description} #ChurchEvents #FreedomAssemblyChurch'
        ];
        
        foreach ($defaultSettings as $key => $value) {
            updateSocialMediaSetting($conn, $key, $value);
        }
    }
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
    error_log("Error creating social_media_settings table: " . $e->getMessage());
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Facebook Settings
        if (isset($_POST['facebook_settings'])) {
            $facebook_enabled = isset($_POST['facebook_enabled']) ? '1' : '0';
            $facebook_app_id = trim($_POST['facebook_app_id']);
            $facebook_app_secret = trim($_POST['facebook_app_secret']);
            $facebook_page_id = trim($_POST['facebook_page_id']);
            $facebook_access_token = trim($_POST['facebook_access_token']);
            
            updateSocialMediaSetting($conn, 'facebook_enabled', $facebook_enabled);
            updateSocialMediaSetting($conn, 'facebook_app_id', $facebook_app_id);
            updateSocialMediaSetting($conn, 'facebook_app_secret', $facebook_app_secret);
            updateSocialMediaSetting($conn, 'facebook_page_id', $facebook_page_id);
            updateSocialMediaSetting($conn, 'facebook_access_token', $facebook_access_token);
            
            $message = "Facebook settings updated successfully.";
        }
        
        // Twitter Settings
        if (isset($_POST['twitter_settings'])) {
            $twitter_enabled = isset($_POST['twitter_enabled']) ? '1' : '0';
            $twitter_api_key = trim($_POST['twitter_api_key']);
            $twitter_api_secret = trim($_POST['twitter_api_secret']);
            $twitter_access_token = trim($_POST['twitter_access_token']);
            $twitter_access_token_secret = trim($_POST['twitter_access_token_secret']);
            
            updateSocialMediaSetting($conn, 'twitter_enabled', $twitter_enabled);
            updateSocialMediaSetting($conn, 'twitter_api_key', $twitter_api_key);
            updateSocialMediaSetting($conn, 'twitter_api_secret', $twitter_api_secret);
            updateSocialMediaSetting($conn, 'twitter_access_token', $twitter_access_token);
            updateSocialMediaSetting($conn, 'twitter_access_token_secret', $twitter_access_token_secret);
            
            $message = "Twitter settings updated successfully.";
        }
        
        // Instagram Settings
        if (isset($_POST['instagram_settings'])) {
            $instagram_enabled = isset($_POST['instagram_enabled']) ? '1' : '0';
            $instagram_app_id = trim($_POST['instagram_app_id']);
            $instagram_app_secret = trim($_POST['instagram_app_secret']);
            $instagram_access_token = trim($_POST['instagram_access_token']);
            
            updateSocialMediaSetting($conn, 'instagram_enabled', $instagram_enabled);
            updateSocialMediaSetting($conn, 'instagram_app_id', $instagram_app_id);
            updateSocialMediaSetting($conn, 'instagram_app_secret', $instagram_app_secret);
            updateSocialMediaSetting($conn, 'instagram_access_token', $instagram_access_token);
            
            $message = "Instagram settings updated successfully.";
        }
        
        // General Social Media Settings
        if (isset($_POST['general_social_settings'])) {
            $social_login_enabled = isset($_POST['social_login_enabled']) ? '1' : '0';
            $social_sharing_enabled = isset($_POST['social_sharing_enabled']) ? '1' : '0';
            $auto_post_birthdays = isset($_POST['auto_post_birthdays']) ? '1' : '0';
            $auto_post_events = isset($_POST['auto_post_events']) ? '1' : '0';
            $birthday_post_template = trim($_POST['birthday_post_template']);
            $event_post_template = trim($_POST['event_post_template']);
            
            updateSocialMediaSetting($conn, 'social_login_enabled', $social_login_enabled);
            updateSocialMediaSetting($conn, 'social_sharing_enabled', $social_sharing_enabled);
            updateSocialMediaSetting($conn, 'auto_post_birthdays', $auto_post_birthdays);
            updateSocialMediaSetting($conn, 'auto_post_events', $auto_post_events);
            updateSocialMediaSetting($conn, 'birthday_post_template', $birthday_post_template);
            updateSocialMediaSetting($conn, 'event_post_template', $event_post_template);
            
            $message = "General social media settings updated successfully.";
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
        error_log("Error updating social media settings: " . $e->getMessage());
    }
}

// Get current settings
$social_settings = getSocialMediaSettings($conn);

// Set default values if not set
$defaults = [
    'facebook_enabled' => '0',
    'facebook_app_id' => '',
    'facebook_app_secret' => '',
    'facebook_page_id' => '',
    'facebook_access_token' => '',
    'twitter_enabled' => '0',
    'twitter_api_key' => '',
    'twitter_api_secret' => '',
    'twitter_access_token' => '',
    'twitter_access_token_secret' => '',
    'instagram_enabled' => '0',
    'instagram_app_id' => '',
    'instagram_app_secret' => '',
    'instagram_access_token' => '',
    'social_login_enabled' => '0',
    'social_sharing_enabled' => '1',
    'auto_post_birthdays' => '0',
    'auto_post_events' => '0',
    'birthday_post_template' => 'Happy Birthday to {name}! 🎂 Wishing you a blessed day filled with joy and happiness. #BirthdayBlessings #FreedomAssemblyChurch',
    'event_post_template' => 'Join us for {event_name} on {event_date} at {event_time}! {event_description} #ChurchEvents #FreedomAssemblyChurch'
];

foreach ($defaults as $key => $value) {
    if (!isset($social_settings[$key])) {
        $social_settings[$key] = $value;
    }
}

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Social Media Integration</h1>
            </div>
            
            <?php if (!empty($message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>General Social Media Settings</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="social_login_enabled" name="social_login_enabled" <?php echo $social_settings['social_login_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="social_login_enabled">Enable Social Login for Member Portal</label>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="social_sharing_enabled" name="social_sharing_enabled" <?php echo $social_settings['social_sharing_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="social_sharing_enabled">Enable Social Sharing Buttons</label>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="auto_post_birthdays" name="auto_post_birthdays" <?php echo $social_settings['auto_post_birthdays'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="auto_post_birthdays">Automatically Post Birthday Wishes to Social Media</label>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="auto_post_events" name="auto_post_events" <?php echo $social_settings['auto_post_events'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="auto_post_events">Automatically Post Church Events to Social Media</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="birthday_post_template" class="form-label">Birthday Post Template</label>
                                    <textarea class="form-control" id="birthday_post_template" name="birthday_post_template" rows="3"><?php echo htmlspecialchars($social_settings['birthday_post_template']); ?></textarea>
                                    <div class="form-text">Available placeholders: {name}, {age}, {date}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="event_post_template" class="form-label">Event Post Template</label>
                                    <textarea class="form-control" id="event_post_template" name="event_post_template" rows="3"><?php echo htmlspecialchars($social_settings['event_post_template']); ?></textarea>
                                    <div class="form-text">Available placeholders: {event_name}, {event_date}, {event_time}, {event_location}, {event_description}</div>
                                </div>
                                
                                <button type="submit" name="general_social_settings" class="btn btn-primary">Save General Settings</button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Facebook Integration</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="facebook_enabled" name="facebook_enabled" <?php echo $social_settings['facebook_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="facebook_enabled">Enable Facebook Integration</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="facebook_app_id" class="form-label">Facebook App ID</label>
                                    <input type="text" class="form-control" id="facebook_app_id" name="facebook_app_id" value="<?php echo htmlspecialchars($social_settings['facebook_app_id']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="facebook_app_secret" class="form-label">Facebook App Secret</label>
                                    <input type="password" class="form-control" id="facebook_app_secret" name="facebook_app_secret" value="<?php echo htmlspecialchars($social_settings['facebook_app_secret']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="facebook_page_id" class="form-label">Facebook Page ID</label>
                                    <input type="text" class="form-control" id="facebook_page_id" name="facebook_page_id" value="<?php echo htmlspecialchars($social_settings['facebook_page_id']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="facebook_access_token" class="form-label">Page Access Token</label>
                                    <input type="password" class="form-control" id="facebook_access_token" name="facebook_access_token" value="<?php echo htmlspecialchars($social_settings['facebook_access_token']); ?>">
                                </div>
                                
                                <button type="submit" name="facebook_settings" class="btn btn-primary">Save Facebook Settings</button>
                            </form>
                            
                            <div class="mt-3">
                                <h6>Setup Instructions:</h6>
                                <ol class="small">
                                    <li>Go to <a href="https://developers.facebook.com/" target="_blank">Facebook Developers</a></li>
                                    <li>Create a new app</li>
                                    <li>Set up Facebook Login</li>
                                    <li>Get your App ID and App Secret</li>
                                    <li>Generate a Page Access Token</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Twitter Integration</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="twitter_enabled" name="twitter_enabled" <?php echo $social_settings['twitter_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="twitter_enabled">Enable Twitter Integration</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="twitter_api_key" class="form-label">API Key</label>
                                    <input type="text" class="form-control" id="twitter_api_key" name="twitter_api_key" value="<?php echo htmlspecialchars($social_settings['twitter_api_key']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="twitter_api_secret" class="form-label">API Secret</label>
                                    <input type="password" class="form-control" id="twitter_api_secret" name="twitter_api_secret" value="<?php echo htmlspecialchars($social_settings['twitter_api_secret']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="twitter_access_token" class="form-label">Access Token</label>
                                    <input type="password" class="form-control" id="twitter_access_token" name="twitter_access_token" value="<?php echo htmlspecialchars($social_settings['twitter_access_token']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="twitter_access_token_secret" class="form-label">Access Token Secret</label>
                                    <input type="password" class="form-control" id="twitter_access_token_secret" name="twitter_access_token_secret" value="<?php echo htmlspecialchars($social_settings['twitter_access_token_secret']); ?>">
                                </div>
                                
                                <button type="submit" name="twitter_settings" class="btn btn-primary">Save Twitter Settings</button>
                            </form>
                            
                            <div class="mt-3">
                                <h6>Setup Instructions:</h6>
                                <ol class="small">
                                    <li>Go to <a href="https://developer.twitter.com/en/portal/dashboard" target="_blank">Twitter Developer Portal</a></li>
                                    <li>Create a new app</li>
                                    <li>Set up authentication</li>
                                    <li>Generate API keys and tokens</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Instagram Integration</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="instagram_enabled" name="instagram_enabled" <?php echo $social_settings['instagram_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="instagram_enabled">Enable Instagram Integration</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="instagram_app_id" class="form-label">App ID</label>
                                    <input type="text" class="form-control" id="instagram_app_id" name="instagram_app_id" value="<?php echo htmlspecialchars($social_settings['instagram_app_id']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="instagram_app_secret" class="form-label">App Secret</label>
                                    <input type="password" class="form-control" id="instagram_app_secret" name="instagram_app_secret" value="<?php echo htmlspecialchars($social_settings['instagram_app_secret']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="instagram_access_token" class="form-label">Access Token</label>
                                    <input type="password" class="form-control" id="instagram_access_token" name="instagram_access_token" value="<?php echo htmlspecialchars($social_settings['instagram_access_token']); ?>">
                                </div>
                                
                                <button type="submit" name="instagram_settings" class="btn btn-primary">Save Instagram Settings</button>
                            </form>
                            
                            <div class="mt-3">
                                <h6>Setup Instructions:</h6>
                                <ol class="small">
                                    <li>Go to <a href="https://developers.facebook.com/" target="_blank">Facebook Developers</a></li>
                                    <li>Create a new app</li>
                                    <li>Add Instagram Basic Display</li>
                                    <li>Configure app settings</li>
                                    <li>Generate access token</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Social Media Integration Preview</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Social Sharing Buttons</h6>
                                    <div class="mb-4 p-3 border rounded">
                                        <h6>Sample Birthday Event</h6>
                                        <p><strong>John Doe's Birthday</strong><br>January 15, 2023</p>
                                        
                                        <div class="social-sharing-buttons">
                                            <button class="btn btn-sm btn-primary"><i class="bi bi-facebook"></i> Share</button>
                                            <button class="btn btn-sm btn-info text-white"><i class="bi bi-twitter"></i> Tweet</button>
                                            <button class="btn btn-sm btn-danger"><i class="bi bi-instagram"></i> Share</button>
                                            <button class="btn btn-sm btn-success"><i class="bi bi-whatsapp"></i> Share</button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6>Social Login Buttons</h6>
                                    <div class="mb-4 p-3 border rounded">
                                        <p>Login with:</p>
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-primary"><i class="bi bi-facebook"></i> Continue with Facebook</button>
                                            <button class="btn btn-info text-white"><i class="bi bi-twitter"></i> Continue with Twitter</button>
                                            <button class="btn btn-danger"><i class="bi bi-google"></i> Continue with Google</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <h6>Automated Social Media Post Preview</h6>
                                    <div class="mb-4 p-3 border rounded">
                                        <div class="card mb-3">
                                            <div class="card-header bg-primary text-white">
                                                <i class="bi bi-facebook"></i> Facebook Post Preview
                                            </div>
                                            <div class="card-body">
                                                <p><?php echo str_replace(
                                                    ['{name}', '{age}', '{date}'],
                                                    ['John Doe', '35', date('F j, Y')],
                                                    htmlspecialchars($social_settings['birthday_post_template'])
                                                ); ?></p>
                                            </div>
                                        </div>
                                        
                                        <div class="card">
                                            <div class="card-header bg-info text-white">
                                                <i class="bi bi-twitter"></i> Twitter Post Preview
                                            </div>
                                            <div class="card-body">
                                                <p><?php echo str_replace(
                                                    ['{event_name}', '{event_date}', '{event_time}', '{event_location}', '{event_description}'],
                                                    ['Sunday Service', 'Sunday, ' . date('F j, Y'), '10:00 AM', 'Main Sanctuary', 'Join us for a powerful time of worship and the Word!'],
                                                    htmlspecialchars($social_settings['event_post_template'])
                                                ); ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <p class="text-muted small">Note: The actual functionality depends on the settings configured above.</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 