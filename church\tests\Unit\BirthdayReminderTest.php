<?php
use PHPUnit\Framework\TestCase;

class BirthdayReminderTest extends TestCase
{
    private $pdo;
    private $reminder;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Reset test database
        resetTestDatabase();
        
        // Create instance of BirthdayReminder
        $this->reminder = new BirthdayReminder($GLOBALS['pdo']);
    }
    
    public function testGetUpcomingBirthdays()
    {
        // Create test members with birthdays
        $todayMemberId = createTestMember([
            'first_name' => 'Today',
            'birth_date' => date('Y-m-d')
        ]);
        
        $tomorrowMemberId = createTestMember([
            'first_name' => 'Tomorrow',
            'birth_date' => date('Y-m-d', strtotime('+1 day'))
        ]);
        
        // Test getting today's birthdays
        $todayBirthdays = $this->reminder->getUpcomingBirthdays(0);
        $this->assertCount(1, $todayBirthdays);
        $this->assertEquals('Today', $todayBirthdays[0]['first_name']);
        
        // Test getting tomorrow's birthdays
        $tomorrowBirthdays = $this->reminder->getUpcomingBirthdays(1);
        $this->assertCount(1, $tomorrowBirthdays);
        $this->assertEquals('Tomorrow', $tomorrowBirthdays[0]['first_name']);
    }
    
    public function testSendBirthdayEmails()
    {
        // Create test member
        $memberId = createTestMember([
            'first_name' => 'Birthday',
            'email' => '<EMAIL>',
            'birth_date' => date('Y-m-d')
        ]);
        
        // Create test template
        $templateId = createTestTemplate([
            'template_name' => 'Birthday Template',
            'subject' => 'Happy Birthday {first_name}!',
            'content' => 'Happy Birthday {first_name}! Have a great day!',
            'is_birthday_template' => 1
        ]);
        
        // Send birthday emails
        $result = $this->reminder->sendBirthdayEmails($templateId);
        
        // Assert email was sent
        $this->assertEquals(1, $result['total_sent']);
        $this->assertEquals(0, $result['total_failed']);
    }
    
    public function testSendBirthdayReminders()
    {
        // Create test member with birthday in 3 days
        $memberId = createTestMember([
            'first_name' => 'Reminder',
            'email' => '<EMAIL>',
            'birth_date' => date('Y-m-d', strtotime('+3 days'))
        ]);
        
        // Create test template
        $templateId = createTestTemplate([
            'template_name' => 'Reminder Template',
            'subject' => 'Birthday Reminder for {first_name}',
            'content' => '{first_name} has a birthday in {DAYS} days!',
            'is_birthday_template' => 0
        ]);
        
        // Send reminder emails
        $result = $this->reminder->sendBirthdayReminders($templateId, 3);
        
        // Assert reminder was sent
        $this->assertEquals(1, $result['total_sent']);
        $this->assertEquals(0, $result['total_failed']);
    }
    
    protected function tearDown(): void
    {
        // Clean up test data
        resetTestDatabase();
        parent::tearDown();
    }
} 